name: External Release [Pa<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>F919]

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  build_and_sign:
    name: Build and Sign APKs
    runs-on: ubuntu-latest

    strategy:
      matrix:
        flavor: [pax, veri, mf919]
        env: [prod]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '11'

      - name: Decrypt Keystore
        run: |
          if [ "${{ matrix.flavor }}" = "mf919" ] || [ "${{ matrix.flavor }}" = "pax" ]; then
            echo "${{ secrets.ANDROID_KEYSTORE_PAX }}" | base64 --decode > keystore.jks
          elif [ "${{ matrix.flavor }}" = "veri" ]; then
            echo "${{ secrets.ANDROID_KEYSTORE_VERIFONE }}" | base64 --decode > keystore.jks
          fi

      - name: Build APK
        run: |
          if [ "${{ matrix.flavor }}" = "mf919" ]; then
            ./gradlew assemblePax${{ matrix.env }}Release --stacktrace
          else
            ./gradlew assemble${{ matrix.flavor }}${{ matrix.env }}Release --stacktrace
          fi

      - name: List Build Outputs (Debug Step)
        run: ls -R app/build/outputs

      - name: Sign APK
        run: |
          if [ "${{ matrix.flavor }}" = "mf919" ]; then
            APK_PATH="app/build/outputs/apk/paxProd/release/app-pax-prod-release-unsigned.apk"
          else
            APK_PATH="app/build/outputs/apk/${{ matrix.flavor }}Prod/release/app-${{ matrix.flavor }}-prod-release-unsigned.apk"
          fi

          SIGNED_APK_PATH="${APK_PATH%-unsigned.apk}-signed.apk"
          ALIGNED_APK_PATH="${SIGNED_APK_PATH%-signed.apk}-aligned.apk"
          FINAL_APK_PATH="${ALIGNED_APK_PATH%-aligned.apk}-final.apk"

          if [ "${{ matrix.flavor }}" = "mf919" ] || [ "${{ matrix.flavor }}" = "pax" ]; then
            storepass="${{ secrets.ANDROID_KEYSTORE_PAX_PASSWORD }}"
            keypass="${{ secrets.ANDROID_KEYSTORE_PAX_KEY_PASSWORD }}"
            alias="${{ secrets.ANDROID_KEYSTORE_PAX_ALIAS }}"
          elif [ "${{ matrix.flavor }}" = "veri" ]; then
            storepass="${{ secrets.ANDROID_KEYSTORE_VERIFONE_PASSWORD }}"
            keypass="${{ secrets.ANDROID_KEYSTORE_VERIFONE_KEY_PASSWORD }}"
            alias="${{ secrets.ANDROID_KEYSTORE_VERIFONE_ALIAS }}"
          fi

          jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore keystore.jks \
            -storepass "$storepass" \
            -keypass "$keypass" \
            -signedjar "$SIGNED_APK_PATH" \
            "$APK_PATH" \
            "$alias"

          ${ANDROID_HOME}/build-tools/30.0.3/zipalign -v 4 "$SIGNED_APK_PATH" "$ALIGNED_APK_PATH"

          if [ "${{ matrix.flavor }}" = "mf919" ]; then
            ${ANDROID_HOME}/build-tools/30.0.3/apksigner sign \
              --ks keystore.jks \
              --ks-pass pass:"$storepass" \
              --key-pass pass:"$keypass" \
              --ks-key-alias "$alias" \
              --out "$FINAL_APK_PATH" \
              "$ALIGNED_APK_PATH"
          else
            mv "$ALIGNED_APK_PATH" "$FINAL_APK_PATH"
          fi

      - name: Upload APK as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.flavor }}-prod-release-apk
          path: |
            app/build/outputs/apk/${{ matrix.flavor == 'mf919' && 'pax' || matrix.flavor }}Prod/release/*-final.apk
          overwrite: true

  create_release:
    name: Create GitHub Release and Tag
    runs-on: ubuntu-latest
    needs: build_and_sign

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Extract Version Name
        id: extract_version
        run: |
          VERSION_NAME=$(./gradlew -q printVersionName)
          echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV
          echo "Version name: $VERSION_NAME"

      - name: Generate Changelog
        id: generate_changelog
        run: |
          git log $(git describe --tags --abbrev=0)..HEAD --pretty=format:"* %s" > CHANGELOG.md

      - name: Create Release Tag
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
          git tag ${{ env.VERSION_NAME }}
          git push https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }} ${{ env.VERSION_NAME }}

      - name: Download APK Artifacts
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.flavor }}-prod-release-apk

      - name: Attach Build Artifacts to Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ env.VERSION_NAME }}
          files: |
            app/build/outputs/apk/${{ matrix.flavor == 'mf919' && 'pax' || matrix.flavor }}Prod/release/*-final.apk
          body_path: CHANGELOG.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}