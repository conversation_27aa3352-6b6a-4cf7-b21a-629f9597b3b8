name: Playstore Release [EDC Saku]

on:
  workflow_dispatch:
    inputs:
      upload-track:
        description: 'Select the track to upload to'
        required: true
        default: 'internal'
        type: choice
        options:
          - internal
          - alpha
          - beta
          - in-app-sharing
      branch:
        description: 'Select the branch to use'
        required: false
        default: 'edc-saku'

jobs:
  aab:
    name: Generate AAB
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: 'jetbrains'

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Configure Gradle distribution
        run: sed -i -e 's/-all.zip/-bin.zip/' gradle/wrapper/gradle-wrapper.properties

      - name: Extract branch name
        shell: bash
        run: echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        id: extract_branch

      - name: Bump version
        run: |
          bash ./scripts/bump_version.sh
        shell: bash

      - name: Comm<PERSON> & <PERSON>ush changes
        uses: actions-js/push@master
        with:
          message: '[skip ci] increase app version code'
          branch: ${{ steps.extract_branch.outputs.branch }}
          github_token: ${{ secrets.ADMIN_WORKFLOW_TOKEN }}

      - name: Write Prod google-services.json & Assemble prod app bundle
        run: |
          echo "$PROD_GSERVICE_JSON" > ./app/src/prod/google-services.json
          echo "$(cat ./app/src/prod/google-services.json)"
          bash ./gradlew bundleplayProdrelease --stacktrace
        shell: bash
        env:
          PROD_GSERVICE_JSON: ${{secrets.PROD_GSERVICE_JSON}}

      - name: List AAB files
        run: |
          echo "Listing AAB files in the expected directory:"
          ls -R app/build/outputs/bundle          

      - name: Sign prod app bundle
        id: sign
        uses: r0adkll/sign-android-release@v1
        with:
          releaseDirectory: app/build/outputs/bundle/playProdRelease
          signingKeyBase64: ${{ secrets.SIGNING_KEY }}
          alias: ${{ secrets.RELEASE_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.RELEASE_KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.RELEASE_KEY_PASSWORD }}

      - name: Upload prod app bundle
        uses: actions/upload-artifact@v4
        with:
          name: app-play-prod-release
          path: app/build/outputs/bundle/playProdRelease/app-play-prod-release.aab
          overwrite: true

      - name: Create service_account.json
        id: createServiceAccount
        run: echo '${{ secrets.SERVICE_ACCOUNT_JSON }}' > service_account.json

      - name: Deploy to Play Store
        id: deploy
        uses: r0adkll/upload-google-play@v1.0.15
        with:
          serviceAccountJson: service_account.json
          packageName: com.bukuwarung.bukuagen
          releaseFiles: app/build/outputs/bundle/playProdRelease/app-play-prod-release.aab
          track: ${{ github.event.inputs.upload-track }}
          whatsNewDirectory: changelog/