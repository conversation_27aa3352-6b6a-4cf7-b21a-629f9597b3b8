# Idempotency Implementation for Double Disbursement Prevention

## Problem Statement

The application was experiencing double disbursement issues where 2-5 identical requests were being sent to the backend within seconds, causing financial losses. Despite UI-level protections (debounce, button disabling), the issue persisted, suggesting network-level problems.

## Root Cause Analysis

### Potential Causes Identified:

1. **Network Layer Issues**:
   - OkHttp retry mechanisms on certain error conditions
   - Token refresh causing request duplication
   - Network timeouts leading to retry attempts

2. **UI State Management**:
   - Race conditions in button click handling
   - Insufficient request-in-flight tracking
   - Multiple ViewModels triggering same requests

3. **Concurrency Issues**:
   - Multiple coroutines executing same operation
   - Lack of request deduplication at application level

## Solution Architecture

### 1. Network-Level Idempotency

#### IdempotencyKeyGenerator
- Generates unique keys based on:
  - Request endpoint and method
  - Request body content (normalized)
  - User context (user ID, client ID)
  - Device context (device ID, serial number)
  - Time window (5-minute buckets)

#### IdempotencyInterceptor
- Adds idempotency headers to financial requests
- Integrates with RequestDeduplicator for duplicate detection
- Handles response caching for successful operations

#### RequestDeduplicator
- Tracks in-flight requests to prevent duplicates
- Caches successful responses for short periods
- Returns 409 Conflict for duplicate requests

### 2. Application-Level Request Tracking

#### RequestTracker
- UI-level duplicate request prevention
- Tracks ongoing operations by unique keys
- Thread-safe with coroutine support

#### SafeClickExtensions
- Extension functions for safe button clicks
- Automatic request state management
- Debounce and one-time click patterns

### 3. ViewModel Integration

Updated PaymentViewModel to:
- Use RequestTracker for duplicate prevention
- Generate unique request keys
- Handle request lifecycle properly

## Implementation Details

### Files Created/Modified:

1. **New Files**:
   - `network/src/main/java/com/bukuwarung/network/idempotency/IdempotencyKeyGenerator.kt`
   - `network/src/main/java/com/bukuwarung/network/interceptors/IdempotencyInterceptor.kt`
   - `network/src/main/java/com/bukuwarung/network/idempotency/RequestDeduplicator.kt`
   - `app/src/main/java/com/bukuwarung/edc/util/RequestTracker.kt`
   - `app/src/main/java/com/bukuwarung/edc/util/SafeClickExtensions.kt`

2. **Modified Files**:
   - `network/src/main/java/com/bukuwarung/network/di/NetworkModule.kt`
   - `network/src/main/java/com/bukuwarung/network/utils/AppProvider.kt`
   - `app/src/main/java/com/bukuwarung/edc/di/AppProviderImpl.kt`
   - `app/src/main/java/com/bukuwarung/edc/payments/ui/core/PaymentViewModel.kt`

### Key Features:

1. **Automatic Idempotency**: Financial endpoints automatically get idempotency keys
2. **Request Deduplication**: Identical requests are blocked at multiple levels
3. **Response Caching**: Successful responses cached briefly to handle retries
4. **UI Protection**: Enhanced button click protection with request tracking
5. **Monitoring**: Built-in statistics and logging for debugging

## Usage Examples

### 1. Using Safe Click Extensions

```kotlin
// In Activity/Fragment
setRequestSafeClickListener(
    view = btnSubmit,
    requestTracker = requestTracker,
    requestKey = "disbursement_${accountId}_${amount}",
    onRequestStart = { showLoading() },
    onRequestFinish = { hideLoading() }
) {
    // Your async operation here
    viewModel.createDisbursement()
}
```

### 2. Manual Request Tracking

```kotlin
// In ViewModel
suspend fun performOperation() {
    val requestKey = requestTracker.generateDisbursementKey(accountId, customerId, amount, bankAccountId)
    
    if (!requestTracker.startRequest(requestKey)) {
        // Request already in progress
        return
    }
    
    try {
        // Perform operation
    } finally {
        requestTracker.finishRequest(requestKey)
    }
}
```

## Configuration

### Idempotent Endpoints
The following endpoints are automatically protected:
- `/api/payments/*/disbursements/*/`
- `/edc-adapter/transfer/posting/*`
- `/edc-adapter/cash-withdrawal/posting/*`
- `/finpro/api/saldo/topup`
- `/api/payments/*/bank_accounts_v2/customer_accounts/add/*`

### Time Windows
- **Idempotency Window**: 5 minutes
- **Response Cache**: 30 seconds
- **Request Timeout**: 5 minutes

## Monitoring and Debugging

### Headers Added to Requests:
- `Idempotency-Key`: Unique key for the request
- `X-Idempotency-Timestamp`: Request timestamp
- `X-Request-ID`: Unique request identifier

### Headers in Responses:
- `X-Idempotency-Replayed`: Present if response was cached
- `X-Cache-Hit`: Present if response came from cache
- `X-Idempotency-Conflict`: Present for 409 responses

### Logging:
All idempotency operations are logged with `[IDEMPOTENCY]` prefix for easy filtering.

## Testing

### Unit Tests Needed:
1. IdempotencyKeyGenerator key generation
2. RequestDeduplicator duplicate detection
3. RequestTracker thread safety
4. Integration tests for full flow

### Manual Testing:
1. Rapid button clicks should be prevented
2. Network retries should not cause duplicates
3. Token refresh should not duplicate requests
4. Cached responses should work correctly

## Deployment Considerations

1. **Gradual Rollout**: Consider feature flags for gradual enablement
2. **Monitoring**: Watch for 409 responses and idempotency logs
3. **Performance**: Monitor impact on request latency
4. **Backend**: Ensure backend supports idempotency headers

## Additional Recommendations

### 1. Backend Idempotency Support
Ensure your backend:
- Respects `Idempotency-Key` header
- Returns same response for duplicate keys
- Has proper key expiration (24 hours recommended)

### 2. Error Handling
- Handle 409 Conflict responses gracefully
- Show appropriate user messages for duplicates
- Log idempotency conflicts for analysis

### 3. Monitoring
- Track idempotency key usage
- Monitor duplicate request rates
- Alert on unusual patterns

### 4. Future Enhancements
- Database-backed request tracking for app restarts
- Configurable idempotency windows per endpoint
- Advanced request fingerprinting
- Integration with analytics for business insights

## Conclusion

This implementation provides comprehensive protection against double disbursements through:
- Network-level idempotency with automatic key generation
- Application-level request deduplication
- Enhanced UI protection mechanisms
- Proper request lifecycle management

The solution addresses the root causes while maintaining good user experience and providing monitoring capabilities for ongoing optimization.
