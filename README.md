# bukuwarung-edc-app
EDC App from Bukuwarung
- Target API version - Android 10
- Architecture used- clean architecture, mvvm with usecases

Layout Directives
- Use constraint layout always
- Follow naming conventions-
  - <widget_type><widget_id>
- Keep hierarchy simple, easier to extend and modify

Code Directives-
- Avoid using third party libraries
  - Gives more control on code
  - Keeps app size controllable
- Use webP images 
- For background drawables 
  - Use bg_<drawable_usability> name for backgrounds of widget
  - drawable_<widget_functionality> for custom drawables specific to widgets
- To start activity with or without parameters, no need to create createIntent(***), 
  instead call openActivity(ClassName::class.java) {
  put<T> ("Key", "Value"")
  }
- For sharedPreferences, use sharedPreferences.put("Key", value) directly
  
Commit Directives
- Use shorter and meaningful commits
- Write proper commit messages, which should tell about the purpose of commit
- Self review commit before asking for PR review from others
- Try to clean up your code before raising PR
- Use <feature/fix/improvement><edc-[JIRA_ID] as identifier for branching
