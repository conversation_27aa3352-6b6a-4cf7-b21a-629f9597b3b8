# Retry Analysis and Idempotency Solutions

## Your 6 Retry Scenario Analysis

### **Timeline of Events (12 seconds, 6 retries):**

```
t=0s:  Request 1 - Key: abc123 → Server (fails with 500)
t=2s:  Request 2 - Key: abc123 → 409 Conflict (blocked)
t=4s:  Request 3 - Key: abc123 → 409 Conflict (blocked)
t=6s:  Request 4 - Key: abc123 → 409 Conflict (blocked)
t=8s:  Request 5 - Key: abc123 → 409 Conflict (blocked)
t=10s: Request 6 - Key: abc123 → 409 Conflict (blocked)
t=12s: Request 7 - Key: abc123 → 409 Conflict (blocked)
```

### **What Happens with Basic Implementation:**

1. **First Request**: Reaches server, gets 500 error, key stays "in-flight"
2. **All Retries**: Get 409 Conflict, never reach server
3. **User Experience**: "Request already being processed" error
4. **Server Load**: Only 1 request hits server (good)
5. **Business Impact**: User can't complete transaction (bad)

## Error Handling Strategies

### **Strategy 1: Clear Key on All Errors (Risky)**

```kotlin
// Clear key on ANY error
catch (e: Exception) {
    requestDeduplicator.removeRequest(idempotencyKey)
    throw e
}
```

**Pros:**
- Allows retries for all errors
- User can always retry

**Cons:**
- **Dangerous for business logic errors** (400, 422)
- Could allow duplicate transactions on validation errors
- May overwhelm server with retries

### **Strategy 2: Smart Error Classification (Recommended)**

```kotlin
when (response.code) {
    // Retryable errors - clear key
    408, 429, 500, 502, 503, 504 -> {
        requestDeduplicator.removeRequest(idempotencyKey)
    }
    
    // Business errors - cache error response
    400, 401, 403, 404, 422 -> {
        requestDeduplicator.cacheResponse(idempotencyKey, response)
    }
    
    // Success - cache success response
    200, 201 -> {
        requestDeduplicator.cacheResponse(idempotencyKey, response)
    }
}
```

### **Strategy 3: Time-Based Key Expiration**

```kotlin
// Shorter in-flight duration for retryable scenarios
private const val IN_FLIGHT_DURATION_MS = 2 * 60_000L // 2 minutes instead of 5

// Different cache durations
private const val SUCCESS_CACHE_DURATION_MS = 30_000L  // 30 seconds
private const val ERROR_CACHE_DURATION_MS = 5_000L     // 5 seconds
```

## Detailed Error Code Analysis

### **Retryable Errors (Clear Key):**

| Code | Meaning | Action | Reason |
|------|---------|--------|---------|
| 408 | Request Timeout | Clear key | Network issue, retry safe |
| 429 | Too Many Requests | Clear key | Rate limiting, retry later |
| 500 | Internal Server Error | Clear key | Server issue, retry safe |
| 502 | Bad Gateway | Clear key | Infrastructure issue |
| 503 | Service Unavailable | Clear key | Temporary server issue |
| 504 | Gateway Timeout | Clear key | Network timeout |

### **Non-Retryable Errors (Cache Error):**

| Code | Meaning | Action | Reason |
|------|---------|--------|---------|
| 400 | Bad Request | Cache error | Invalid request data |
| 401 | Unauthorized | Cache error | Authentication issue |
| 403 | Forbidden | Cache error | Permission issue |
| 404 | Not Found | Cache error | Resource doesn't exist |
| 422 | Unprocessable Entity | Cache error | Validation failed |

### **Success Responses (Cache Success):**

| Code | Meaning | Action | Reason |
|------|---------|--------|---------|
| 200 | OK | Cache success | Operation completed |
| 201 | Created | Cache success | Resource created |
| 202 | Accepted | Cache success | Async operation started |

## Real-World Scenarios

### **Scenario 1: Network Timeout**
```
Request 1: 504 Gateway Timeout → Clear key
Request 2: 200 Success → Cache success
Result: ✅ Transaction succeeds on retry
```

### **Scenario 2: Validation Error**
```
Request 1: 422 Invalid amount → Cache error
Request 2: Same request → Return cached 422
Result: ✅ User sees consistent error, no duplicate attempts
```

### **Scenario 3: Server Error**
```
Request 1: 500 Internal Error → Clear key
Request 2: 500 Internal Error → Clear key
Request 3: 200 Success → Cache success
Result: ✅ Eventually succeeds
```

### **Scenario 4: Rapid Clicks**
```
Click 1: Request sent → In-flight
Click 2: 409 Conflict → Blocked
Click 3: 409 Conflict → Blocked
Result: ✅ Only one request processed
```

## Implementation Recommendations

### **1. Use Smart Error Classification**

```kotlin
class ErrorClassifier {
    fun shouldRetry(responseCode: Int): Boolean {
        return responseCode in setOf(408, 429, 500, 502, 503, 504)
    }
    
    fun shouldCache(responseCode: Int): Boolean {
        return responseCode in setOf(200, 201, 400, 401, 403, 404, 422)
    }
}
```

### **2. Implement Exponential Backoff**

```kotlin
class RetryPolicy {
    fun getRetryDelay(attemptNumber: Int): Long {
        return minOf(1000L * (2.0.pow(attemptNumber)).toLong(), 30000L)
    }
    
    fun shouldRetry(attemptNumber: Int, responseCode: Int): Boolean {
        return attemptNumber < 3 && ErrorClassifier.shouldRetry(responseCode)
    }
}
```

### **3. Add Request Context**

```kotlin
data class RequestContext(
    val attemptNumber: Int = 1,
    val originalTimestamp: Long = System.currentTimeMillis(),
    val userInitiated: Boolean = true
) {
    fun isExpired(maxAgeMs: Long = 5 * 60 * 1000L): Boolean {
        return System.currentTimeMillis() - originalTimestamp > maxAgeMs
    }
}
```

### **4. Enhanced Monitoring**

```kotlin
class IdempotencyMetrics {
    fun trackDuplicateBlocked(key: String, reason: String) {
        Analytics.trackEvent("idempotency_duplicate_blocked", mapOf(
            "key_hash" to key.hashCode().toString(),
            "reason" to reason,
            "timestamp" to System.currentTimeMillis()
        ))
    }
    
    fun trackRetryAllowed(key: String, errorCode: Int) {
        Analytics.trackEvent("idempotency_retry_allowed", mapOf(
            "key_hash" to key.hashCode().toString(),
            "error_code" to errorCode,
            "timestamp" to System.currentTimeMillis()
        ))
    }
}
```

## User Experience Considerations

### **Error Messages:**

```kotlin
fun getErrorMessage(response: Response): String {
    return when {
        response.headers()["X-Idempotency-Conflict"] != null -> 
            "Your request is being processed. Please wait..."
            
        response.headers()["X-Idempotency-Replayed"] != null -> 
            "This request was already processed successfully"
            
        response.code == 429 -> 
            "Too many requests. Please wait a moment and try again"
            
        response.code in 500..599 -> 
            "Server error. Your request will be retried automatically"
            
        else -> 
            "Request failed. Please check your input and try again"
    }
}
```

### **UI State Management:**

```kotlin
sealed class RequestState {
    object Idle : RequestState()
    object InProgress : RequestState()
    object RetryingAfterError : RequestState()
    data class Success(val data: Any) : RequestState()
    data class Error(val message: String, val canRetry: Boolean) : RequestState()
}
```

## Testing Strategy

### **Unit Tests:**

```kotlin
@Test
fun `should clear key on retryable errors`() {
    val response = mockResponse(500)
    interceptor.handleResponse(response, "test_key")
    assertFalse(deduplicator.isInFlight("test_key"))
}

@Test
fun `should cache non-retryable errors`() {
    val response = mockResponse(400)
    interceptor.handleResponse(response, "test_key")
    assertTrue(deduplicator.isCached("test_key"))
}
```

### **Integration Tests:**

```kotlin
@Test
fun `should handle retry scenario correctly`() {
    // First request fails with 500
    mockServer.enqueue(MockResponse().setResponseCode(500))
    // Second request succeeds
    mockServer.enqueue(MockResponse().setResponseCode(200))
    
    // Make requests
    val response1 = apiService.createDisbursement(request)
    val response2 = apiService.createDisbursement(request)
    
    // Verify both reached server
    assertEquals(2, mockServer.requestCount)
    assertTrue(response2.isSuccessful)
}
```

## Conclusion

The improved implementation with smart error classification provides:

1. **Safety**: Prevents duplicate transactions on business errors
2. **Usability**: Allows retries on network/server errors  
3. **Performance**: Reduces server load through caching
4. **Monitoring**: Comprehensive tracking and analytics
5. **Flexibility**: Configurable timeouts and retry policies

This approach balances financial safety with user experience, ensuring that legitimate retries are allowed while preventing dangerous duplicates.
