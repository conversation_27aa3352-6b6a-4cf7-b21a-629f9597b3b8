# Idempotency Usage Examples

## 1. Updating Button Clicks in Activities

### Before (Vulnerable to Double Clicks):
```kotlin
// In PaymentConfirmationActivity
binding.btnConfirm.setOnClickListener {
    // This can be clicked multiple times rapidly
    paymentViewModel.createPaymentOut()
}
```

### After (Protected with Idempotency):
```kotlin
// In PaymentConfirmationActivity
class PaymentConfirmationActivity : AppCompatActivity() {
    
    @Inject
    lateinit var requestTracker: RequestTracker
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Option 1: Using extension function
        setRequestSafeClickListener(
            view = binding.btnConfirm,
            requestTracker = requestTracker,
            requestKey = requestTracker.generateDisbursementKey(
                accountId = Utils.getPaymentAccountId(),
                customerId = Utils.getPaymentAccountId(),
                amount = amount.toString(),
                bankAccountId = bankAccount?.id
            ),
            onRequestStart = {
                binding.btnConfirm.text = "Processing..."
                binding.progressBar.visibility = View.VISIBLE
            },
            onRequestFinish = {
                binding.btnConfirm.text = "Confirm Payment"
                binding.progressBar.visibility = View.GONE
            }
        ) {
            paymentViewModel.createPaymentOut()
        }
        
        // Option 2: Using debounced click (simpler)
        binding.btnConfirm.setDebouncedClickListener(1000L) { view ->
            paymentViewModel.createPaymentOut()
        }
        
        // Option 3: One-time click (for critical operations)
        binding.btnConfirm.setOneTimeClickListener(5000L) { view ->
            paymentViewModel.createPaymentOut()
        }
    }
}
```

## 2. Updating Transfer Money Activity

### Before:
```kotlin
// In TransferMoneyActivity
binding.btnTransfer.setOnClickListener {
    transferViewModel.executeTransfer(amount, targetAccount)
}
```

### After:
```kotlin
// In TransferMoneyActivity
setRequestSafeClickListener(
    view = binding.btnTransfer,
    requestTracker = requestTracker,
    requestKey = requestTracker.generateTransferKey(
        accountId = Utils.getPaymentAccountId(),
        amount = amount.toString(),
        targetAccount = targetAccount
    ),
    onRequestStart = {
        binding.btnTransfer.showLoadingState(true)
        binding.tvStatus.text = "Processing transfer..."
    },
    onRequestFinish = {
        binding.btnTransfer.showLoadingState(false)
        binding.tvStatus.text = ""
    }
) {
    transferViewModel.executeTransfer(amount, targetAccount)
}
```

## 3. Updating Cash Withdrawal Activity

### Before:
```kotlin
// In CashWithdrawalActivity
binding.btnWithdraw.setOnClickListener {
    cashWithdrawalViewModel.processCashWithdrawal(amount)
}
```

### After:
```kotlin
// In CashWithdrawalActivity
setRequestSafeClickListener(
    view = binding.btnWithdraw,
    requestTracker = requestTracker,
    requestKey = requestTracker.generateCashWithdrawalKey(
        accountId = Utils.getPaymentAccountId(),
        amount = amount.toString()
    ),
    onRequestStart = {
        binding.btnWithdraw.isEnabled = false
        binding.btnWithdraw.text = "Processing..."
    },
    onRequestFinish = {
        binding.btnWithdraw.isEnabled = true
        binding.btnWithdraw.text = "Withdraw Cash"
    }
) {
    cashWithdrawalViewModel.processCashWithdrawal(amount)
}
```

## 4. Fragment Usage

```kotlin
// In PaymentFragment
class PaymentFragment : Fragment() {
    
    @Inject
    lateinit var requestTracker: RequestTracker
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setRequestSafeClickListener(
            view = binding.btnPay,
            requestTracker = requestTracker,
            requestKey = "payment_${paymentId}",
            onRequestStart = { showPaymentProgress() },
            onRequestFinish = { hidePaymentProgress() }
        ) {
            paymentViewModel.processPayment()
        }
    }
    
    private fun showPaymentProgress() {
        binding.btnPay.visibility = View.GONE
        binding.progressBar.visibility = View.VISIBLE
        binding.tvStatus.text = "Processing payment..."
    }
    
    private fun hidePaymentProgress() {
        binding.btnPay.visibility = View.VISIBLE
        binding.progressBar.visibility = View.GONE
        binding.tvStatus.text = ""
    }
}
```

## 5. ViewModel Integration

### Enhanced ViewModel with Request Tracking:
```kotlin
@HiltViewModel
class EnhancedPaymentViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val requestTracker: RequestTracker
) : ViewModel() {
    
    fun createDisbursement(amount: BigDecimal, bankAccountId: String) {
        viewModelScope.launch {
            val requestKey = requestTracker.generateDisbursementKey(
                accountId = Utils.getPaymentAccountId(),
                customerId = Utils.getPaymentAccountId(),
                amount = amount.toString(),
                bankAccountId = bankAccountId
            )
            
            // Check if already in progress
            if (requestTracker.isRequestInProgress(requestKey)) {
                _uiState.value = UiState.Error("Request already in progress")
                return@launch
            }
            
            // Start tracking
            if (!requestTracker.startRequest(requestKey)) {
                _uiState.value = UiState.Error("Duplicate request detected")
                return@launch
            }
            
            try {
                _uiState.value = UiState.Loading
                
                val request = DisbursementRequest(
                    amount = amount,
                    // ... other fields
                )
                
                val response = paymentUseCase.createDisbursement(
                    Utils.getPaymentAccountId(),
                    Utils.getPaymentAccountId(),
                    request
                )
                
                if (response.isSuccessful) {
                    _uiState.value = UiState.Success(response.body())
                } else {
                    _uiState.value = UiState.Error(response.errorMessage())
                }
                
            } catch (e: Exception) {
                _uiState.value = UiState.Error(e.message ?: "Unknown error")
            } finally {
                requestTracker.finishRequest(requestKey)
            }
        }
    }
}
```

## 6. Custom Request Keys

```kotlin
// For complex operations, create custom request keys
class CustomRequestKeys {
    
    fun generateTopupKey(accountId: String, amount: String, method: String): String {
        return "topup:$accountId:$amount:$method"
    }
    
    fun generateBankAccountValidationKey(accountNumber: String, bankCode: String): String {
        return "bank_validation:$bankCode:$accountNumber"
    }
    
    fun generateQrisPaymentKey(qrCode: String, amount: String): String {
        val qrHash = qrCode.hashCode().toString()
        return "qris_payment:$qrHash:$amount"
    }
}
```

## 7. Error Handling

```kotlin
// Handle idempotency-related errors
class IdempotencyErrorHandler {
    
    fun handleResponse(response: Response<*>) {
        when (response.code()) {
            409 -> {
                if (response.headers()["X-Idempotency-Conflict"] != null) {
                    // This is a duplicate request
                    showMessage("Request is already being processed")
                } else {
                    // Other conflict
                    showMessage("Request conflict occurred")
                }
            }
            200 -> {
                if (response.headers()["X-Idempotency-Replayed"] != null) {
                    // This response was cached/replayed
                    logEvent("Idempotency cache hit")
                }
            }
        }
    }
}
```

## 8. Testing Idempotency

```kotlin
// Test rapid button clicks
@Test
fun testRapidButtonClicks() {
    // Simulate rapid clicks
    repeat(5) {
        onView(withId(R.id.btnConfirm)).perform(click())
    }
    
    // Verify only one request was made
    verify(paymentUseCase, times(1)).createDisbursement(any(), any(), any())
}

// Test request deduplication
@Test
fun testRequestDeduplication() {
    val requestKey = "test_key"
    
    // First request should succeed
    assertTrue(requestTracker.startRequest(requestKey))
    
    // Second request should fail
    assertFalse(requestTracker.startRequest(requestKey))
    
    // After finishing, should succeed again
    requestTracker.finishRequest(requestKey)
    assertTrue(requestTracker.startRequest(requestKey))
}
```

## 9. Monitoring and Analytics

```kotlin
// Add analytics for idempotency events
class IdempotencyAnalytics {
    
    fun trackDuplicateRequest(requestKey: String) {
        Analytics.trackEvent("idempotency_duplicate_blocked", mapOf(
            "request_key" to requestKey,
            "timestamp" to System.currentTimeMillis()
        ))
    }
    
    fun trackIdempotencyKeyGenerated(endpoint: String, keyHash: String) {
        Analytics.trackEvent("idempotency_key_generated", mapOf(
            "endpoint" to endpoint,
            "key_hash" to keyHash
        ))
    }
}
```

## 10. Configuration and Feature Flags

```kotlin
// Make idempotency configurable
object IdempotencyConfig {
    
    fun isIdempotencyEnabled(): Boolean {
        return RemoteConfig.getBoolean("idempotency_enabled", true)
    }
    
    fun getIdempotencyWindow(): Long {
        return RemoteConfig.getLong("idempotency_window_ms", 5 * 60 * 1000L)
    }
    
    fun getEnabledEndpoints(): Set<String> {
        return RemoteConfig.getString("idempotency_endpoints", "")
            .split(",")
            .toSet()
    }
}
```

These examples show how to gradually migrate your existing code to use the idempotency system while maintaining backward compatibility and adding proper error handling.
