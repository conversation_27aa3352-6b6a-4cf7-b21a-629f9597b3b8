plugins {
    alias libs.plugins.android.application
    alias libs.plugins.kotlin.android
    alias libs.plugins.hilt
    alias libs.plugins.google.services
    alias libs.plugins.ksp
    alias libs.plugins.firebase.crashlytics
    alias libs.plugins.firebase.perf
    id("kotlin-parcelize") // can't use version catalog for this plugin.
}


def versionMajor = 2
def versionMinor = 49
def versionPatch = 0

android {
    namespace "com.bukuwarung.edc"
    defaultConfig {
        applicationId "com.bukuwarung.bukuagen"
        compileSdk libs.versions.androidCompileSdk.get().toInteger()
        minSdk libs.versions.androidMinSdk.get().toInteger()
        targetSdk libs.versions.androidTargetSdk.get().toInteger()
        versionCode versionMajor * 10000 + versionMinor * 100 + versionPatch
        versionName "${versionMajor}.${versionMinor}.${versionPatch}"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    dataBinding {
        enabled = true
    }

    buildTypes {
        debug {
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "true"
        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "false"
        }
    }
    sourceSets {
        main {
            java.srcDirs = ['src/main/java', 'src/main/aidl']
            aidl.srcDirs = ['src/main/aidl']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
        buildConfig true
        aidl true
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    flavorDimensions = ["store", "env"]
    productFlavors {
        play {
            dimension "store"
            applicationId "com.bukuwarung.bukuagen"
            isDefault true
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
            }
        }
        veri {
            dimension "store"
            applicationId "com.bukuwarung.edc"
            ndk {
                abiFilters 'armeabi'
            }
        }
        pax {
            dimension "store"
            applicationId "com.bukuwarung.bukuagen"
            ndk {
                abiFilters 'armeabi'
            }
        }

        dev {
            dimension "env"
            versionNameSuffix "-DEV"
            applicationIdSuffix ".dev"
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungdev.page.link"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungdev.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungdev.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/p?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-dev.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-dev.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-dev.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-dev.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-dev.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-dev.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://2yvdmltuje.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-dev.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyc/verify-prompt"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-dev.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-dev.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-dev.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-dev.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-dev.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-dev.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "S3_BUCKET", '"https://bukuwarungac-image-dev.s3.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"4eeb93e63b515fc12d7a5d575306f4b0"')
            buildConfigField("String", "DEFAULT_MERCHANT_ID", '"718000100010001"')
            buildConfigField("String", "PAX_CLIENT_ID", '"RVSMT470H8SF86GPSTUG"')
            buildConfigField("String", "PAX_CLIENT_SECRET", '"G16KT5PHE2WS5GOHBJJS619WG10HN8H6OI4FUOBI"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_APP_KEY", '"KtmWP7DO6QtG0i1vbhvqKKHSJl8%2BmnkL5ZBvuB9wGTA1MzgfVvOZZ7tgL3egmb5t"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_ACCESS_KEY", '"Xzs0lywwEXg7JxuZLAcbiMPgRKbqWnr4QqgVkKOtxgFwJeIU5XH3xEUpRxGuT1Kt%2FRILH4cVxPC0p2yqLknCj62Mf8TjJZDr525%2FPH4V54cK%2BE2U7jqTGA%3D%3D"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY", '"KtmWP7DO6QueYN9i4KkbyjKLz%2B%2FWpVeO4RM2dRFfcocI6A8LiAL4GUFLSaEqDjNa"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY", '"Xzs0lywwEXjQQ5gTt02PjVZj%2F1H4Av7TZs5KU1U0t%2BokIsKxU4X9fwo53t1jFol36AaF1zgZk9pFEhMmIH0m7U9AGrZEEGgIXA3%2B4JJtZyjuFrxBLDsGsuAcvXu6%2Bht%2B"')
            buildConfigField("String", "CLIENT_ID", '"2e3570c6-317e-4524-b284-980e5a4335b6"')
            buildConfigField("String", "CLIENT_SECRET", '"S81VsdrwNUN23YARAL54MFjB2JSV2TLn"')
            buildConfigField("String","EDC_DASHBOARD_URL",'"https://api-dev.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN"')
            buildConfigField("String","EDC_LANDING_URL",'"https://api-dev.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN"')
            buildConfigField("String","TIKTOK_APP_ID",'"7473416227906453505"')
            buildConfigField("String","MINI_ATMPRO_BUY_EDC_EXTERNAL_URL",'"https://api-dev.bukuwarung.com/mx-mweb/edc/landing/external"')
            buildConfigField("String","BUREAU_CREDENTIAL_ID",'"9d9fc64d-2fff-4659-ab46-7e07c7ee392d"')
        }
        stg {
            dimension "env"
            versionNameSuffix "-STG"
            applicationIdSuffix ".staging"
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungstg.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-staging-v1.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-staging-v1.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-staging-v1.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-staging-v1.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-staging-v1.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-staging-v1.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyc/verify-prompt"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-staging-v1.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-staging-v1.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-staging-v1.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"3fa727dc6d716f0b0dc8c5efae314277"')
            buildConfigField("String", "DEFAULT_MERCHANT_ID", '"718000100010001"')
            buildConfigField("String", "PAX_CLIENT_ID", '"RVSMT470H8SF86GPSTUG"')
            buildConfigField("String", "PAX_CLIENT_SECRET", '"G16KT5PHE2WS5GOHBJJS619WG10HN8H6OI4FUOBI"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_APP_KEY", '"KtmWP7DO6QtG0i1vbhvqKKHSJl8%2BmnkL5ZBvuB9wGTA1MzgfVvOZZ7tgL3egmb5t"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_ACCESS_KEY", '"Xzs0lywwEXg7JxuZLAcbiMPgRKbqWnr4QqgVkKOtxgFwJeIU5XH3xEUpRxGuT1Kt%2FRILH4cVxPC0p2yqLknCj62Mf8TjJZDr525%2FPH4V54cK%2BE2U7jqTGA%3D%3D"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY", '"KtmWP7DO6QueYN9i4KkbyjKLz%2B%2FWpVeO4RM2dRFfcocI6A8LiAL4GUFLSaEqDjNa"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY", '"Xzs0lywwEXjQQ5gTt02PjVZj%2F1H4Av7TZs5KU1U0t%2BokIsKxU4X9fwo53t1jFol36AaF1zgZk9pFEhMmIH0m7U9AGrZEEGgIXA3%2B4JJtZyjuFrxBLDsGsuAcvXu6%2Bht%2B"')
            buildConfigField("String", "CLIENT_ID", '"2e3570c6-317e-4524-b284-980e5a4335b6"')
            buildConfigField("String", "CLIENT_SECRET", '"S81VsdrwNUN23YARAL54MFjB2JSV2TLn"')
            buildConfigField("String","EDC_DASHBOARD_URL",'"https://api-staging-v1.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN"')
            buildConfigField("String","EDC_LANDING_URL",'"https://api-staging-v1.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN"')
            buildConfigField("String","TIKTOK_APP_ID",'"7473416227906453505"')
            buildConfigField("String","MINI_ATMPRO_BUY_EDC_EXTERNAL_URL",'"https://api-staging-v1.bukuwarung.com/mx-mweb/edc/landing/external"')
            buildConfigField("String","BUREAU_CREDENTIAL_ID",'"9d9fc64d-2fff-4659-ab46-7e07c7ee392d"')
        }
        prod {
            dimension "env"
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarung.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-v4.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-v4.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-v4.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-v4.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-v4.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-v4.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.60.0-bud"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-v4.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyc/verify-prompt"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-v4.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-v4.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-v4.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-v4.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-v4.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-v4.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"704d441f5fd99d04f5f547c4323446c8"')
            buildConfigField("String", "DEFAULT_MERCHANT_ID", '"718039455370001"')
            buildConfigField("String", "PAX_CLIENT_ID", '"R41Q34DF3X4S3PZ4D3LT"')
            buildConfigField("String", "PAX_CLIENT_SECRET", '"6YS0JF8VLCJYPS0KMZ4F8KDYV0R7SQUJ6VZ9NKD0"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_APP_KEY", '"KtmWP7DO6QtG0i1vbhvqKKHSJl8%2BmnkL5ZBvuB9wGTA1MzgfVvOZZ7tgL3egmb5t"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_ACCESS_KEY", '"Xzs0lywwEXg7JxuZLAcbiMPgRKbqWnr4QqgVkKOtxgFwJeIU5XH3xEUpRxGuT1Kt%2FRILH4cVxPC0p2yqLknCj62Mf8TjJZDr525%2FPH4V54cK%2BE2U7jqTGA%3D%3D"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY", '"KtmWP7DO6QueYN9i4KkbyjKLz%2B%2FWpVeO4RM2dRFfcocI6A8LiAL4GUFLSaEqDjNa"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY", '"Xzs0lywwEXjQQ5gTt02PjVZj%2F1H4Av7TZs5KU1U0t%2BokIsKxU4X9fwo53t1jFol36AaF1zgZk9pFEhMmIH0m7U9AGrZEEGgIXA3%2B4JJtZyjuFrxBLDsGsuAcvXu6%2Bht%2B"')
            buildConfigField("String", "CLIENT_ID", '"2e3570c6-317e-4524-b284-980e5a4335b6"')
            buildConfigField("String", "CLIENT_SECRET", '"S81VsdrwNUN23YARAL54MFjB2JSV2TLn"')
            buildConfigField("String","EDC_DASHBOARD_URL",'"https://api-v4.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN"')
            buildConfigField("String","EDC_LANDING_URL",'"https://api-v4.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN"')
            buildConfigField("String","TIKTOK_APP_ID",'"7473416227906453505"')
            buildConfigField("String","MINI_ATMPRO_BUY_EDC_EXTERNAL_URL",'"https://api-v4.bukuwarung.com/mx-mweb/edc/landing/external"')
            buildConfigField("String","BUREAU_CREDENTIAL_ID",'"75750154-d69a-46e1-bf13-82b308afe145"')
        }
    }

    gradle.taskGraph.beforeTask { Task task ->
        if (task.name ==~ /process.*GoogleServices/) {
            android.applicationVariants.configureEach { variant ->
                def build = variant.buildType.name
                def store = variant.productFlavors[0].name
                def env = variant.productFlavors[1].name

                if (task.name ==~ /(?i)process${store}${env}${build}GoogleServices/) {
                    copy {
                        from "src/$env/"
                        include "google-services.json"
                        into "."
                    }
                }
            }
        }
    }
}

hilt {
    enableAggregatingTask = true
}

dependencies {
    // Project dependencies (remain unchanged)
    implementation project(':network')
    implementation project(':ui-component')
    implementation project(':bluetooth-devices-setup')

    // File dependencies (remain unchanged)
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation files('libs/sun.misc.BASE64Decoder.jar')
    implementation files('libs/bcprov-jdk15on-160.jar')

    // Use BOMs (Bill of Materials) from the catalog
    implementation platform(libs.firebase.bom)
    implementation platform(libs.kotlin.bom)

    // Use bundles for common groups
    implementation libs.bundles.lifecycle
    implementation libs.bundles.retrofit
    implementation libs.bundles.camera
    implementation libs.bundles.databinding
    implementation libs.bundles.firebase

    // Individual libraries from the catalog
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.google.material
    implementation libs.androidx.annotation
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.activity.ktx
    implementation libs.androidx.work.runtime.ktx
    implementation libs.androidx.browser
    implementation libs.androidx.paging.runtime.ktx
    implementation libs.androidx.swiperefreshlayout
    implementation libs.androidx.security.crypto

    // Hilt
    implementation libs.hilt.android
    implementation libs.androidx.hilt.work
    ksp libs.hilt.compiler

    // Google Play & Firebase
    implementation libs.google.play.services.maps
    implementation libs.google.play.services.location
    implementation libs.google.play.services.auth
    implementation libs.firebase.firestore.ktx
    implementation libs.firebase.auth
    implementation libs.firebase.appcheck.playintegrity
    implementation libs.google.play.app.update.ktx
    implementation libs.google.mlkit.face.detection

    // Bureau
    implementation libs.bureau.device.intelligence
    implementation libs.bureau.apponomics

    // Other third-party libraries
    implementation libs.progressbutton
    implementation libs.shimmer
    implementation libs.glide.core
    ksp libs.glide.compiler
    implementation libs.auth0.jwtdecode
    implementation libs.lottie
    implementation libs.paperdb
    implementation libs.mixpanel
    implementation libs.zoho.mobilisten
    implementation libs.compressor
    implementation libs.tiktok.sdk

    implementation files('libs/sun.misc.BASE64Decoder.jar')
    implementation files('libs/bcprov-jdk15on-160.jar')

    // RxJava
    implementation libs.retrofit.adapter.rxjava2
    implementation libs.rxjava2

    // Dependency with an exclusion
    implementation(libs.paxstore.sdk) {
        exclude group: 'com.google.code.gson', module: 'gson'
    }

    // Testing
    testImplementation libs.junit
    androidTestImplementation libs.bundles.testing.android
}
// Custom Gradle task to print the version name
task printVersionName {
    doLast {
        // Access the versionName from the defaultConfig block in the android configuration
        println "v${android.defaultConfig.versionName}"
    }
}
