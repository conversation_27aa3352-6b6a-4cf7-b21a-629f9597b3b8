# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-keep class com.google.android.gms.location.** { *; }
-target 1.6
-optimizations method/*,!code/simplification/arithmetic,code/*
-optimizationpasses 2
-allowaccessmodification
-mergeinterfacesaggressively
-useuniqueclassmembernames
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
-renamesourcefileattribute SourceFile
-adaptresourcefilenames **.properties
-adaptresourcefilecontents **.properties,META-INF/MANIFEST.MF
-verbose
-dontwarn android.os.**,android.net.**

#models
-keep class com.bukuwarung.edc.card.data.** { *; }
-keep class com.bukuwarung.edc.global.network.model.** { *; }
-keep class com.bukuwarung.edc.homepage.data.model.** { *; }
-keep class com.bukuwarung.edc.login.data.model.** { *; }
-keep class com.bukuwarung.edc.payments.data.model.** { *; }
-keep class com.bukuwarung.edc.verifyotp.data.model.** { *; }
-keep class com.bukuwarung.edc.ppob.common.model.** { *; }
-keep class com.bukuwarung.edc.ppob.confirmation.model.** { *; }
-keep class com.bukuwarung.edc.ppob.recentsandfavourites.model.** { *; }
-keep class com.bukuwarung.edc.ppob.train.model.** { *; }
-keep class com.bukuwarung.edc.card.carddetails.model.** { *; }
 -keep class com.bukuwarung.edc.card.cashwithdrawal.model.** { *; }
#dto
-dontwarn com.pax.market.api.sdk.java.base.dto.**
-keep class com.pax.market.api.sdk.java.base.dto.**{*;}

#By default Google keeps classes of  Activity 、Service ...
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.preference.Preference

#pax
-keepclasseswithmembers,allowshrinking class * {
    native <methods>;
}
#Tiktok
-keep class com.tiktok.** { *; }
-keep class com.android.billingclient.api.** { *; }

-keepclassmembers enum  * {
    public static **[] values();
    public static ** valueOf(String);
}
-keep public class * {
    public protected <fields>;
    public protected <methods>;
}
-keep public abstract class * extends @android.os.IInterface * {
    public <fields>;
    public <methods>;
}
-keepclassmembers class * extends android.os.Parcelable {
    static ** CREATOR;
}

#neptuneapi classes
-keep class com.pax.dal.** {
    <fields>;
    <methods>;
}

-keep class com.pax.nep.** {
    <fields>;
    <methods>;
}

-keep class com.pax.neptunelite.** {
    <fields>;
    <methods>;
}

-keep class com.pax.neptuneliteapi.** {
    <fields>;
    <methods>;
}

# AGP 8 changes android.r8.failOnMissingClasses to always be true and not an treat missing as error.
# https://developer.android.com/build/releases/past-releases/agp-7-0-0-release-notes#r8-missing-class-warning
# TODO: Remove this when we have modularize the variants so no missing classes will be there.

# from bukuwarung webview
-dontwarn com.abedelazizshe.lightcompressorlibrary.**
-dontwarn id.privy.privypass_liveness.**
# from tiktok
-dontwarn com.android.billingclient.api.**
# from morefun sdk
-dontwarn com.cnepay.cnepayinterfacelib.**
-dontwarn com.elink.sdk.**
-dontwarn com.google.zxing.**
-dontwarn com.lk.qf.pay.**
-dontwarn com.yeepay.mpos.money.**
-dontwarn com.zfzf.depend.**
# remove this when androidx.security is updated https://github.com/tink-crypto/tink/issues/702
-dontwarn com.google.api.client.http.**
-dontwarn org.joda.time.**

# unknown
-dontwarn com.whty.device.inter.**
-dontwarn java.lang.reflect.**

# guava
-keep class java.lang.reflect.AnnotatedType { *; }
-keep class com.google.common.reflect.** { *; }




