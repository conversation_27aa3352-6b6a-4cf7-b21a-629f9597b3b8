package com.pax.p_service.aidl;

import android.os.Parcel;
import android.os.Parcelable;

public class DRLData implements Parcelable {
    private byte[] drlID = null; //Dynamic Limit ID (Amex:9f70 Visa:9F5A) Default ID [FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF]
    private byte[] clssFloorLimit = null; //cn6 1.00=[00, 00, 00, 00, 01, 00]
    private byte[] clssTransLimit = null; //cn6 1.00=[00, 00, 00, 00, 01, 00]

    protected DRLData(Parcel in) {
        drlID = in.createByteArray();
        clssFloorLimit = in.createByteArray();
        clssTransLimit = in.createByteArray();
        cvmRequiredLimit = in.createByteArray();
    }

    public static final Creator<DRLData> CREATOR = new Creator<DRLData>() {
        @Override
        public DRLData createFromParcel(Parcel in) {
            return new DRLData(in);
        }

        @Override
        public DRLData[] newArray(int size) {
            return new DRLData[size];
        }
    };

    public byte[] getDrlID() {
        return drlID;
    }
    public byte[] getClssFloorLimit() {
        return clssFloorLimit;
    }
    public byte[] getClssTransLimit() {
        return clssTransLimit;
    }
    public byte[] getCvmRequiredLimit() {
        return cvmRequiredLimit;
    }
    private byte[] cvmRequiredLimit = null;

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeByteArray(drlID);
        parcel.writeByteArray(clssFloorLimit);
        parcel.writeByteArray(clssTransLimit);
        parcel.writeByteArray(cvmRequiredLimit);
    }
}