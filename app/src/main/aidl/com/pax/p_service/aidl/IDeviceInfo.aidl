package com.pax.p_service.aidl;
import com.pax.p_service.aidl.TusnData;

/**
 * \cn_
 * @brief
 *
 * \_en_
 * @brief get some information of the device
 *
 * \en_e
 * \code{.java}
 * \endcode
  */
interface IDeviceInfo {

    /**
     * \cn_
     * @brief 获取终端设备序列号
     *
     * \_en_
     * @brief get the serial number(SN) of the terminal.
     *
     * \en_e
     * \code{.java}
     * \endcode
    */
    String getSerialNo();

    /**
     * \cn_
     * @brief 获取终端IMSI号
     *
     * \_en_
     * @brief get the IMSI of the terminal.
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getIMSI();

    /**
     * \cn_
     * @brief 获取终端IMEI号
     *
     * \_en_
     * @brief get the IMEI of the terminal.
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getIMEI();

    /**
     * \cn_
     * @brief 获取终端SIM卡ICCID号
     *
     * \_en_
     * @brief get the ICCID of the SIM card which present.
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getICCID();

    /**
     * \cn_
     * @brief 获取厂商名称
     *
     * \_en_
     * @brief get name of manufacture
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getManufacture();

    /**
     * \cn_
     * @brief 获取终端型号
     *
     * \_en_
     * @brief get model of the terminal
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getModel();

    /**
     * \cn_
     * @brief 获取Android操作系统版本
     *
     * \_en_
     * @brief get the version of the Android OS.
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getAndroidOSVersion();

    /**
     * \cn_
     * @brief 获取Android内核版本
     *
     * \_en_
     * @brief get the version of Android Kernel
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getAndroidKernelVersion();

    /**
     * \cn_
     * @brief 获取终端ROM版本
     *
     * \_en_
     * @brief get the ROM version of Android.
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getROMVersion();

    /**
     * \cn_
     * @brief 获取终端固件版本
     *
     * \_en_
     * @brief get the firmare version of the terminal.
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getFirmwareVersion();

    /**
     * \cn_
     * @brief 获取终端硬件版本
     *
     * \_en_
     * @brief get the hardware version
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    String getHardwareVersion();

    /**
     * \cn_
     * @brief 更新终端系统时间
     *
     * \_en_
     * @brief update the system time with setting
     * @param date – Date: yyyyMMdd
     * @param time – Time: HHmmss
     * @return true for success, false for failure
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
    */
    boolean updateSystemTime(String date, String time);

    /**
     * <p> set system function by bundle param.
     * Note: INSTALLERAUTOSTART function need ROM support. ROM begin to support from version V1.1.202111150949 INTLv7
     * <p><pre>{@code
     *      Bundle bundle = new Bundle();
     *      bundle.putBoolean("HOMEKEY", true);
     *      bundle.putBoolean("STATUSBARKEY", true);
     * }
     * </pre>
     * @param bundle
     * <ul>
     *     <li>key: HOMEKEY {@code String}; value: true/false @{@code boolean}; true;enable Home-Key, false:disable Home-Key</li>
     *     <li>key: STATUSBARKEY {@code String}; value: true/false @{@code boolean}; true;enable Status-Bar, false:disable Status-Bar</li>
     *     <li>key: INSTALLERAUTOSTART{@code String}; value: true/false @{@code boolean}; true(default) - usbInstaller app run automatically when OTG is connected, false - on the contrary</li>
     * </ul>
     * @return true:set system function success; false:set system function fail;
     * @since 1.x.x
     *
     */
    boolean setSystemFunction(in Bundle bundle);
    /**
     * <p> get the Total RAM. Unit is byte
     *
     * @return {@code String}
     * @since 1.x.x
     */
    String getRamTotal();
    /**
     * <p> get the available RAM capacity. Unit is byte.
     *
     * @return {@code String}
     * @since 1.x.x
     */
    String getRamAvailable();
    /**
     * <p> get the flash RAM capacity. Unit is byte.
     *
     * @return {@code String}
     * @since 1.x.x
     */
    String getRomTotal();
    /**
     * <p> get the available flash RAM capacity. Unit is byte.
     *
     * @return {@code String}
     * @since 1.x.x
     */
    String getRomAvailable();


}
