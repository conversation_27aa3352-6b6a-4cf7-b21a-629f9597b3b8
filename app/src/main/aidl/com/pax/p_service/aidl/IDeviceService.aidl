package com.pax.p_service.aidl;

import com.pax.p_service.aidl.IDeviceInfo;
import com.pax.p_service.aidl.IMagCardReader;
import com.pax.p_service.aidl.IDeviceService;
import com.pax.p_service.aidl.IInsertCardReader;
import com.pax.p_service.aidl.IPinpad;
import com.pax.p_service.aidl.IPrinter;
import com.pax.p_service.aidl.IRFCardReader;
import com.pax.p_service.aidl.IEMV;
import com.pax.p_service.aidl.ISerialPort;
import com.pax.p_service.aidl.IBeeper;

/**
 * \cn_
 * @brief 设备服务对象，提供范围终端各外设对象的服务接口
 *
 * \_en_
 * @brief Device service, get each interface (object) in this interface
 *
 * \en_e
 * \code{.java}
 * \endcode
 * @author: baoxl
 */
interface IDeviceService {
    /**
     * \cn_
     * @brief 获取磁卡操作句柄
     *
     * @return IMagCardReader对象，参见IMagCardReader.aidl类 | @return IMagCardReader, please refer IMagCardReader.aidl
     * \_en_
     * @brief get the IMagCardReader for magnetic card reader
     *
     * @return IMagCardReader, please refer IMagCardReader.aidl
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see IMagCardReader
     */
    IMagCardReader getMagCardReader();
    
    /**
     * \cn_
     * @brief 获取IC卡操作对象
     *
     * 0 IC卡 1 SAM1卡 2 SAM2卡 | 0 for IC card slot, 1 for SAM1 card slot, 2 for SAM2 card slot
     * @return IC卡操作对象，参见IInsertCardReader.aidl类 | @return IInertCardReader, please refer IInsertCardReader.aidl
     * \_en_
     * @brief get the IInsertCardReader for smart card and PSAM card
     *
     * @param slotNo 0 for IC card slot, 1 for SAM1 card slot, 2 for SAM2 card slot
     * @return IInertCardReader, please refer IInsertCardReader.aidl
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see IInertCardReader
     */
    IInsertCardReader getInsertCardReader(int slotNo);
    
    /**
     * \cn_
     * @brief 获取RF卡操作对象
     *
     * @return RF卡操作对象，参见IRFCardReader.aidl类| @ return IRFCardReader, please refer IRFCardReader.aidl
     * \_en_
     * @brief get the IRFCardReader for CTLS card
     *
     * @return IRFCardReader, please refer IRFCardReader.aidl
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see IRFCardReader
     */
    IRFCardReader getRFCardReader();
    
    /**
     * \cn_
     * @brief 获取密码键盘操作对象
     *
     * @param kapId  密码键盘kapId索引，每个kapId对一个逻辑密码键盘 | kapId, the index refer the key set
     * @return IPinpad对象，参见IPinpad.aidl类 | @return IPinpad, please refer IPinpad.aidl
     * \_en_
     * @brief get IPinpad for Pinpad
     *
     * @param kapId  the index refer the keys set
     * @return IPinpad, please refer IPinpad.aidl
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see IPinpad
     */
    IPinpad getPinpad(int kapId);
    
    /**
     * \cn_
     * @brief 获取打印机操作对象
     *
     * @return IPrinter对象，参见IPrinter.aidl类 | @return IPrinter, please refer IPrinter.aidl
     * \_en_
     * @brief get IPrinter for printer
     *
     * @return IPrinter, please refer IPrinter.aidl
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see IPrinter
     */
    IPrinter getPrinter();
    
    /**
     * \cn_
     * @brief 设备操作对象
     *
     * @return IDeviceInfo对象，参见IDeviceInfo.aidl类 | @return IDeviceInfo, please refer IDeviceInfo.aidl
     * \_en_
     * @brief get IDeviceInfo for device information
     *
     * @return IDeviceInfo, please refer IDeviceInfo.aidl
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see IDeviceInfo
     */
    IDeviceInfo getDeviceInfo();

    /**
     * \cn_
     * @brief 获取EMV流程操作对象
     *
     * @return IEMV对象，参见IEMV.aidl类 | @ return IEMV, please refer IEMV.aidl
     * \_en_
     * @brief get IEMV
     *
     * @return IEMV, please refer IEMV.aidl
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
     */
    IEMV getEMV();

     /**
      * \cn_
      * @brief 获取串口操作对象
      *
      * @param deviceType - 串口连接线种类 | String rs232 or usb-rs232
      * @return ISerialPort对象，参见ISerialPort.aidl类 | @return object ISerialPort, please refer ISerialPort.aidl
      * \_en_
      * @brief get the ISerialPort for serial
      *
      * @param deviceType <BR>
      *     String "rs232" means the port via build in serial chip micro USB cable (one side is micro USB connect to terminal, another side is 9 pin interface connect to COM port in PC.)<BR>
      *     "usb-rs232" means the port via micro USB cable<BR>
      *
      *     Set VID & PID in string pattern "usb2rs232-VID-PID", sample: "usb2rs232-11CA-0204"
      *     Only set VID in string pattern "usb2rs232-VID", sample: "usb2rs232-11CA"
      *
      *
      * @return object ISerialPort, please refer ISerialPort.aidl
      * \en_e
      * \code{.java}
      * \endcode
      * @version
      * @see ISerialPort
      */
     ISerialPort getSerialPort(String deviceType);

    /**
     * <p> get the IBeeper interface object for Beeper.
     *
     * @return Beeper object
     * @since 1.x.x
     */
    IBeeper getBeeper();
}
