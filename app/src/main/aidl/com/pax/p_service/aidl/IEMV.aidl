// IEMV.aidl
package com.pax.p_service.aidl;
import com.pax.p_service.aidl.CheckCardListener;
import com.pax.p_service.aidl.UPCardListener;
import com.pax.p_service.aidl.EMVHandler;
import com.pax.p_service.aidl.OnlineResultHandler;
import com.pax.p_service.aidl.CandidateAppInfo;
import com.pax.p_service.aidl.DRLData;
import com.pax.p_service.aidl.BLKData;

/**
 * \_en_
 * @brief EMV object for processing EMV
 *
 * \en_e
 */
interface IEMV {
    /**
     * \_en_
     * @brief check card, non-block method
     *
     * @param cardOption the card type (list)
     * <ul>
     * <li>supportMagCard(boolean) support magnetic card</li>
     * <li>supportSmartCard(boolean) support Smart card</li>
     * <li>supportCTLSCard(boolean) support CTLS card</li>
     * </ul>
     * @param timeout the time out(seconds)
     * @param listener the listerner while found card
     * @version
     * @see stopCheckCard startEMV CheckCardListener
     */
	void checkCard(in Bundle cardOption, int timeout, CheckCardListener listener);

    /**
     * \_en_
     * @brief stop check card
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see checkCard startEMV CheckCardListener
     */
	void stopCheckCard();

    /**
     * \_en_
     * @brief start EMV process
     *
     * @param transflow processing type
     * <ul>
     * <li>1：EMV processing</li>
     * <li>2：EMV simplified processing</li>
     * </ul>
     * @param intent request setting
     * <ul>
     * <li>cardType(int): card type
     *      * CARD_INSERT(0)- smart IC card
     *      * CARD_RF(1)- CTLS card </li>
     * <li>transProcessCode(byte): (1Byte) Translation type (9C first two digits of the ISO 8583:1987 Processing Code)</li>
     * <li>authAmount(long): auth-amount (transaction amount)</li>
     * <li>isSupportSM(boolean): is support SM </li>
     * <li>isForceOnline(boolean): is force online </li>
     * <li>merchantName(String):merchant Name (var. bytes)</li>
     * <li>merchantId(String): merchant ID (15 bytes)</li>
     * <li>terminalId(String):terminal ID (8 bytes)</li>
     * <li>transCurrCode(String): currency code(5F2A), if not set, kernel will find the tag in AID string.</li>
     * <li>otherAmount(String): set Other Amount (9F03) value</li>
     * <li>panConfirmTimeOut(int): set timeout of pan confirm, if not set then default 60s(just support smart card)</li>
     * <li>appSelectTimeOut(int): set timeout of selectApp, if not set then default 60s(just support smart card)</li>
     * <li>traceNo(String):trace no (var. bytes)</li>
     * <li>ctlsPriority(byte): CTLS application priority, no necessary, b0-MyDebit b1~b7 to be define</li>
     * <li>ctlsAidsForSingleTrans(ArrayList<String>): CTLS transaction input temporary aid params(AID + KernelID(9F2A01xx) + transType(DF2901xx) + transCurrCode(DF2A02xxxx))</li>
     * <li>isForceOffline(boolean): is force offline, no necessary, false is default (just support AMEX kernel)</li>
     * <li>isTerminalTypeSetInAID(boolean): default value is false(default vaule is 0x22), you should confirm this tag(9F35) in your AID String when you set this tag is true.</li>
     * <li>ctlsEmvAbortWhenAppBlocked(boolean): when CTLS app blocked then EMV abort</li>
     * </ul>
     * @param handler the call back handler, please refer EMVHandler
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see startEMV abortEMV
     */
	void startEMV(int processType, in Bundle intent, EMVHandler handler);

    /**
     * \_en_
     * @brief stop EMV
     *
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
     * */
	void abortEMV();

    /**
     * \_en_
     * @brief update AID parameter
     *
     * @param operation the setting
     * <ul>
     * <li>1：append</li>
     * <li>2：remove</li>
     * <li>3：clear all</li>
     * </ul>
     * @param aidType type of AID parameter
     * <ul>
     * <li>1：contact(smart card)</li>
     * <li>2：contactless</li>
     * </ul>
     * @param aid the AID parameter
     * @return result, true on success, false on failure
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see getAID
     */
    boolean updateAID(int operation,int aidType, String aid);

    /**
     * \_en_
     * @brief update CA public KEY
     *
     * @param operation the setting
     * <ul>
     * <li>1：append</li>
     * <li>2：remove</li>
     * <li>3：clear all</li>
     * </ul>
     * @param rid the CA public KEY
     * @return true on success, false on failure
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see getRID
     */
    boolean updateRID(int operation, String rid);

    /**
     * \_en_
     * @brief select application (multi-application card)
     *
     * @param index the index of application, start from 1, and 0 means cancel
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see EMVHandler#onSelectApplication
     */
    void importAppSelection(int index);

    /**
     * \_en_
     * @brief import the PIN
     *
     * @param option(int) - 操作选项 | the option
     * <ul>
     * <li> CANCEL(0) cancel</li>
     * <li> CONFIRM(1) confirm</li>
     * </ul>
     * @param pin the PIN data
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see EMVHandler#onRequestInputPIN
     */
    void importPin(int option, in byte[] pin);

/**
     * \_en_
     * @brief import the result of card hodler verification
     *
     * @param option the option
     * <ul>
     * <li> CANCEL(0) cancel ( BYPASS )</li>
     * <li> CONFIRM(1) confirm</li>
     * <li> NOTMATCH(2) not match</li>
     * </ul>
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see EMVHandler#onConfirmCertInfo
     */
    void importCertConfirmResult(int option);

    /**
     * \_en_
     * @brief import the result of card verification
     *
     * @param pass true on pass, false on error
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see EMVHandler#onConfirmCardInfo
     */
    void importCardConfirmResult(boolean pass);


    /**
     * \_en_
     * @brief input the online response
     *
     * @param onlineResult  set the result ( response )
     * <ul>
     * <li> isOnline(boolean)is online</li>
     * <li> respCode(String) the response code</li>
     * <li> authCode(String) the authorize code</li>
     * <li> field55(String) the response of field 55 data</li>
     * </ul>
     * @param handler the result , please refer OnlineResultHandler
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see EMVHandler#onRequestOnlineProcess
     */
    void importOnlineResult(in Bundle onlineResult, OnlineResultHandler handler);

    /**
     * \_en_
     * @brief set EMV (kernel) data in trans process (DCC)
     *
     * In emv flow(onConfirmCardInfo callback or onRequestInputPIN callback), you can modify the emv data. <b>just support smartcard</b><br>
     * for example:<br>
     * 1.firt you set aidString 5F2A=0156, but in onConfirmCardInfo callback you want to reset this tag 5F2A=0116, you can use this interface.<br>
     * 2.second you set authAmount=100(9F02) in startEmv, in onConfirmCardInfo callback you can reset the auth amount.
     * @param tlvList the TLV list
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
     */
    void setEMVData(in List<String> tlvList);

    /**
     * \_en_
     * @brief get kernal data list in Tag-Length-Value format
     *
	 * @param taglist the tag list want query
	 * @return the response in TLV format, null means no response got
     * \en_e
     * \code{.java}
     {
         String[] strlist = {"9F33", "9F40", "9F10", "9F26", "95", "9F37", "9F1E", "9F36",
                 "82", "9F1A", "9A", "9B", "50", "84", "5F2A", "8F"};

         String strs = iemv.getAppTLVList(strlist);
      }
     * \endcode
     * @version
     * @see
	 */
	String getAppTLVList(in String[] taglist);

    /**
     * \_en_
     * @brief get card (emv) data by tag
     *
	 * @param tagName the tag name
	 * @return the emv data got
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see
	 */
	byte[] getCardData(String tagName);

	/**
     * \_en_
     * @brief get EMV data
     *
     * such as card number, valid dtae, card serial number, etc.
     * <em> will return null if the data is not avalible at the current EMV process</em>
	 * @param tagName tag name
	 * <ul>
     * <li> PAN card No.</li>
     * <li> TRACK2 track No.2</li>
     * <li> CARD_SN card SN (Serial Number)</li>
     * <li> EXPIRED_DATE expried date</li>
     * <li> DATE date</li>
     * <li> TIME time</li>
     * <li> BALANCE balance</li>
     * <li> CURRENCY currency</li>
     * </ul>
	 * @return the return data of EMV
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see getCardData
	 *
	 */
	String getEMVData(String tagName);

    /**
     * \en_
     * @brief update CTLS Visa APID parameter
     *
     * @param operation the setting
     * <ul>
     * <li>1：append</li>
     * <li>2：clear</li>
     * </ul>
     * @param DRLData data
     * @return result, true on success, false on failure
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see DRLData
     */
    boolean updateDRL(int operation, in DRLData drlData);


	/**
     * \_en_
     * @brief set EMV kernel to use. set this interface before startEMV()
	 * @param customAidList is Map<String, Integer>, String - custom aid and Integer - kernelID(check CTLSKernelID)
     * \en_e
     * \code{.java}
     * \endcode
     * @version
     * @see CTLSKernelID
	*
	*/
	void registerKernelAID(in Map customAidList);

    /**
     * select spec version of kernel before startEMV(), pls refer CTLSKernelID
	 * @param kernelID:
	 * <pre>
	 * Map < Integer kernelID, Integer ver >
	 *      kernelId- kernelID(check CTLSKernelID) ver- Specification Version
	 *      kernelId:2(Master)  0 - 3.1 ver(default), 1 - 3.1.2 ver
	 *      kernelId:4(AMEX)  0 - 3.1 ver (default), 1 - 4.0.2 ver
	 *      kernelId:5(JCB)  0 - 1.3 ver(default), 1 - 1.4 ver
	 * </pre>
     * @since 2.20.3.12
     */
	void reRegistKernelVersion(in Map tlvList);
}
