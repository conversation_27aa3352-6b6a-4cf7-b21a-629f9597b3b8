package com.pax.p_service.aidl;

import com.pax.p_service.aidl.QrCodeContent;
import com.pax.p_service.aidl.PrinterListener;

/**
 * \cn_
 * 打印机对象<br/>
 * 实现对文本、条形码、二维码和图片的打印功能。
 * \_en_
 * The printer object to print strings, barcode, QR code, image
 * \en_e
 */
interface IPrinter {    
    /**
	 * \en_
     * @brief get printer status
     *
	 * @return the status:
	 * <ul>
	 * <li>ERROR_NONE(0x00) - normal</li>
	 * <li>ERROR_PAPERENDED(0xF0) - Paper out</li>
	 * <li>ERROR_NOCONTENT(0xF1) - no content</li>
     * <li>ERROR_HARDERR(0xF2) - printer error</li>
     * <li>ERROR_OVERHEAT(0xF3) - over heat</li>
     * <li>ERROR_NOBM(0xF6) - no black mark</li>
     * <li>ERROR_BUSY(0xF7) - printer is busy</li>
     * <li>ERROR_MOTORERR(0xFB) - moto error</li>
     * <li>ERROR_LOWVOL(0xE1) - battery low</li>
     * <li>ERROR_NOTTF(0xE2) - no ttf</li>
     * <li>ERROR_BITMAP_TOOWIDE(0xE3) - width of bitmap too wide</li>
     * </ul>
     * \en_e
	 */
	int getStatus();

    /**
	 * \cn_
	 * 添加图片打印
	 * @param format - 打印格式，可设置打印的位置、宽度、高度
	 * <ul>
	 * <li>offset(int) - 打印起始位置</li>
	 * <li>width(int) - 宽度(<=384)</li>
     * <li>height(int) - 高度</li>
     * <li>gray(int) - 灰度（0~255）</li>
     * </ul>
	 * @param imageData - 图片数据
	 * \_en_
	 *  @brief Add an image to print
	 * @param format - the format setting
	 * <ul>
	 * <li>offset(int) - the offset from left</li>
	 * <li>width(int) - the width of the image want to print.(MAX = 384)</li>
     * <li>height(int) - the height want to print</li>
     * <li>gray(int) - set pixcel gray to pint（0~255 default = 128）</li>
     * </ul>
	 * @param imageData - the image buffer
	 * \en_e
	 * <p>
	 * \code{.java}
	      // get image buffer from id
	     private byte[] getBitmapByte(int id) {
	          BitmapFactory.Options bfoOptions = new BitmapFactory.Options();
              bfoOptions.inScaled = false;
              ByteArrayOutputStream out = new ByteArrayOutputStream();
              BitmapFactory.decodeResource(context.getResources(), id, bfoOptions).compress(Bitmap.CompressFormat.JPEG, 100, out);
              try {
                  out.flush();
                  out.close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
              return out.toByteArray();
          }
	 * \endcode
	 * \code{.java}
 // get image buffer from file
 public byte[] image2byte(String path) {
     byte[] data = null;
     FileInputStream input = null;
     try {
         input = new FileInputStream(new File(path));
         ByteArrayOutputStream output = new ByteArrayOutputStream();
         byte[] buf = new byte[1024];
         int numBytesRead = 0;
         while ((numBytesRead = input.read(buf)) != -1) {
             output.write(buf, 0, numBytesRead);
         }
         data = output.toByteArray();
         output.close();
         input.close();
     } catch (FileNotFoundException ex1) {
         ex1.printStackTrace();
     } catch (IOException ex1) {
         ex1.printStackTrace();
     }
     return data;
 }
	 * \endcode
	 */
	void addImage(in Bundle format, in byte[] imageData);

	/**
    	 * \cn_
    	 * 添加一行打印文本
    	 * \_en_
    	 * @brief Add text string to print
    	 *
    	 * \en_e
    	 * \cn_
    	 * @param format - 打印字体格式
    	 * \_en_
    	 * @param format - the format setting
    	 * \en_e
    	 * <ul>
    	 * <li>font(int, size of the font)<br>
    	 *      |- 0:small(size16)<br>
    	 *      |- 1:normal(size24)<br>
    	 *      |- 2:normal_bigger(size24 double height & bold)<br>
    	 *      |- 3:large(size32)<br>
    	 *      |- 4:large_bigger(size32 double height & bold)<br>
    	 *      |- 5:huge(size48)
    	 *      |- 6:normal_wide(size24 double width & bold)
    	 *      |- 7:large_wide(size32 double width & bold)
    	 *
    	 * </li>
    	 * <li>fontStyle(String)<br>
    	 *      |--/xxxx/xx.ttf(absolute path, custom font by user)
    	 * </li>
    	 * <li>align(int) - 0:left, 1:center, 2:right</li>
    	 * \cn_
    	 * <li>bold(boolean) - true粗体 - false常规</li>
    	 * <li>newline(boolean) - true:换行, false:不换行</li>
    	 * \_en_
    	 * <li>bold(boolean) - true: bold - false: normal</li>
    	 * <li>newline(boolean) - true: new line after print, false: normal </li>
    	 * <li>scale_w(float) - multiple Width</li>
    	 * <li>scale_h(float) - multiple Height</li>
    	 * \en_e
    	 * </ul>
    	 * \cn_
    	 * @param text - 打印文本
    	 * \_en_
    	 * @param text - the text string want to print
    	 * \en_e
    	 * @see addTextInLine()
    	 */
    	void addText(in Bundle format, String text);

    /**
     * \cn_
	 * 添加二维码打印
	 * @param format - 打印格式，可设置打印的位置、期望高度
	 * <ul>
	 * <li>offset(int) - 打印起始位置 </li>
	 * <li>expectedHeight(int) - 期望高度</li>
	 * </ul>
	 * @param qrCode - 二维码内容
	 * \_en_
	 * @brief add a QR code to print
	 * @param format - the format
	 * <ul>
	 * <li>offset(int) - the offset from the left </li>
	 * <li>expectedHeight(int) - the expected height & width of the QR code. The actual size should multiple of the minimun pixel size of QR code</li>
	 * </ul>
	 * @param qrCode - the string of the QR code
	 * \en_e
	 */
	void addQrCode(in Bundle format, String qrCode);
    /**
	 * \cn_
	 * 走纸
	 * @param lines - 行数(lines > 1 && lines <= 50)
	 * \_en_
	 * @brief Feed the paper
	 * @param lines - lines should > 1 && lines <= 50<BR>The lines should be the actual lines+1 because the current line need be counted.
	 * \en_e
	 */
	void feedLine(int lines);
	
	/**
	 * \cn_
	 * 启动打印
	 * @param listener - 打印结果监听器
	 * \_en_
	 * @brief Start print
	 * @param listener - the call back listener to tell the print result
	 * \en_e
	 */
	void startPrint(PrinterListener listener);
	void init();
}
