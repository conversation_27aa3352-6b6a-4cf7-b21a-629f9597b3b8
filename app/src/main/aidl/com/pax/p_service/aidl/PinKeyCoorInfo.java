package com.pax.p_service.aidl;

import android.os.Parcel;
import android.os.Parcelable;

public class PinKeyCoorInfo implements Parcelable {
    private String keyName;
    private int coor1_x;
    private int coor1_y;
    private int coor2_x;
    private int coor2_y;
    private int keyType;

    public PinKeyCoorInfo(Parcel in) {
        keyName = in.readString();
        coor1_x = in.readInt();
        coor1_y = in.readInt();
        coor2_x = in.readInt();
        coor2_y = in.readInt();
        keyType = in.readInt();
    }

    public PinKeyCoorInfo(String key,int x1, int y1, int x2,int y2, int type) {
        keyName = key;
        coor1_x = x1;
        coor1_y = y1;
        coor2_x = x2;
        coor2_y = y2;
        keyType = type;
    }

    public static final Creator<PinKeyCoorInfo> CREATOR = new Creator<PinKeyCoorInfo>() {
        @Override
        public PinKeyCoorInfo createFromParcel(Parcel in) {
            return new PinKeyCoorInfo(in);
        }

        @Override
        public PinKeyCoorInfo[] newArray(int size) {
            return new PinKeyCoorInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(keyName);
        parcel.writeInt(coor1_x);
        parcel.writeInt(coor1_y);
        parcel.writeInt(coor2_x);
        parcel.writeInt(coor2_y);
        parcel.writeInt(keyType);
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public int getCoor1_x() {
        return coor1_x;
    }

    public void setCoor1_x(int coor1_x) {
        this.coor1_x = coor1_x;
    }

    public int getCoor1_y() {
        return coor1_y;
    }

    public void setCoor1_y(int coor1_y) {
        this.coor1_y = coor1_y;
    }

    public int getCoor2_x() {
        return coor2_x;
    }

    public void setCoor2_x(int coor2_x) {
        this.coor2_x = coor2_x;
    }

    public int getCoor2_y() {
        return coor2_y;
    }

    public void setCoor2_y(int coor2_y) {
        this.coor2_y = coor2_y;
    }

    public int getKeyType() {
        return keyType;
    }

    public void setKeyType(int keyType) {
        this.keyType = keyType;
    }
}
