package com.bukuwarung.edc

import android.os.Build
import android.widget.Toast
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.util.Utils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.zoho.livechat.android.ZohoLiveChat

object ZohoChat {

    const val CARD_READER_INSTRUCTION = "card_reader_instruction"

    fun openZohoChat(entryPoint:String) {
        ZohoLiveChat.Chat.setLanguage("in")
        ZohoLiveChat.Chat.show()
        ZohoLiveChat.Visitor.setContactNumber(
            Utils.getPhoneNumber()
        )
        ZohoLiveChat.Visitor.setEmail(
            Utils.getPhoneNumber().plus("@merchant.com")
        )
        try {
            if (Utils.getBusinessName().isNullOrEmpty()) {
                ZohoLiveChat.Visitor.setName("No Name")
            } else {
                ZohoLiveChat.Visitor.setName(Utils.getBusinessName())
            }
            ZohoLiveChat.Visitor.addInfo("terminal_id", Utils.getTerminalId())
            ZohoLiveChat.Visitor.addInfo("connected_serial_number", Utils.getDeviceSerialNumber())
            if (!Utils.isFixedTerminal() && Utils.getDeviceSerialNumber().isNotEmpty()) {
                ZohoLiveChat.Visitor.addInfo(
                    "device_activation",
                    if (Utils.getConnectedDeviceMasterKey(Utils.getDeviceSerialNumber())
                            .isNullOrEmpty()
                    ) "not_activated" else "activated"
                )
                ZohoLiveChat.Visitor.addInfo(
                    "TMS",
                    BuildConfig.API_BASE_URL.plus("/panacea/panacea/edc/terminal-management-system?search=" + Utils.getDeviceSerialNumber())
                )
            }
            if(Utils.isFixedTerminal()){
                ZohoLiveChat.Visitor.addInfo(
                    "manufacturer",
                    Build.MANUFACTURER
                )
            }
            val map = HashMap<String, String>()
            map[PpobAnalytics.ENTRY_POINT] = entryPoint
            Analytics.trackEvent("customer_support_chat",map)
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
        try {
            if (BuildConfig.DEBUG) {
                ZohoLiveChat.getApplicationManager()?.currentActivity?.let{
                    Toast.makeText(it, "Zoho Open from $entryPoint", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    object EntryPoint {
        const val CASH_WITHDRAWAL = "cash_withdrawal"
        const val BALANCE_INQUIRY = "balance_inquiry"
        const val TRANSFER = "transfer"
        const val EDC_ORDER = "edc_order"
    }
}

typealias ZohoChatEntryPoint = ZohoChat.EntryPoint