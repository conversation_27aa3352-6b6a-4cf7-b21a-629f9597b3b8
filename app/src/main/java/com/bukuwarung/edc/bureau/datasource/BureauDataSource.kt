package com.bukuwarung.edc.bureau.datasource

import com.bukuwarung.edc.bureau.model.request.BureauTrackEventRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface BureauDataSource {

    @POST("api/v1/auth/event/track")
    suspend fun trackAuthEvent(
        @Header("session-id") sessionId: String,
        @Body request: BureauTrackEventRequest
    ): Response<Unit>
}