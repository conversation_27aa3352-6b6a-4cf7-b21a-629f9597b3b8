package com.bukuwarung.edc.bureau.di

import com.bukuwarung.edc.bureau.datasource.BureauDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class BureauModule {

    @Singleton
    @Provides
    fun provideBureauDataSource(@Named("normal") retrofit: Retrofit): BureauDataSource {
        return retrofit.create(BureauDataSource::class.java)
    }
}