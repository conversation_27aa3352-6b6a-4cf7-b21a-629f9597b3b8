package com.bukuwarung.edc.bureau.usecase

import com.bukuwarung.edc.bureau.model.request.BureauTrackEventRequest
import com.bukuwarung.edc.bureau.repository.BureauRepository
import javax.inject.Inject

class BureauUseCase @Inject constructor(private val bureauRepository: BureauRepository) {

    suspend fun trackAuthEvent(sessionId: String, request: BureauTrackEventRequest) =
        bureauRepository.trackAuthEvent(sessionId, request)
}