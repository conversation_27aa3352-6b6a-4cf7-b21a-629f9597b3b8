package com.bukuwarung.edc.card

import com.bukuwarung.edc.card.data.repository.CheckBalanceRepository
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import com.bukuwarung.edc.card.transfermoney.data.repository.TransferMoneyRepository
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import javax.inject.Inject

class CardUseCase @Inject constructor(
    private val checkBalanceRepository: CheckBalanceRepository,
    private val transferMoneyRepository: TransferMoneyRepository
) {

    suspend fun fetchCardBalance(
        serialNumber: String,
        accountId: String,
        checkBalanceRequest: CheckBalanceRequest
    ) = checkBalanceRepository.fetchCardBalance(serialNumber, accountId, checkBalanceRequest)

    suspend fun inquireTransferMoney(
        serialNumber: String,
        accountId: String,
        requestBody: TransferMoneyRequestResponseBody
    ) =
        transferMoneyRepository.enquireTransferMoney(serialNumber, accountId, requestBody)

    suspend fun cashWithdrawalInquiry(
        serialNumber: String,
        accountId: String,
        requestBody: TransferMoneyRequestResponseBody
    ) =
        transferMoneyRepository.cashWithdrawalInquiry(serialNumber, accountId, requestBody)

    suspend fun transferMoney(
        serialNumber: String,
        accountId: String,
        requestBody: TransferMoneyRequestResponseBody
    ) = transferMoneyRepository.transferMoney(serialNumber, accountId, requestBody)
}