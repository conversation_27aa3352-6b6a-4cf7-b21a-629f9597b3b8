package com.bukuwarung.edc.card.activation.ui

import android.content.res.Resources
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.activation.ui.constant.CardActivationConstants
import com.bukuwarung.edc.databinding.BottomsheetEdcActivationBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.ui.BTRouterActivity.Companion.CONTENTS
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getClassTag
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EdcActivationBottomSheet(
    private val activatedStatus: ActivatedStatus,
    private val callback: (() -> Unit)?
) :
    BaseBottomSheetDialogFragment() {

    companion object {

        fun createInstance(
            fr: FragmentManager,
            activatedStatus: ActivatedStatus,
            argument: Bundle,
            callback: (() -> Unit)? = null

        ) =
            EdcActivationBottomSheet(activatedStatus, callback).apply {
                arguments = argument
            }.show(fr, getClassTag())
    }

    private lateinit var _binding: BottomsheetEdcActivationBinding
    private lateinit var className: String
    private val bodyContents by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(CONTENTS, HomePageBody::class.java)
        } else {
            arguments?.getParcelable(CONTENTS)
        }
    }
    private val transactionMode by lazy {
        arguments?.getString("mode") ?: TransactionType.TRANSFER_INQUIRY.type
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BottomsheetEdcActivationBinding.inflate(layoutInflater, container, false)
        return _binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupBottomSheetBehavior()
        setUpView()
        className = arguments?.getString("className").orEmpty()
        Log.d("--->className", className)
        Log.d("--->mode", transactionMode)
        Log.d("--->contents", bodyContents.toString())
        _binding.ivClose.singleClick {
            dialog?.dismiss()

        }
        _binding.btnConfirm.singleClick {
            val map = HashMap<String, String>()
            when (activatedStatus) {
                ActivatedStatus.START -> {
                    if (Utils.isFixedTerminal()) {
                        map["entry_point"] = "pop_up_android_screen"
                        map["redirection_to"] = "android_activation_flow"
                        callback?.invoke()
                    } else {
                        map["redirection_to"] = "saku_connection_flow"
                        requireActivity().finish()
                        requireContext().openActivity(Class.forName(className)) {
                            putString("mode", transactionMode)
                            putParcelable(CONTENTS, bodyContents)
                        }
                    }
                }

                ActivatedStatus.COMPLETE -> {
                    requireActivity().finish()
                    className = "com.bukuwarung.edc.card.ui.BTCardReaderInstructionActivity"
                    requireContext().openActivity(Class.forName(className)) {
                        putString("mode", transactionMode)
                        putParcelable(CONTENTS, bodyContents)
                    }
                }
            }
            Analytics.trackEvent(CardActivationConstants.EVENT_EDC_ACTIVATION_BUTTON_CLICKED, map)
            dialog?.dismiss()
        }

    }

    private fun setupBottomSheetBehavior() {
        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let {
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
            dialog.setCancelable(false)
        }
    }

    private fun setUpView() {
        when (activatedStatus) {
            ActivatedStatus.START -> {
                val image = if (Utils.isFixedTerminal()) {
                    R.drawable.ic_activate_android
                } else {
                    R.drawable.ic_card_reader_sleep
                }
                val title = if (Utils.isFixedTerminal()) {
                    R.string.activate_edc_first_android
                } else {
                    R.string.activate_edc_first
                }
                val description = if (Utils.isFixedTerminal()) {
                    R.string.you_have_a_edc_device_activate_it_android
                } else {
                    R.string.you_have_a_edc_device_activate_it
                }
                _binding.ivActivation.setImageDrawable(
                    ContextCompat.getDrawable(
                        requireContext(),
                        image
                    )
                )
                _binding.tvTitle.text = getString(title)
                _binding.tvDescription.text = getString(description)
                _binding.btnConfirm.text = getString(R.string.activate_now)
            }

            ActivatedStatus.COMPLETE -> {
                _binding.ivActivation.setImageDrawable(
                    ContextCompat.getDrawable(
                        requireContext(),
                        R.drawable.ic_edc_activated
                    )
                )
                _binding.tvTitle.text = getString(R.string.edc_successfully_activated)
                _binding.tvDescription.text =
                    getString(R.string.edc_successfully_activated__description)
                when (transactionMode) {
                    TransactionType.TRANSFER_INQUIRY.type -> {
                        _binding.btnConfirm.text = getString(R.string.transfer)
                    }

                    TransactionType.CASH_WITHDRAWAL.type -> {
                        _binding.btnConfirm.text = getString(R.string.withdraw_cash)
                    }

                    TransactionType.BALANCE_INQUIRY.type -> {
                        _binding.btnConfirm.text = getString(R.string.filter_history_check_balance)
                    }
                }

            }
        }
    }
}

enum class ActivatedStatus {
    START,
    COMPLETE
}