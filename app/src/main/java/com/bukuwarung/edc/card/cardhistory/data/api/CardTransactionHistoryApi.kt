package com.bukuwarung.edc.card.cardhistory.data.api

import com.bukuwarung.edc.card.cardhistory.model.TransactionHistoryResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface CardTransactionHistoryApi {

    @GET("edc-adapter/transaction/history/{account_id}/v2")
    suspend fun getTransactionHistory(
        @Path("account_id") accountId: String,
        @Query("type") type: String?,
        @Query("page_number") pageNumber: Int,
        @Query("page_size") pageSize: Int,
        @Query("order") order: String?,
        @Query("start_date") startDate: String?,
        @Query("end_date") endDate: String?,
        @Query("terminal_id") terminalId: String?,
        @Query("status") status: String?,
    ): Response<TransactionHistoryResponse>
}