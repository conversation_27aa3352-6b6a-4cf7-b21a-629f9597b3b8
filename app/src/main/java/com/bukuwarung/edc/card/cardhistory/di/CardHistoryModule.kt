package com.bukuwarung.edc.card.cardhistory.di

import com.bukuwarung.edc.card.cardhistory.data.api.CardTransactionHistoryApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class CardHistoryModule {

    @Provides
    @Singleton
    fun provideCardHistoryDataSource(@Named("normal") retrofit: Retrofit): CardTransactionHistoryApi =
        retrofit.create(CardTransactionHistoryApi::class.java)
}