package com.bukuwarung.edc.card.cardhistory.pagingdatasource

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.bukuwarung.edc.card.cardhistory.model.HistoryItem
import com.bukuwarung.edc.card.cardhistory.usecase.CardTransactionHistoryUseCase
import com.bukuwarung.edc.order.orderhistory.usecase.EdcOrderHistoryUseCase

class TransactionHistoryPagingSource(
    val useCase: CardTransactionHistoryUseCase,
    val edcOrderHistoryUseCase: EdcOrderHistoryUseCase,
    val accountId: String,
    val pageNumber: Int = 0,
    val pageSize: Int,
    val order: String?,
    val startDate: String?,
    val endDate: String?,
    val type: String?,
    val isOrderHistory: Boolean = false,
    val terminalId: String?,
    val status: String?
) : PagingSource<Int, HistoryItem>() {
    override fun getRefreshKey(state: PagingState<Int, HistoryItem>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, HistoryItem> {
        val curPage = params.key ?: pageNumber
        return try {
            val response = if (isOrderHistory) edcOrderHistoryUseCase.getTransactionHistory(
                curPage,
                pageSize,
                order,
                startDate,
                endDate,
                type
            ) else useCase.getTransactionHistory(
                accountId,
                curPage,
                pageSize,
                order,
                startDate,
                endDate,
                type,
                terminalId,
                status
            )
            LoadResult.Page(
                data = response,
                prevKey = if (curPage == 0) null else curPage - 1,
                nextKey = if (response.isEmpty()) null else curPage + 1
            )
        } catch (e: Exception) {
            LoadResult.Error(Exception(e.message))
        }
    }
}