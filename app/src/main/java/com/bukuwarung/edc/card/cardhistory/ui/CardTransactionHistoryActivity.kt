package com.bukuwarung.edc.card.cardhistory.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.paging.CombinedLoadStates
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.activation.ui.EdcActivationFragment
import com.bukuwarung.edc.card.activation.ui.EdcActivationFragment.EDCActivationFragmentType
import com.bukuwarung.edc.card.activation.ui.constant.CardActivationConstants
import com.bukuwarung.edc.card.cardhistory.adapter.TransactionHistoryAdapter
import com.bukuwarung.edc.card.cardhistory.adapter.TransactionItemLoadStateAdapter
import com.bukuwarung.edc.card.cardhistory.model.HistoryItem
import com.bukuwarung.edc.card.cardhistory.viewmodel.CardTransactionHistoryViewModel
import com.bukuwarung.edc.card.constant.CardHistoryAnalyticsConstants
import com.bukuwarung.edc.card.constant.CardHistoryAnalyticsConstants.SORT_PROPERTY_VALUE_NEW_TO_OLD
import com.bukuwarung.edc.card.constant.CardHistoryAnalyticsConstants.SORT_PROPERTY_VALUE_OLD_TO_NEW
import com.bukuwarung.edc.card.data.model.EdcOrderWarrantyNudgeBody
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.EDC_ORDER_WARRANTY_NUDGE
import com.bukuwarung.edc.card.ui.transactiondetail.ui.TransactionDetailsActivity
import com.bukuwarung.edc.card.ui.transactiondetail.ui.TransactionDetailsActivity.Companion.BALANCE_INQUIRY
import com.bukuwarung.edc.card.ui.transactiondetail.ui.TransactionDetailsActivity.Companion.TRANSFER_POSTING
import com.bukuwarung.edc.card.ui.transactiondetail.ui.TransactionDetailsActivity.Companion.WITHDRAW_CASH
import com.bukuwarung.edc.databinding.ActivityCardHistoryBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.order.orderdetail.ui.EdcOrderDetailsActivity
import com.bukuwarung.edc.order.orderhistory.enums.EdcDevicePlanType
import com.bukuwarung.edc.order.orderhistory.enums.HistoryType
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.payments.ui.history.bottomsheet.CardTransactionSortingBottomSheet
import com.bukuwarung.edc.payments.ui.history.bottomsheet.DateFilterBottomSheet
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.addQuery
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.getClassTag
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityAndFinish
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL
import com.google.android.material.tabs.TabLayout
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Calendar

@AndroidEntryPoint
class CardTransactionHistoryActivity : AppCompatActivity() {

    companion object {
        const val HISTORY_TYPE = "history_type"
    }

    private val viewModel: CardTransactionHistoryViewModel by viewModels()

    private lateinit var binding: ActivityCardHistoryBinding
    private lateinit var adapter: TransactionHistoryAdapter
    private val deviceVendor by lazy { intent?.getStringExtra("device_vendor") ?: "" }
    private val edcHistoryType by lazy {
        intent?.getStringExtra(HISTORY_TYPE) ?: HistoryType.transaction.name
    }
    private val terminalId by lazy {
        intent?.getStringExtra("terminal_id") ?: Utils.getTerminalId()
    }
    private val paymentAccountId by lazy {
        intent?.getStringExtra("payment_account_id") ?: Utils.getPaymentAccountId()
    }
    private val serialNumber by lazy {
        intent?.getStringExtra("serial_number") ?: Utils.getHardwareSerialNumber()
    }

    private val warrantyNudge by lazy {
        Utils.sharedPreferences.get(EDC_ORDER_WARRANTY_NUDGE,"")
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCardHistoryBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setUpToolBar()
        trackHistoryEvent()
        viewModel.getCurrentDevice(serialNumber)
        getWarrantyNudge()
        checkFragmentState()
        if (deviceVendor.isEmpty()) {
            if (Utils.isAtmPro()) {
                binding.tvEdcDeviceName.showView()
                binding.viewDivider.showView()
                binding.btnBuyAtmProEdc.showView()
                if (Utils.getUserRegisteredDevices().isNotEmpty()) {
                    val device = Utils.getUserRegisteredDevices()[0]
                    binding.tvEdcDeviceName.text = device.vendor.plus("-").plus(device.serialNumber)
                }
            } else {
                binding.btnBuyAtmProEdc.hideView()
                binding.tvEdcDeviceName.hideView()
                binding.viewDivider.hideView()
            }
        } else {
            binding.tvEdcDeviceName.showView()
            binding.viewDivider.showView()
            binding.tvEdcDeviceName.text =
                Utils.getDeviceNameBasedOnVendor(deviceVendor, serialNumber)
        }

        binding.rvHistory.layoutManager = LinearLayoutManager(this)
        adapter = TransactionHistoryAdapter(this, edcHistoryType, {
            redirectToTrxHistoryDetailPage(it)
        }, {
            activateEdc(it)
        })
        binding.rvHistory.adapter = adapter.withLoadStateHeaderAndFooter(
            header = TransactionItemLoadStateAdapter(),
            footer = TransactionItemLoadStateAdapter()
        )
        adapter.addLoadStateListener { loadState ->
            binding.rvHistory.isVisible = loadState.source.refresh is LoadState.NotLoading
            binding.pbLoading.isVisible = loadState.source.refresh is LoadState.Loading
            val isEmpty = loadState.source.refresh is LoadState.NotLoading && adapter.itemCount == 0
            binding.layoutEmptyView.root.isVisible =
                (edcHistoryType == HistoryType.transaction.name) && (isEmpty || loadState.source.refresh is LoadState.Error)
            binding.layoutEmptyEdcOrderView.root.isVisible =
                edcHistoryType == HistoryType.order.name && (isEmpty || loadState.source.refresh is LoadState.Error)
            binding.layoutEmptyView.btnEmptyCta.text = getString(R.string.retry)
            handleError(loadState)
        }
        binding.layoutEmptyView.btnEmptyCta.singleClick {
            adapter.retry()
        }
        binding.layoutEmptyEdcOrderView.btnEmptyCta.singleClick {
            openActivityAndFinish(WebviewActivity::class.java) {
                putString(
                    WEBVIEW_URL,
                    BuildConfig.EDC_DASHBOARD_URL
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
        }

        binding.btnBuyAtmProEdc.singleClick {
            val entryPoint = if (Utils.isAtmPro()) "MINIATMPRO" else "BUKUAGEN"
            openActivity(WebviewActivity::class.java) {
                putString(
                    WEBVIEW_URL,
                    BuildConfig.MINI_ATMPRO_BUY_EDC_EXTERNAL_URL.addQuery("entryPoint=$entryPoint")
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
        }

        loadInitialData()

        with(binding) {
            if (edcHistoryType == HistoryType.transaction.name) {
                tlHistory.addTab(
                    tlHistory.newTab().setText(getString(R.string.filter_history_all))
                )
                tlHistory.addTab(
                    tlHistory.newTab().setText(getString(R.string.filter_history_check_balance))
                )
                tlHistory.addTab(
                    tlHistory.newTab().setText(getString(R.string.filter_history_transfer))
                )
                tlHistory.addTab(
                    tlHistory.newTab().setText(getString(R.string.withdraw_cash))
                )
            } else {
                tlHistory.hideView()
            }
            subscribeState()
            tvFilterDate.singleClick {
                DateFilterBottomSheet.createInstance(
                    supportFragmentManager,
                    true,
                    viewModel.defaultOrSelectedDateFilter
                ) { dateFilter ->
                    dateFilter?.let {
                        viewModel.defaultOrSelectedDateFilter = it
                        tvFilterDate.text = calculateLabel(it)
                        val startDate = Calendar.getInstance()
                        val (startTimeInMillis, endDateInMillis) = PaymentAuxilliary.getDates(it)
                        val endDateInYYYYMMDDFormat = DateTimeUtils.getFormattedDateTime(
                            endDateInMillis ?: startDate.timeInMillis,
                            DateTimeUtils.YYYY_MM_DD
                        )
                        val startDateInYYYYMMDDFormat = DateTimeUtils.getFormattedDateTime(
                            startTimeInMillis ?: startDate.timeInMillis,
                            DateTimeUtils.YYYY_MM_DD
                        )
                        viewModel.pageNumber = 0
                        viewModel.startDate = startDateInYYYYMMDDFormat
                        viewModel.endDate = endDateInYYYYMMDDFormat

                        viewModel.getTransactionHistoryData(
                            pageNumber = viewModel.pageNumber,
                            type = if (edcHistoryType == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
                            startDate = viewModel.startDate,
                            endDate = viewModel.endDate,
                            paymentAccountId = paymentAccountId,
                            historyType = edcHistoryType,
                            terminalId = terminalId
                        )
                        tvFilterDate.isSelected = true
                        updateFilter()
                    }
                }
            }
            tvSort.singleClick {
                CardTransactionSortingBottomSheet.createInstance(
                    supportFragmentManager,
                    getString(R.string.sort),
                    viewModel.sortOptions,
                    getDefaultSort()
                ) {
                    viewModel.sort = it
                    tvSort.text =it.second
                    viewModel.pageNumber = 0
                    viewModel.getTransactionHistoryData(
                        pageNumber = viewModel.pageNumber,
                        type = if (edcHistoryType == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
                        order = viewModel.sort.first,
                        paymentAccountId = Utils.getPaymentAccountId(),
                        historyType = edcHistoryType,
                        terminalId = terminalId,
                        status = viewModel.status.first
                    )
                    tvSort.isSelected = true
                    updateFilter()
                }
            }

            tvStatus.singleClick {
                CardTransactionSortingBottomSheet.createInstance(
                    supportFragmentManager,
                    getString(R.string.filter_status),
                    viewModel.statusOptions,
                    getDefaultStatus()
                ) {
                    viewModel.status = it
                    tvStatus.text =it.second
                    viewModel.pageNumber = 0
                    viewModel.getTransactionHistoryData(
                        pageNumber = viewModel.pageNumber,
                        type = if (edcHistoryType == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
                        order = viewModel.sort.first,
                        paymentAccountId = Utils.getPaymentAccountId(),
                        historyType = edcHistoryType,
                        terminalId = terminalId,
                        status = viewModel.status.first
                    )
                    tvStatus.isSelected = true
                    updateFilter()
                }
            }

            tlHistory.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    viewModel.pageNumber = 0
                    when (tab?.position) {
                        0 -> {
                            viewModel.type = ""
                        }

                        1 -> {
                            viewModel.type = BALANCE_INQUIRY
                        }

                        2 -> {
                            viewModel.type = TRANSFER_POSTING
                        }

                        3 -> {
                            viewModel.type = WITHDRAW_CASH
                        }
                    }
                    viewModel.getTransactionHistoryData(
                        pageNumber = viewModel.pageNumber,
                        type = if (edcHistoryType == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
                        startDate = viewModel.startDate,
                        endDate = viewModel.endDate,
                        paymentAccountId = Utils.getPaymentAccountId(),
                        historyType = edcHistoryType,
                        terminalId = terminalId,
                        status = viewModel.status.first,
                        order = viewModel.sort.first
                    )
                    trackHistoryEvent()
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    // no implementation
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                    // no implementation
                }

            })

            ivClearFilter.singleClick {
                ivClearFilter.hideView()
                binding.tvSort.isSelected = false
                binding.tvSort.text = getString(R.string.sort)
                binding.tvFilterDate.isSelected = false
                binding.tvFilterDate.text = getString(R.string.select_date)
                binding.tvStatus.isSelected = false
                binding.tvStatus.text = getString(R.string.filter_status)
                viewModel.type = viewModel.type
                viewModel.pageNumber = 0
                viewModel.startDate = null
                viewModel.endDate = null
                viewModel.sort = Pair("", "")
                viewModel.status = Pair("", "")
                viewModel.defaultOrSelectedDateFilter = DateFilter(
                    label = "Hari ini",
                    presetValue = PaymentConst.DATE_PRESET.TODAY,
                    endDate = null,
                    startDate = null,
                    isChecked = true,
                    endDays = null,
                    startDays = null,
                )
                loadInitialData()
            }
        }


    }

    private fun getDefaultSort(): Pair<String, String> {
        if(viewModel.sort.first.isNotEmpty() && viewModel.sort.second.isNotEmpty()) {
            return viewModel.sort
        }
        return Pair("DESC", "Terbaru - Terlama")
    }

    private fun getDefaultStatus(): Pair<String, String> {
        if(viewModel.status.first.isNotEmpty() && viewModel.status.second.isNotEmpty()) {
            return viewModel.status
        }
        return Pair("SUCCESS", "Berhasil")
    }

    private fun activateEdc(it: HistoryItem) {
        val map = HashMap<String, String>()
        map.put("entry_point","purchase_history_page")
        if (!Utils.isSakuDeviceBasedOnVendor(it.vendor.orEmpty())) {
            //android
            map.put("redirection_to","android_activation_education_screen")
            addEdcAndroidActivationFragment()
        } else {
            //saku
            map.put("redirection_to","saku_connection_flow")
            val device = Utils.getUserRegisteredDevices()
            val deviceSerialNumberList = device.map { it.serialNumber }
            openActivityForResult(SetupBluetoothDeviceActivity::class.java, launcher) {
                putString(
                    SetupBluetoothDeviceActivity.DEVICE_TYPE,
                    SetupBluetoothDeviceActivity.CARD_READER
                )
                putStringArrayList(
                    SetupBluetoothDeviceActivity.DEVICE_SN_LIST,
                    ArrayList(deviceSerialNumberList)
                )
            }
        }
        Analytics.trackEvent(CardActivationConstants.EVENT_EDC_ACTIVATION_BUTTON_CLICKED, map)
    }

    private fun checkFragmentState() {
        supportFragmentManager.registerFragmentLifecycleCallbacks(object :
            FragmentManager.FragmentLifecycleCallbacks() {

            override fun onFragmentAttached(fm: FragmentManager, f: Fragment, context: Context) {
                super.onFragmentAttached(fm, f, context)
                if (f is EdcActivationFragment) {
                    binding.clDetails.hideView()
                    binding.containerEdcAndroidActivation.showView()
                }
            }

            override fun onFragmentDetached(fm: FragmentManager, f: Fragment) {
                super.onFragmentDetached(fm, f)
                if (f is EdcActivationFragment) {
                    binding.clDetails.showView()
                    binding.containerEdcAndroidActivation.hideView()
                }
            }

        }, false)
    }

    val launcher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()){result: ActivityResult->
        if (result.resultCode == RESULT_OK) {
            if (result.resultCode == RESULT_OK) {
                val intent = Intent(this, HomePageActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                intent.putExtra("IS_ACTIVATED", true)
                startActivity(intent)
            }
        }
    }

    private fun addEdcAndroidActivationFragment() {
        val fragment = EdcActivationFragment.newInstance(type = EDCActivationFragmentType.Android)
        val transaction = supportFragmentManager.beginTransaction()
        transaction.add(R.id.container_edc_android_activation, fragment)
        transaction.addToBackStack(fragment.getClassTag())
        transaction.commit()
    }

    private fun trackHistoryEvent() {
        val map = HashMap<String, String>()
        map[CardHistoryAnalyticsConstants.EVENT_PROPERTY_TRANSACTION_TAB_SELECTED] =
            when (viewModel.type) {
                BALANCE_INQUIRY -> CardHistoryAnalyticsConstants.TRANSACTION_TYPE_VALUE_TAB_BALANCE_CHECK
                TRANSFER_POSTING -> CardHistoryAnalyticsConstants.TRANSACTION_TYPE_VALUE_TAB_TRANSFER
                else -> CardHistoryAnalyticsConstants.TRANSACTION_TYPE_VALUE_TAB_ALL
            }
        map[CardHistoryAnalyticsConstants.EVENT_PROPERTY_SORT] =
            if (viewModel.sort.second == viewModel.NEWEST_TO_OLDEST) SORT_PROPERTY_VALUE_NEW_TO_OLD else SORT_PROPERTY_VALUE_OLD_TO_NEW
        map[CardHistoryAnalyticsConstants.EVENT_PROPERTY_TIME_FILTER] =
            viewModel.defaultOrSelectedDateFilter.presetValue?.name.orEmpty()
        Analytics.trackEvent(CardHistoryAnalyticsConstants.EVENT_VISIT_HISTORY, map)
    }

    private fun loadInitialData() {
        viewModel.getTransactionHistoryData(
            pageNumber = viewModel.pageNumber,
            type = if (edcHistoryType == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
            startDate = viewModel.startDate,
            endDate = viewModel.endDate,
            paymentAccountId = Utils.getPaymentAccountId(),
            historyType = edcHistoryType,
            terminalId = terminalId
        )
    }

    private fun updateFilter() {
        with(binding) {
            ivClearFilter.isVisible = tvFilterDate.isSelected || tvSort.isSelected || tvStatus.isSelected
        }
        trackHistoryEvent()
    }

    private fun handleError(loadState: CombinedLoadStates) {
        val errorState = loadState.source.append as? LoadState.Error
            ?: loadState.source.prepend as? LoadState.Error
        errorState?.let {
            Toast.makeText(this, "error occurred", Toast.LENGTH_SHORT).show()
        }
    }

    private fun subscribeState() {
        viewModel.transactionData.observe(this@CardTransactionHistoryActivity) {
            lifecycleScope.launch {
                if (it != null) {
                    adapter.submitData(it)
                }
            }
        }

        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.deviceDetail.collectLatest {
                Utils.setBusinessNameForSelectedSerialNumber(
                    it?.data?.storeName ?: Utils.getBusinessName()
                )
            }
        }
    }

    private fun setUpToolBar() {
        with(binding.toolbar) {
            tvTitle.text =
                if (edcHistoryType == HistoryType.order.name) getString(R.string.edc_order_history_title) else getString(
                    R.string.card_history
                )
            btnBack.singleClick { onBackPressed() }
        }

    }

    private fun calculateLabel(dateFilter: DateFilter): String {
        return if (dateFilter.presetValue?.name?.uppercase() == "CUSTOM_RANGE") {
            val startDate =
                DateTimeUtils.getFormattedDateTime(
                    dateFilter.startDate ?: 0,
                    DateTimeUtils.DD_MMM_YY
                )
            val endDate =
                DateTimeUtils.getFormattedDateTime(dateFilter.endDate ?: 0, DateTimeUtils.DD_MMM_YY)
            return startDate.plus("-").plus(endDate)
        } else dateFilter.label
    }

    private fun calculateStartDateEndDate(
        startDate: Calendar,
        dateFilter: DateFilter
    ): Pair<Long?, Long?> {
        val endDate = startDate.clone() as Calendar
        when (dateFilter.presetValue?.name?.uppercase()) {
            "TODAY" -> {
                endDate.add(Calendar.DAY_OF_MONTH, 1)
            }

            "LAST_SEVEN_DAYS" -> {
                endDate.add(Calendar.DAY_OF_MONTH, 7)
            }

            "THIS_MONTH" -> {
                endDate.add(Calendar.MONTH, 1)
            }

            "CUSTOM_RANGE" -> return Pair(dateFilter.startDate, dateFilter.endDate)

            else -> throw IllegalArgumentException("not supported")
        }
        return Pair(startDate.timeInMillis, endDate.timeInMillis)
    }

    private fun redirectToTrxHistoryDetailPage(data: HistoryItem) {

        if (HistoryType.order.name == edcHistoryType) {
            openActivity(EdcOrderDetailsActivity::class.java) {
                putString(EdcOrderDetailsActivity.ORDER_ID, data.id)
            }
        } else {
            openActivity(TransactionDetailsActivity::class.java) {
                putString(TransactionDetailsActivity.INTENT_KEY_TRANSACTION_TYPE, data.type)
                putString(TransactionDetailsActivity.INTENT_KEY_TERMINAL_ID, terminalId)
                putString(TransactionDetailsActivity.INTENT_KEY_TRANSACTION_ID, data.id)
                putDouble(
                    TransactionDetailsActivity.INTENT_KEY_TRANSFER_AMOUNT,
                    data.amount ?: 0.00
                )
                putString(TransactionDetailsActivity.INTENT_KEY_TRANSACTION_DATE, data.date)
                putString(
                    TransactionDetailsActivity.INTENT_KEY_REFERENCE_NUMBER,
                    data.referenceNumber
                )
            }
        }
    }

    private fun getWarrantyNudge() {
        val typeToken = object : TypeToken<EdcOrderWarrantyNudgeBody>() {}.type
        val edcWarrantyNudge = Gson().fromJson<EdcOrderWarrantyNudgeBody>(warrantyNudge, typeToken)
        // Should be shown only if he has a Morefun device and is a saku user.
        binding.warrantyLayout.root.visibility =
            (edcWarrantyNudge.isVisible == true && Utils.isCardReader() && Utils.hasAMoreFunSakuDevice()).asVisibility()
        val orderChannel = if (Utils.isAtmPro()) "MiniATMPro" else "BUKUAGEN"
        binding.warrantyLayout.btnRedirect.singleClick {
            val url =
                BuildConfig.API_BASE_URL + edcWarrantyNudge.redirectionUrl?.addQuery("order_type=$orderChannel")
            openActivity(WebviewActivity::class.java) {
                putString(
                    WEBVIEW_URL,
                    url
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
        }
        binding.warrantyLayout.ivClose.singleClick {
            binding.warrantyLayout.root.hideView()
        }
    }

}