package com.bukuwarung.edc.card.cardhistory.usecase

import com.bukuwarung.edc.card.cardhistory.data.repository.CardHistoryRepository
import com.bukuwarung.edc.card.cardhistory.model.HistoryItem
import javax.inject.Inject

class CardTransactionHistoryUseCase @Inject constructor(private val transactionHistoryRepository: CardHistoryRepository) {

    suspend fun getTransactionHistory(
        accountId: String,
        pageNumber: Int,
        pageSize: Int,
        order: String?,
        startDate: String?,
        endDate: String?,
        type: String?,
        terminalId: String?,
        status: String?
    ): ArrayList<HistoryItem> {
        return try {
            val response = transactionHistoryRepository.getTransactionHistory(
                accountId,
                pageNumber,
                pageSize,
                order,
                startDate,
                endDate,
                type,
                terminalId,
                status
            )
            if (response.isSuccessful) response.body()?.history!! else throw Exception()
        } catch (e: Exception) {
            throw Exception()
        }
    }
}