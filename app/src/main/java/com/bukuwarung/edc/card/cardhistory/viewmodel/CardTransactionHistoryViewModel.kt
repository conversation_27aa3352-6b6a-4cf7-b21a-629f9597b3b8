package com.bukuwarung.edc.card.cardhistory.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.map
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.card.cardhistory.model.HistoryItem
import com.bukuwarung.edc.card.cardhistory.pagingdatasource.TransactionHistoryPagingSource
import com.bukuwarung.edc.card.cardhistory.usecase.CardTransactionHistoryUseCase
import com.bukuwarung.edc.homepage.usecase.HomePageUseCase
import com.bukuwarung.edc.login.data.model.UserDetail
import com.bukuwarung.edc.order.orderhistory.enums.HistoryType
import com.bukuwarung.edc.order.orderhistory.usecase.EdcOrderHistoryUseCase
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.network.utils.ResourceState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class HistoryListItem {
    data class HistoryItem(val historyItem: com.bukuwarung.edc.card.cardhistory.model.HistoryItem) :
        HistoryListItem()

    data class HistoryItemSeparator(val data: String) : HistoryListItem()
}

@HiltViewModel
class CardTransactionHistoryViewModel @Inject constructor(
    private val cardTransactionHistoryUseCase: CardTransactionHistoryUseCase,
    private val edcOrderHistoryUseCase: EdcOrderHistoryUseCase,
    private val homePageUseCase: HomePageUseCase
) : ViewModel() {

    private var pager: Pager<Int, HistoryItem> = createPager()

    var startDate: String? = null
    var endDate: String? = null
    var type = ""
    var pageNumber = 0
    var sort: Pair<String, String> = Pair("", "")
    var status: Pair<String, String> = Pair("", "")
    var defaultOrSelectedDateFilter: DateFilter = DateFilter(
        label = "Hari ini",
        presetValue = PaymentConst.DATE_PRESET.TODAY,
        endDate = null,
        startDate = null,
        isChecked = true,
        endDays = null,
        startDays = null
    )
    val NEWEST_TO_OLDEST = "DESC"
    val OLDEST_TO_NEWEST = "ASC"

    val sortOptions = listOf(
        NEWEST_TO_OLDEST to "Terbaru - Terlama",
        OLDEST_TO_NEWEST to "Terlama - Terbaru"
    )

    val statusOptions = listOf(
        "SUCCESS" to "Berhasil",
        "PENDING" to "Pending",
        "FAILED" to "Gagal"
    )

    private var _transactionData = MutableLiveData<PagingData<HistoryListItem>>()
    val transactionData: LiveData<PagingData<HistoryListItem>> get() = _transactionData

    private val _deviceDetailFlow = MutableSharedFlow<UserDetail?>()
    val deviceDetail = _deviceDetailFlow.asSharedFlow()


    private fun createPager(
        pageNumber: Int = 0,
        pageSize: Int = 10,
        order: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: String? = null,
        accountId: String = "",
        historyType: String = HistoryType.transaction.name,
        terminalId: String = "",
        status: String? = null
    ): Pager<Int, HistoryItem> {
        return Pager(
            config = PagingConfig(pageSize = 10, prefetchDistance = 2),
            pagingSourceFactory = {
                TransactionHistoryPagingSource(
                    cardTransactionHistoryUseCase,
                    edcOrderHistoryUseCase,
                    accountId = accountId,
                    pageNumber = pageNumber,
                    pageSize = pageSize,
                    order = order,
                    startDate = startDate,
                    endDate = endDate,
                    type = type,
                    isOrderHistory = historyType == HistoryType.order.name,
                    terminalId = terminalId,
                    status = status
                )
            })
    }

    fun getTransactionHistoryData(
        pageNumber: Int,
        pageSize: Int = 10,
        order: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: String? = null,
        paymentAccountId: String,
        historyType: String,
        terminalId: String,
        status: String? = null
    ) {
        val newPager =
            createPager(
                pageNumber = pageNumber,
                pageSize = pageSize,
                order = order,
                startDate = startDate,
                endDate = endDate,
                type = type,
                accountId = paymentAccountId,
                historyType = historyType,
                terminalId = terminalId,
                status = status
            )
        pager = newPager
        viewModelScope.launch {
            newPager.flow.map { pagingData ->
                pagingData.map {
                    HistoryListItem.HistoryItem(it)
                }
            }
                .map {
                    it.insertSeparators { before, after ->

                        if (after == null) {
                            return@insertSeparators null
                        }
                        val nameOfAfterItem = DateTimeUtils.getUTCTimeToLocalDateTime(
                            if (historyType == HistoryType.order.name) after.historyItem.paymentDate else after.historyItem.date,
                            DateTimeUtils.DD_MMM_YYYY
                        )

                        if (before == null) {
                            return@insertSeparators HistoryListItem.HistoryItemSeparator(
                                nameOfAfterItem.orEmpty()
                            )
                        }
                        val nameBeforeItem = DateTimeUtils.getUTCTimeToLocalDateTime(
                            if (historyType == HistoryType.order.name) after.historyItem.paymentDate else before.historyItem.date,
                            DateTimeUtils.DD_MMM_YYYY
                        )
                        if (nameBeforeItem != nameOfAfterItem) {
                            return@insertSeparators HistoryListItem.HistoryItemSeparator(
                                nameOfAfterItem.orEmpty()
                            )
                        } else {
                            null
                        }
                    }
                }
                .cachedIn(viewModelScope).collect {
                    _transactionData.value = it
                }
        }

    }

    fun getCurrentDevice(serialNumber: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val response = homePageUseCase.fetchUserDetails(serialNumber)) {
                    is ResourceState.Success -> {
                        _deviceDetailFlow.emit(response.data)
                    }
                    is ResourceState.Failure ->{
                        _deviceDetailFlow.emit(null)
                        bwLog(Exception(response.throwable))
                    }
                    else -> {
                        _deviceDetailFlow.emit(null)
                    }
                }
            } catch (e: Exception){
                bwLog(e = e)
            }
        }
    }


}

