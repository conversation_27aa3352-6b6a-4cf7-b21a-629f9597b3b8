package com.bukuwarung.edc.card.cashwithdrawal.api

import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccountResponse
import com.bukuwarung.edc.card.transfermoney.model.EdcBankResponse
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface CashWithdrawalApi {

    @PUT("edc-adapter/settlement/bank/{payment_account_id}")
    suspend fun addSettlementBankAccount(
        @Path("payment_account_id") paymentAccountId: String,
        @Query("terminal_id") terminalId: String,
        @Body bankAccount: SettlementBankAccount
    ): Response<SettlementBankAccount>

    @GET("edc-adapter/settlement/bank")
    suspend fun getSettlementBankList(
        @Query("terminal_id") terminalId: String,
        @Query("is_primary") isPrimary: Boolean
    ): Response<SettlementBankAccountResponse>

    @PUT("edc-adapter/settlement/bank/primary/{bank_account_id}")
    suspend fun setPrimarySettlementBankAccount(
        @Path("bank_account_id") bankAccountId: String,
    ): Response<Unit>

    @DELETE("edc-adapter/settlement/bank/{bank_account_id}")
    suspend fun deleteSettlementBankAccount(
        @Path("bank_account_id") bankAccountId: String,
    ): Response<Unit>
}