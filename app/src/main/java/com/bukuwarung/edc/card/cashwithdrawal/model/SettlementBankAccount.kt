package com.bukuwarung.edc.card.cashwithdrawal.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class SettlementBankAccount(

    @field:SerializedName("bank_code")
    val bankCode: String? = null,

    @field:SerializedName("account_number")
    val accountNumber: String? = null,

    @field:SerializedName("beneficiary_name")
    val beneficiaryName: String? = null,

    @field:SerializedName("is_primary")
    val isPrimary: Boolean? = false,

    @field:SerializedName("bank_name")
    val bankName: String? = null,

    @field:SerializedName("id")
    val id: String? = null,

    @field:SerializedName("bank_logo")
    val bankLogo: String? = null,

    @SerializedName("is_cashback_withdrawal")
    var isCashbackWithdrawal: Boolean = false
) : Parcelable
