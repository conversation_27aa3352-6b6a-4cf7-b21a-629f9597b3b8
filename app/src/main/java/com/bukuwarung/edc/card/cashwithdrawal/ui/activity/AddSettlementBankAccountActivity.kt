package com.bukuwarung.edc.card.cashwithdrawal.ui.activity

import android.app.Activity
import android.content.Intent
import android.os.Handler
import android.util.Log
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.bluetooth_printer.base.BaseActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.card.cashwithdrawal.ui.bottomsheet.AddUsedBankBottomSheet
import com.bukuwarung.edc.card.cashwithdrawal.ui.bottomsheet.ProcessViewBottomSheet
import com.bukuwarung.edc.card.cashwithdrawal.viewmodel.CashWithdrawAddBankAccountViewModel
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.CHECK_SETTLEMENT_BANK_ACCOUNT_ATTEMPT
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.CHECK_SETTLEMENT_BANK_ACCOUNT_RESULT
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.INPUT_SETTLEMENT_BANK_ACCOUNT_PAGE
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.SETTLEMENT_BANK_ACCOUNT_SAVED_RESULT
import com.bukuwarung.edc.databinding.ActivityAddSettlementBankAccountBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.order.orderdetail.ui.EdcOrderDetailsActivity.Companion.EDC_ORDER_DETAIL
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig.getPaymentConfigs
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.ui.addbank.AddBankAccountActivity
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity
import com.bukuwarung.edc.payments.ui.widgets.BankAccountView
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.ToastUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.setSettlementBankAccount
import com.bukuwarung.edc.util.addQuery
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.isFalse
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.visibleIfTrue
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

@AndroidEntryPoint
class AddSettlementBankAccountActivity : BaseActivity() {
    private lateinit var binding: ActivityAddSettlementBankAccountBinding

    companion object {
        const val TAGD = "DEBUG--->"
        const val ENTRY_POINT = "ENTRY_POINT"
        private const val RC_SELECT_BANK = 98
        const val NO_INTERNET_ERROR_MESSAGE =
            "Terjadi kesalahan dengan permintaanmu, silakan cek koneksi internetmu"
        const val ADDED_BANK_ACCOUNT = "added_bank_account"
        const val RNL_WITHDRAWAL_ACCOUNT = "EDC_REVENUE_SHARING"
    }

    private val entryPoint by lazy { intent?.getStringExtra(ENTRY_POINT) }

    private var processingViewHandler: Handler? = null
    private val viewModel: CashWithdrawAddBankAccountViewModel by viewModels()
    override fun setViewBinding() {
        binding = ActivityAddSettlementBankAccountBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)
        binding.toolbar.setNavigationOnClickListener {
            Utils.hideKeyboard(this)
            onBackPressed()
        }
        binding.toolBarMenu.setOnClickListener { openHelp() }
        Log.d(TAGD, "entryPoint: $entryPoint")

        Analytics.trackEvent(INPUT_SETTLEMENT_BANK_ACCOUNT_PAGE)

        binding.buttonSelectBank.setOnClickListener {
            val intent = Intent(this, SelectBankActivity::class.java).apply {
                val transactionType =
                    if (entryPoint?.equals(EDC_ORDER_DETAIL) == true) TransactionType.EDC_ORDER.type else if (entryPoint?.equals(
                            RNL_WITHDRAWAL_ACCOUNT
                        ) == true
                    ) TransactionType.RNL_WITHDRAWAL.type else TransactionType.CASH_WITHDRAWAL.type
                putExtra(SelectBankActivity.TRANSACTION_TYPE, transactionType)
            }
            startActivityForResult(intent, RC_SELECT_BANK)
        }

        binding.inputAccountNumber.afterTextChanged {
            binding.txtLabelAccountNumber.setTextColor(getColorCompat(R.color.black_60))
            binding.inputAccountNumber.setTextColor(getColorCompat(R.color.blue_60))
            binding.layoutInputAccountNumber.boxStrokeColor = getColorCompat(R.color.blue_60)
            viewModel.processIntent(
                CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.OnAccountNumberChanged(
                    it
                )
            )
        }

        binding.buttonVerify.setOnClickListener {
            Analytics.trackEvent(CHECK_SETTLEMENT_BANK_ACCOUNT_ATTEMPT)
            binding.inputAccountNumber.setTextColor(getColorCompat(R.color.black))
            viewModel.processIntent(CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.OnVerifyBankAccount(entryPoint))
            Utils.hideKeyboard(this)
        }



        binding.buttonSaveAccount.setOnClickListener {
            if (entryPoint?.equals(EDC_ORDER_DETAIL) == true) {
                viewModel.processIntent(CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.AddMerchantBankAccount)
            } else if (entryPoint?.equals(RNL_WITHDRAWAL_ACCOUNT) == true) {
                viewModel.processIntent(CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.OnAddRnLWithdrawalBankAccount)
            } else {
                viewModel.processIntent(CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.OnAddSettlementBankAccount)
            }
        }

        binding.bankAccountView.deleteBankView {
            binding.inputAccountNumber.setText("")
            viewModel.processIntent(CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.OnRemoveSelectedBank)
        }
    }

    private fun openHelp() {
        ZohoChat.openZohoChat("add_settlement_bank_account");
    }

    override fun onDestroy() {
        super.onDestroy()
        processingViewHandler?.removeCallbacksAndMessages(null)
        processingViewHandler = null
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK) return
        when (requestCode) {
            RC_SELECT_BANK -> {
                val bank = data?.getParcelableExtra<Bank>(AddBankAccountActivity.SELECTED_BANK)
                bank?.run {
                    viewModel.processIntent(
                        CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.OnSelectBankAccount(
                            bank
                        )
                    )
                }
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun showProcessingViewBottomSheet() {
        val processingViewBS = ProcessViewBottomSheet.createInstance(
            getString(R.string.wait_still_processing),
            getString(R.string.please_dont_close_util_finished)
        )
        processingViewBS.show(supportFragmentManager, ProcessViewBottomSheet.TAG)
    }

    private fun showInActiveBottomSheet(message: String) {
        val bottomSheet = AddUsedBankBottomSheet.newInstance(message)
        bottomSheet.show(supportFragmentManager, "AddUsedBankBottomsheet")
    }

    override fun subscribeState() {

        lifecycleScope.launchWhenStarted {
            viewModel.viewEvent.collectLatest { event ->
                when (event) {
                    is CashWithdrawAddBankAccountViewModel.Event.ShowErrorMessage -> {
                        val prop = hashMapOf(
                            "check_status" to "no",
                            "failed_reason" to event.message
                        )
                        Analytics.trackEvent(CHECK_SETTLEMENT_BANK_ACCOUNT_RESULT, prop)
                        handleErrorApi(
                            event.message,
                            event.code,
                            event.bankDetails,
                        )
                    }

                    is CashWithdrawAddBankAccountViewModel.Event.ShowBankAccountDetail -> {
                        val prop = hashMapOf("check_status" to "yes", "failed_reason" to "")
                        Analytics.trackEvent(CHECK_SETTLEMENT_BANK_ACCOUNT_RESULT, prop)
                        showBankAccountDetail(event.bankAccount)
                    }

                    is CashWithdrawAddBankAccountViewModel.Event.ShowBottomSheet -> {
                        when (event.type) {
                            CashWithdrawAddBankAccountViewModel.BottomSheetType.NO_INTERNET.name -> {
                                showPaymentDownBottomSheet(false)
                            }

                            CashWithdrawAddBankAccountViewModel.BottomSheetType.TIMEOUT.name -> {
                                showProcessingViewBottomSheet()
                            }

                            else -> {
                                showPaymentDownBottomSheet(true)
                            }

                        }
                    }

                    is CashWithdrawAddBankAccountViewModel.Event.AddBankAccountSuccess -> {
                        val prop = hashMapOf("saved_status" to "yes")
                        Analytics.trackEvent(SETTLEMENT_BANK_ACCOUNT_SAVED_RESULT, prop)
                        Utils.setCashWithdrawalFirstTimePopupShown()
                        setSettlementBankAccount(event.bankAccount)
                        ToastUtil.setToast(
                            this@AddSettlementBankAccountActivity,
                            0,
                            getString(R.string.bank_account_added),
                            binding.toolbar,
                            Snackbar.LENGTH_SHORT
                        ) {
                            if (entryPoint?.equals(EDC_ORDER_DETAIL) == true) {
                                finish()
                                startWebview(getPaymentConfigs().merchantBanksUrl.addQuery("entryPoint=BUKUAGEN"))
                            } else {
                                val intent = Intent().apply {
                                    putExtra(ADDED_BANK_ACCOUNT, event.bankAccount)
                                }
                                setResult(RESULT_OK, intent)
                                finish()
                            }

                        }
                    }

                    else -> {

                    }
                }
            }
        }
        lifecycleScope.launch {
            viewModel.bankViewStateFlow.collectLatest { it ->
                binding.txtBankName.text = it.bankAccount?.bankName
                binding.bankAccountView.visibility =
                    (it.showAccountDetails || it.showBlockedError).asVisibility()
                binding.txtErrorNoBank.visibility = it.showBankError.asVisibility()
                binding.buttonVerify.isEnabled = it.isVerifyButtonEnabled
                binding.buttonVerify.visibility = (!it.verificationLoader).visibleIfTrue()
                binding.txtErrorAccount.visibility = it.showVerificationError.asVisibility()
                binding.buttonSaveAccount.visibility = (!it.addingLoader).asVisibility()
                binding.progressVerificationAdd.visibility = it.addingLoader.asVisibility()
                binding.progressVerification.visibility = it.verificationLoader.asVisibility()
                binding.layoutInputAccountNumber.error =
                    getString(R.string.fragment_add_bank_account_empty_account)
                binding.layoutInputAccountNumber.isErrorEnabled = it.showAccountInputError
                binding.buttonSaveAccount.isEnabled = it.isButtonEnabled
            }
        }
    }

    private fun showBankAccountDetail(bankAccount: BankAccount) {
        binding.bankAccountView.showView()
        when {
            bankAccount.matchingStatus.isFalse -> {
                binding.bankAccountView.setBankViewForCashWithdrawal(
                    bankAccount, BankAccountView.BankStatus.UNSUPPORTED,
                    getString(R.string.account_name_matching_error)
                )
            }

            else -> {
                binding.bankAccountView.setBankViewForCashWithdrawal(
                    bankAccount, BankAccountView.BankStatus.VERIFIED
                )
            }
        }
    }

    private fun handleErrorApi(
        message: String?,
        code: String,
        bankDetails: BankAccount?
    ) {
        val regex5xxError = Regex("""\b5\d{2}\b""")

        with(binding) {
            when {
                code.equals(PpobConst.IN_ACTIVE, ignoreCase = true) -> {
                    showInActiveBottomSheet(message.orEmpty())
                }

                code.equals(PpobConst.BLOCKED, ignoreCase = true) -> {
                    bankAccountView.rootView.showView()
                    bankAccountView.setBankViewForCashWithdrawal(
                        BankAccount(
                            bankCode = bankDetails?.bankCode.orEmpty(),
                            accountNumber = bankDetails?.accountNumber
                        ),
                        BankAccountView.BankStatus.BLOCKED,
                    )
                }

                code.equals(PpobConst.NOT_SUPPORTED, ignoreCase = true) -> {
                    bankAccountView.showView()
                    bankAccountView.setBankViewForCashWithdrawal(
                        bankDetails,
                        BankAccountView.BankStatus.UNSUPPORTED,
                       errorMessage = message.orEmpty()
                    )
                }

                code.contains(regex5xxError) -> {
                    binding.txtErrorAccount.showView()
                    binding.txtErrorAccount.text = "some unknown error occurred"
                }

                else -> {
                    if (message == NO_INTERNET_ERROR_MESSAGE) {
                        showPaymentDownBottomSheet(false)
                    } else {
                        binding.txtErrorAccount.showView()
                        binding.txtErrorAccount.text = if (!message.isNullOrBlank()) message
                        else getString(R.string.bank_account_not_found)
                    }
                }
            }
        }
    }
    private fun startWebview(url: String) {
        openActivity(
            WebviewActivity::class.java,
        ) {
            putString(
                ClassConstants.WEBVIEW_URL, url
            )
            putBoolean(ClassConstants.HIDE_TOOLBAR, true)
        }
    }
}
