package com.bukuwarung.edc.card.cashwithdrawal.ui.bottomsheet

import android.os.Bundle
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.BottomsheetAddUsedBankAccountBinding
import com.bukuwarung.edc.global.Constant.FAQ_USED_ACCOUNT_BW_URL
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.util.Utils.makeSectionOfTextClickable
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.ui_component.utils.getColorCompat
import com.bukuwarung.ui_component.utils.setSingleClickListener

class AddUsedBankBottomSheet : BaseBottomSheetDialogFragment() {
    private var bottomSheetAddUsedBankBinding: BottomsheetAddUsedBankAccountBinding? = null

    private val binding get() = bottomSheetAddUsedBankBinding!!

    companion object {
        private const val ERROR_MESSAGE = "ERROR_MESSAGE"
        fun newInstance(message: String? = null): AddUsedBankBottomSheet = AddUsedBankBottomSheet().apply {
            val bundle = Bundle()
            bundle.putString(ERROR_MESSAGE, message)
            arguments = bundle
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        bottomSheetAddUsedBankBinding = BottomsheetAddUsedBankAccountBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            closeDialog.setSingleClickListener {
                dialog?.dismiss()
            }
            btClose.setSingleClickListener {
                dialog?.dismiss()
            }
            subtitleTxt.text = makeSectionOfTextClickable((arguments?.getString(ERROR_MESSAGE)
                    ?: getString(R.string.bank_account_used_subtitle)) + " " + getString(R.string.bold_text), getString(R.string.bold_text), object : ClickableSpan() {
                override fun onClick(widget: View) {
                    requireContext().openActivity(WebviewActivity::class.java) {
                        putString(ClassConstants.WEBVIEW_URL, FAQ_USED_ACCOUNT_BW_URL)
                    }
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                    ds.color = subtitleTxt.context.getColorCompat(R.color.blue_60)
                }
            })
            subtitleTxt.movementMethod = LinkMovementMethod.getInstance()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        bottomSheetAddUsedBankBinding = null
    }
}
