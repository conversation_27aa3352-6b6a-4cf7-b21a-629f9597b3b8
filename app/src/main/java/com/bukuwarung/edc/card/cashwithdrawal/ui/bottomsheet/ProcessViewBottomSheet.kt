package com.bukuwarung.edc.card.cashwithdrawal.ui.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ProcessingViewBottomSheetBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.ui_component.base.BaseErrorView


class ProcessViewBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        private const val TITLE = "title"
        private const val MESSAGE = "message"
        const val TAG = "ProcessViewBottomSheet"

        fun createInstance(title: String, message: String) =
            ProcessViewBottomSheet().apply {
                val bundle = Bundle().apply {
                    putString(TITLE, title)
                    putString(MESSAGE, message)
                }
                arguments = bundle
            }
    }

    interface Callback {
        fun onDismiss()
        fun onButtonClicked()
    }

    private val title by lazy { arguments?.getString(TITLE) }
    private val message by lazy { arguments?.getString(MESSAGE) }

    private var callback: Callback? = null

    private var _binding: ProcessingViewBottomSheetBinding? = null
    private val binding get() = _binding!!
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.BottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ProcessingViewBottomSheetBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { callback = it as? Callback }
        if (context is Callback) callback = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        with(binding) {
            bukuErrorView.setErrorType(
                BaseErrorView.Companion.ErrorType.CUSTOM,
                title,
                message,
                null,
                R.drawable.ic_time_out
            )
        }
    }

//    override fun ctaClicked() {
//        dismiss()
//        callback?.onButtonClicked()
//    }
//
//    override fun messageClicked() {
//
//    }
}
