package com.bukuwarung.edc.card.cashwithdrawal.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.usecase.CashWithdrawalUseCase
import com.bukuwarung.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.order.orderdetail.ui.EdcOrderDetailsActivity.Companion.EDC_ORDER_DETAIL
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.request.BankAccountRequest
import com.bukuwarung.edc.payments.data.model.request.BankValidationRequest
import com.bukuwarung.edc.payments.usecase.PaymentUseCase
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.isFalse
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONObject
import javax.inject.Inject

@HiltViewModel
class CashWithdrawAddBankAccountViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val cashWithdrawalUseCase: CashWithdrawalUseCase
) : ViewModel() {


    val _bankViewStateFlow = MutableStateFlow(ViewState())
    val bankViewStateFlow: StateFlow<ViewState> get() = _bankViewStateFlow.asStateFlow()

    private var timerJob: Job? = null

    sealed class AddSettlementBankAccountIntent {

        data class OnSelectBankAccount(val bank: Bank) :
            AddSettlementBankAccountIntent()

        data class OnAccountNumberChanged(val accountNumber: String) :
            AddSettlementBankAccountIntent()

        data class OnVerifyBankAccount(val entryPoint:String?) : AddSettlementBankAccountIntent()

        object OnAddSettlementBankAccount : AddSettlementBankAccountIntent()

        data class OnDeleteBankAccount(val bankId: String) : AddSettlementBankAccountIntent()

        object OnRemoveSelectedBank : AddSettlementBankAccountIntent()

        object GetSettlementBankList : AddSettlementBankAccountIntent()

        object GetRnLWithdrawalBankList : AddSettlementBankAccountIntent()

        object AddMerchantBankAccount:AddSettlementBankAccountIntent()

        object OnAddRnLWithdrawalBankAccount:AddSettlementBankAccountIntent()
    }

    fun processIntent(intent: AddSettlementBankAccountIntent) {
        when (intent) {
            is AddSettlementBankAccountIntent.OnSelectBankAccount -> {
                setSelectedBank(intent.bank)
            }

            is AddSettlementBankAccountIntent.OnAccountNumberChanged -> {
                onAccountNumberChanged(intent.accountNumber)
            }

            is AddSettlementBankAccountIntent.OnVerifyBankAccount -> {
                validateBankAccount(intent.entryPoint)
            }

            is AddSettlementBankAccountIntent.OnAddSettlementBankAccount -> {
                addBankAccount(AddBankAccountType.SETTLEMENT)
            }

            is AddSettlementBankAccountIntent.OnDeleteBankAccount -> {
                deleteBankAccount(intent.bankId)
            }

            is AddSettlementBankAccountIntent.OnRemoveSelectedBank -> {
                removeBank()
            }

            is AddSettlementBankAccountIntent.GetSettlementBankList -> {

                getSettlementBankAccount()
            }
            is AddSettlementBankAccountIntent.AddMerchantBankAccount->{
                // for rnl also
                addBankAccount(AddBankAccountType.MERCHANT)
            }
            is AddSettlementBankAccountIntent.GetRnLWithdrawalBankList -> {
                // for rnl also have to make changes
                getRnlWithdrawalBankAccount()
            }

            is AddSettlementBankAccountIntent.OnAddRnLWithdrawalBankAccount->{
                addBankAccount(AddBankAccountType.RnL)
            }
        }
    }

    data class ViewState(
        val bankAccount: BankAccount? = null,
        val bankAccountNumber: String = "",
        val bankVerificationError: String = "",
        val verificationLoader: Boolean = false,
        val addingLoader: Boolean = false,
        val showBankError: Boolean = false,
        val showAccountInputError: Boolean = false,
        val showAccountDetails: Boolean = false,
        val showVerificationError: Boolean = false,
        val isButtonEnabled: Boolean = false,
        val isVerifyButtonEnabled:Boolean=false,
        val showBlockedError: Boolean = false,
        val bankListLoader: Boolean = false
    )

    sealed class Event {
        data class SetResultSuccess(val bankAccount: BankAccount) : Event()
        data class ShowErrorMessage(
            val message: String?,
            val code: String = "",
            val bankDetails: BankAccount? = null,
        ) : Event()

        data class ShowBankAccountDetail(val bankAccount: BankAccount) : Event()
        object StartTimer : Event()
        object StopTimer : Event()
        data class ShowBottomSheet(val type: String) : Event()

        data class BankListLoaded(val bankList: List<BankAccount>) : Event()

        object BankAccountDeleted : Event()

        data class AddBankAccountSuccess(val bankAccount: SettlementBankAccount) : Event()

    }

    private val _viewEvent = MutableSharedFlow<Event>()
    val viewEvent: SharedFlow<Event> = _viewEvent


    private fun validateBankAccount(entryPoint: String?) {
        setBankViewState(
            _bankViewStateFlow.value.copy(
                verificationLoader = true,
                showAccountDetails = false,
                isButtonEnabled = false
            )
        )
        val bankCode =
            if (entryPoint.equals(EDC_ORDER_DETAIL)) _bankViewStateFlow.value.bankAccount?.bankCode.orEmpty()
            else _bankViewStateFlow.value.bankAccount?.paymentBankCode.orEmpty()
        viewModelScope.launch {
//            onEvent(BankEvent.StartTimer)
            try {
                val response = paymentUseCase.validateBankAccount(
                    Utils.getPaymentAccountId(),
                    BankValidationRequest(
                        bankCode = bankCode,
                        accountNumber = _bankViewStateFlow.value.bankAccountNumber,
                        accountOwner = "SELF",
                        isPaymentIn = true
                    )
                )

                if (response.isSuccessful) {
                    val bankAccount = response.body()
                    bankAccount?.let {
                        if (it.isDisabled.isTrue) {
                            setBankViewState(
                                _bankViewStateFlow.value.copy(
                                    verificationLoader = false,
                                    showBankError = false,
                                    showVerificationError = true,
                                    showAccountDetails = false,
                                    showBlockedError = true,
                                    isButtonEnabled = false
                                )
                            )

                            setEventStatus(
                                Event.ShowErrorMessage(
                                    null,
                                    PpobConst.NOT_SUPPORTED,
                                    _bankViewStateFlow.value.bankAccount
                                )
                            )
                        } else {
                            if (it.accountHolderName.isNullOrBlank() || it.accountNumber.isNullOrBlank()) {
                                setBankViewState(
                                    _bankViewStateFlow.value.copy(
                                        verificationLoader = false,
                                        showBankError = false,
                                        showVerificationError = true,
                                        showAccountDetails = false,
                                        isButtonEnabled = false, showBlockedError = false
                                    )
                                )
                                setEventStatus(Event.ShowErrorMessage(null))
                            } else {
                                setBankViewState(
                                    _bankViewStateFlow.value.copy(
                                        verificationLoader = false,
                                        showAccountDetails = true,
                                        showVerificationError = false,
                                        isButtonEnabled = (!(bankAccount.matchingStatus.isFalse || bankAccount.accountAlreadyExists.isTrue)
                                                || (bankAccount.accountAlreadyExists.isTrue && bankAccount.manualVerificationStatus == "VERIFIED")),
                                        showBlockedError = false,
                                        bankAccount = BankAccount(
                                            bankCode = _bankViewStateFlow.value.bankAccount?.bankCode,
                                            bankName = _bankViewStateFlow.value.bankAccount?.bankName,
                                            accountNumber = bankAccount.accountNumber,
                                            accountHolderName = bankAccount.accountHolderName,
                                            logo = _bankViewStateFlow.value.bankAccount?.logo,
                                            paymentBankCode = _bankViewStateFlow.value.bankAccount?.paymentBankCode.orEmpty(),
                                        )
                                    )
                                )
                                setEventStatus(Event.ShowBankAccountDetail(it))
                            }
                        }
                    }

                } else {
                    val json = JSONObject(response.errorBody()?.string())

                    var errorCode = ""
                    if (json.has("code")) {
                        errorCode = json.getString("code")
                    }
                    var errorMessage = ""
                    if (json.has("message")) {
                        errorMessage = json.getString("message")
                    }
                    setBankViewState(
                        _bankViewStateFlow.value.copy(
                            verificationLoader = false,
                            showBankError = false,
                            showVerificationError = (!errorCode.equals(
                                PpobConst.BLOCKED,
                                ignoreCase = true
                            ) && !errorCode.equals(PpobConst.IN_ACTIVE, ignoreCase = true)),
                            showAccountDetails = false,
                            showBlockedError = errorCode.equals(
                                PpobConst.BLOCKED,
                                ignoreCase = true
                            ),
                            isButtonEnabled = false
                        )
                    )
                    setEventStatus(
                        Event.ShowErrorMessage(
                            if (errorMessage.isNotNullOrEmpty()) errorMessage else response.errorMessage(),
                            errorCode,
                            _bankViewStateFlow.value.bankAccount
                        )
                    )
                }


            } catch (e: NoConnectivityException) {
                setBankViewState(
                    _bankViewStateFlow.value.copy(
                        verificationLoader = false,
                        showBankError = false,
                        showVerificationError = true,
                        showAccountDetails = false,
                        isButtonEnabled = false, showBlockedError = false
                    )
                )
                setEventStatus(Event.ShowBottomSheet(BottomSheetType.NO_INTERNET.name))
            } catch (e: Exception) {
                setBankViewState(
                    _bankViewStateFlow.value.copy(
                        verificationLoader = false,
                        showBankError = false,
                        showVerificationError = true,
                        showAccountDetails = false,
                        isButtonEnabled = false, showBlockedError = false
                    )
                )
                setEventStatus(Event.ShowErrorMessage(e.message))
            }
        }
    }

    private fun setBankViewState(viewState: ViewState) {
        _bankViewStateFlow.value = viewState
    }

    private suspend fun setEventStatus(event: Event) {
        _viewEvent.emit(event)
    }

    // for Rnl isCashbackWithdrawal would be true
    // for Cash withdrawal transaction isPrimary would be true
    private fun addBankAccount(type: AddBankAccountType) {
        setBankViewState(_bankViewStateFlow.value.copy(verificationLoader = false, addingLoader = false, isButtonEnabled = false))
        viewModelScope.launch {
            val settlementBankAccount = SettlementBankAccount(
                bankCode = _bankViewStateFlow.value.bankAccount?.bankCode.orEmpty(),
                bankName = _bankViewStateFlow.value.bankAccount?.bankName,
                accountNumber = _bankViewStateFlow.value.bankAccountNumber,
                beneficiaryName = _bankViewStateFlow.value.bankAccount?.accountHolderName,
                bankLogo = _bankViewStateFlow.value.bankAccount?.logo,
                isPrimary = type==AddBankAccountType.SETTLEMENT,
                isCashbackWithdrawal = type== AddBankAccountType.RnL
            )

            try {
                val response =
                    if (type == AddBankAccountType.SETTLEMENT || type == AddBankAccountType.RnL) cashWithdrawalUseCase.addSettlementBankAccount(
                        Utils.getPaymentAccountId(),
                        Utils.getTerminalId(),
                        settlementBankAccount
                    ) else {
                        paymentUseCase.addMerchantBankAccount(
                            Utils.getPaymentAccountId(),
                            BankAccountRequest(
                                bankCode = _bankViewStateFlow.value.bankAccount?.bankCode.orEmpty(),
                                accountNumber = _bankViewStateFlow.value.bankAccountNumber
                            )
                        )
                    }
                if (response.isSuccessful) {
                    val bankAccount = response.body()
                    bankAccount?.let {
                        setBankViewState(
                            _bankViewStateFlow.value.copy(
                                verificationLoader = false,
                                addingLoader = false,
                                showAccountDetails = true,
                                showVerificationError = false, showBlockedError = false
                            )
                        )
                        setEventStatus(Event.AddBankAccountSuccess(settlementBankAccount))
                    }
                } else {
                    val message = "Terjadi kesalahan pada sistem. Silakan coba kembali"
                    setBankViewState(_bankViewStateFlow.value.copy(verificationLoader = false, showBlockedError = true,isButtonEnabled = false))
                    setEventStatus(
                        Event.ShowErrorMessage(
                            message,
                            PpobConst.NOT_SUPPORTED,
                            _bankViewStateFlow.value.bankAccount
                        )
                    )
                }
            } catch (e: NoConnectivityException) {
                val message = "Silakan periksa koneksi internet Anda dan coba kembali."
                setBankViewState(_bankViewStateFlow.value.copy(verificationLoader = false, showBlockedError = true,isButtonEnabled = false))
                setEventStatus(
                    Event.ShowErrorMessage(
                        message,
                        PpobConst.NOT_SUPPORTED,
                        _bankViewStateFlow.value.bankAccount
                    )
                )
            } catch (e: Exception) {
                setBankViewState(_bankViewStateFlow.value.copy(verificationLoader = false, showBlockedError = true, isButtonEnabled = false))
                setEventStatus(Event.ShowErrorMessage(e.message))
            }
        }
    }

    //for Cash withdrawal transaction isPrimary would be true
    private fun getSettlementBankAccount() {
        setBankViewState(ViewState(bankListLoader = true))
        viewModelScope.launch {
            try {
                val response = cashWithdrawalUseCase.getSettlementBankList(
                    Utils.getTerminalId(),
                    isPrimary = true,
                    isCashBankWithdrawal = null
                )
                setBankViewState(ViewState(bankListLoader = false))
                if (response.isSuccessful) {
                    val bankAccount = response.body()?.bankAccounts
                    bankAccount?.let {
                        setEventStatus(Event.BankListLoaded(it))
                    }
                } else {
                    val errorMessage = response.errorMessage()
                    setEventStatus(Event.ShowErrorMessage(errorMessage))
                }
            } catch (e: NoConnectivityException) {
                setEventStatus(Event.ShowBottomSheet(BottomSheetType.NO_INTERNET.name))
            } catch (e: Exception) {
                // need to aad error handling
                setEventStatus(Event.ShowErrorMessage(e.message))
            }
        }
    }

    // for Rnl isCashbackWithdrawal would be true
    private fun getRnlWithdrawalBankAccount() {
        setBankViewState(ViewState(bankListLoader = true))
        viewModelScope.launch {
            try {
                val response = cashWithdrawalUseCase.getSettlementBankList(
                    Utils.getTerminalId(),
                    isCashBankWithdrawal = true,
                    isPrimary = null
                )
                setBankViewState(ViewState(bankListLoader = false))
                if (response.isSuccessful) {
                    val bankAccount = response.body()?.bankAccounts
                    bankAccount?.let {
                        setEventStatus(Event.BankListLoaded(it))
                    }
                } else {
                    val errorMessage = response.errorMessage()
                    setEventStatus(Event.ShowErrorMessage(errorMessage))
                }
            } catch (e: NoConnectivityException) {
                setEventStatus(Event.ShowBottomSheet(BottomSheetType.NO_INTERNET.name))
            } catch (e: Exception) {
                // need to aad error handling
                setEventStatus(Event.ShowErrorMessage(e.message))
            }
        }
    }

    private fun deleteBankAccount(bankId: String) {
        setBankViewState(_bankViewStateFlow.value.copy(showAccountDetails = false))
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val response = cashWithdrawalUseCase.deleteSettlementBankAccount(bankId)
                if (response.isSuccessful) {
                    setEventStatus(Event.BankAccountDeleted)
                } else {
                    setEventStatus(Event.ShowErrorMessage(response.errorMessage()))
                }
            } catch (e: Exception) {
                setEventStatus(Event.ShowErrorMessage(e.message))
            }
        }
    }

    private fun startTimer() {
//        onEvent(BankEvent.StopTimer)
        timerJob = viewModelScope.launch {
            delay(30000)
            setEventStatus(Event.ShowBottomSheet(BottomSheetType.TIMEOUT.name))
        }
    }

    private fun stopTimer() {
        timerJob?.cancel()
        timerJob = null
    }

    private fun setSelectedBank(bank: Bank?) {
        val newViewState = _bankViewStateFlow.value.copy(
            bankAccount = BankAccount(
                bankCode = bank?.bankCode,
                bankName = bank?.bankName,
                logo = bank?.logo,
                paymentBankCode = bank?.paymentBankCode,
                status = bank?.status
            ),
            verificationLoader = false,
            showAccountInputError = false,
            showBankError = false,
            showVerificationError = false,
            showBlockedError = false,
            showAccountDetails = false,
        )
        setBankViewState(newViewState)
    }

    private fun onAccountNumberChanged(accountNumber: String) {
        val verifyButtonState =
            _bankViewStateFlow.value.bankAccount != null && accountNumber.isNotNullOrBlank()
        val newState = _bankViewStateFlow.value.copy(
            bankAccountNumber = accountNumber,
            showAccountInputError = false,
            showBankError = false,
            showVerificationError = false,
            showBlockedError = false,
            showAccountDetails = false,
            isButtonEnabled = false,
            isVerifyButtonEnabled = verifyButtonState
        )
        setBankViewState(
            newState
        )
    }

    private fun removeBank() {
        setBankViewState(
            _bankViewStateFlow.value.copy(
                bankAccount = null,
                showAccountDetails = false,
                bankAccountNumber = "",
                isButtonEnabled = false,
                isVerifyButtonEnabled = false
            )
        )
    }

    enum class BottomSheetType {
        NO_INTERNET,
        TIMEOUT,
        IN_ACTIVE
    }

    enum class AddBankAccountType {
        SETTLEMENT,
        MERCHANT,
        RnL
    }
}