package com.bukuwarung.edc.card.constant

object CardHistoryAnalyticsConstants {
    const val EVENT_VISIT_HISTORY = "visit_history"
    const val EVENT_VISIT_HISTORY_DETAILS = "visit_history_detail"
    const val EVENT_PROPERTY_TRANSACTION_TYPE = "transaction_type"
    const val EVENT_PROPERTY_TRANSACTION_TAB_SELECTED = "transaction_tab_selected"
    const val EVENT_PROPERTY_SORT = "sort"
    const val EVENT_PROPERTY_TIME_FILTER = "time_filter"
    const val SORT_PROPERTY_VALUE_OLD_TO_NEW = "old-new"
    const val SORT_PROPERTY_VALUE_NEW_TO_OLD = "new-old"
    const val TRANSACTION_TYPE_VALUE_TAB_BALANCE_CHECK = "tab_balance_check"
    const val TRANSACTION_TYPE_VALUE_TAB_TRANSFER = "tab_transfer"
    const val TRANSACTION_TYPE_VALUE_TAB_ALL = "tab_all"
}