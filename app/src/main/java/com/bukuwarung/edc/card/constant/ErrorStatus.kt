package com.bukuwarung.edc.card.constant

import com.morefun.yapi.ServiceResult

/**
 * enum for printer
 * error code values are defined in com.vfi.smartpos.deviceservice.aidl.PrinterListener
 *
 * <li>ERROR_NONE(0x00) - normal</li>
 * <li>ERROR_PAPERENDED(0xF0) - Paper out</li>
 * <li>ERROR_NOCONTENT(0xF1) - no content</li>
 * <li>ERROR_HARDERR(0xF2) - printer error</li>
 * <li>ERROR_OVERHEAT(0xF3) - over heat</li>
 * <li>ERROR_NOBM(0xF6) - no black mark</li>
 * <li>ERROR_BUSY(0xF7) - printer is busy</li>
 * <li>ERROR_MOTORERR(0xFB) - moto error</li>
 * <li>ERROR_LOWVOL(0xE1) - battery low</li>
 * <li>ERROR_NOTTF(0xE2) - no ttf</li>
 * <li>ERROR_BITMAP_TOOWIDE(0xE3) - width of bitmap too wide</li>
 */
enum class ErrorStatus(val errorCode: Int, val msg: String, val errorLevel: Int) {
    ERROR_NONE(PrintConst.VF_ERROR_NONE, "Retry..", PrintConst.ALERT_WARNING),
    ERROR_PAPER_FINISH(
        PrintConst.VF_ERROR_PAPER_ENDED,
        "Kertas thermal tidak tersedia", PrintConst.ALERT_ERROR
    ),
    ERROR_HARDWARE(
        PrintConst.VF_ERROR_HARDWARE_ERR, "Terjadi kerusakan pada printer",
        PrintConst.ALERT_ERROR
    ),
    ERROR_OVERHEAT(
        PrintConst.VF_ERROR_OVERHEAT,
        "Mesin printer terlalu panas", PrintConst.ALERT_WARNING
    ),
    ERROR_MOTOR_ERROR(
        PrintConst.VF_ERROR_MOTOR_ERR,
        "Terjadi kerusakan pada printer", PrintConst.ALERT_ERROR
    ),
    ERROR_BUSY(
        PrintConst.VF_ERROR_BUSY,
        "Printer sedang digunakan", PrintConst.ALERT_WARNING
    ),
    ERROR_LOW_BATTERY(
        PrintConst.VF_ERROR_LOW_BATTERY,
        "Baterai printer lemah", PrintConst.ALERT_ERROR
    ),
    ERROR_NO_CONTENT(
        PrintConst.VF_ERROR_NOCONTENT,
        "No content to print", PrintConst.ALERT_ERROR
    ),
    ERROR_SYSTEM(PrintConst.VF_SYSTEM_ERROR, "Terjadi gangguan sistem", PrintConst.ALERT_ERROR);

    companion object {
        fun findByErrorCode(id: Int): ErrorStatus {
            for (status in values()) {
                if (status.errorCode == id) {
                    return status
                }
            }
            return ERROR_SYSTEM
        }
        fun findByMFErrorCode(serviceResult: Int): ErrorStatus {
            return when (serviceResult) {
                ServiceResult.Printer_PaperLack -> ERROR_PAPER_FINISH
                ServiceResult.Printer_Fault -> ERROR_HARDWARE
                ServiceResult.Printer_Low_Power -> ERROR_LOW_BATTERY
                ServiceResult.Printer_Busy -> ERROR_BUSY
                ServiceResult.Printer_Other_Error -> ERROR_MOTOR_ERROR
                ServiceResult.Printer_No_Printer -> ERROR_NO_CONTENT
                ServiceResult.Printre_TooHot -> ERROR_OVERHEAT
                else -> ERROR_SYSTEM
            }
        }
    }
}