package com.bukuwarung.edc.card.constant

import com.bukuwarung.edc.ZohoChatEntryPoint

object PrintConst {

    const val VF_ERROR_NONE= 0x00
    const val VF_ERROR_PAPER_ENDED=0xF0
    const val VF_ERROR_NOCONTENT=0xF1
    const val VF_ERROR_HARDWARE_ERR=0xF2
    const val VF_ERROR_OVERHEAT=0xF3
    const val VF_ERROR_BUSY=0xF7
    const val VF_ERROR_MOTOR_ERR=0xFB
    const val VF_ERROR_LOW_BATTERY=0xE1
    const val VF_SYSTEM_ERROR = -1

    const val ALERT_INFO = 0
    const val ALERT_WARNING = 2
    const val ALERT_ERROR = 3

    const val COPY_TYPE_NONE = 0
    const val COPY_TYPE_BANK = 1
    const val COPY_TYPE_CUSTOMER = 2
    const val COPY_TYPE_MERCHANT = 3

    const val TRANSACTION_TYPE_CHECK_BALANCE = 1
    const val TRANSACTION_TYPE_TRANSFER = 2
    const val TRANSACTION_TYPE_CASH_WITHDRAWAL = 3

    const val PRINT_SCREEN_CLOSE_WAIT_TIME = 2000L

}

fun Int?.toZohoEntryPoint(): String {
    return when(this) {
        PrintConst.TRANSACTION_TYPE_CHECK_BALANCE -> ZohoChatEntryPoint.BALANCE_INQUIRY
        PrintConst.TRANSACTION_TYPE_TRANSFER -> ZohoChatEntryPoint.TRANSFER
        PrintConst.TRANSACTION_TYPE_CASH_WITHDRAWAL -> ZohoChatEntryPoint.CASH_WITHDRAWAL
        else -> ""
    }
}