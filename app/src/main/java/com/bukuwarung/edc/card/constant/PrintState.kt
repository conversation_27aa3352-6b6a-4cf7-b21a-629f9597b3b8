package com.bukuwarung.edc.card.constant

import com.bukuwarung.edc.R

enum class PrintState(
    val isLoader: <PERSON><PERSON><PERSON>,
    val messageTitle: Int,
    val messageBody: Int,
    val stateImage: Int,
    val receiptType: ReceiptType
) {
    PRINTING_BANK_RECEIPT(
        true, R.string.printing_bank_slip_title, R.string.wait_for_process, R.drawable.ic_hourglass,
        ReceiptType.RECEIPT_TYPE_BANK
    ),
    CONFIRM_PRINT_CUSTOMER_RECEIPT(
        false, R.string.confirm_customer_slip_title, R.string.confirm_customer_slip_body, R.drawable.ic_printing,
        ReceiptType.RECEIPT_TYPE_CUSTOMER
    ),
    PRINTING_CUSTOMER_RECEIPT(
        true, R.string.printing_customer_slip, R.string.wait_for_process, R.drawable.ic_hourglass,
        ReceiptType.RECEIPT_TYPE_CUSTOMER
    ),
    CONFIRM_PRINT_MERCHANT_RECEIPT(
        false, R.string.confirm_merchant_slip_title, R.string.confirm_merchant_slip_body, R.drawable.ic_printing,
        ReceiptType.RECEIPT_TYPE_MERCHANT
    ),
    PRINTING_MERCHANT_RECEIPT(
        true, R.string.printing_merchant_slip, R.string.wait_for_process, R.drawable.ic_hourglass,
        ReceiptType.RECEIPT_TYPE_MERCHANT
    ),
    PRINT_RECEIPT_COMPLETE(
        false, R.string.printing_merchant_slip, R.string.wait_for_process, R.drawable.ic_hourglass,
        ReceiptType.RECEIPT_TYPE_NONE
    );
}