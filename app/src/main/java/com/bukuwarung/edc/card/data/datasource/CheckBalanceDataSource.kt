package com.bukuwarung.edc.card.data.datasource

import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path

interface CheckBalanceDataSource {

    @POST("edc-adapter/balance/check/{accountId}")
    suspend fun fetchCardBalance(
        @Header("x-edc-serial-number") serialNumber: String,
        @Path("accountId") accountId: String,
        @Body checkBalanceRequest: CheckBalanceRequest
    ): Response<CardReceiptResponse>
}