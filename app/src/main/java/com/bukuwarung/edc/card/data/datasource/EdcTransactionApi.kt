package com.bukuwarung.edc.card.data.datasource

import com.bukuwarung.edc.card.data.model.*
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Path

interface EdcTransactionApi {

    @POST("edc-adapter/transactions/reversal/{accountId}")
    suspend fun submitReversal(
        @Path("accountId") accountId: String,
        @Body reversalRequest: TransactionReversalRequest
    ): Response<Void>

    @POST("edc-adapter/balance/check/confirm/{accountId}")
    suspend fun confirmBalanceCheck(
        @Path("accountId") accountId: String,
        @Body transactionConfirmRequest: TransactionConfirmRequest
    ): Response<Void>


    @POST("edc-adapter/transfer/posting/confirm/{accountId}")
    suspend fun confirmTransferPosting(
        @Path("accountId") accountId: String,
        @Body transactionConfirmRequest: TransactionConfirmRequest
    ): Response<Void>

    @POST("edc-adapter/cash-withdrawal/posting/confirm/{accountId}")
    suspend fun confirmCashWithdrawalPosting(
        @Path("accountId") accountId: String,
        @Body transactionConfirmRequest: TransactionConfirmRequest
    ): Response<Void>

    @POST("edc-adapter/transactions/reversal/incomplete-trx/{accountId}")
    suspend fun submitIncompleteTransaction(
        @Path("accountId") accountId: String,
        @Body incompleteTransactionRequest: IncompleteTransactionRequest
    ): Response<Array<String>>

}