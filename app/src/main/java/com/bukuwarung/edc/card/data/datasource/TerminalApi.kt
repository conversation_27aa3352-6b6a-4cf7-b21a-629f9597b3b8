package com.bukuwarung.edc.card.data.datasource

import com.bukuwarung.edc.card.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface TerminalApi {

    @POST("/ac/api/v2/edc/terminals/{serialNumber}/pair")
    suspend fun pairBluetoothDevice(
        @Path("serialNumber") serialNumber: String,
        @Query("appDeviceId") appDeviceId: String,
        @Query("androidId") androidId: String,
        @Query("btName") btName: String,
        @Query("btAddress") btAddress: String
    ): Response<TmsResponse<TerminalPairedDevice>>

    @GET("/ac/api/v2/edc/terminals/{serialNumber}")
    suspend fun getTerminal(@Path("serialNumber") serialNumber: String): Response<TmsResponse<Terminal>>

    @POST("/ac/api/v2/edc/terminals/{serialNumber}/activate")
    suspend fun activateTerminal(
        @Path("serialNumber") serialNumber: String,
        @Header("app-device-id") appDeviceId: String,
        @Body request: ActivationRequest
    ): Response<TmsResponse<ActivationResponse>>

    @PATCH("/ac/api/v2/edc/terminals/{serialNumber}/status")
    suspend fun updateTerminalStatus(
        @Path("serialNumber") serialNumber: String,
        @Body request: Map<String, String>,
        @Header("app-device-id") appDeviceId: String
    ): Response<TmsResponse<Terminal>>

    @PUT("/ac/api/v2/edc/terminals/{serialNumber}")
    suspend fun updateTerminal(@Path("serialNumber") serialNumber: String, @Body terminal: Terminal,
                               @Query("reactivation") reactivation: Boolean,): Response<TmsResponse<Terminal>>

    @PATCH("/ac/api/v2/edc/terminals/{serialNumber}/location")
    suspend fun updateTerminalLocation(@Path("serialNumber") serialNumber: String, @Body terminalLocation: TerminalLocation): Response<TmsResponse<TerminalLocation>>

}
