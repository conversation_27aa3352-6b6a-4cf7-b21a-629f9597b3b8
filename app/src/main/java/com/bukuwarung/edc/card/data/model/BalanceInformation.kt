package com.bukuwarung.edc.card.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class BalanceInformation(
    @SerializedName("account_type")
    val accountType: String? = null,
    @SerializedName("amount_type")
    val amountType: String? = null,
    @SerializedName("balance")
    val balance: String? = null,
    @SerializedName("currency_code")
    val currencyCode: String? = null
) : Parcelable