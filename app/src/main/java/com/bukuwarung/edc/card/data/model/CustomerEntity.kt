package com.bukuwarung.edc.card.data.model

import java.io.Serializable

data class CustomerEntity(
    var address: String? = null,
    var altCustomerId: String? = null,
    var balance: Double = java.lang.Double.valueOf(0.0),
    var bookId: String? = null,
    var countryCode: String? = null,
    var customerId: String? = null,
    var deleted: Int = Integer.valueOf(0),
    var dueDate: String? = null,
    var enableSmsAlerts: Int = Integer.valueOf(1),
    var enableTxnDetailSharing: Int? = null,
    var image: String? = null,
    var imageUploadPending: Int = Integer.valueOf(0),
    var language: Int? = null,
    var lastModifiedAt: Long = java.lang.Long.valueOf(0),
    var name: String? = null,
    var phone: String? = null
) : Serializable
