import com.google.gson.annotations.SerializedName

data class DynamicKeyboardBody(
    @SerializedName("isFromFirebase")
    var isFromFirebase: <PERSON>olean,
    @SerializedName("keyboardType")
    var keyboardType: Int,
    @SerializedName("row1")
    var row1: List<String>,
    @SerializedName("row2")
    var row2: List<String>,
    @SerializedName("row3")
    var row3: List<String>,
    @SerializedName("row4")
    var row4: List<String>,
)
