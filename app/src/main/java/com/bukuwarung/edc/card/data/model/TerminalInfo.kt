package com.bukuwarung.edc.card.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.edc.card.domain.model.EdcResponse
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Keep
@Parcelize
data class Terminal(
    val serialNumber: String,
    val btName: String?,
    val btAddress: String?,
    val status: String?,
    val appDeviceId: String?,
    val appVersion: String?,
    val androidId: String?,
    val hardwareVersion: String?,
    val reactivationKey: String?,
    val softwareVersion: String?,
    val currentLocationId: String?,
    val manufacturer:String?,
    val model:String?,
    val additionalDetails: @RawValue Map<String, Any>?
) : TmsData

@Keep
@Parcelize
data class TerminalLocation(
    val serialNumber: String,
    val operation: String,
    val location: String,
    val latitude: String,
    val longitude: String,
    val outOfRange: Boolean,
    val deviceId: String
) : TmsData

@Keep
@Parcelize
data class TerminalPairedDevice(
    val userId: String,
    val appDeviceId: String,
    val androidId: String,
    val btName: String,
    val btAddress: String,
    val pairedAt: String
) : TmsData

@Keep
@Parcelize
data class ActivationRequest(
    val fcmToken: String
) : TmsData

@Keep
@Parcelize
data class TmsException(
    val message: String,
    val errorCode: String,
    val result: Boolean,
) : Parcelable

interface TmsData : Parcelable

@Keep
data class TmsResponse<T>(
    val data: T,
    val result: Boolean,
)

@Keep
@Parcelize
data class ActivationResponse(
    val status: String,
    val activationToken: String
) : TmsData
