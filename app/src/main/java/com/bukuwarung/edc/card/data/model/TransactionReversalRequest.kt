package com.bukuwarung.edc.card.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class TransactionReversalRequest(
    @SerializedName("stan")
    val stan: String?,
    @SerializedName("transaction_date")
    val transactionDate: String?,
    @SerializedName("terminal_id")
    val terminalId: String?,
    @SerializedName("merchant_id")
    val merchantId: String?,
    @SerializedName("transaction_type")
    val transactionType: String?,
    @SerializedName("reversal_icc_data")
    val reversalIccData: String?,
    @SerializedName("card_number")
    val cardNumber: String?,
    @SerializedName("card_expiry")
    val cardExpiry: String?,
    @SerializedName("pin_block")
    val pinBlock: String?,
    @SerializedName("track_2_data")
    val track2Data: String?,
    @SerializedName("account_type")
    val accountType: String?
)