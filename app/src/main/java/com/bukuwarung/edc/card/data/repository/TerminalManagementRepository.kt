package com.bukuwarung.edc.card.data.repository

import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.edc.card.data.datasource.TerminalApi
import com.bukuwarung.edc.card.data.model.*
import com.bukuwarung.edc.util.Utils
import retrofit2.Response
import javax.inject.Inject

class TerminalManagementRepository @Inject constructor(private val api: TerminalApi) {

    suspend fun activateTerminal(serialNumber: String, appDeviceId: String, fcmToken: String): Response<TmsResponse<ActivationResponse>> {
        return api.activateTerminal(serialNumber, appDeviceId, ActivationRequest(fcmToken))
    }

    suspend fun updateTerminalStatus(serialNumber: String, status: String, appDeviceId: String): Response<TmsResponse<Terminal>> {
        return api.updateTerminalStatus(serialNumber, mapOf("status" to status), appDeviceId)
    }

    suspend fun getTerminal(serialNumber: String): Response<TmsResponse<Terminal>> {
        return api.getTerminal(serialNumber)
    }

    suspend fun updateTerminal(serialNumber: String, terminal: Terminal): Response<TmsResponse<Terminal>> {
        if(Utils.getMasterKey().isNullOrEmpty() || Utils.getConnectedDeviceMasterKey(serialNumber).isNullOrEmpty()){
            return api.updateTerminal(serialNumber, terminal, true)
        }else {
            try {
                Utils.setTerminalMasterKey(Utils.getConnectedDeviceMasterKey(serialNumber))
            }catch (e: Exception){
                e.printStackTrace()
            }
            return api.updateTerminal(serialNumber, terminal,false)
        }
    }

    suspend fun updateTerminalLocation(serialNumber: String, terminalLocation: TerminalLocation): Response<TmsResponse<TerminalLocation>> {
        return api.updateTerminalLocation(serialNumber, terminalLocation)
    }

    suspend fun pairBluetoothDevice(serialNumber: String, appDeviceId: String, androidId: String, btName: String, btAddress: String): Response<TmsResponse<TerminalPairedDevice>> {
        return api.pairBluetoothDevice(serialNumber,appDeviceId, androidId, btName, btAddress)
    }

    fun getPairedDeviceMacAddress(): String {
        return BluetoothDevices.getPairedCardReader()?:""
    }
}
