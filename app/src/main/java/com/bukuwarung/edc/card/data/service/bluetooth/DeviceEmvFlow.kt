package com.bukuwarung.edc.card.data.service.bluetooth

import android.os.Bundle
import android.util.Log
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.edc.card.data.util.EmvUtil
import com.bukuwarung.edc.card.domain.model.CardReaderResult
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.OnlineTransactionResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_VALID
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_CONFIRM_CARD_INFO
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstOnlineResultHandler
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class DeviceEmvFlow @Inject constructor(private val edcDeviceService: ExternalCardReaderServiceImpl) {

    val TAG = "EDC_EMV_READ_CARD"

    fun inputOnlineResult(responseCode:String, field55IccData:String ) = callbackFlow {
        val onlineResult = Bundle()

        onlineResult.putBoolean(
            ConstIPBOC.inputOnlineResult.onlineResult.KEY_isOnline_boolean,
            true
        )
        onlineResult.putString(
            ConstIPBOC.inputOnlineResult.onlineResult.KEY_respCode_String,
            responseCode
        )
        onlineResult.putString(
            ConstIPBOC.inputOnlineResult.onlineResult.KEY_field55_String,
            if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC)) "XXXX" else field55IccData
        )

        try {
            val iccData = onlineResult.getString(ConstIPBOC.inputOnlineResult.onlineResult.KEY_field55_String)
            val onlineAuthResult = CardReaderHelper.getInstance()?.confirmOnlineResult(responseCode, iccData!!)
            val onlineDataValidationResult = EmvUtil.getOnlineDataValidationResult(onlineAuthResult!!.result, onlineAuthResult.data)
            if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE)){
                //intentionally break the flow
            }else {
                trySend(onlineDataValidationResult!!)
            }
        } catch (e: Exception) {
            var data = Bundle()
            data.putString(
                Constants.KEY_VALIDATION_ERROR_CODE,
                Constants.DEVICE_TRANSACTION_VALIDATION_ERROR
            )
            val onlineDataValidationResult = EdcResponse.Success(
                OnlineTransactionResult(
                    ConstOnlineResultHandler.onProccessResult.result.Online_AAC,
                    data,
                    "online result refused"
                )
            )
            if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE)){
                //intentionally break the flow
            }else {
                trySend(onlineDataValidationResult!!)
            }
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "inputOnlineResult flow close")
            }
        }
    }
}