package com.bukuwarung.edc.card.data.service.bluetooth

import android.graphics.Bitmap
import android.util.Log
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.bluetooth_printer.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLineBold
import com.bukuwarung.bluetooth_printer.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.bluetooth_printer.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.bluetooth_printer.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.bluetooth_printer.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.bluetooth_printer.model.PairedDevice
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.bluetooth_printer.model.printTypes.ImagePrintType
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.bluetooth_printer.utils.PrinterCallBack
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.constant.PrintConst.TRANSACTION_TYPE_CHECK_BALANCE
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_MAG
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orDash
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.orNil
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

/*
temporary class to handle miniatm receipt
needs to be moved to generic printer
 */
class TianyuReceiptPrinterFlow @Inject constructor(
    private val edcDeviceService: ExternalCardReaderServiceImpl,
    private val printCommand: ReceiptPrintCommand,
    private val header: ByteArray?,
    private val bukuAgen: ByteArray?,
    private val headerBitmap: Bitmap?,
    private val bukuAgenBitmap: Bitmap?
) {
    val TAG = "EDC_PRINT"

    private fun getFormattedText(): ArrayList<BasePrintType> {
        val receipt = printCommand.receipt
        val printableList = ArrayList<BasePrintType>()

        if (headerBitmap != null) {
            val fixedBitmap = resizeBitmap(headerBitmap,250,120 )
            printableList.add(appendExtraLine())
            printableList.add(
                ImagePrintType.Builder(fixedBitmap ?: headerBitmap)
                    .setAlignment(BasePrinter.ALIGNMENT_CENTER).build()
            )
        }

        val businessName = Utils.getBusinessNameForSelectedSerialNumber().ifBlank { Utils.getBusinessName() }
        if (businessName.isNotBlank()) printableList.add(appendCenterWithNewLine(businessName.uppercase()))

        if (Utils.getBusinessAddress().isNotBlank()) printableList.add(appendCenterWithNewLine(Utils.getBusinessAddress()))

        if (printCommand.destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name) {
            printableList.add(appendCenterWithNewLine("---RE-PRINT---"))
        }

        val date = receipt?.transactionDate?.replace("/", "-")
        var dateTime = DateTimeUtils.getFormattedLocalDateTime(
            date.orEmpty(),
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        if (printCommand.destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name) {
            dateTime = DateTimeUtils.getUTCTimeToLocalDateTime(
                date.orEmpty(),
                DateTimeUtils.DD_MMM_YYYY_HH_MM
            )
        }
        printableList.add(appendLeftWithNewLine("Waktu      : $dateTime"))
        val terminalId = if (printCommand.terminalId.isNullOrBlank()) Utils.getTerminalId() else printCommand.terminalId
        printableList.add(appendLeftWithNewLine("Terminal ID: $terminalId"))
        printableList.add(appendLeftWithNewLine("Merchant ID: ${Utils.getMerchantId()}"))
        val trcRc = "${receipt?.systemTraceAuditNumber}/${receipt?.responseCode.orDefault("00")}"
        printableList.add(appendLeftWithNewLine("Trace/RC   : $trcRc"))

        printableList.add(appendLeftWithNewLine("Ref No     : ${receipt?.rrn}"))
        printableList.add(appendLeftWithNewLine("- - - - - - - - - - - - - - - -"))

        val accountTypeTranslation = when (printCommand.accountType) {
            PaymentConst.TYPE_SAVINGS -> "DEBIT TABUNGAN"
            PaymentConst.TYPE_CHECKING -> "GIRO"
            else -> ""
        }
        printableList.add(appendLeftWithNewLine("Jenis Kartu: $accountTypeTranslation"))
        val cardEntryMode = if (printCommand.cardEntryMode == CARD_ENTRY_MODE_MAG) {
            "Magnetic   :"
        } else {
            "Chip       :"
        }
        printableList.add(appendLeftWithNewLine("$cardEntryMode ${printCommand.maskedPan}"))

        if (printCommand.printType == TRANSACTION_TYPE_CHECK_BALANCE) {
            printableList.add(appendCenterWithNewLine("Saldo "))
            printableList.add(appendCenterWithNewLineBold(Utils.formatAmount(receipt?.balanceInformation?.balance?.toDouble())))
        } else {
            val bankNameSource = receipt?.sourceDetails?.bankName.orEmpty()
            val nameSource = Utils.maskSensitiveInfo(receipt?.sourceDetails?.name.orEmpty())
            val accountNoSource = Utils.maskSensitiveInfo(receipt?.sourceDetails?.accountNumber.orEmpty())
            val formatedSourceBankAcc = formatBankAccount(bankNameSource, nameSource, accountNoSource)
            printableList.add(appendLeftWithNewLine("Rek. Asal  : $formatedSourceBankAcc"))
            if (!isCashWithdrawal((receipt))) {
                val bankNameDest = receipt?.destDetails?.bankName.orEmpty()
                val nameDest = receipt?.destDetails?.name.orEmpty()
                val accountNoDest = receipt?.destDetails?.accountNumber.orEmpty()
                val formatedDestBankAcc = formatBankAccount(nameDest, bankNameDest, accountNoDest)
                printableList.add(appendLeftWithNewLine("Rek. Tujuan: $formatedDestBankAcc"))
            }
            if (printCommand.notes.isNotNullOrBlank()) {
                printableList.add(appendLeftWithNewLine("Berita     : ${printCommand.notes}"))
            }

            val status = when (receipt?.endUserStatus) {
                EndUserStatusValues.SUCCESS -> {
                    "Transaksi Berhasil"
                }

                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    "Pencairan Diproses"
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    if (isCashWithdrawal((receipt))) {
                        "Transaksi Pending"
                    } else {
                        "Transaksi Diproses"
                    }
                }

                EndUserStatusValues.PENDING -> {
                    "Transaksi Pending"
                }

                else -> {
                    ""
                }
            }
            printableList.add(appendCenterWithNewLine(status))
            printableList.add(appendCenterWithNewLineBold(Utils.formatAmount(receipt?.amount)))

            when (receipt?.endUserStatus) {
                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    printableList.add(appendCenterWithNewLine("Pencairan uang diproses. Jangan"))
                    printableList.add(appendCenterWithNewLine("khawatir, transaksi akan sukses."))
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    if (isCashWithdrawal((receipt))) {
                        printableList.add(appendCenterWithNewLine("Transaksi Pending. Cek berkala"))
                        printableList.add(appendCenterWithNewLine("ke Agen untuk mengetahui status"))
                        printableList.add(appendCenterWithNewLine("akhir transaksi Anda."))
                    } else {
                        printableList.add(appendCenterWithNewLine("Transaksi diproses. Cek berkala"))
                        printableList.add(appendCenterWithNewLine("ke Agen untuk mengetahui status"))
                        printableList.add(appendCenterWithNewLine("akhir transaksi Anda."))
                    }
                }

                EndUserStatusValues.PENDING -> {
                    if (isCashWithdrawal((receipt))) {
                        printableList.add(appendCenterWithNewLine("Transaksi Pending. Cek berkala"))
                        printableList.add(appendCenterWithNewLine("ke Agen untuk mengetahui status"))
                        printableList.add(appendCenterWithNewLine("akhir transaksi Anda."))
                    } else {
                        printableList.add(appendCenterWithNewLine("Transaksi Pending. Cek berkala"))
                        printableList.add(appendCenterWithNewLine("ke Agen untuk mengetahui status"))
                        printableList.add(appendCenterWithNewLine("akhir transaksi Anda."))
                    }
                }
            }
        }

        printableList.add(appendLeftWithNewLine("- - - - - - - - - - - - - - - -"))
        if (bukuAgenBitmap != null) {
            printableList.add(
                ImagePrintType.Builder(bukuAgenBitmap).setAlignment(BasePrinter.ALIGNMENT_CENTER)
                    .setNewLinesAfter(0)
                    .build()
            )
        }
        printableList.add(appendCenterWithNewLine("SIMPAN RESI INI SEBAGAI"))
        printableList.add(appendCenterWithNewLine("BUKTI TRANSAKSI YANG SAH"))
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        return printableList
    }

    private fun getFormattedTextForPpob(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        val textBuilder = StringBuilder()
        val order = printCommand.orderResponse
        val item = order?.items?.firstOrNull()
        val sellingPrice = when {
            order?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0 -> order?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
            item?.sellingPrice.orNil > 0.0 -> item?.sellingPrice
            else -> order?.amount
        }
        val date = DateTimeUtils.getLocalStringFromUtc(
            order?.createdAt,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        // Add business name and phone number if available
        if (Utils.getBusinessName()
                .isNotBlank()
        ) printableList.add(appendCenterWithNewLine(Utils.getBusinessName()))
        if (Utils.getPhoneNumber()
                .isNotBlank()
        ) printableList.add(appendCenterWithNewLine(Utils.getPhoneNumber()))
        // Leave 1 empty lines
        printableList.add(appendExtraLine())
        /** date printed*/
        printableList.add(appendLeftAndRight("Tanggal", date.orDash))
        /** payment code printed*/
        printableList.add(appendLeftAndRight("Kode Pembayaran", order?.transactionId.orDash))
        /** customer name printed */
        printableList.add(appendLeftAndRight("Pelanggan", item?.details?.customerName.orDash))
        /** customer phone number printed */
        printableList.add(appendLeftAndRight("", item?.details?.customerNumber.orDash))
        printableList.add(appendDashLine())
        when (item?.beneficiary?.category.orEmpty()) {
            PpobConst.CATEGORY_PULSA -> {
                /** phone number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor HP",
                        item?.beneficiary?.phoneNumber.orDash
                    )
                )
                /** product printed*/
                printableList.add(appendLeftAndRight("Pulsa", item?.name.orDash))
                /** serial number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Serial Number",
                        item?.details?.serialNumber.orDash
                    )
                )
            }

            PpobConst.CATEGORY_PAKET_DATA -> {
                /** phone number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor HP",
                        item?.beneficiary?.phoneNumber.orDash
                    )
                )
                /** product printed*/
                printableList.add(appendLeftAndRight("Paket Data", item?.name.orDash))
                /** serial number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Serial Number",
                        item?.details?.serialNumber.orDash
                    )
                )
            }

            PpobConst.CATEGORY_LISTRIK -> {
                if (item?.beneficiary?.code?.equals(PpobConst.CATEGORY_LISTRIK_POSTPAID).isTrue || item?.beneficiary?.code?.equals(
                        PpobConst.CATEGORY_PLN_POSTPAID
                    ).isTrue
                ) {
                    /** customer id printed*/
                    printableList.add(
                        appendLeftAndRight(
                            "ID Pelanggan",
                            item?.beneficiary?.accountNumber.orDash
                        )
                    )
                    /** periode printed*/
                    printableList.add(appendLeftAndRight("Periode", item?.details?.periode.orDash))
                    /** total billing printed*/
                    printableList.add(
                        appendLeftAndRight(
                            "Total Lembar Tagihan",
                            item?.details?.totalLembarTagihan.orDash
                        )
                    )
                    /** tarif printed*/
                    printableList.add(appendLeftAndRight("Tarif/Daya", item?.details?.tarif.orDash))
                    /** total tagihan printed*/
                    printableList.add(
                        appendLeftAndRight(
                            "Total Tagihan",
                            Utils.formatAmount(order?.amount)
                        )
                    )
                } else {
                    /** product printed*/
                    printableList.add(appendLeftAndRight("Token Listrik", item?.name.orDash))
                    /** account number printed*/
                    printableList.add(
                        appendLeftAndRight(
                            "ID Pelanggan",
                            item?.beneficiary?.accountNumber.orDash
                        )
                    )
                    /** customer name printed*/
                    printableList.add(
                        appendLeftAndRight(
                            "Nama Pelanggan",
                            item?.details?.customerName.orDash
                        )
                    )
                    /** Total Kwh printed*/
                    printableList.add(
                        appendLeftAndRight(
                            "Total kWh",
                            item?.details?.totalKwh.orDash
                        )
                    )
                    /** tarif printed*/
                    printableList.add(appendLeftAndRight("Tarif/Daya", item?.details?.tarif.orDash))
                    /** dotted line printed*/
                    printableList.add(appendDashLine())
                    /** kode token printed*/
                    printableList.add(appendCenterWithNewLine("Kode Token"))
                    /** total payment value printed*/
                    printableList.add(appendCenterWithNewLineBold(item?.details?.token.orDash))
                }

            }

            PpobConst.CATEGORY_EWALLET -> {
                /** phone number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor HP",
                        item?.beneficiary?.phoneNumber.orDash
                    )
                )
                /** customer name printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nama Pelanggan",
                        item?.details?.customerName.orDash
                    )
                )
                /** provider printed*/
                printableList.add(appendLeftAndRight("Provider", item?.details?.billerName.orDash))
                /** nominal topup printed*/
                printableList.add(appendLeftAndRight("Nominal Top Up", item?.name.orDash))
                /** serial number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Serial Number",
                        item?.details?.serialNumber.orDash
                    )
                )
            }

            PpobConst.CATEGORY_LISTRIK_POSTPAID, PpobConst.CATEGORY_PLN_POSTPAID -> {

            }

            PpobConst.CATEGORY_PULSA_POSTPAID -> {
                /** operator printed*/
                printableList.add(appendLeftAndRight("Operator", item?.details?.productName.orDash))
                /** phone number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor HP",
                        item?.beneficiary?.phoneNumber.orDash
                    )
                )
                /** customer name printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nama Pelanggan",
                        item?.details?.customerName.orDash
                    )
                )
            }

            PpobConst.CATEGORY_BPJS -> {
                /** card number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor Kartu",
                        item?.details?.customerNumber.orDash
                    )
                )
                /** customer name printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nama Pelanggan",
                        item?.details?.customerName.orDash
                    )
                )
                /** no of family printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Jumlah Keluarga",
                        item?.details?.memberCount.orDash
                    )
                )
                /** periode printed*/
                printableList.add(appendLeftAndRight("Periode", item?.details?.period.orDash))
            }

            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                /** customer name printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nama Pelanggan",
                        item?.details?.customerName.orDash
                    )
                )
                /** provider printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Provider",
                        order?.metadata?.billerName.orDash
                    )
                )
                /** periode printed*/
                printableList.add(appendLeftAndRight("Periode", item?.details?.period.orDash))
                /** total tagihan printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Total Tagihan",
                        Utils.formatAmount(order?.amount)
                    )
                )
            }

            PpobConst.CATEGORY_PDAM -> {
                /** pdam printed*/
                printableList.add(appendLeftAndRight("PDAM", order?.metadata?.billerName.orDash))
                /** phone number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor HP",
                        item?.beneficiary?.phoneNumber.orDash
                    )
                )
                /** customer name printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nama Pelanggan",
                        item?.details?.customerName.orDash
                    )
                )
                /** periode printed*/
                printableList.add(appendLeftAndRight("Periode", item?.details?.period.orDash))
                /** total tagihan printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Total Tagihan",
                        Utils.formatAmount(order?.amount)
                    )
                )
            }

            PpobConst.CATEGORY_MULTIFINANCE -> {
                /** contact number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor Kontak",
                        item?.details?.customerNumber.orDash
                    )
                )
                /** customer name printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nama Pelanggan",
                        item?.details?.customerName.orDash
                    )
                )
                /** product printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Produk Angsuran",
                        order?.metadata?.billerName.orDash
                    )
                )
                /** periode printed*/
                printableList.add(appendLeftAndRight("Periode", item?.details?.period.orDash))
                /** fine printed*/
                printableList.add(appendLeftAndRight("Denda", item?.details?.fine.orDash))
            }

            PpobConst.CATEGORY_VEHICLE_TAX -> {
                /** customer name printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nama Pelanggan",
                        item?.details?.customerName.orDash
                    )
                )
                /** phone number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor HP",
                        item?.beneficiary?.phoneNumber.orDash
                    )
                )
                /** policy number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor Polisi",
                        item?.details?.policyNumber.orDash
                    )
                )
                /** vehicle brand printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Merek Kendaraan",
                        item?.details?.vehicleBrand.orDash
                    )
                )
                /** periode printed*/
                printableList.add(appendLeftAndRight("Periode", item?.details?.period.orDash))
                /** transportation type printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Jenis Kendaraan",
                        item?.details?.vehicleName.orDash
                    )
                )
                /** vehicle type printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Tipe Kendaraan",
                        item?.details?.vehicleType.orDash
                    )
                )
                /** vehicle color printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Warna Kendaraan",
                        item?.details?.vehicleColor.orDash
                    )
                )
                /** vehicle build year printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Tahun Buat Kendaraan",
                        item?.details?.buildYear.orDash
                    )
                )
                /** machine number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor Mesin",
                        item?.details?.machineNumber.orDash
                    )
                )
                /** frame number printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Nomor Rangka/NIK/VIN",
                        item?.details?.frameNumber.orDash
                    )
                )
                /** pkb printed*/
                printableList.add(appendLeftAndRight("PKB", item?.details?.pkb.orDash))
                /** total tagihan printed*/
                printableList.add(
                    appendLeftAndRight(
                        "Total Tagihan",
                        Utils.formatAmount(item?.amount)
                    )
                )
            }
        }
        printableList.add(appendDashLine())
        printableList.add(appendCenterWithNewLine("Total Pembayaran"))
        printableList.add(appendCenterWithNewLineBold(Utils.formatAmount(sellingPrice)))
        printableList.add(appendDashLine())
        printableList.add(appendCenterWithNewLine("Dibuat dengan MiniATM BukuAgen"))
        printableList.add(appendCenterWithNewLine("Bagian dari BukuWarung"))
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        return printableList
    }

    private fun getFormattedTextForPaymentOut(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        val order = printCommand.orderResponse
        val item = order?.items?.firstOrNull()
        // Add business name and phone number if available
        if (Utils.getBusinessName().isNotBlank())
            printableList.add(appendCenterWithNewLine(Utils.getBusinessName()))
        if (Utils.getPhoneNumber().isNotBlank())
            printableList.add(appendCenterWithNewLine(Utils.getPhoneNumber()))
        //adding dashed line
        printableList.add(appendDashLine())
        // date printed
        printableList.add(
            appendLeftAndRight(
                "Tanggal",
                DateTimeUtils.getLocalStringFromUtc(
                    order?.createdAt,
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            )
        )
        // printing transactionId
        printableList.add(appendLeftAndRight("Kode Pembayaran", order?.transactionId))
        // printing sender name
        printableList.add(appendLeftAndRight("Nama Pengirim", Utils.getBusinessName()))
        //printing dashed line
        printableList.add(appendDashLine())
        // printing sender payment details
        printableList.add(appendCenterWithNewLine("Detail Pengirim"))
        printableList.add(appendCenterWithNewLineBold("${order?.payments?.getOrNull(0)?.paymentMethod?.code} - ${Utils.getPhoneNumber()}"))
        // printing destination payment details
        printableList.add(appendCenterWithNewLine("Rekening Tujuan"))
        printableList.add(appendCenterWithNewLineBold(item?.beneficiary?.name))
        printableList.add(appendCenterWithNewLineBold("${item?.beneficiary?.code} - ${item?.beneficiary?.accountNumber}"))
        printableList.add(appendCenterWithNewLine("Pembayaran Berhasil"))
        if (order?.agentFeeInfo?.amount != 0.0) {
            printableList.add(appendDashLine())
            printableList.add(
                appendLeftAndRight(
                    "Jumlah Pembayaran",
                    Utils.formatAmount(order?.items?.get(0)?.sellingPrice)
                )
            )
            printableList.add(
                appendLeftAndRight(
                    "Biaya Layanan",
                    Utils.formatAmount(order?.agentFeeInfo?.amount)
                )
            )
        }
        printableList.add(appendDashLine())
        printableList.add(appendCenterWithNewLine("Total Pembayaran"))
        val total = order?.items?.get(0)?.sellingPrice.orNil + order?.agentFeeInfo?.amount.orNil
        printableList.add(appendCenterWithNewLineBold(Utils.formatAmount(total)))
        printableList.add(appendDashLine())
        printableList.add(appendCenterWithNewLine("Dibuat dengan MiniATM BukuAgen"))
        printableList.add(appendCenterWithNewLine("Bagian dari BukuWarung"))
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        return printableList
    }

    //non generic code, to be fixed later
    fun printReceipt() = callbackFlow {
        Log.d(TAG, "start print check balance receipt")
        val printText = when {
            printCommand.receipt != null -> getFormattedText()
            printCommand.orderResponse != null && printCommand.orderResponse?.items?.firstOrNull()?.sku.equals(
                PaymentConst.TYPE_PAYMENT_OUT,
                false
            ) -> getFormattedTextForPaymentOut()
            printCommand.orderResponse != null &&
                    PaymentAuxilliary.isPpob(printCommand.orderResponse?.items?.firstOrNull()?.beneficiary?.category) -> getFormattedTextForPpob()
            else -> arrayListOf()
        }

        try {
            printPrintables(
                printText, object : PermissionCallback {
                    override fun onPermissionRequired(permissions: Array<String>) {
                        val printerResult = PrinterResult(true, ErrorStatus.ERROR_NONE)
                        trySend(EdcResponse.Success(printerResult))
                    }
                }, object : PrinterCallBack {
                    override fun connectingWithPrinter() {
                        bwLog("printer", "connectingWithPrinter")
                    }

                    override fun printingOrderSentSuccessfully() {
                        bwLog("PrinterListener", "Printer success ")
                    }

                    override fun connectionFailed(error: String) {
                        bwLog("printer", "connectionFailed $error")
                        val printerResult = PrinterResult(false, ErrorStatus.ERROR_HARDWARE)
                        trySend(EdcResponse.Success(printerResult))
                    }

                    override fun onError(error: String) {
                        bwLog("printer", "onError $error")
                        val printerResult = PrinterResult(false, ErrorStatus.ERROR_HARDWARE)
                        trySend(EdcResponse.Success(printerResult))
                    }

                    override fun onMessage(message: String) {
                        bwLog("printer", "onMessage $message")
                    }

                    override fun disconnected() {
                        bwLog("printer", "disconnected")
                        val printerResult = PrinterResult(false, ErrorStatus.ERROR_NONE)
                        trySend(EdcResponse.Success(printerResult))
                    }
                }
            )
        } catch (e: Exception) {
            bwLog("PrinterListener", "Printer error ")
            val printerResult = PrinterResult(false, ErrorStatus.ERROR_NONE)
            trySend(EdcResponse.Success(printerResult))
            bwLog(e = e)
        } finally {
            awaitClose {
                bwLog(TAG, "${printCommand.printType} receipt print flow closed")
            }
        }
    }


    fun printPrintables(
        printableList: ArrayList<BasePrintType>,
        callback: PermissionCallback,
        printCallback: PrinterCallBack
    ) {
        val pairedPrinter = BluetoothDevices.getPairedPrinterList()
        pairedPrinter?.forEach {
            val printingHelper =
                BluetoothDevices.printer(PairedDevice(it?.name!!, it?.macAddress!!))
            printingHelper.printAll(printableList, callback, printCallback)
        }
    }

    private fun resizeBitmap(original: Bitmap?, width: Int, height: Int): Bitmap? {
        original?.let {
            return Bitmap.createScaledBitmap(original, width, height, true)
        }?: return null
    }


    private fun formatBankAccount(string1: String, string2: String, string3: String): String {
        val join = "$string1 - $string2 - $string3"
        return join.chunked(19).joinToString(" ".repeat(13))
    }

    private fun isCashWithdrawal(receipt: CardReceiptResponse? = null):Boolean{
        return receipt?.transactionType == "CASH_WITHDRAWAL" || receipt?.transactionType == "CASH_WITHDRAWAL_POSTING"
    }
}
