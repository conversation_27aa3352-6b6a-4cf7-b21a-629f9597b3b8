package com.bukuwarung.edc.card.data.service.bluetooth

import android.util.Log
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

/*
temporary class to handle miniatm receipt
needs to be moved to generic printer
 */
class UtilReceiptPrinterFlow @Inject constructor(private val edcDeviceService: ExternalCardReaderServiceImpl, private val printCommand: ReceiptPrintCommand, private val header: ByteArray?) {

    val TAG = "EDC_PRINT"
    
    //non generic code, to be fixed later
    fun printReceipt() = callbackFlow {
        try {
            val  printerResult = PrinterResult(false,ErrorStatus.findByErrorCode(-1))
            trySend(EdcResponse.Success(printerResult))
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, printCommand.printType.toString()+" receipt print flow close")
            }
        }
    }
}