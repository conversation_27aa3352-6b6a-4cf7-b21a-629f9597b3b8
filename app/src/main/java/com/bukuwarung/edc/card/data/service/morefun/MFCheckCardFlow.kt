package com.bukuwarung.edc.card.data.service.morefun

import android.os.Bundle
import android.util.Log
import com.bukuwarung.analytic.BtAnalyticConstant.EDC_BRAND
import com.bukuwarung.analytic.BtAnalyticConstant.PAIRING_STATUS
import com.bukuwarung.analytic.BtAnalyticConstant.SAKU_NAME
import com.bukuwarung.analytic.BtAnalyticConstant.SUCCESS
import com.bukuwarung.edc.card.data.util.EmvUtil
import com.bukuwarung.edc.card.domain.model.CheckCardResult
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_MAG
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_NONE
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.morefun.yapi.ServiceResult
import com.morefun.yapi.device.pinpad.DukptCalcObj
import com.morefun.yapi.device.reader.icc.IccReaderSlot
import com.morefun.yapi.emv.EmvTransDataConstrants
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import javax.inject.Inject

class MFCheckCardFlow @Inject constructor(private val edcDeviceService: MFDeviceServiceImpl) {

    companion object {
        private const val TAG = "EMV-MFCheckCardFlow"
        private var instance: MFCheckCardFlow? = null

        @JvmStatic
        fun getInstance(edcDeviceService: MFDeviceServiceImpl): MFCheckCardFlow {
            return instance ?: synchronized(this) {
                instance ?: MFCheckCardFlow(edcDeviceService).also { instance = it }
            }
        }
    }

    private var checkCardJob: Job? = null

    fun startCheckCard(timeOut: Int) = callbackFlow {
        Log.d(TAG, "Start Check Card Flow")
        val mfCheckCardListener = MFCheckCardListener(
            onSearchResult = { ret, magCardInfoEntity ->
                Log.d(TAG, "onCardSwiped: card swiped")
                val info = Bundle()
                if (ret == ServiceResult.Success) {
                    with(info) {
                        putInt("CARD_ENTRY_MODE", Constants.CARD_ENTRY_MODE_MAG)
                        putString(
                            ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String,
                            magCardInfoEntity.cardNo
                        )
                        putString(
                            ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String,
                            magCardInfoEntity.serviceCode
                        )
                        if(magCardInfoEntity.tk2.isNotNullOrBlank() && magCardInfoEntity.tk2.contains(magCardInfoEntity.cardNo)) {
                            putString(
                                ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String,
                                magCardInfoEntity.tk2
                            )
                        }else{
                            putString(
                                ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String,
                                convertMFtoPax(magCardInfoEntity.tk1)
                            )
                        }
                        putString(
                            ConstPBOCHandler.onConfirmCardInfo.info.KEY_CARD_SN_String,
                            magCardInfoEntity.ksn
                        )
                        putString(
                            ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String,
                            magCardInfoEntity.expDate
                        )
                        val result = """ 
                        PAN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String)}
                        TRACK2:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String)}
                        CARD_SN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_CARD_SN_String)}
                        SERVICE_CODE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String)}
                        EXPIRED_DATE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String)}
                        CARD_ENTRY_MODE:${info.getInt("CARD_ENTRY_MODE")}
                        """.prependIndent()
                        Log.d(TAG, "onCardSwiped: $result")
                        var checkCardResult = CheckCardResult(
                            CARD_ENTRY_MODE_MAG,
                            info,
                            Constants.CARD_CHECK_STATUS_SUCCESS
                        )
                        val cardResponse = EdcResponse.Success(checkCardResult)
                        try {
                            val props = HashMap<String, String>()
                            props[ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String] =
                                magCardInfoEntity.expDate
                            props[ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String] =
                                magCardInfoEntity.serviceCode
                            props[ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String] =
                                magCardInfoEntity.tk2.substring(0,6)
                            props[ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String] =
                                magCardInfoEntity.cardNo.substring(0, 6)
                            Analytics.trackEvent("debug_card_swiped", props);
                        }catch (e: Exception){
                            e.printStackTrace()
                        }
                        trySend(cardResponse)
                    }
                } else {
                    Log.i("check_card_error", "error reading magnetic card $ret")
                    val checkCardResult = CheckCardResult(
                        CARD_ENTRY_MODE_NONE,
                        null,
                        Constants.CARD_CHECK_STATUS_ERROR
                    )
                    val cardResponse = EdcResponse.Success(checkCardResult)
                    trySend(cardResponse)
                }
            },
        )

        try {
            val magCardReader = DeviceHelper.getMagCardReader()
            magCardReader.setIsCheckLrc(true)
            val bundle = Bundle()
            bundle.putInt(DukptCalcObj.Param.DUKPT_KEY_INDEX, 0)
            magCardReader.searchCard(mfCheckCardListener.magCardListener, 60, bundle)

            checkCardJob?.cancel()
            checkCardJob = launch {
                val startTime = System.currentTimeMillis()
                while (System.currentTimeMillis() - startTime < timeOut*1000) {
                    Log.i(TAG, "CheckChipCard..")
                    val icCardExist: Boolean = DeviceHelper
                        .getIccCardReader(IccReaderSlot.ICSlOT1)
                        .isCardExists

                    if (icCardExist) {
                        stopCheckCard()
                        abortEmv()

                        val checkCardResult = CheckCardResult(
                            CARD_ENTRY_MODE_IC,
                            null,
                            Constants.CARD_CHECK_STATUS_SUCCESS
                        )
                        val cardResponse = EdcResponse.Success(checkCardResult)
                        EdcResponse.CardAvailable(true)
                        trySend(cardResponse)
                        return@launch
                    }
                    delay(300)
                }
                stopCheckCard()
                abortEmv()

                val checkCardResult = CheckCardResult(
                    CARD_ENTRY_MODE_NONE,
                    null,
                    Constants.CARD_CHECK_STATUS_TIMEOUT
                )
                val cardResponse = EdcResponse.Success(checkCardResult)
                trySend(cardResponse)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "check card flow close")
            }
        }
    }

    private fun convertMFtoPax(mfTrack2: String): String {
        // Step 1: Remove all spaces and unnecessary slashes
        val cleanedTrack = mfTrack2.replace(" ", "").replace("/", "")

        // Step 2: Find the separator '^' and split the string appropriately
        val firstCaretIndex = cleanedTrack.indexOf('^')
        val secondCaretIndex = cleanedTrack.indexOf('^', firstCaretIndex + 1)

        // Step 3: Extract card number and the rest of the data
        val cardNumber = cleanedTrack.substring(1, firstCaretIndex) // Start from 1 to skip 'B'
        val otherData = cleanedTrack.substring(secondCaretIndex + 1) // Data after the second '^'

        // Step 4: Construct the Pax Track2 format
        val paxTrack2 = "$cardNumber=$otherData"

        // Step 5: Pad the second part if necessary (assume total length of data part should be 19)
        val components = paxTrack2.split('=')
        if (components.size == 2) {
            val paddedData = components[1].padEnd(20, '0')
            var result = components[0] + "=" + paddedData
            return result.substring(0, 37)
        }

        // Return the original if the format does not split correctly
        return mfTrack2
    }


    fun checkCardAvailability(timeOut: Long) = callbackFlow {
        Log.d(TAG, "Check Card Availability")
        try {
            try {
                Thread.sleep(500)
            } catch (e: InterruptedException) {
                throw RuntimeException(e)
            }
            val icCardExist: Boolean =
                DeviceHelper.getIccCardReader(IccReaderSlot.ICSlOT1).isCardExists()
            trySend(EdcResponse.CardAvailable(icCardExist))
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "check card availability flow close")
            }
        }
    }

    fun abortEmv() {
        Log.d(TAG, "abort emv flow start")
        try {
            DeviceHelper.getEmvHandler().endPBOC()
            Log.d(TAG, "abort emv flow complete")
        } catch (e: Exception) {
            Log.d(TAG, "abort emv flow failed")
        }
    }

    fun stopCheckCard(): Boolean {
        Log.d(TAG, "stopCheckCard")
        try {
            checkCardJob?.cancel()
            DeviceHelper.getIccCardReader(IccReaderSlot.ICSlOT1).stopSearch()
            DeviceHelper.getMagCardReader().stopSearch()
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return true
    }

    private fun showResult(msg: String) {
        Log.i(TAG, "[EMV]-$msg")
    }
}