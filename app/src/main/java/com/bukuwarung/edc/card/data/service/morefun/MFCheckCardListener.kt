package com.bukuwarung.edc.card.data.service.morefun

import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import com.bukuwarung.edc.card.data.util.EmvUtil
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.util.Utils.bwLog
import com.morefun.yapi.device.reader.mag.MagCardInfoEntity
import com.morefun.yapi.device.reader.mag.OnSearchMagCardListener
import com.morefun.yapi.emv.OnEmvProcessListener
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler

class MFCheckCardListener(
    onSearchResult: (Int, MagCardInfoEntity) -> Unit = { _, _ -> },
    onConfirmCardNo: (bundle: Bundle) -> Unit = {},
    onRequestAmount: () -> Unit = {},
    onSelectApplication: (List<String>, isFirstSelect: Boolean) -> Unit = { _, _ -> },
    onGetCardResult: (retCode: Int, bundle: Bundle) -> Unit = { _, _ -> },
    onCardHolderInputPin: (isOnlinePin: Boolean, offlinePinType: Int) -> Unit = { _, _ -> },
    onCertVerify: (certName: String, certInfo: String) -> Unit = { _, _ -> },
    onFinish: (result: Int, data: Bundle) -> Unit = { _, _ -> },
    onOnlineProc: (data: Bundle) -> Unit = {}
) {
    val magCardListener: OnSearchMagCardListener = object : OnSearchMagCardListener.Stub() {
        override fun onSearchResult(ret: Int, magCardInfoEntity: MagCardInfoEntity) {
            onSearchResult(ret, magCardInfoEntity)
        }
    }

    val emvListener = object : OnEmvProcessListener.Stub() {
        @Throws(RemoteException::class)
        override fun onSelApp(appNameList: List<String>, isFirstSelect: Boolean) {
            showResult("ON SEL APP")
            onSelectApplication(appNameList, isFirstSelect)
        }

        @Throws(RemoteException::class)
        override fun onConfirmCardNo(cardNo: String) {
            showResult("CONFIRM CARD:$cardNo")
            DeviceHelper.getEmvHandler().onSetConfirmCardNoResponse(true)
            val info = Bundle()
            val track2 = EmvUtil.readTrack2()
            showResult("onGetCardResult track2:$track2")
            info.putString(
                ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String,
                EmvUtil.readPan()
            )
            info.putString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String, track2)
            info.putString(
                ConstPBOCHandler.onConfirmCardInfo.info.KEY_CARD_SN_String,
                EmvUtil.cardSn()
            )
            info.putString(
                ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String,
                EmvUtil.getServiceCodeFromTrack2(track2)
            )
            info.putString(
                ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String,
                EmvUtil.expDate()
            )
            info.putInt("CARD_ENTRY_MODE", CARD_ENTRY_MODE_IC)
            onConfirmCardNo(info)
        }

        /**
         *
         * @param isOnlinePin
         * @param offlinePinType  3:offline pin normal 2:offline pin again 1:offline pin last
         * @throws RemoteException
         */
        @Throws(RemoteException::class)
        override fun onCardHolderInputPin(isOnlinePin: Boolean, offlinePinType: Int) {
            showResult("IS ONLINE PIN:$isOnlinePin")
            onCardHolderInputPin(isOnlinePin, offlinePinType)
        }

        @Throws(RemoteException::class)
        override fun onPinPress(keyCode: Byte) {
        }

        @Throws(RemoteException::class)
        override fun onDisplayOfflinePin(retCode: Int) {
            showResult("DISPLAY OFFLINE PIN:$retCode")
        }

        @Throws(RemoteException::class)
        override fun inputAmount(type: Int) {
            showResult("INPUT AMOUNT")
            onRequestAmount()
        }

        @Throws(RemoteException::class)
        override fun onGetCardResult(retCode: Int, bundle: Bundle) {
            showResult("onGetCardResult ret:$retCode")
            onGetCardResult(retCode, bundle)
        }

        @Throws(RemoteException::class)
        override fun onDisplayMessage() {
            showResult("onDisplayMessage")
        }

        @Throws(RemoteException::class)
        override fun onUpdateServiceAmount(serviceRelatedData: String) {
            showResult("onUpdateServiceAmount: $serviceRelatedData")
        }

        @Throws(RemoteException::class)
        override fun onCheckServiceBlackList(pan: String, amount: String) {
        }

        @Throws(RemoteException::class)
        override fun onGetServiceDirectory(directory: ByteArray) {
            //DeviceHelper.getEmvHandler().onGetServiceDirectory(0)
        }

        @Throws(RemoteException::class)
        override fun onRupayCallback(type: Int, bundle: Bundle) {
            //val data = bundle.getByteArray(EmvRupayCallback.RUPAY_DATA_OUT)
            //val ret = Bundle()
            //ret.putInt(EmvRupayCallback.KEY_RET_CODE, 0)
            //DeviceHelper.getEmvHandler().onSetRupayCallback(type, ret)
        }

        @Throws(RemoteException::class)
        override fun onOnlineProc(data: Bundle) {
            showResult("GO ONLINE")
            onOnlineProc(data)
        }

        @Throws(RemoteException::class)
        override fun onContactlessOnlinePlaceCardMode(mode: Int) {
            showResult("onContactlessOnlinePlaceCardMode $mode")
        }

        @Throws(RemoteException::class)
        override fun onFinish(retCode: Int, data: Bundle) {
            showResult("ON FINISH")
            onFinish(retCode, data)
            //showResult("TIME:" + (System.currentTimeMillis() - startTick) + "ms")
            //showResult("CVM FLAG:" + data.getInt(EmvOnlineRequest.CVM_FLAG))
            //showResult("CVM SIGNATURE:" + data.getBoolean(EmvOnlineRequest.CVM_SIGNATURE))
            //emvFinish(retCode, data)
        }

        @Throws(RemoteException::class)
        override fun onCertVerify(certName: String, certInfo: String) {
            showResult("onCertVerify $certName, $certInfo")
            onCertVerify(certName, certInfo)
        }

        @Throws(RemoteException::class)
        override fun onSetAIDParameter(aid: String) {
            showResult("ON SET AID:$aid")
        }

        @Throws(RemoteException::class)
        override fun onSetCAPubkey(rid: String, index: Int, algMode: Int) {
            showResult("ON SET CAPK")
        }

        @Throws(RemoteException::class)
        override fun onTRiskManage(pan: String, panSn: String) {
            showResult("ON TRISK MANAGE")
        }

        @Throws(RemoteException::class)
        override fun onSelectLanguage(language: String) {
            showResult("ON SELECT LANGUAGE")
        }

        @Throws(RemoteException::class)
        override fun onSelectAccountType(accountTypes: List<String>) {
            showResult("ON SELECT ACCOUNT TYPE")
        }

        @Throws(RemoteException::class)
        override fun onIssuerVoiceReference(pan: String) {
            showResult("ON ISSUER VOICE REFERENCE")
        }
    }

    fun showResult(msg: String) {
        bwLog(msg = "[EMV-LISTENER] $msg")
    }
}