package com.bukuwarung.edc.card.data.service.morefun

import android.os.Bundle
import android.util.Log
import com.bukuwarung.edc.card.data.util.EmvUtil
import com.bukuwarung.edc.card.data.util.EmvUtil.getTransBundle
import com.bukuwarung.edc.card.data.util.TlvDataList
import com.bukuwarung.edc.card.domain.model.CardReaderResult
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.OnlineTransactionResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_ERROR
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_VALID
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_CONFIRM_CARD_INFO
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_CONFIRM_CERT_INFO
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_AMOUNT
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_INPUT_PIN
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_ONLINE
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_SELECT_APPLICATION
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.HexUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.morefun.yapi.ServiceResult
import com.morefun.yapi.device.reader.icc.IccReaderSlot
import com.morefun.yapi.emv.EmvOnlineResult
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC.startEMV.intent.KEY_authAmount_long
import com.vfi.smartpos.deviceservice.constdefine.ConstOnlineResultHandler
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

class MFEmvFlow @Inject constructor(private val edcDeviceService: MFDeviceServiceImpl) {

    companion object {
        private const val TAG = "EMV-MFEmvFlow"
        private var instance: MFEmvFlow? = null

        @JvmStatic
        fun getInstance(edcDeviceService: MFDeviceServiceImpl): MFEmvFlow {
            if (instance == null) {
                instance = MFEmvFlow(edcDeviceService)
            }
            return instance!!
        }
    }


    private val transactionResultChannel = Channel<EdcResponse<OnlineTransactionResult>>()

    fun inputOnlineResult(responseCode: String, field55IccData: String) = callbackFlow {
        val onlineResult = Bundle()

        onlineResult.putString(EmvOnlineResult.REJCODE, responseCode)
        onlineResult.putByteArray(
            EmvOnlineResult.RECVARPC_DATA,
            HexUtil.hexStringToByte(if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC)) "XXXX" else field55IccData)
        )

        try {
            DeviceHelper.getEmvHandler().onSetOnlineProcResponse(ServiceResult.Success, onlineResult)
            val transactionResult = transactionResultChannel.receive()
            trySend(transactionResult)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                bwLog(msg = "[$TAG] inputOnlineResult flow close")
            }
        }
    }

    fun startInsertCardRead(emvIntent: Bundle, timeout: Int) = callbackFlow {
        bwLog(msg = "[$TAG] start emv flow")
        val mfCheckCardListener = MFCheckCardListener(
            onRequestAmount = {
                bwLog(msg = "[$TAG] onRequestAmount")
                val cardData = CardReaderResult(operation = EMV_CALLBACK_REQUEST_AMOUNT)
                trySend(EdcResponse.Success(cardData))
            },
            onSelectApplication = { _, _ ->
                bwLog(msg = "[$TAG] onSelectApplication")
                val cardData = CardReaderResult(operation = EMV_CALLBACK_SELECT_APPLICATION)
                trySend(EdcResponse.Success(cardData))
            },
            onConfirmCardNo = { info ->
                val result = """ 
                    PAN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String)}
                    TRACK2:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String)}
                    CARD_SN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_CARD_SN_String)}
                    SERVICE_CODE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String)}
                    EXPIRED_DATE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String)}
                    """.prependIndent()
                bwLog(msg = "[$TAG] MFEmvFlow-onConfirmCardInfo $result")
                val cardData = CardReaderResult(
                    CARD_ENTRY_MODE_IC,
                    info,
                    CARD_STATUS_VALID,
                    operation = EMV_CALLBACK_CONFIRM_CARD_INFO
                )
                val cardResponse = EdcResponse.Success(cardData)
                trySend(cardResponse)
            },
            onCardHolderInputPin = { _, _ ->
                val cardData = CardReaderResult(inputPin = true, operation = EMV_CALLBACK_REQUEST_INPUT_PIN)
                trySend(EdcResponse.Success(cardData))
            },
            onOnlineProc = { _ ->
                bwLog(msg = "[$TAG] onRequestOnlineProcess")
                val cardData = CardReaderResult(operation = EMV_CALLBACK_REQUEST_ONLINE)
                if(emvIntent.getByte(ConstIPBOC.startEMV.intent.KEY_transProcessCode_byte) == 0x40.toByte()) {
                    val tags: MutableList<String> = ArrayList()
                    tags.add("9F0306000000000000")
                    tags.add("9C0140")
                    Log.d("onRequestOnlineProcess", "setemvdata")
                    edcDeviceService.emv?.setEMVData(tags)
                }
                trySend(EdcResponse.Success(cardData))
            },
            onCertVerify = { certName, certInfo ->
                val cardData = CardReaderResult(operation = EMV_CALLBACK_CONFIRM_CERT_INFO)
                bwLog(msg = "[$TAG] onConfirmCertInfo")
            },
            onFinish = { result, data ->
                Log.i(TAG, "onFinish retCode:$result, data:$data")

                if (result == -8014) {
                    val cardData = CardReaderResult(
                        cardStatus = CARD_STATUS_ERROR
                    )
                    val cardResponse = EdcResponse.Success(cardData)
                    trySend(cardResponse)
                    bwLog(e = Exception("EMV-ON-FINISH-ERROR: $result"))
                } else {

                    val str = """
                            RESULT:$result
                            TC_DATA:${
                        data.getString(
                            ConstOnlineResultHandler.onProccessResult.data.KEY_TC_DATA_String,
                            "not defined"
                        )
                    }
                            SCRIPT_DATA:${
                        data.getString(
                            ConstOnlineResultHandler.onProccessResult.data.KEY_SCRIPT_DATA_String,
                            "not defined"
                        )
                    }
                            REVERSAL_DATA:${
                        data.getString(
                            ConstOnlineResultHandler.onProccessResult.data.KEY_REVERSAL_DATA_String,
                            "not defined"
                        )
                    }
                            """.trimIndent()
                    Log.d(TAG, "inputOnlineResult -> onProccessResult: $result $str")
                    var resultCode = result
                    if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API)) {
                        //mock api icc data is always invalid, to continue transaction process device verification has to be hardcodeed to success
                        resultCode = ConstOnlineResultHandler.onProccessResult.result.TC
                        if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE)) {
                            //intentionally break the flow
                            launch {
                                val dataIncomplete = Bundle()
                                data.putString(
                                    Constants.KEY_VALIDATION_ERROR_CODE,
                                    Constants.DEVICE_TRANSACTION_VALIDATION_ERROR
                                )
                                val onlineDataValidationResult = EdcResponse.Success(
                                    OnlineTransactionResult(
                                        ConstOnlineResultHandler.onProccessResult.result.Online_AAC,
                                        dataIncomplete,
                                        "online result refused"
                                    )
                                )
                                transactionResultChannel.send(onlineDataValidationResult)
                            }
                            return@MFCheckCardListener
                        } else if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC)) {
                            //intentionally break the flow
                            resultCode = ConstOnlineResultHandler.onProccessResult.result.Online_AAC
                        }
                    } else if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_REVERSAL)) {
                        resultCode = ConstOnlineResultHandler.onProccessResult.result.Online_AAC
                    }
                    //since device has validated backend response, transaction can be removed from incomplete state
                    Utils.clearIncompleteTransaction()
                    launch {
                        if (resultCode != ConstOnlineResultHandler.onProccessResult.result.TC) {
                            data.putString(
                                Constants.KEY_VALIDATION_ERROR_CODE,
                                Constants.DEVICE_TRANSACTION_VALIDATION_ERROR
                            )
                        }

                        val onlineDataValidationResult = EdcResponse.Success(
                            OnlineTransactionResult(
                                resultCode,
                                data,
                                if (resultCode == ConstOnlineResultHandler.onProccessResult.result.TC) "online result TC(success)" else "online result refused"
                            )
                        )
                        transactionResultChannel.send(onlineDataValidationResult)
                    }
                }
            }
        )

        try {
            val handler = DeviceHelper.getEmvHandler()
            val amount = emvIntent.getLong(KEY_authAmount_long,0).toString()
            val trans = getTransBundle(amount, emvIntent)
            val ret = try {
                handler.emvTrans(trans, mfCheckCardListener.emvListener)
            } catch (e: Exception) {
                bwLog(e = e)
                -2
            }
            if (ret != 0) {
                bwLog(e = Exception("EMV-INIT-ERROR: $ret"))
                endEMV()
                try {
                    val props = HashMap<String, String>()
                    props["ret"] = "$ret"
                    props["ysdk_version"] = DeviceHelper.getServiceVersion()
                    Analytics.trackEvent("emv_init_error", props)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } else {
                bwLog(msg = "[$TAG] EMV-INIT-SUCCESS, listening card . . .")
            }
        } catch (e: Exception) {
            bwLog(e = e)
        } finally {
            awaitClose {
                bwLog(msg = "[$TAG] check card flow close")
            }
        }
    }

    fun importPin(pinBlock: String) {
        bwLog(msg = "[$TAG] importPin >> $pinBlock")
        DeviceHelper.getEmvHandler().onSetCardHolderInputPin(HexUtil.hexStringToByte(pinBlock))
    }

    fun getEmvTagData(tags: Array<String>): String {
        val tlv: String = EmvUtil.getTLVDatas(tags) ?: ""
        val tlvDataList = TlvDataList.fromBinary(tlv)
        return tlvDataList.toString()
    }

    fun importCardConfirmResult(isConfirm: Boolean) {
        bwLog(msg = "[$TAG] importCardConfirmResult $isConfirm")
        edcDeviceService.emv?.importCardConfirmResult(isConfirm)
    }

    fun updateAID(operation: Int, aidType: Int, aid: String?): Boolean {
        bwLog(msg = "[$TAG] updateAID = $aid")
        return edcDeviceService.emv?.updateAID(operation, aidType, aid)!!
    }

    fun updateRID(operation: Int, rid: String?): Boolean {
        bwLog(msg = "[$TAG] updateRID = $rid")
        return edcDeviceService.emv?.updateRID(operation, rid)!!
    }

    fun endEMV() {
        try {
            DeviceHelper.getEmvHandler().endPBOC()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    fun abortEmv() {
        bwLog(msg = "[$TAG] abort emv flow start")
        try {
            DeviceHelper.getEmvHandler().endPBOC()
            bwLog(msg = "[$TAG] abort emv flow complete")
        } catch (e: Exception) {
            bwLog(msg = "[$TAG] abort emv flow failed", e = e)
        }
    }

    fun stopCheckCard(): Boolean {
        bwLog(msg = "[$TAG] check card start")
        try {
            DeviceHelper.getIccCardReader(IccReaderSlot.ICSlOT1).stopSearch()
            DeviceHelper.getMagCardReader().stopSearch()
        } catch (e: Exception) {
            bwLog(e = e)
            return false
        }
        return true
    }

}