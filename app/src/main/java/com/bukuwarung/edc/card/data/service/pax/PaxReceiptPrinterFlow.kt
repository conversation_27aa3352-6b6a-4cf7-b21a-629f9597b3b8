package com.bukuwarung.edc.card.data.service.pax

import android.graphics.Bitmap
import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import com.bukuwarung.analytic.BtAnalytics
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.constant.PrintConst.TRANSACTION_TYPE_CHECK_BALANCE
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_MAG
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.printer.EDCPrint
import com.bukuwarung.edc.printer.util.PaxPrinterUtils
import com.bukuwarung.edc.printer.util.PrintUtil
import com.bukuwarung.edc.util.*
import com.pax.dal.exceptions.PrinterDevException
import com.pax.p_service.aidl.PrinterListener
import com.vfi.smartpos.deviceservice.aidl.PrinterConfig
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class PaxReceiptPrinterFlow @Inject constructor(
    private val edcDeviceService: PaxDeviceServiceImpl,
    private val printCommand: ReceiptPrintCommand,
    private val header: ByteArray?,
    private val bukuAgen: ByteArray?,
    private val headerBitmap: Bitmap?,
    private val bukuAgenBitmap: Bitmap?
) {

    val TAG = "EDC_PRINT"
    private val printer = edcDeviceService.device?.printer
    private val headerImage = Bundle().apply {
        putInt("offset", 60)
        putInt("width", 80)
        putInt("height", 10)
        putString(PrinterConfig.addTextInLine.GlobalFont.BundleName, "/system/fonts/DroidSansMono.ttf")
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
    }

    val fmtCenterAddText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
    }

    private val fmtAddText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.LEFT)
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
    }

    val fmtCenterLargeText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
    }

    val fmtCenterBoldText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
        putBoolean("isBold", true)
    }

    val format = Bundle().apply {
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
    }

    val footerFormat = Bundle().apply {
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
        putBoolean("newline", true)
        format.putBoolean("usePaxService",true)
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
    }

    val endingFooter = Bundle().apply {
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.SMALL_16_16)
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.LEFT)
    }

    fun printReceipt() = callbackFlow {
        bwLog("$TAG->Start print check balance receipt")
        try {
            if (printCommand.receipt != null) {
                updatePrinterWithCardTrxResponse()
            } else if (printCommand.orderResponse != null) {
                updatePrinterWithOrderResponse()
            }
            val printerResult = PrinterResult(true, ErrorStatus.ERROR_NONE)
            trySend(EdcResponse.Success(printerResult))
        } catch (ex: PrinterDevException) {
            val printerResult = PrinterResult(false, ErrorStatus.findByErrorCode(ex.errCode))
            trySend(EdcResponse.Success(printerResult))
            bwLog(ex)
        } catch (e: Exception) {
            val printerResult = PrinterResult(false, ErrorStatus.findByErrorCode(-1))
            trySend(EdcResponse.Success(printerResult))
            bwLog(e)
        } finally {
            awaitClose { bwLog("$TAG->${printCommand.printType} receipt print flow closed") }
        }
    }

    private fun buildFooter(): String {
        val textBuild = StringBuilder()
        appendCenterText(textBuild,"**${printCommand.copy?.message}**")
        textBuild.appendLine()
        appendCenterText(textBuild,"SIMPAN RESI INI SEBAGAI")
        appendCenterText(textBuild,"BUKTI TRANSAKSI YANG SAH")
        textBuild.appendLine("\n\n\n\n")
        return textBuild.toString()
    }

    private fun appendCenterText(textBuilder: StringBuilder, text: String) {
        val leftIndent = (32-text.length)/2
        repeat(leftIndent) { textBuilder.append(" ") }
        textBuilder.append(text.replace("\n"," ")).append("\n")
    }

    private fun updatePrinterWithCardTrxResponse() {
        headerBitmap?.let { PaxPrinterUtils.printBitmapOptimized(it) }
        val textBuilder = StringBuilder()
        val receipt = printCommand.receipt
        // Add business name and address if available
        val businessName = Utils.getBusinessNameForSelectedSerialNumber().ifBlank { Utils.getBusinessName() }
        if (businessName.isNotBlank()) appendCenterText(
            textBuilder,
            businessName
        )
        if (Utils.getBusinessAddress().isNotBlank()) appendCenterText(
            textBuilder,
            Utils.getBusinessAddress()
        )

        textBuilder.append("\n")
        if (printCommand.destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name) {
            appendCenterText(textBuilder, "---RE-PRINT---")
            textBuilder.append("\n")
        }

        val date = receipt?.transactionDate?.replace("/", "-")
        val readableTime =
            if (printCommand.destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name) {
                DateTimeUtils.getUTCTimeToLocalDateTime(
                    date.orEmpty(),
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            } else {
                DateTimeUtils.getFormattedLocalDateTime(
                    date.orEmpty(),
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            }
        textBuilder.append("Waktu      :$readableTime").append("\n")

        val terminalId = if (printCommand.terminalId.isNullOrBlank()) Utils.getTerminalId() else printCommand.terminalId
        textBuilder.append("Terminal ID:$terminalId").append("\n")
        textBuilder.append("Merchant ID:${Utils.getMerchantId()}").append("\n")
        textBuilder.append("Trace/RC   :${receipt?.systemTraceAuditNumber}/${receipt?.responseCode.orDefault("00")}").append("\n")
        textBuilder.append("Ref No     :${receipt?.rrn}").append("\n")
        textBuilder.append("--------------------------------").append("\n")
        // Add account type
        val accountTypeTranslation = when (printCommand.accountType) {
            PaymentConst.TYPE_SAVINGS -> "DEBIT TABUNGAN"
            PaymentConst.TYPE_CHECKING -> "GIRO"
            else -> ""
        }
        textBuilder.append("Jenis Kartu:$accountTypeTranslation").append("\n")
        val cardEntryMode = if (printCommand.cardEntryMode == CARD_ENTRY_MODE_MAG) {
            "Magnetic   :"
        } else {
            "Chip       :"
        }
        textBuilder.append("$cardEntryMode${printCommand.maskedPan}").append("\n")

        if (printCommand.printType == TRANSACTION_TYPE_CHECK_BALANCE) {
            textBuilder.append("\n")
            appendCenterText(textBuilder,"Saldo")
            PaxPrinterUtils.printMultiline(textBuilder.toString())
            PaxPrinterUtils.printMediumBigCenteredText(Utils.formatAmount(receipt?.balanceInformation?.balance?.toDouble()))
        } else {
            val sourceBankName = receipt?.sourceDetails?.bankName.orEmpty()
            val sourceName = Utils.maskSensitiveInfo(receipt?.sourceDetails?.name.orEmpty())
            val panNumber = Utils.maskSensitiveInfo(receipt?.sourceDetails?.accountNumber.orEmpty())
            textBuilder.append("Rek. Asal  :${formatBankAccount(sourceBankName, sourceName, panNumber)}").append("\n")
            if (!isCashWithdrawal(receipt)) {
                val destName = receipt?.destDetails?.name.orEmpty()
                val destBankName = receipt?.destDetails?.bankName.orEmpty()
                val destNumber = receipt?.destDetails?.accountNumber.orEmpty()
                textBuilder.append("Rek. Tujuan:${formatBankAccount(destName, destBankName, destNumber)}").append("\n")
            }
            if (printCommand.notes.isNotNullOrBlank()) {
                textBuilder.append("Berita     :${printCommand.notes}").append("\n")
            }

            val status = when (receipt?.endUserStatus) {
                EndUserStatusValues.SUCCESS -> {
                    "Transaksi Berhasil"
                }

                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    "Pencairan Diproses"
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    if(isCashWithdrawal(receipt)){
                        "Transaksi Pending"
                    } else {
                        "Transaksi Diproses"
                    }
                }

                EndUserStatusValues.PENDING -> {
                    "Transaksi Pending"
                }

                else -> {
                    ""
                }
            }
            textBuilder.append("\n")
            appendCenterText(textBuilder, status)
            PaxPrinterUtils.printMultiline(textBuilder.toString())
            PaxPrinterUtils.printMediumBigCenteredText("${Utils.formatAmount(receipt?.amount)}")
            val messageTextSb = StringBuilder()
            when (receipt?.endUserStatus) {
                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    appendCenterText(messageTextSb, "Pencairan uang diproses. Jangan khawatir, transaksi akan sukses.\n")
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    if(isCashWithdrawal(receipt)){
                        appendCenterText(messageTextSb, "Transaksi Pending. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda.\n")
                    } else {
                        appendCenterText(messageTextSb, "Transaksi diproses. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda.\n")
                    }
                }

                EndUserStatusValues.PENDING -> {
                    if (isCashWithdrawal(receipt)) {
                        appendCenterText(messageTextSb, "Transaksi Pending. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda.\n")
                    } else {
                        appendCenterText(messageTextSb, "Transaksi Pending. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda.\n")
                    }
                }
            }
            PaxPrinterUtils.printMultiline(messageTextSb.toString())
        }
        // Add accumulated text to the printer
        PaxPrinterUtils.printMultiline("--------------------------------")
        bukuAgenBitmap?.let{PaxPrinterUtils.printBitmapOptimized(it)}
        val footerText = buildFooter()
        PaxPrinterUtils.printMultiline(footerText)
        PaxPrinterUtils.commitPrintJob()
    }

    private fun updatePrinterWithOrderResponse() {
        val textBuilder = StringBuilder()
        val order = printCommand.orderResponse
        val item = order?.items?.firstOrNull()
        val sellingPrice = when {
            order?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0 -> order?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
            item?.sellingPrice.orNil > 0.0 -> item?.sellingPrice
            else -> order?.amount
        }
        val date = DateTimeUtils.getLocalStringFromUtc(
            order?.createdAt,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        // Add business name and phone number if available
        if (Utils.getBusinessName().isNotBlank()) appendCenterText(
            textBuilder,
            Utils.getBusinessName()
        )
        if (Utils.getPhoneNumber().isNotBlank()) appendCenterText(
            textBuilder,
            Utils.getPhoneNumber()
        )
        // Leave 2 empty lines
        textBuilder.append("\n\n")
        /** date printed*/
        textBuilder.append(keyValueString(key = "Tanggal", value = date.orDash)).append("\n")
        /** payment code printed*/
        textBuilder.append(keyValueString(key = "Kode\nPembayaran", value = order?.transactionId.orDash))
            .append("\n")
        /** customer name printed */
        textBuilder.append(
            keyValueString(
                key = "Pelanggan",
                value = item?.details?.customerName.orDash
            )
        ).append("\n")
        /** customer phone number printed */
        textBuilder.append(keyValueString(key = "", value = item?.details?.customerNumber.orDash))
            .append("\n")
        addDottedLine(textBuilder)
        when (item?.beneficiary?.category.orEmpty()) {
            PpobConst.CATEGORY_PULSA -> {
                /** phone number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor HP",
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                ).append("\n")
                /** product printed*/
                textBuilder.append(keyValueString(key = "Pulsa", value = item?.name.orDash))
                    .append("\n")
                /** serial number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Serial Number",
                        value = item?.details?.serialNumber.orDash
                    )
                ).append("\n")
            }

            PpobConst.CATEGORY_PAKET_DATA -> {
                /** phone number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor HP",
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                ).append("\n")
                /** product printed*/
                textBuilder.append(keyValueString(key = "Paket Data", value = item?.name.orDash))
                    .append("\n")
                /** serial number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Serial Number",
                        value = item?.details?.serialNumber.orDash
                    )
                ).append("\n")
            }

            PpobConst.CATEGORY_LISTRIK -> {
                if(item?.beneficiary?.code?.equals(PpobConst.CATEGORY_LISTRIK_POSTPAID).isTrue || item?.beneficiary?.code?.equals(PpobConst.CATEGORY_PLN_POSTPAID).isTrue){
                    /** customer id printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "ID Pelanggan",
                            value = item?.beneficiary?.accountNumber.orDash
                        )
                    ).append("\n")
                    /** periode printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "Periode",
                            value = item?.details?.periode.orDash
                        )
                    ).append("\n")
                    /** total billing printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "Total Lembar\nTagihan",
                            value = item?.details?.totalLembarTagihan.orDash
                        )
                    ).append("\n")
                    /** tarif printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "Tarif/Daya",
                            value = item?.details?.tarif.orDash
                        )
                    ).append("\n")
                    /** total tagihan printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "Total Tagihan",
                            value = Utils.formatAmount(order?.amount)
                        )
                    ).append("\n")
                } else {
                    /** product printed*/
                    textBuilder.append(keyValueString(key = "Token Listrik", value = item?.name.orDash))
                        .append("\n")
                    /** account number printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "ID Pelanggan",
                            value = item?.beneficiary?.accountNumber.orDash
                        )
                    ).append("\n")
                    /** customer name printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "Nama Pelanggan",
                            value = item?.details?.customerName.orDash
                        )
                    ).append("\n")
                    /** Total Kwh printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "Total kWh",
                            value = item?.details?.totalKwh.orDash
                        )
                    ).append("\n")
                    /** tarif printed*/
                    textBuilder.append(
                        keyValueString(
                            key = "Tarif/Daya",
                            value = item?.details?.tarif.orDash
                        )
                    ).append("\n")
                    /** dotted line printed*/
                    addDottedLine(textBuilder)
                    /** kode token printed*/
                    appendCenterText(textBuilder, "Kode Token")
                    /** total payment value printed*/
                    appendCenterText(textBuilder, item?.details?.token.orDash)
                }
            }
            PpobConst.CATEGORY_EWALLET -> {
                /** phone number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor HP",
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                ).append("\n")
                /** customer name printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nama Pelanggan",
                        value = item?.details?.customerName.orDash
                    )
                ).append("\n")
                /** provider printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Provider",
                        value = item?.details?.billerName.orDash
                    )
                ).append("\n")
                /** nominal topup printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nominal Top Up",
                        value = item?.name.orDash
                    )
                ).append("\n")
                /** serial number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Serial Number",
                        value = item?.details?.serialNumber.orDash
                    )
                ).append("\n")
            }
            PpobConst.CATEGORY_PULSA_POSTPAID -> {
                /** operator printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Operator",
                        value = item?.details?.productName.orDash
                    )
                ).append("\n")
                /** phone number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor HP",
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                ).append("\n")
                /** customer name printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nama Pelanggan",
                        value = item?.details?.customerName.orDash
                    )
                ).append("\n")
            }

            PpobConst.CATEGORY_BPJS -> {
                /** card number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor Kartu",
                        value = item?.details?.customerNumber.orDash
                    )
                ).append("\n")
                /** customer name printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nama Pelanggan",
                        value = item?.details?.customerName.orDash
                    )
                ).append("\n")
                /** no of family printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Jumlah\nKeluarga",
                        value = item?.details?.memberCount.orDash
                    )
                ).append("\n")
                /** periode printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Periode",
                        value = item?.details?.period.orDash
                    )
                ).append("\n")
            }

            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                /** customer name printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nama Pelanggan",
                        value = item?.details?.customerName.orDash
                    )
                ).append("\n")
                /** provider printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Provider",
                        value = order?.metadata?.billerName.orDash
                    )
                ).append("\n")
                /** periode printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Periode",
                        value = item?.details?.period.orDash
                    )
                ).append("\n")
                /** total tagihan printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Total Tagihan",
                        value = Utils.formatAmount(order?.amount)
                    )
                ).append("\n")
            }

            PpobConst.CATEGORY_PDAM -> {
                /** pdam printed*/
                textBuilder.append(
                    keyValueString(
                        key = "PDAM",
                        value = order?.metadata?.billerName.orDash
                    )
                ).append("\n")
                /** phone number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor HP",
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                ).append("\n")
                /** customer name printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nama Pelanggan",
                        value = item?.details?.customerName.orDash
                    )
                ).append("\n")
                /** periode printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Periode",
                        value = item?.details?.period.orDash
                    )
                ).append("\n")
                /** total tagihan printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Total Tagihan",
                        value = Utils.formatAmount(order?.amount)
                    )
                ).append("\n")
            }

            PpobConst.CATEGORY_MULTIFINANCE -> {
                /** contact number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor Kontak",
                        value = item?.details?.customerNumber.orDash
                    )
                ).append("\n")
                /** customer name printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nama Pelanggan",
                        value = item?.details?.customerName.orDash
                    )
                ).append("\n")
                /** product printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Produk\nAngsuran",
                        value = order?.metadata?.billerName.orDash
                    )
                ).append("\n")
                /** periode printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Periode",
                        value = item?.details?.period.orDash
                    )
                ).append("\n")
                /** fine printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Denda",
                        value = item?.details?.fine.orDash
                    )
                ).append("\n")
            }

            PpobConst.CATEGORY_VEHICLE_TAX -> {
                /** customer name printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nama Pelanggan",
                        value = item?.details?.customerName.orDash
                    )
                ).append("\n")
                /** phone number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor HP",
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                ).append("\n")
                /** policy number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor Polisi",
                        value = item?.details?.policyNumber.orDash
                    )
                ).append("\n")
                /** vehicle brand printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Merek\nKendaraan",
                        value = item?.details?.vehicleBrand.orDash
                    )
                ).append("\n")
                /** periode printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Periode",
                        value = item?.details?.period.orDash
                    )
                ).append("\n")
                /** transportation type printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Jenis\nKendaraan",
                        value = item?.details?.vehicleName.orDash
                    )
                ).append("\n")
                /** vehicle type printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Tipe Kendaraan",
                        value = item?.details?.vehicleType.orDash
                    )
                ).append("\n")
                /** vehicle color printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Warna\nKendaraan",
                        value = item?.details?.vehicleColor.orDash
                    )
                ).append("\n")
                /** vehicle build year printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Tahun Buat\nKendaraan",
                        value = item?.details?.buildYear.orDash
                    )
                ).append("\n")
                /** machine number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor Mesin",
                        value = item?.details?.machineNumber.orDash
                    )
                ).append("\n")
                /** frame number printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Nomor Rangka/\nNIK/VIN",
                        value = item?.details?.frameNumber.orDash
                    )
                ).append("\n")
                /** pkb printed*/
                textBuilder.append(keyValueString(key = "PKB", value = item?.details?.pkb.orDash))
                    .append("\n")
                /** total tagihan printed*/
                textBuilder.append(
                    keyValueString(
                        key = "Total Tagihan",
                        value = Utils.formatAmount(item?.amount)
                    )
                ).append("\n")
            }
        }
        addDottedLine(textBuilder)
        appendCenterText(textBuilder, "Total Pembayaran")
        appendCenterText(textBuilder, Utils.formatAmount(sellingPrice))
        addDottedLine(textBuilder)
        appendCenterText(textBuilder, "Dibuat dengan MiniATM BukuAgen")
        appendCenterText(textBuilder, "Bagian dari BukuWarung")
        textBuilder.appendLine("\n\n\n\n")
        PaxPrinterUtils.printMultiline(textBuilder.toString())
        PaxPrinterUtils.commitPrintJob()
    }

    private fun keyValueString(key: String, value: String): String {
        var result = ""
        if (key.length <= 14) {
            result += key
            repeat(14 - key.length) { result += " " }
        } else {
            val firstHalfString = key.split("\n").first()
            if (firstHalfString.length <= 14) {
                result += firstHalfString
                repeat(14 - firstHalfString.length) { result += " " }
            }
        }
        if (value.length > 18) {
            result += value.substring(0, 18)
            result += "\n"
            result += keyValueString(
                key = if (key.length > 14) key.split("\n")[1] else "",
                value = value.substring(18)
            )
        } else {
            result += value
            if (key.length > 14) {
                result += "\n"
                result += key.split("\n")[1]
            }
        }
        return result
    }

    private fun addDottedLine(textBuilder: StringBuilder) {
        textBuilder.append("--------------------------------").append("\n")
    }

    private fun formatBankAccount(string1: String, string2: String, string3: String): String {
        val join = "$string1 - $string2 - $string3"
        return join.chunked(19).joinToString(" ".repeat(13))
    }

    private fun isCashWithdrawal(receipt: CardReceiptResponse? = null):Boolean{
        return receipt?.transactionType == "CASH_WITHDRAWAL" || receipt?.transactionType == "CASH_WITHDRAWAL_POSTING"
    }
}
