package com.bukuwarung.edc.card.data.service.pax

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.util.Log
import com.bukuwarung.edc.global.Constant.DEVICE_SERVICE_VERIFONE
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.put
import com.pax.p_service.aidl.IDeviceService
import javax.inject.Inject

class PaxServiceConnection @Inject constructor(private val edcService: PaxDeviceServiceImpl) {

    companion object {
        private const val TAG = "PAX_SERVICE_CONN"
        const val PACKAGE_NAME = "com.bukuwarung.pax.pservice.pie"
        private const val ACTION_NAME = "com.pax.p_service"
    }

    fun connect(): Boolean {
        if (edcService?.device == null){
            Log.d(TAG, "start device service")
            val serviceConnection: ServiceConnection = object : ServiceConnection {
                override fun onServiceConnected(componentName: ComponentName, iBinder: IBinder) {
                    Log.d(
                        TAG,
                        "Device Service connection connected"
                    )
                    edcService!!.device = IDeviceService.Stub.asInterface(iBinder)
                    edcService!!.pinpad = edcService!!.device?.getPinpad(0)
                    edcService!!.emv = edcService!!.device?.emv
                }

                override fun onServiceDisconnected(componentName: ComponentName) {
                    Log.d(TAG, "Device Service connection disconnected")
                    edcService!!.device = null
                    val intent = Intent().setAction(ACTION_NAME).setPackage(PACKAGE_NAME)
                    edcService!!.context.bindService(
                        intent,
                        this, Context.BIND_AUTO_CREATE
                    )
                }
            }
            val intent = Intent().setAction(ACTION_NAME).setPackage(PACKAGE_NAME)
            val res = edcService!!.context.bindService(
                intent,
                serviceConnection,
                Context.BIND_AUTO_CREATE
            )
            if (res) {
                Utils.sharedPreferences.put("DEVICE_SERVICE",DEVICE_SERVICE_VERIFONE)
                Log.d(TAG, "Service connection exist")
                return true
            } else {
                Log.d(TAG, "Service connection doesn't exist")
                return false
            }
        }
        return true
    }

}