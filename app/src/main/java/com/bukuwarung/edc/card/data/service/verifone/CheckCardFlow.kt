package com.bukuwarung.edc.card.data.service.verifone

import android.os.Bundle
import android.util.Log
import com.bukuwarung.edc.card.domain.model.CheckCardResult
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_MAG
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_NONE
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.CARD_INSERT_SOUND
import com.vfi.smartpos.deviceservice.aidl.CheckCardListener
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class CheckCardFlow @Inject constructor(private val edcDeviceService: VFDeviceServiceImpl) {

    val TAG = "EDC_SEARCH_CARD"

    fun startCheckCard(timeOut: Int) = callbackFlow {
        Log.d(TAG, "start check card")
        val listener: CheckCardListener = object : CheckCardListener.Stub() {

            override fun onCardSwiped(info: Bundle) {
                Log.d(TAG, "onCardSwiped: card swiped")
                info?.putInt("CARD_ENTRY_MODE",Constants.CARD_ENTRY_MODE_MAG)
                val result = """ 
                    PAN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String)}
                    TRACK2:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String)}
                    CARD_SN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_CARD_SN_String)}
                    SERVICE_CODE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String)}
                    EXPIRED_DATE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String)}
                    CARD_ENTRY_MODE:${info.getInt("CARD_ENTRY_MODE")}
                    """.prependIndent()
                Log.d(TAG, "onCardSwiped: $result")
                var checkCardResult = CheckCardResult(CARD_ENTRY_MODE_MAG,info,Constants.CARD_CHECK_STATUS_SUCCESS)
                val cardResponse = EdcResponse.Success(checkCardResult)
                trySend(cardResponse)
            }

            override fun onCardPowerUp() {
                Log.i(TAG, "onCardPowerUp Insert")
                var checkCardResult = CheckCardResult(CARD_ENTRY_MODE_IC,null,Constants.CARD_CHECK_STATUS_SUCCESS)
                val cardResponse = EdcResponse.Success(checkCardResult)
                EdcResponse.CardAvailable(true)
                if (Utils.sharedPreferences.getBoolean(CARD_INSERT_SOUND, false)) {
                    edcDeviceService?.device?.beeper?.startBeep(100)
                }
                trySend(cardResponse)
            }


            override fun onCardActivate() {
                Log.i(TAG, "onCardActivate RF")
            }


            override fun onTimeout() {
                Log.i(TAG, "onTimeout")
                var checkCardResult = CheckCardResult(CARD_ENTRY_MODE_NONE,null,Constants.CARD_CHECK_STATUS_TIMEOUT)
                val cardResponse = EdcResponse.Success(checkCardResult)
                trySend(cardResponse)
            }


            override fun onError(error: Int, message: String) {
                Log.i("check_card_error", "$error : $message")
                var checkCardResult = CheckCardResult(CARD_ENTRY_MODE_NONE,null,Constants.CARD_CHECK_STATUS_ERROR)
                val cardResponse = EdcResponse.Success(checkCardResult)
                trySend(cardResponse)
            }
        }
        val bundle = Bundle()
        bundle.putBoolean("supportMagCard", Utils.isMagCardEnabled())
        bundle.putBoolean("supportICCard", true)
        try {
            edcDeviceService?.emv?.checkCard(bundle, timeOut, listener)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "check card flow close")
            }
        }
    }


    fun checkCardAvailability(timeOut: Long) = callbackFlow {
        Log.d(TAG, "start check card")
        if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)){
            trySend(EdcResponse.CardAvailable(false))
        }
        val listener: CheckCardListener = object : CheckCardListener.Stub() {

            override fun onCardSwiped(data: Bundle) {
                Log.d(TAG, "onCardSwiped: card swiped")
                edcDeviceService?.emv?.stopCheckCard()
                trySend(EdcResponse.CardAvailable(false))
            }

            override fun onCardPowerUp() {
                Log.i(TAG, "onCardPowerUp Insert")
                edcDeviceService?.emv?.stopCheckCard()
                trySend(EdcResponse.CardAvailable(true))
            }


            override fun onCardActivate() {
                Log.i(TAG, "onCardActivate RF")
                edcDeviceService?.emv?.stopCheckCard()
                trySend(EdcResponse.CardAvailable(false))
            }


            override fun onTimeout() {
                Log.i(TAG, "onTimeout")
                edcDeviceService?.emv?.stopCheckCard()
                trySend(EdcResponse.CardAvailable(false))

            }


            override fun onError(error: Int, message: String) {
                Log.i("check_card_error", "$error : $message")
                edcDeviceService?.emv?.stopCheckCard()
                trySend(EdcResponse.CardAvailable(false))
            }
        }
        val bundle = Bundle()
        bundle.putBoolean("supportMagCard", Utils.isMagCardEnabled())
        bundle.putBoolean("supportICCard", true)
        try {
            edcDeviceService?.emv?.checkCard(bundle, 1, listener)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "check card availability flow close")
            }
        }
    }

    fun stopCheckCard(): Boolean {
        Log.d(TAG, "check card start")
        try {
            edcDeviceService?.emv?.stopCheckCard()
        } catch (e: Exception) {
            e.printStackTrace()
            return false;
        }
        return true;
    }

}