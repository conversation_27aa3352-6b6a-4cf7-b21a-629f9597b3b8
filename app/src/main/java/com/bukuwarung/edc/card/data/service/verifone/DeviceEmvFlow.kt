package com.bukuwarung.edc.card.data.service.verifone

import android.os.Bundle
import android.util.Log
import com.bukuwarung.edc.card.domain.model.CardReaderResult
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.OnlineTransactionResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_UNSUPPORTED
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_VALID
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_CONFIRM_CARD_INFO
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_CONFIRM_CERT_INFO
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_AMOUNT
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_INPUT_PIN
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_ONLINE
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_SELECT_APPLICATION
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_TRANSACTION_RESULT
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_NOAPP
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_UNSUPPORTED_ECCARD
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.vfi.smartpos.deviceservice.aidl.EMVHandler
import com.vfi.smartpos.deviceservice.aidl.OnlineResultHandler
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstOnlineResultHandler
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class DeviceEmvFlow @Inject constructor(private val edcDeviceService: VFDeviceServiceImpl) {

    val TAG = "EDC_EMV"

    fun inputOnlineResult(responseCode:String, field55IccData:String ) = callbackFlow {
        val onlineResult = Bundle()

        onlineResult.putBoolean(
            ConstIPBOC.inputOnlineResult.onlineResult.KEY_isOnline_boolean,
            true
        )
        onlineResult.putString(
            ConstIPBOC.inputOnlineResult.onlineResult.KEY_respCode_String,
            responseCode
        )
        onlineResult.putString(
            ConstIPBOC.inputOnlineResult.onlineResult.KEY_field55_String,
            if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC)) "XXXX" else field55IccData
        )

        try {
            edcDeviceService.emv?.importOnlineResult(
                onlineResult,
                object : OnlineResultHandler.Stub() {
                    override fun onProccessResult(result: Int, data: Bundle?) {
                        Log.d(TAG, "inputOnlineResult -> onProccessResult: $result $data")
                        val str = """
                            RESULT:$result
                            TC_DATA:${
                            data!!.getString(
                                ConstOnlineResultHandler.onProccessResult.data.KEY_TC_DATA_String,
                                "not defined"
                            )
                        }
                            SCRIPT_DATA:${
                            data.getString(
                                ConstOnlineResultHandler.onProccessResult.data.KEY_SCRIPT_DATA_String,
                                "not defined"
                            )
                        }
                            REVERSAL_DATA:${
                            data.getString(
                                ConstOnlineResultHandler.onProccessResult.data.KEY_REVERSAL_DATA_String,
                                "not defined"
                            )
                        }
                            """.trimIndent()
                        Log.d(TAG, "inputOnlineResult -> onProccessResult: $result $str")
                        var resultCode = result
                        if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_REVERSAL)){
                            resultCode = ConstOnlineResultHandler.onProccessResult.result.Online_AAC
                        }else if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE)){
                            //intentionally break the flow
                            return
                        }else if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API)){
                            //mock api icc data is always invalid, to continue transaction process device verification has to be hardcodeed to success
                            resultCode = ConstOnlineResultHandler.onProccessResult.result.TC
                        }
                        if (resultCode != ConstOnlineResultHandler.onProccessResult.result.TC) {
                            data.putString(
                                Constants.KEY_VALIDATION_ERROR_CODE,
                                Constants.DEVICE_TRANSACTION_VALIDATION_ERROR
                            )
                        }
                        val onlineDataValidationResult = EdcResponse.Success(
                            OnlineTransactionResult(
                                resultCode,
                                data,
                                if (result == ConstOnlineResultHandler.onProccessResult.result.TC) "online result TC(success)" else "online result refused"
                            )
                        )
                        trySend(onlineDataValidationResult)
                    }
                })
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "inputOnlineResult flow close")
            }
        }
    }


    fun startInsertCardRead(emvIntent: Bundle, timeout: Int) = callbackFlow {

        Log.d(TAG, "start emv flow")
        val handler: EMVHandler = object : EMVHandler.Stub() {
            override fun onRequestAmount() {
                Log.d(TAG, "onRequestAmount")
                val cardData = CardReaderResult(operation = EMV_CALLBACK_REQUEST_AMOUNT)
                trySend(EdcResponse.Success(cardData))
            }

            override fun onSelectApplication(appList: List<Bundle>) {
                Log.d(TAG, "onSelectApplication")
                val cardData = CardReaderResult(operation = EMV_CALLBACK_SELECT_APPLICATION)
                trySend(EdcResponse.Success(cardData))
            }

            override fun onConfirmCardInfo(info: Bundle) {
                val result = """ 
                    PAN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String)}
                    TRACK2:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String)}
                    CARD_SN:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_CARD_SN_String)}
                    SERVICE_CODE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String)}
                    EXPIRED_DATE:${info.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String)}
                    """.prependIndent()
                Log.d(TAG, "onConfirmCardInfo $result")
                val cardData = CardReaderResult(
                    CARD_ENTRY_MODE_IC,
                    info,
                    CARD_STATUS_VALID,
                    operation = EMV_CALLBACK_CONFIRM_CARD_INFO
                )
                val cardResponse = EdcResponse.Success(cardData)
                trySend(cardResponse)
            }

            override fun onRequestInputPIN(isOnlinePin: Boolean, retryTimes: Int) {
                Log.d(TAG, "onRequestInputPIN")
                val cardData =
                    CardReaderResult(inputPin = true, operation = EMV_CALLBACK_REQUEST_INPUT_PIN)
                trySend(EdcResponse.Success(cardData))
            }

            override fun onConfirmCertInfo(certType: String, certInfo: String) {
                val cardData = CardReaderResult(operation = EMV_CALLBACK_CONFIRM_CERT_INFO)
                Log.d(TAG, "onConfirmCertInfo")
            }

            override fun onRequestOnlineProcess(aaResult: Bundle) {
                Log.d(TAG, "onRequestOnlineProcess")
                val cardData = CardReaderResult(operation = EMV_CALLBACK_REQUEST_ONLINE)
                trySend(EdcResponse.Success(cardData))
            }

            override fun onTransactionResult(result: Int, data: Bundle) {
                Log.d(TAG, "onTransactionResult result: $result data: $data")
                val msg = data.getString("ERROR")
                val tc = data.getString("TC_DATA")
                Log.d(TAG, "onTransactionResult: message=$msg tc=$tc")
                //From EmvHandler.java
                val cardData = if(result == EMV_NOAPP || result == EMV_UNSUPPORTED_ECCARD){
                    CardReaderResult(cardStatus = CARD_STATUS_UNSUPPORTED, operation = EMV_CALLBACK_TRANSACTION_RESULT)
                }else{
                    CardReaderResult( operation = EMV_CALLBACK_TRANSACTION_RESULT)
                }

                val cardResponse = EdcResponse.Success(cardData)
                trySend(cardResponse)
            }
        }

        try {
            edcDeviceService.emv?.apply {
                startEMV(ConstIPBOC.startEMV.processType.full_process, emvIntent, handler)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "flow close")
            }
        }
    }

    fun importPin(pinBlock: String) {
        Log.d(TAG, "importPin $pinBlock")
        edcDeviceService.emv?.importPin(1, pinBlock.toByteArray())
    }

    fun getEmvTagData(tags: Array<String>): String {
        Log.d(TAG, "tag list >> $tags")
        var data = edcDeviceService.emv?.getAppTLVList(tags)
        Log.d(TAG, "tag data << $data")
        return data.toString()
    }

    fun importCardConfirmResult(isConfirm: Boolean) {
        Log.d(TAG, "importCardConfirmResult $isConfirm")
        edcDeviceService.emv?.importCardConfirmResult(isConfirm)
    }

    fun updateAID(operation: Int, aidType: Int, aid: String?): Boolean {
        Log.d(TAG, "updateAID = $aid")
        return edcDeviceService.emv?.updateAID(operation, aidType, aid)!!
    }

    fun updateRID(operation: Int, rid: String?): Boolean {
        Log.d(TAG, "updateRID = $rid")
        return edcDeviceService.emv?.updateRID(operation, rid)!!
    }

    fun abortEmv() {
        Log.d(TAG, "abort emv flow start")
        try {
            edcDeviceService.emv?.stopCheckCard()
            edcDeviceService.emv?.abortEMV()
            Log.d(TAG, "abort emv flow complete")
        } catch (e: Exception) {
            Log.d(TAG, "abort emv flow failed")
        }
    }


}