package com.bukuwarung.edc.card.data.service.verifone

import android.os.Build
import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.bukuwarung.edc.printer.util.PrintUtil
import com.vfi.smartpos.deviceservice.aidl.PrinterConfig
import com.vfi.smartpos.deviceservice.aidl.PrinterListener
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class PrinterFlow @Inject constructor(private val edcDeviceService: VFDeviceServiceImpl) {

    val TAG = "EDC_PRINT"
    fun startPrint() = callbackFlow {
        Log.d(TAG, "start check card")
        val printerListener = object : PrinterListener.Stub() {
            @Throws(RemoteException::class)
            override fun onFinish() {
                Log.d(TAG, "on finish SetPrintComplete")
                val  printerResult = PrinterResult(false,ErrorStatus.ERROR_NONE)
                trySend(EdcResponse.Success(printerResult))
            }

            @Throws(RemoteException::class)
            override fun onError(error: Int) {
                Log.e("PrinterListener", "Printer error ")
                //error implementation is confusing, implement error result later
                val  printerResult = PrinterResult(false,ErrorStatus.findByErrorCode(error))
                trySend(EdcResponse.Success(printerResult))
            }
        }
        try {
            edcDeviceService?.device?.printer?.startPrint(printerListener)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "check card flow close")
            }
        }
    }

    fun printTest() = callbackFlow {
        try {
            val printer = edcDeviceService.device?.printer
            val headerFormat = Bundle()
            headerFormat.putInt(PrinterConfig.addText.FontSize.BundleName, 0)
            headerFormat.putString("fontStyle", "/system/fonts/DroidSansMono.ttf");
            val fmtAddTextInLine = Bundle()
            fmtAddTextInLine.putInt(
                PrinterConfig.addText.Alignment.BundleName,
                PrinterConfig.addText.Alignment.LEFT
            )
            fmtAddTextInLine.putInt(
                PrinterConfig.addText.FontSize.BundleName,
                PrinterConfig.addText.FontSize.NORMAL_24_24
            )
            printer?.addText(headerFormat, PrintUtil.printUtil.getDottedLine(30))
            printer?.addText(headerFormat, Build.MODEL +" "+ Build.MANUFACTURER)
            printer?.addText(headerFormat, "Bukuwarung EDC App Test Print")
            printer?.addText(headerFormat, PrintUtil.printUtil.getDottedLine(30))
            startPrint().collect { response ->
                trySend(response)
            }
        }catch (e:Exception){
            e.printStackTrace()
        }finally {
            awaitClose {
                Log.d(TAG, "print test slip flow close")
            }
        }
    }

}