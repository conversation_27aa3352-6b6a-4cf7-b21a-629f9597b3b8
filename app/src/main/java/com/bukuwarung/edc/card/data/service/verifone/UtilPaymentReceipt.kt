package com.bukuwarung.edc.card.data.service.verifone

import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.vfi.smartpos.deviceservice.aidl.PrinterConfig
import com.vfi.smartpos.deviceservice.aidl.PrinterListener
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class UtilPaymentReceipt @Inject constructor(private val edcDeviceService: VFDeviceServiceImpl, private val printReceipt: String) {

    fun printReceipt() = callbackFlow {
        val printer = edcDeviceService.printer

        val headerFormat = Bundle()
//        val printer = edcDeviceService.device?.printer
        headerFormat.putInt(PrinterConfig.addText.FontSize.BundleName, 0)
        headerFormat.putString(PrinterConfig.addTextInLine.GlobalFont.BundleName, "/system/fonts/DroidSansMono.ttf");
        val fmtAddTextInLine = Bundle()
        fmtAddTextInLine.putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.LEFT)

        val printableString = printReceipt.split("\n")
        var printableText = ""

        for (i in printableString) {
            printableText = i
            when(i) {
                "left" -> {
                    fmtAddTextInLine.putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.LEFT)
                }
                "left_spacing_30" -> {
                    fmtAddTextInLine.putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.LEFT)
                }

                "normal" -> {
                    fmtAddTextInLine.putInt(
                        PrinterConfig.addText.FontSize.BundleName,
                        PrinterConfig.addText.FontSize.NORMAL_24_24
                    )
                }

                "center" -> {
                    fmtAddTextInLine.putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
                }

                "small" -> {
                    fmtAddTextInLine.putInt(
                        PrinterConfig.addText.FontSize.BundleName,
                        PrinterConfig.addText.FontSize.SMALL_16_16
                    )
                }

                "bold" -> {
                    fmtAddTextInLine.putInt(
                        PrinterConfig.addText.FontSize.BundleName,
                        PrinterConfig.addText.FontSize.NORMAL_DH_24_48_IN_BOLD
                    )
                    fmtAddTextInLine.putString("fontStyle", "/system/fonts/DroidSans-Bold.ttf")
                }

                "super" -> {
                    fmtAddTextInLine.putInt(
                        PrinterConfig.addText.FontSize.BundleName,
                        PrinterConfig.addText.FontSize.NORMAL_DH_24_48_IN_BOLD
                    )
                    fmtAddTextInLine.putString("fontStyle", "/system/fonts/DroidSans-Bold.ttf")
                }

                "feedline" -> {
                    printer?.feedLine(1)
                }

                "feedline2" -> {
                    printer?.feedLine(2)
                }

                "dotted_line" -> {
                    printer?.addText(fmtAddTextInLine, getDottedLine(32))
                }

                else ->
                    printer?.addText(fmtAddTextInLine, printableText)
            }
        }

        val printerListener = object : PrinterListener.Stub() {
            @Throws(RemoteException::class)
            override fun onFinish() {
                val  printerResult = PrinterResult(true, ErrorStatus.ERROR_NONE)
                trySend(EdcResponse.Success(printerResult))
            }

            @Throws(RemoteException::class)
            override fun onError(error: Int) {
                Log.e("PrinterListener", "Printer error ")
                //error implementation is confusing, implement error result later
                val  printerResult = PrinterResult(false, ErrorStatus.findByErrorCode(error))
                trySend(EdcResponse.Success(printerResult))
            }
        }
        try {
            edcDeviceService?.printer?.startPrint(printerListener)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {

            }
        }
    }

    private fun getDottedLine(n: Int): String {
        var s = ""
        repeat(n) {
            s += "-"
        }

        return s
    }

}