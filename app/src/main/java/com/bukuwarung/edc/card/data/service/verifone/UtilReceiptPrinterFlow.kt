package com.bukuwarung.edc.card.data.service.verifone

import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.constant.PrintConst.TRANSACTION_TYPE_CHECK_BALANCE
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_MAG
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.*
import com.vfi.smartpos.deviceservice.aidl.PrinterConfig
import com.vfi.smartpos.deviceservice.aidl.PrinterListener
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

/*
temporary class to handle miniatm receipt
needs to be moved to generic printer
 */
class UtilReceiptPrinterFlow @Inject constructor(
    private val edcDeviceService: VFDeviceServiceImpl,
    private val printCommand: ReceiptPrintCommand,
    private val header: ByteArray?,
    private val bukuAgen: ByteArray?
) {
    val TAG = "EDC_PRINT"
    val printer = edcDeviceService.printer

    private val headerImage = Bundle().apply {
        putInt("offset", 60)
        putInt("width", 80)
        putInt("height", 10)
        putString(PrinterConfig.addTextInLine.GlobalFont.BundleName, "/system/fonts/DroidSansMono.ttf")
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
    }
    private val headerFormat = Bundle().apply {
        putInt(PrinterConfig.addText.FontSize.BundleName, 0)
        putString(PrinterConfig.addTextInLine.GlobalFont.BundleName, "/system/fonts/DroidSansMono.ttf")
    }

    private val fmtAddText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.LEFT)
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
    }

    private val fmtCenterAddText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
    }

    private val fmtCenterLargeText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
        putString(PrinterConfig.addTextInLine.GlobalFont.BundleName, "/system/fonts/DroidSansMono.ttf")
    }
    val format = Bundle().apply {
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
    }
    val footerFormat = Bundle().apply {
        putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_24_24)
        putBoolean("newline", true)
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
    }

    //non generic code, to be fixed later
    fun printReceipt() = callbackFlow {
        Log.d(TAG, "start print check balance receipt")

        if (printCommand.receipt != null) updatePrinterWithCardTrxResponse()
        if (printCommand.orderResponse != null) updatePrinterWithOrderResponse()
        printer?.feedLine(5)

        val printerListener = object : PrinterListener.Stub() {
            @Throws(RemoteException::class)
            override fun onFinish() {
                Log.d(TAG, "onFinish print complete receipt ${printCommand.printType} copy ${printCommand.copy?.name}")
                val printerResult = PrinterResult(true, ErrorStatus.ERROR_NONE)
                trySend(EdcResponse.Success(printerResult))
            }

            @Throws(RemoteException::class)
            override fun onError(error: Int) {
                Log.e("PrinterListener", "Printer error ")
                val printerResult = PrinterResult(false, ErrorStatus.findByErrorCode(error))
                trySend(EdcResponse.Success(printerResult))
            }
        }
        try {
            printer?.startPrint(printerListener)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "${printCommand.printType} receipt print flow closed")
            }
        }
    }

    private fun updatePrinterWithOrderResponse(){
        val order = printCommand.orderResponse
        val item = order?.items?.firstOrNull()
        val sellingPrice = when {
            order?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0 -> order?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
            item?.sellingPrice.orNil > 0.0 -> item?.sellingPrice
            else -> order?.amount
        }
        val date = DateTimeUtils.getLocalStringFromUtc(
            order?.createdAt,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        // Add business name and phone number if available
        if (Utils.getBusinessName().isNotBlank()) printer?.addText(fmtCenterAddText, Utils.getBusinessName())
        if (Utils.getPhoneNumber().isNotBlank()) printer?.addText(fmtCenterAddText, Utils.getPhoneNumber())
        // Leave 2 empty lines
        printer?.feedLine(2)
        /** date printed*/
        printer?.addText(fmtAddText, keyValueString(key = "Tanggal", value = date.orDash))
        /** payment code printed*/
        printer?.addText(fmtAddText, keyValueString(key = "kode\nPembayaran", value = order?.transactionId.orDash))
        /** customer name printed */
        printer?.addText(fmtAddText, keyValueString(key = "Pelanggan", value = item?.details?.customerName.orDash))
        /** customer phone number printed */
        printer?.addText(fmtAddText, keyValueString(key = "", value = item?.details?.customerNumber.orDash))
        addDottedLine()
        when (item?.beneficiary?.category.orEmpty()) {
            PpobConst.CATEGORY_PULSA -> {
                /** phone number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor HP", value = item?.beneficiary?.phoneNumber.orDash))
                /** product printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Pulsa", value = item?.name.orDash))
                /** serial number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Serial Number", value = item?.details?.serialNumber.orDash))
            }
            PpobConst.CATEGORY_PAKET_DATA -> {
                /** phone number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor HP", value = item?.beneficiary?.phoneNumber.orDash))
                /** product printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Paket Data", value = item?.name.orDash))
                /** serial number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Serial Number", value = item?.details?.serialNumber.orDash))
            }
            PpobConst.CATEGORY_LISTRIK -> {
                if(item?.beneficiary?.code?.equals(PpobConst.CATEGORY_LISTRIK_POSTPAID).isTrue || item?.beneficiary?.code?.equals(PpobConst.CATEGORY_PLN_POSTPAID).isTrue){
                    /** customer id printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "ID Pelanggan", value = item?.beneficiary?.accountNumber.orDash))
                    /** periode printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Periode", value = item?.details?.periode.orDash))
                    /** total billing printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Total Lembar\nTagihan", value = item?.details?.totalLembarTagihan.orDash))
                    /** tarif printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Tarif/Daya", value = item?.details?.tarif.orDash))
                    /** total tagihan printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Total Tagihan", value = Utils.formatAmount(order?.amount)))
                } else {
                    /** product printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Token Listrik", value = item?.name.orDash))
                    /** account number printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "ID Pelanggan", value = item?.beneficiary?.accountNumber.orDash))
                    /** customer name printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
                    /** Total Kwh printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Total kWh", value = item?.details?.totalKwh.orDash))
                    /** tarif printed*/
                    printer?.addText(fmtAddText, keyValueString(key = "Tarif/Daya", value = item?.details?.tarif.orDash))
                    /** dotted line printed*/
                    addDottedLine()
                    /** kode token printed*/
                    printer?.addText(fmtCenterAddText, "Kode Token")
                    /** total payment value printed*/
                    printer?.addText(fmtCenterAddText, item?.details?.token.orDash)
                }
            }
            PpobConst.CATEGORY_EWALLET -> {
                /** phone number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor HP", value = item?.beneficiary?.phoneNumber.orDash))
                /** customer name printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
                /** provider printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Provider", value = item?.details?.billerName.orDash))
                /** nominal topup printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nominal Top Up", value = item?.name.orDash))
                /** serial number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Serial Number", value = item?.details?.serialNumber.orDash))
            }
            PpobConst.CATEGORY_PULSA_POSTPAID -> {
                /** operator printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Operator", value = item?.details?.productName.orDash))
                /** phone number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor HP", value = item?.beneficiary?.phoneNumber.orDash))
                /** customer name printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
            }
            PpobConst.CATEGORY_BPJS -> {
                /** card number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor Kartu", value = item?.details?.customerNumber.orDash))
                /** customer name printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
                /** no of family printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Jumlah\nKeluarga", value = item?.details?.memberCount.orDash))
                /** periode printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Periode", value = item?.details?.period.orDash))
            }
            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                /** customer name printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
                /** provider printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Provider", value = order?.metadata?.billerName.orDash))
                /** periode printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Periode", value = item?.details?.period.orDash))
                /** total tagihan printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Total Tagihan", value = Utils.formatAmount(order?.amount)))
            }
            PpobConst.CATEGORY_PDAM -> {
                /** pdam printed*/
                printer?.addText(fmtAddText, keyValueString(key = "PDAM", value = order?.metadata?.billerName.orDash))
                /** phone number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor HP", value = item?.beneficiary?.phoneNumber.orDash))
                /** customer name printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
                /** periode printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Periode", value = item?.details?.period.orDash))
                /** total tagihan printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Total Tagihan", value = Utils.formatAmount(order?.amount)))
            }
            PpobConst.CATEGORY_MULTIFINANCE -> {
                /** contact number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor Kontak", value = item?.details?.customerNumber.orDash))
                /** customer name printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
                /** product printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Produk\nAngsuran", value = order?.metadata?.billerName.orDash))
                /** periode printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Periode", value = item?.details?.period.orDash))
                /** fine printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Denda", value = item?.details?.fine.orDash))
            }
            PpobConst.CATEGORY_VEHICLE_TAX -> {
                /** customer name printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nama Pelanggan", value = item?.details?.customerName.orDash))
                /** phone number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor HP", value = item?.beneficiary?.phoneNumber.orDash))
                /** policy number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor Polisi", value = item?.details?.policyNumber.orDash))
                /** vehicle brand printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Merek\nKendaraan", value = item?.details?.vehicleBrand.orDash))
                /** periode printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Periode", value = item?.details?.period.orDash))
                /** transportation type printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Jenis\nKendaraan", value = item?.details?.vehicleName.orDash))
                /** vehicle type printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Tipe Kendaraan", value = item?.details?.vehicleType.orDash))
                /** vehicle color printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Warna\nKendaraan", value = item?.details?.vehicleColor.orDash))
                /** vehicle build year printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Tahun Buat\nKendaraan", value = item?.details?.buildYear.orDash))
                /** machine number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor Mesin", value = item?.details?.machineNumber.orDash))
                /** frame number printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Nomor Rangka/\nNIK/VIN", value = item?.details?.frameNumber.orDash))
                /** pkb printed*/
                printer?.addText(fmtAddText, keyValueString(key = "PKB", value = item?.details?.pkb.orDash))
                /** total tagihan printed*/
                printer?.addText(fmtAddText, keyValueString(key = "Total Tagihan", value = Utils.formatAmount(item?.amount)))
            }
        }
        addDottedLine()
        printer?.addText(fmtCenterAddText, "Total Pembayaran")
        printer?.addText(fmtCenterAddText, Utils.formatAmount(sellingPrice))
        addDottedLine()
        printer?.addText(fmtCenterAddText, "Dibuat dengan MiniATM BukuAgen")
        printer?.addText(fmtCenterAddText, "Bagian dari BukuWarung")
    }

    private fun updatePrinterWithCardTrxResponse(){
        val receipt = printCommand.receipt
        printer?.addImage(headerImage, header)
        // Add business name and address if available
        val businessName =
            Utils.getBusinessNameForSelectedSerialNumber().ifBlank { Utils.getBusinessName() }

        if (businessName.isNotBlank()) printer?.addText(fmtCenterAddText, businessName)
        if (Utils.getBusinessAddress().isNotBlank()) printer?.addText(
            fmtCenterAddText,
            Utils.getBusinessAddress()
        )
        // Leave 2 empty lines
        printer?.feedLine(1)
        if (printCommand.destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name) {
            printer?.addText(fmtCenterAddText,"---RE-PRINT---")
        }
        printer?.feedLine(1)

        val date = receipt?.transactionDate?.replace("/", "-")
        val readableDate =
            if (printCommand.destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name) {
                DateTimeUtils.getUTCTimeToLocalDateTime(
                    date.orEmpty(),
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            } else {
                DateTimeUtils.getFormattedLocalDateTime(
                    date.orEmpty(),
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            }
        val terminalId = if (printCommand.terminalId.isNullOrBlank()) Utils.getTerminalId() else printCommand.terminalId
        val accountTypeTranslation = when (printCommand.accountType) {
            PaymentConst.TYPE_SAVINGS -> "DEBIT TABUNGAN"
            PaymentConst.TYPE_CHECKING -> "GIRO"
            else -> ""
        }
        val cardEntryMode = if (printCommand.cardEntryMode == CARD_ENTRY_MODE_MAG) {
            "Magnetic   "
        } else {
            "Chip       "
        }

        printer?.addText(fmtAddText, "Waktu      : $readableDate")
        printer?.addText(fmtAddText, "Terminal ID: $terminalId")
        printer?.addText(fmtAddText, "Merchant ID: ${Utils.getMerchantId()}")
        printer?.addText(fmtAddText, "Trace/RC   : ${receipt?.systemTraceAuditNumber}/${receipt?.responseCode.orDefault("00")}")
        printer?.addText(fmtAddText, "Ref No.    : ${receipt?.rrn}")
        printer?.addText(fmtAddText, "--------------------------------")
        printer?.addText(fmtAddText, "Jenis Kartu: $accountTypeTranslation")
        printer?.addText(fmtAddText, "$cardEntryMode: ${printCommand.maskedPan}")
        //headerFormat.putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.RIGHT)
        if (printCommand.printType == TRANSACTION_TYPE_CHECK_BALANCE) {
            printer?.feedLine(2)
            printer?.addText(fmtCenterLargeText, "Saldo ")
            format.putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_DH_24_48_IN_BOLD)
            format.putString("fontStyle", "/system/fonts/DroidSans-Bold.ttf")
            printer?.addText(format, Utils.formatAmount(receipt?.balanceInformation?.balance?.toDouble()))
            printer?.feedLine(2)
        } else {
            val sourceAccount = formatBankAccount(receipt?.sourceDetails?.bankName.orEmpty(),
                Utils.maskSensitiveInfo(receipt?.sourceDetails?.name.orEmpty()),
                Utils.maskSensitiveInfo(receipt?.sourceDetails?.accountNumber.orEmpty()))

            printer?.addText(fmtAddText, "Rek. Asal  : $sourceAccount")
            if (!isCashWithdrawal(receipt)) {
                val destinationAccount = formatBankAccount(
                    receipt?.destDetails?.name.orEmpty(),
                    receipt?.destDetails?.bankName.orEmpty(),
                    receipt?.destDetails?.accountNumber.orEmpty()
                )
                printer?.addText(fmtAddText, "Rek. Tujuan: $destinationAccount")
            }

            printer?.addText(fmtAddText, "Berita     : ${printCommand.notes.orDash}")

            val status = when (receipt?.endUserStatus) {
                EndUserStatusValues.SUCCESS -> {
                    "Transaksi Berhasil"
                }

                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    "Pencairan Diproses"
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    if (isCashWithdrawal(receipt)) {
                        "Transaksi Pending"
                    } else {
                        "Transaksi Diproses"
                    }
                }

                EndUserStatusValues.PENDING -> {
                    "Transaksi Pending"
                }

                else -> {
                    ""
                }
            }
            printer?.feedLine(2)
            printer?.addText(fmtCenterLargeText, status)
            format.putInt(PrinterConfig.addText.FontSize.BundleName, PrinterConfig.addText.FontSize.NORMAL_DH_24_48_IN_BOLD)
            format.putString("fontStyle", "/system/fonts/DroidSans-Bold.ttf")
            printer?.addText(format, Utils.formatAmount(receipt?.amount))
            when (receipt?.endUserStatus) {
                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    printer?.addText(fmtCenterLargeText,"Pencairan uang diproses.\nJangan khawatir, transaksi\nakan sukses.")
                    printer?.feedLine(1)
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    if (isCashWithdrawal(receipt)) {
                        printer?.addText(fmtCenterLargeText,"Transaksi Pending. Cek\nberkala ke Agen untuk\nmengetahui status akhir\ntransaksi Anda.")
                    } else {
                        printer?.addText(fmtCenterLargeText,"Transaksi diproses. Cek\nberkala ke Agen untuk\nmengetahui status akhir\ntransaksi Anda.")
                    }
                    printer?.feedLine(1)
                }

                EndUserStatusValues.PENDING -> {
                    if (isCashWithdrawal(receipt)) {
                        printer?.addText(fmtCenterLargeText,"Transaksi Pending. Cek\nberkala ke Agen untuk\nmengetahui status akhir\ntransaksi Anda.")
                    } else {
                        printer?.addText(fmtCenterLargeText,"Transaksi Pending. Cek\nberkala ke Agen untuk\nmengetahui status akhir\ntransaksi Anda.")
                    }
                    printer?.feedLine(1)
                }
            }
        }
        printer?.addText(fmtAddText, "--------------------------------")
        val footerImage = Bundle().apply {
            putInt("offset", 130)
            putInt("width", 80)
            putInt("height", 10)
            putString(PrinterConfig.addTextInLine.GlobalFont.BundleName, "/system/fonts/DroidSansMono.ttf")
            putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
        }
        printer?.addImage(footerImage, bukuAgen)
        printer?.addText(fmtCenterAddText, "SIMPAN RESI INI SEBAGAI")
        printer?.addText(fmtCenterAddText, "BUKTI TRANSAKSI YANG SAH")
        printer?.addText(fmtCenterAddText, "**${printCommand.copy?.message}**")
    }

    private fun keyValueString(key: String, value: String): String {
        var result = ""
        if (key.length <= 14) {
            result += key
            repeat(14 - key.length) { result += " " }
        } else {
            val firstHalfString = key.split("\n").first()
            if (firstHalfString.length <= 14) {
                result += firstHalfString
                repeat(14 - firstHalfString.length) { result += " " }
            }
        }
        if (value.length > 18) {
            result += value.substring(0, 18)
            result += "\n"
            result += keyValueString(
                key = if (key.length > 14) key.split("\n")[1] else "",
                value = value.substring(18)
            )
        } else {
            result += value
            if (key.length > 14) {
                result += "\n"
                result += key.split("\n")[1]
            }
        }
        return result
    }

    private fun addDottedLine(){
        printer?.addText(fmtAddText, "--------------------------------")
    }

    private fun formatBankAccount(string1: String, string2: String, string3: String): String {
        val join = "$string1 - $string2 - $string3"
        return join.chunked(19).joinToString(" ".repeat(13))
    }

    private fun isCashWithdrawal(receipt: CardReceiptResponse? = null):Boolean{
        return receipt?.transactionType == "CASH_WITHDRAWAL" || receipt?.transactionType == "CASH_WITHDRAWAL_POSTING"
    }
}
