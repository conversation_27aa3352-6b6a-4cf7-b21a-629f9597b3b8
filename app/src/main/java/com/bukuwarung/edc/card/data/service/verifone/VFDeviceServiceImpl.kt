package com.bukuwarung.edc.card.data.service.verifone

import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.ErrorData
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.service.*
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.vfi.smartpos.deviceservice.aidl.IDeviceService
import com.vfi.smartpos.deviceservice.aidl.IEMV
import com.vfi.smartpos.deviceservice.aidl.IPinpad
import com.vfi.smartpos.deviceservice.aidl.IPrinter
import com.vfi.smartpos.deviceservice.aidl.PinpadKeyType
import com.vfi.smartpos.deviceservice.constdefine.ConstIPinpad.startPinInput.param.Value_desType_3DES
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VFDeviceServiceImpl @Inject constructor(val context: Context): IEdcDeviceService {

    var device: IDeviceService? = null
    var pinpad: IPinpad? = null
    var emv: IEMV? = null
    var printer: IPrinter? = null

    companion object {
        const val TERMINAL_MASTER_KEY_ID = 97
        const val TERMINAL_WORK_KEY_ID = 1
    }

    override fun connectService():Boolean {
        return VFServiceConnection(this).connect();
    }

    override fun startCheckCard(timeOut: Int) = CheckCardFlow(this).startCheckCard(timeOut)

    override fun stopCheckCard() = CheckCardFlow(this).stopCheckCard()

    override fun abortEmv() = DeviceEmvFlow(this).abortEmv()

    override fun updateAID(operation: Int, aidType: Int, aid: String?): Boolean = DeviceEmvFlow(this).updateAID(operation, aidType, aid)

    override fun updateRID(operation: Int, rid: String?): Boolean = DeviceEmvFlow(this).updateRID(operation, rid)

    override fun startInsertCardRead(emvIntent: Bundle, timeout: Int) = DeviceEmvFlow(this).startInsertCardRead(emvIntent,timeout) as Flow<EmvResponse>

    override fun startPrint() = PrinterFlow(this).startPrint() as Flow<PrinterResponse>

    override fun printTestSlip() = PrinterFlow(this).printTest() as Flow<PrinterResponse>

    override fun printReceipt(
        printCommand: ReceiptPrintCommand,
        header: ByteArray?,
        bukuAgen: ByteArray?,
        headerBitmap: Bitmap?,
        bukuAgenBitmap: Bitmap?
    ) = UtilReceiptPrinterFlow(
        this,
        printCommand,
        header,
        bukuAgen
    ).printReceipt() as Flow<PrinterResponse>

    override fun getEmvTagData(tags: Array<String>): String = DeviceEmvFlow(this).getEmvTagData(tags)
    override fun checkCardAvailability(timeOut: Long): Flow<EdcResponse.CardAvailable> = CheckCardFlow(this).checkCardAvailability(timeOut)
    override fun stopPinpadPinInput() {
        pinpad?.endPinInputCustomView()
    }

    override fun startPinpadPinInput() {
        pinpad?.startPinInputCustomView()
    }

    override fun initPinpad(cardNumber: String, pinpadButtonCoordinates: List<PinpadButtonCoordinates>) = VFPinpadFlow(this).initPinpad(cardNumber,pinpadButtonCoordinates);
    override fun doBeep(durationMills: Int) {
        device?.beeper?.startBeep(durationMills)
    }

    override fun inputOnlineResult(responseCode:String, field55IccData:String): Flow<TransactionValidationResult> = DeviceEmvFlow(this).inputOnlineResult(responseCode, field55IccData)
    /*
    masterKey -> TMK
    workingKey -> TWK/TPK
     */

    override fun loadKeys(masterKey: String, workKey: String): LoadKeyResponse {
        var isMasterKeyLoaded: Boolean? = false
        var isWorkingKeyLoaded: Boolean? = false
        try {
            isMasterKeyLoaded = pinpad?.loadMainKey(TERMINAL_MASTER_KEY_ID, Utils.hexStr2Byte(masterKey), null)
            Log.d("PIN_BLOCK", "loaded master_key: $isMasterKeyLoaded")
        } catch (e: Exception) {
            e.printStackTrace()
            return EdcResponse.Failure(ErrorData(-1, "Load master key failed"))
        }
        try {
            isWorkingKeyLoaded = pinpad?.loadWorkKey(
                PinpadKeyType.PINKEY,
                TERMINAL_MASTER_KEY_ID,
                TERMINAL_WORK_KEY_ID,
                Utils.hexStr2Byte(workKey),
                null
            )
            Log.d("PIN_BLOCK", "loaded $isWorkingKeyLoaded workingKey:"+workKey)
        } catch (e: Exception) {
            e.printStackTrace()
            return EdcResponse.Failure(ErrorData(-1, "Load work key failed"))
        }
        return if (isWorkingKeyLoaded.isTrue && isMasterKeyLoaded.isTrue) {
            EdcResponse.Success(true)
        } else {
            EdcResponse.Failure(ErrorData(-1, "UNKNOWN"))
        }
    }

    override fun getPinBlock(panNumber: String, pin: String): PinBlockResponse {
        if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)){
            return EdcResponse.Success(Utils.getDummyPinblock())
        }
        val ret: ByteArray? = pinpad?.encryptPinFormat0(TERMINAL_WORK_KEY_ID, Value_desType_3DES, panNumber.toByteArray(), pin)
        val result: String? = Utils.byte2HexStr(ret)
        Log.d("PIN_BLOCK", "pan: $panNumber pin: $pin pinBlock: $result")
        return EdcResponse.Success(result);
    }

    override fun importCardConfirmResult(isConfirm:Boolean) = DeviceEmvFlow(this).importCardConfirmResult(isConfirm)

    override fun importPin(pinBlock: String) = DeviceEmvFlow(this).importPin(pinBlock)

    override fun printPaymentReceipt(paymentReceipt: String) = UtilPaymentReceipt(this, paymentReceipt).printReceipt() as Flow<PrinterResponse>

    override fun loadMasterKey(masterKey: String): Boolean {
        try {
            val isMasterKeyLoaded = pinpad?.loadMainKey(TERMINAL_MASTER_KEY_ID, Utils.hexStr2Byte(masterKey), null) == true
            Log.d("PIN_BLOCK", "loaded master_key: $isMasterKeyLoaded")
            return isMasterKeyLoaded
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }
}