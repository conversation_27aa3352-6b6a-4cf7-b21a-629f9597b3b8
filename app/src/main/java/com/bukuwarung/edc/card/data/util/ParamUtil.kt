package com.bukuwarung.edc.card.data.util

import android.util.Log
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isNotNullOrBlank

object ParamUtil {
    fun processTerminalParameters(terminalParameterList:List<TerminalParameterData>){
        val eventProp = hashMapOf<String, String>()
        terminalParameterList.forEach { terminalParameter ->
            if (terminalParameter.packageName == BuildConfig.APPLICATION_ID && terminalParameter.value.isNotNullOrBlank()) {
                Log.d(VeristoreBroadcastListener.TAG, "processing terminal param[${terminalParameter.key}], value[${terminalParameter.value}]")
                when (terminalParameter.key) {
                    ParamKeys.TERMINAL_ID -> {
                        Utils.setTmsTerminalId(terminalParameter.value.toString())
                        eventProp["tid"] = terminalParameter.value.toString()
                    }
                    ParamKeys.TERMINAL_MASTER_KEY -> {
                        Utils.setTerminalMasterKey(terminalParameter.value.toString())
                        eventProp["tmk_len"] = terminalParameter.value.toString().length.toString()
                    }
                    ParamKeys.MERCHANT_PHONE_NUMBER -> {
                        Utils.setTerminalMerchantPhone(terminalParameter.value.toString())
                        eventProp["tms_phone"] = terminalParameter.value.toString().length.toString()
                    }
                    ParamKeys.MERCHANT_ID -> {
                        Utils.setMerchantId(terminalParameter.value.toString())
                        eventProp["mid"] = terminalParameter.value.toString()
                    }
                    ParamKeys.MERCHANT_ADDRESS -> Utils.setBusinessAddress(terminalParameter.value.toString())
                    ParamKeys.SETTING_SUPPORTED_MAG_CARD_PREFIX -> {
                        terminalParameter.value?.let { it1 ->
                            Utils.setMagEnabledPrefix(
                                it1
                            )
                        }
                        eventProp["mag_card_prefix"] = terminalParameter.value.toString()
                    }
                    ParamKeys.SETTING_IS_MAG_CARD_ENABLED -> {
                        Utils.isMagCardEnabled(terminalParameter.value.toBoolean())
                        eventProp["is_mag_card_enabled"] = terminalParameter.value.toString()
                    }
                    ParamKeys.SETTING_AID -> {
                        terminalParameter.value?.let { it1 -> Utils.setApplicationId(it1) }
                        eventProp["aid"] = terminalParameter.value.toString()
                    }
                }
            }
        }
        Analytics.trackEvent("update_terminal_parameter", eventProp)
    }
}