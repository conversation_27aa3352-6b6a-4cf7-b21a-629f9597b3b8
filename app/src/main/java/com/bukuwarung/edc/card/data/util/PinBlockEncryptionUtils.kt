import javax.crypto.Cipher
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.DESedeKeySpec

object PinBlockEncryptionUtils {

    // Encrypt or decrypt data using 3DES
    private fun tripleDesEncryptDecrypt(mode: Int, data: ByteArray, key: ByteArray): ByteArray {
        val keyBytes = adjustKeyLength(key)
        val desedeKeySpec = DESedeKeySpec(keyBytes)
        val keyFactory = SecretKeyFactory.getInstance("DESede")
        val secretKey = keyFactory.generateSecret(desedeKeySpec)
        val cipher = Cipher.getInstance("DESede/ECB/NoPadding") // NoPadding for working with 8-byte blocks

        cipher.init(mode, secretKey)
        return cipher.doFinal(data)
    }

    // Adjust the key length to 24 bytes for 3DES
    private fun adjustKeyLength(key: ByteArray): ByteArray {
        return when (key.size) {
            24 -> key // Already 24 bytes
            16 -> key + key.copyOfRange(0, 8) // 16 bytes: Extend to 24 bytes by repeating the first 8 bytes
            else -> throw IllegalArgumentException("Invalid key size: ${key.size}. Key must be 16 or 24 bytes.")
        }
    }

    // Convert hex string to byte array
    private fun hexStringToByteArray(hexString: String): ByteArray {
        val len = hexString.length
        return ByteArray(len / 2) { i ->
            ((Character.digit(hexString[i * 2], 16) shl 4) +
                    Character.digit(hexString[i * 2 + 1], 16)).toByte()
        }
    }

    // Convert byte array to hex string
    private fun byteArrayToHexString(byteArray: ByteArray): String {
        return byteArray.joinToString("") { "%02X".format(it) }
    }

    // Format the PIN into a PIN block using ISO-0 format
    private fun formatPinBlock(pin: String, pan: String): ByteArray {
        // Create the PIN block
        val pinBlock = ByteArray(8)

        // Format the PIN part
        val pinLength = pin.length
        val pinBlockString = StringBuilder()
        pinBlockString.append(pinLength.toString().padStart(2, '0'))
        pinBlockString.append(pin.padEnd(14, 'F'))

        // Convert the PAN to the PAN block (rightmost 12 digits, excluding the check digit)
        val panBlockString = "0000" + pan.substring(pan.length - 13, pan.length - 1)

        // XOR the PIN block with the PAN block
        for (i in pinBlock.indices) {
            pinBlock[i] = (pinBlockString.substring(i * 2, i * 2 + 2).toInt(16) xor
                    panBlockString.substring(i * 2, i * 2 + 2).toInt(16)).toByte()
        }

        return pinBlock
    }

    // Generate the encrypted PIN block
    fun encryptPinFormat0(pin: String, pan: String, masterKeyHex: String, pinKeyWorkKeyHex: String): String {
        val masterKeyBytes = hexStringToByteArray(masterKeyHex)
        val encryptedWorkingKeyBytes = hexStringToByteArray(pinKeyWorkKeyHex)

        // Decrypt the working key using the master key
        val workingKeyBytes = tripleDesEncryptDecrypt(Cipher.DECRYPT_MODE, encryptedWorkingKeyBytes, masterKeyBytes)

        // Format the PIN block
        val pinBlock = formatPinBlock(pin, pan)
        print("pinBlock: $pinBlock")
        // Encrypt the PIN block with the decrypted working key
        val encryptedPinBlock = tripleDesEncryptDecrypt(Cipher.ENCRYPT_MODE, pinBlock, workingKeyBytes)
        print("encryptedPinBlock: $encryptedPinBlock")
        // Convert to hex string and return
        return byteArrayToHexString(encryptedPinBlock)
    }
}


/*

workingKey:15525E5B65FA73F5C3440AE3B1BCDD8F
masterKey:E0374937A1854AEAF89D29B6C86DBC54BCB951FD31F24C45

test1
cardNumber: 5049481207839253
pin: 123456
expected pinBlock:070E499849E9C733

test2:
cardNumber: ****************
pin: 123456
expected pinBlock:894778E5923857FB

test3:
cardNumber: ****************
pin: 123456
expected pinBlock:67B78B83262F7B90

 */
fun convertMFtoPax(mfTrack2: String): String {
    // Step 1: Remove all spaces and unnecessary slashes
    val cleanedTrack = mfTrack2.replace(" ", "").replace("/", "")

    // Step 2: Find the separator '^' and split the string appropriately
    val firstCaretIndex = cleanedTrack.indexOf('^')
    val secondCaretIndex = cleanedTrack.indexOf('^', firstCaretIndex + 1)

    // Step 3: Extract card number and the rest of the data
    val cardNumber = cleanedTrack.substring(1, firstCaretIndex) // Start from 1 to skip 'B'
    val otherData = cleanedTrack.substring(secondCaretIndex + 1) // Data after the second '^'

    // Step 4: Construct the Pax Track2 format
    val paxTrack2 = "$cardNumber=$otherData"

    // Step 5: Pad the second part if necessary (assume total length of data part should be 19)
    val components = paxTrack2.split('=')
    if (components.size == 2) {
//        val paddedData = components[1].padEnd(19, '0')
//        return components[0] + "=" + paddedData
        return components[0]
    }

    // Return the original if the format does not split correctly
    return mfTrack2
}



fun main() {
//    val cardNumber = "5049481207839253"  // expected pinBlock:070E499849E9C733
//    val cardNumber = "****************"  // expected pinBlock:894778E5923857FB
    val cardNumber = "5049481207839253"  // expected pinBlock:67B78B83262F7B90
    val pin = "111111"  // Example PIN
    val masterKey = "E0374937A1854AEAF89D29B6C86DBC54BCB951FD31F24C45" // Provided master key in hex (24 bytes)
    val pinKeyWorkKey = "15525E5B65FA73F5C3440AE3B1BCDD8F" // Provided working key in hex (16 bytes)

    // Generate the encrypted PIN block
    val encryptedPinBlock = PinBlockEncryptionUtils.encryptPinFormat0(pin, cardNumber, masterKey, pinKeyWorkKey)
    val mfTrack2 = "B****************^                        / ^281022062506802590             "
    val paxTrack2 = convertMFtoPax(mfTrack2)
    println(paxTrack2)
    println("Encrypted PIN Block: $paxTrack2 $encryptedPinBlock")
}