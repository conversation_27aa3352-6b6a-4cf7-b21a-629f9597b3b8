package com.bukuwarung.edc.card.data.util;

import android.os.Build;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;

public class TripleDESUtil {

    private static SecretKey getKeyFromSerial(String serialNumber) throws Exception {
        byte[] keyBytes = serialNumber.getBytes("UTF-8");
        MessageDigest md = MessageDigest.getInstance("SHA-1");
        keyBytes = md.digest(keyBytes);
        keyBytes = Arrays.copyOf(keyBytes, 24); // Use only the first 168 bits (21 bytes)
        return new SecretKeySpec(keyBytes, "DESede");
    }

    public static String encrypt(String strToEncrypt, String serialNumber) {
        try {
            SecretKey secretKey = getKeyFromSerial(serialNumber);
            Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                return Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes("UTF-8")));
            } else {
                return android.util.Base64.encodeToString(cipher.doFinal(strToEncrypt.getBytes("UTF-8")), 0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String decrypt(String strToDecrypt, String serialNumber) {
        try {
            SecretKey secretKey = getKeyFromSerial(serialNumber);
            Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)), "UTF-8");
            }else {
                return new String(cipher.doFinal(android.util.Base64.decode(strToDecrypt, 0)));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        String serialNumber = "1234";
        String originalString = "<EMAIL>";

        String encryptedString = TripleDESUtil.encrypt(originalString, serialNumber);
        String decryptedString = TripleDESUtil.decrypt(encryptedString, serialNumber);

        System.out.println("Original String: " + originalString);
        System.out.println("Encrypted String: " + encryptedString);
        System.out.println("Decrypted String: " + decryptedString);
    }
}

