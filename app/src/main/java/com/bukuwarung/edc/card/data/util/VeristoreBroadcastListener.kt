package com.bukuwarung.edc.card.data.util

import android.os.Bundle
import android.util.Log
import cn.verifone.VeristoreMsgTool.client.manager.bean.VeristoreSendData
import cn.verifone.VeristoreMsgTool.client.manager.global.ResultCode
import cn.verifone.VeristoreMsgTool.client.manager.global.ResultType
import cn.verifone.VeristoreMsgTool.client.manager.receiver.VeristoreManager
import cn.verifone.VeristoreMsgTool.client.manager.receiver.VeristoreReceiverListener
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.card.data.util.ParamUtil.processTerminalParameters
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.google.errorprone.annotations.Keep
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File
import java.io.InputStream
import java.lang.reflect.Type
import javax.inject.Singleton

@Keep
data class TerminalParameterData(
    val id: Int,
    val key: String,
    val packageName: String,
    val value: String? = ""
)

object ParamKeys {
    const val TERMINAL_ID = "TP-Terminal-terminalId"
    const val TERMINAL_MASTER_KEY = "TP-Terminal-TMK"
    const val MERCHANT_ID = "TP-Merchant-merchantId"
    const val MERCHANT_PHONE_NUMBER = "TP-Merchant-phoneNumber"
    const val MERCHANT_ADDRESS = "TP-Merchant-merchantAddress"
    const val SETTING_IS_MAG_CARD_ENABLED = "TP-Settings-isMagCardEnabled"
    const val SETTING_SUPPORTED_MAG_CARD_PREFIX = "TP-Settings-magCardPrefix"
    const val SETTING_AID = "TP-Settings-applicationId"
}

@Singleton
internal class VeristoreBroadcastListener : VeristoreReceiverListener {

    companion object {
        const val TAG = "VERISTORE_LISTENER"
    }

    override fun receiveVeriStoreData(sendData: VeristoreSendData?) {
        Log.d(TAG, "receiveVeriStoreData")

        sendData?.let {
            Log.d(TAG, "resultType: ${it.resultType}")
            Log.d(TAG, "msgId: ${it.msgId}")
            Log.d(TAG, "path: ${it.path}")
            Log.d(TAG, "taskExecResult: ${it.taskExecResult}")
            Log.d(TAG, "mqttMessage: ${it.mqttMessage}")
            Log.d(TAG, "taskId: ${it.taskId}")

            when (it.resultType) {
                ResultType.RECEIVE_PARAM_RESULT -> {
                    val result = loadTerminalParameter(it.path)
                    sendBroadcastToVeristore(it, if (result) ResultCode.RECEIVE_SUCCESS else ResultCode.RECEIVE_UNSUCCESS)
                }
                ResultType.REQUEST_LOCATION -> {
                    sendBroadcastToVeristore(it, ResultCode.RECEIVE_SUCCESS)
                }
                else -> {
                    Log.d(TAG, "Unhandled result type: ${it.resultType}")
                }
            }
        } ?: Log.e(TAG, "sendData is null")
    }

    private fun sendBroadcastToVeristore(sendData: VeristoreSendData, resultCode: Int) {
        val bundle = Bundle().apply {
            putInt("resultType", sendData.resultType)
            putInt("resultCode", resultCode)
            putLong("msgId", sendData.msgId)
            putString("taskId", sendData.taskId)
            Utils.getTmsTerminalId()?.let {
                putString("deviceId", it)
            }
            if (resultCode == ResultCode.RECEIVE_UNSUCCESS) {
                putString("alertType", "Custom Alert")
                putString("alertMsg", "Failed loading terminal parameter")
            }
        }

        Log.d(TAG, "sendBroadcastToVeristore $bundle")
        VeristoreManager.sendBroadcastToVeristore(EdcApplication.instance, bundle)
    }

    private fun loadTerminalParameter(path: String?): Boolean {
        return try {
            Log.d(TAG, "start loading terminal parameter")

            path?.let {
                val inputStream: InputStream = File(it).inputStream()
                val paramFileLineList = inputStream.bufferedReader().useLines { lines -> lines.toList() }
                val type: Type = object : TypeToken<ArrayList<TerminalParameterData>>() {}.type

                paramFileLineList.forEach { line ->
                    val terminalParameterList: List<TerminalParameterData> = Gson().fromJson(line, type)
                    processTerminalParameters(terminalParameterList)
                }
                Log.d(TAG, "finish loading terminal parameter")
                true
            } ?: run {
                Log.e(TAG, "Path is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "failed loading terminal parameter ${e.message}", e)
            false
        }
    }

    override fun installAppBusyState() {
        Log.d(TAG, "installAppBusyState")
    }

    override fun removeAppBusyState() {
        VeristoreManager.isBusy(EdcApplication.instance, false)
        Log.d(TAG, "removeAppBusyState")
    }

    override fun receiveDeviceId(deviceId: String?) {
        Log.d(TAG, "receiveDeviceId $deviceId")
    }

    override fun receiveLocation(latitude: String?, longitude: String?) {
        Log.d(TAG, "receiveLocation $latitude $longitude")
    }

    override fun receiveDeviceAndMarketId(taskExecResult: Int) {
        Log.d(TAG, "receiveDeviceAndMarketId $taskExecResult")
    }

    override fun receive3rdAppParams(result: Int) {
        Log.d(TAG, "receive3rdAppParams $result")
    }

    override fun receiveReplacingTerminalResult(result1: Int, result2: Int) {
        Log.d(TAG, "receiveReplacingTerminalResult $result1 $result2")
    }
}
