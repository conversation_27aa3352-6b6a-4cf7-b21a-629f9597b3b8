package com.bukuwarung.edc.card.di

import com.bukuwarung.edc.card.data.datasource.TerminalApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object TmsApiModule {

    @Provides
    @Singleton
    fun provideTerminalApi(@Named("tms-retrofit") retrofit: Retrofit): TerminalApi {
        return retrofit.create(TerminalApi::class.java)
    }
}
