package com.bukuwarung.edc.card.domain.model

import android.os.Bundle
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_NONE
import com.google.errorprone.annotations.Keep

@Keep
data class CardReaderResult(
    var inputMode:Int = Constants.CARD_ENTRY_MODE_NONE,
    var data: Bundle? = null,
    var cardStatus:Int = CARD_STATUS_NONE,
    var inputPin:Boolean = false,
    var iccData: String? = null,
    var operation: Int = Constants.EMV_CALLBACK_NONE
)