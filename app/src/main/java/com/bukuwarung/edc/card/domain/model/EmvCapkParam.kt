package com.bukuwarung.edc.card.domain.model

import com.bukuwarung.edc.card.domain.model.base.EMVTLVParam

class EmvCapkParam : EMVTLVParam() {
    init {
        /**
         * default value of some tags
         * value null means the tag is optional
         */
        defaultTagValue = arrayOf(
            DefaultTagValue(0xDF06, "01"),
            DefaultTagValue(TAG_Algorithm_DF07, "01"),
            DefaultTagValue(TAG_Exponent_DF04, "03")
        )
    }

    override fun clean() {
        super.clean()
        if (defaultTagValue != null) {
            for (tagValue in defaultTagValue) {
                tagValue.available = true
            }
        }
    }

    companion object {
        /**
         * RID Tag list
         */
        var TAG_RID_9F06 = 0x9F06
        var TAG_Index_9F22 = 0x9F22
        var TAG_ExpiryDate_DF05 = 0xDF05
        var TAG_Algorithm_DF07 = 0xDF07
        var TAG_KEY_DF02 = 0xDF02
        var TAG_Exponent_DF04 = 0xDF04
        var TAG_Hash_DF03 = 0xDF03
    }
}