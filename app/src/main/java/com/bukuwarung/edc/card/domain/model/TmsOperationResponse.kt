package com.bukuwarung.edc.card.domain.model

import com.google.errorprone.annotations.Keep

sealed class TmsOperationResponse<out T> {

    @Keep
    data class Success<out T>(
        val data: T
    ): TmsOperationResponse<T>()

    @Keep
    data class Failure(val error: TmsError) : TmsOperationResponse<Nothing>()
}

@Keep
enum class TmsFailureType(val code: String, val message: String) {
    ACTIVATION_FAILURE("E21", "Unexpected error firebase realtime"),
    DEVICE_FAILED_LOADING_KEY("E23", "Unexpected error device failed loading key"),
    FIREBASE_FAILED_LOADING_KEY("E22", "Firebase failed loading master key"),
    TERMINAL_NOT_CONFIGURED("E24", "Terminal doesn't have activation data"),
    ACTIVATION_FAILED_BE("E20", "Activation failed at BE"),
    PAIRING_FAILURE("E25", "Device pairing failed"),
    HEARTBEAT_FAILURE("E18", "Device heartbeat update failed"),
    UNEXPECTED_ERROR("500", "Unexpected error"),
    UNKNOWN_FAILURE("999", "Unknown error"),
    UNAUTHORIZED("401", "Uauthorized error");

    companion object {
        private val map = values().associateBy(TmsFailureType::code)
        fun fromCode(code: String) = map[code] ?: UNKNOWN_FAILURE
    }
}

@Keep
data class TmsError(
    val type: TmsFailureType,
    val additionalMessage: String? = null
) {
    val code: String get() = type.code
    val message: String get() = additionalMessage ?: type.message
}

@Keep
data class TerminalSystemException(
    val result: Boolean,
    val errorCode: String,
    val messages: String
)