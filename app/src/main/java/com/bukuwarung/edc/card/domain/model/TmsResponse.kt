package com.bukuwarung.edc.card.domain.model

import com.google.errorprone.annotations.Keep

sealed class TmsResponse<out T> {
    @Keep
    data class Success<out T>(
        val data: T,
        val result: Boolean,
    ): TmsResponse<T>()

    @Keep
    data class Failure(
        val message: String,
        val errorCode: String,
        val result: <PERSON><PERSON>an
    ): TmsResponse<Nothing>()
}