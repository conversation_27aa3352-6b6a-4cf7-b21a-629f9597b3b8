package com.bukuwarung.edc.card.domain.service

import android.graphics.Bitmap
import android.os.Bundle
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.domain.model.*
import kotlinx.coroutines.flow.Flow

interface IEdcDeviceService {

    fun connectService():Boolean
    fun startCheckCard(timeOut: Int): Flow<CardReaderResponse>
    fun startInsertCardRead(emvIntent: Bundle, timeout: Int): Flow<EmvResponse>
    fun startPrint(): Flow<PrinterResponse>
    fun stopCheckCard(): Boolean
    fun printReceipt(
        printCommand: ReceiptPrintCommand,
        header: ByteArray?,
        bukuAgen: ByteArray?,
        headerBitmap: Bitmap?,
        bukuAgenBitmap: Bitmap?
    ): Flow<PrinterResponse>

    fun printPaymentReceipt(paymentReceipt: String): Flow<PrinterResponse>
    fun printTestSlip(): Flow<PrinterResponse>
    fun loadKeys(masterKey: String, workKey: String): LoadKeyResponse
    fun getPinBlock(panNumber: String, pin: String): EdcResponse<String?>
    fun importPin(pinBlock: String)
    fun inputOnlineResult(responseCode:String, field55IccData:String): Flow<TransactionValidationResult>
    fun updateAID(operation: Int, aidType: Int, aid: String?): Boolean
    fun updateRID(operation: Int, rid: String?): Boolean
    fun importCardConfirmResult(isConfirm: Boolean)
    fun abortEmv()
    fun getEmvTagData(tags: Array<String>): String
    fun checkCardAvailability(timeOut: Long): Flow<EdcResponse.CardAvailable>
    fun stopPinpadPinInput()
    fun startPinpadPinInput()
    fun initPinpad(cardNumber: String, pinKeyCoordinate: List<PinpadButtonCoordinates>): Flow<PinpadResult>
    fun doBeep(durationMills: Int)
    fun loadMasterKey(masterKey: String): Boolean
}

typealias CardReaderResponse = EdcResponse<CheckCardResult>
typealias EmvResponse = EdcResponse<CardReaderResult>
typealias TransactionValidationResult = EdcResponse<OnlineTransactionResult>
typealias PrinterResponse = EdcResponse<PrinterResult>
typealias LoadKeyResponse = EdcResponse<Boolean>
typealias PinBlockResponse = EdcResponse<String?>