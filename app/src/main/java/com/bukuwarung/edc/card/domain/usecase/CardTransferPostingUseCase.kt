package com.bukuwarung.edc.card.domain.usecase

import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.data.model.PinCardErrorResponse
import com.bukuwarung.edc.card.data.model.TransactionConfirmRequest
import com.bukuwarung.edc.card.data.model.TransactionReversalRequest
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.EdcTransactionResponse
import com.bukuwarung.edc.card.domain.model.FailureType
import com.bukuwarung.edc.card.domain.model.OnlineTransactionResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.data.repository.TransferMoneyRepository
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.card.ui.ErrorMapping.noInternetErrorCode
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.google.gson.Gson
import com.vfi.smartpos.deviceservice.constdefine.ConstOnlineResultHandler
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import org.json.JSONObject
import retrofit2.Response
import javax.inject.Inject

class CardTransferPostingUseCase @Inject constructor(
    private val transferMoneyRepository: TransferMoneyRepository,
    private val inputOnlineResultUseCase: EvmInputOnlineResultUseCase,
    private val edcTransactionUseCase: EdcTransactionUseCase
) {
    suspend operator fun invoke(
        accountId: String,
        transactionType: TransactionType,
        transferMoneyRequest: TransferMoneyRequestResponseBody
    ) = callbackFlow {
        try {
            val serialNumber =
                Utils.getHardwareSerialNumber().ifEmpty { Utils.getDeviceSerialNumber() }
            //send transfer posting request to backend
            val transferMoneyResponse =
                if (transactionType == TransactionType.CASH_WITHDRAWAL) transferMoneyRepository.cashWithdrawalPosting(
                    serialNumber,
                    accountId,
                    transferMoneyRequest
                ) else transferMoneyRepository.transferMoney(
                    serialNumber,
                    accountId,
                    transferMoneyRequest
                )
            //start validation of transaction response iccData - ARPC validation at device
            if (transferMoneyResponse.isSuccessful) {
                transferMoneyResponse.body().let { cardReceiptResponse ->
                    /*
                     * condition1: in case of pending transaction http response is 200 but AJ response code is 68 in response body
                     * device doesn't need to verify iccData for such cases.
                     * Condition2: when testing on android devices, success response can be send without iccData validation by device
                     * Condition3: for magnetic card, ARPC data validation is not required
                     */
                    if(cardReceiptResponse?.responseCode != "00"
                        || Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)
                        || transferMoneyRequest.posEntryMode == Constants.POS_ENTRY_MODE_MAG_CARD
                        || (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API) && !(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE) || Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC) || Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_REVERSAL)))){
                        //send acknowledgement to backend to avoid transaction reversal
                        edcTransactionUseCase.confirmTransferPostingUseCase(
                            Utils.getPaymentAccountId(),
                            transactionType,
                            TransactionConfirmRequest(cardReceiptResponse?.id)
                        )
                        trySend(EdcTransactionResponse.Success(cardReceiptResponse))
                        return@let
                    }
                    /*
                    if iccData is null, transaction reversal needs to trigger without online verification
                     */
                    if(cardReceiptResponse?.iccData == null){
                        submitTransactionReversal(
                            transferMoneyRequest,
                            transferMoneyRequest.iccData,
                            cardReceiptResponse!!
                        )
                        trySend(
                            EdcTransactionResponse.Failure(
                                type = FailureType.DEVICE_CONFIRMATION_FAILURE,
                                code = Constants.DEVICE_TRANSACTION_VALIDATION_ERROR,
                                message = "online result refused",
                                data = "",
                                stan = cardReceiptResponse.systemTraceAuditNumber.orEmpty()
                            )
                        )
                        return@let
                    }
                    /*
                     * Backend gets iccData (ARPC) from AJ after transaction is executed.
                     * Device has to validate this data to make sure card was not tempered
                     * EMV will take online transaction result as input and gives result in callback
                     */
                    cardReceiptResponse?.iccData?.let {
                        inputOnlineResultUseCase.invoke(
                            cardReceiptResponse.responseCode!!,
                            it
                        ).collect { it ->

                            val validationResult =
                                it as EdcResponse.Success<OnlineTransactionResult>

                            if (validationResult.data.result == ConstOnlineResultHandler.onProccessResult.result.TC) {
                                /*
                                 * after transaction is validated by device, backend is expecting confirmation of ARPC validation
                                 * send transaction confirmation request to backend if received iccData was valid
                                 */
                                edcTransactionUseCase.confirmTransferPostingUseCase(
                                    Utils.getPaymentAccountId(),
                                    transactionType,
                                    TransactionConfirmRequest(cardReceiptResponse.id)
                                )
                                trySend(EdcTransactionResponse.Success(cardReceiptResponse))
                            } else {
                                //if transaction device check failed, device gives REVERSAL_DATA string that needs to be sent in reversal API call
                                val reversalData =
                                    validationResult.data.resultData?.getString("REVERSAL_DATA")
                                /*
                                 * if device rejects card transaction due to invalid backend iccData
                                 * device needs to submit transaction reversal instead of transaction confirmation
                                 */
                                submitTransactionReversal(
                                    transferMoneyRequest,
                                    transferMoneyRequest.iccData,
                                    cardReceiptResponse!!
                                )
                                /*
                                 * set E01 as failure reason if device rejected iccData
                                 */
                                cardReceiptResponse.status = "PENDING"
                                cardReceiptResponse.endUserStatus = EndUserStatusValues.PENDING_REFRESH
                                cardReceiptResponse.responseCode = "E01"
                                trySend(EdcTransactionResponse.Success(cardReceiptResponse))
                            }
                        }
                    }
                }
            } else {
                val httpErrorCode = transferMoneyResponse.code()
                /*
                 * if backend API failed to execute transaction, appropriate error code, message is expected from backend
                 * it has to be provided in Failure object
                 */
                if (httpErrorCode == Constant.UNPROCESSABLE_ENTITY) {
                    val errorBody = JSONObject(transferMoneyResponse.errorBody()?.string())
                    val apiError = Gson().fromJson(
                        errorBody.getString("error"),
                        PinCardErrorResponse::class.java
                    )
                    val stan: String? = errorBody.optString("system_trace_audit_number", null)
                    trySend(
                        EdcTransactionResponse.Failure(
                            type = FailureType.TRANSACTION_FAILURE,
                            code = apiError.code,
                            message = apiError.message,
                            stan = stan
                        )
                    )
                } else {
                    trySend(
                        EdcTransactionResponse.Failure(
                            type = FailureType.HTTP_FAILURE,
                            code = httpErrorCode.toString(),
                            message = transferMoneyResponse.message(),
                            data = transferMoneyResponse.errorMessage()
                        )
                    )
                }
                Utils.clearIncompleteTransaction()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            trySend(
                EdcTransactionResponse.Failure(
                    type = FailureType.HTTP_FAILURE,
                    code = noInternetErrorCode[0],
                    message = e.message.toString()
                )
            )
        } finally {
            awaitClose {
                //flow closed
            }
        }
    }

    private suspend fun submitTransactionReversal(
        transferMoneyRequest: TransferMoneyRequestResponseBody,
        reversalData: String?,
        cardReceiptResponse: CardReceiptResponse
    ): Response<Void> {
        val reversalRequest = TransactionReversalRequest(
            stan = cardReceiptResponse.systemTraceAuditNumber,
            transactionDate = DateTimeUtils.getCurrentDateYYYYMMDD(),
            terminalId = Utils.getTerminalId(),
            merchantId = Utils.getMerchantId(),
            transactionType = TransactionType.TRANSFER_POSTING.type,
            reversalIccData = reversalData,
            cardNumber = transferMoneyRequest.cardNumber,
            cardExpiry = transferMoneyRequest.cardExpiry,
            pinBlock = transferMoneyRequest.pinBlock,
            track2Data = transferMoneyRequest.track2Data,
            accountType = cardReceiptResponse.balanceInformation?.accountType
        )
        return edcTransactionUseCase.submitTransactionReversal(
            Utils.getPaymentAccountId(),
            reversalRequest
        )
    }

}