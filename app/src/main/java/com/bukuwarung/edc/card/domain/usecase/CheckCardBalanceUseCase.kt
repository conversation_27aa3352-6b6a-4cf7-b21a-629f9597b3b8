package com.bukuwarung.edc.card.domain.usecase

import android.util.Log
import com.bukuwarung.edc.card.data.model.*
import com.bukuwarung.edc.card.data.repository.CheckBalanceRepository
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.EdcTransactionResponse
import com.bukuwarung.edc.card.domain.model.FailureType
import com.bukuwarung.edc.card.domain.model.OnlineTransactionResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.card.ui.ErrorMapping.transactionTimeOutErrorCode
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.global.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.google.gson.Gson
import com.vfi.smartpos.deviceservice.constdefine.ConstOnlineResultHandler
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import org.json.JSONObject
import retrofit2.Response
import javax.inject.Inject

class CheckCardBalanceUseCase @Inject constructor(
    private val checkBalanceRepository: CheckBalanceRepository,
    private val inputOnlineResultUseCase: EvmInputOnlineResultUseCase,
    private val edcTransactionUseCase: EdcTransactionUseCase
) {
    val TAG = "CheckCardBalanceUseCase"
    suspend operator fun invoke(
        accountId: String,
        checkBalanceRequest: CheckBalanceRequest
    ) = callbackFlow {
        try {
            //send check balance request to backend
            val checkBalanceResponse =
                checkBalanceRepository.fetchCardBalance(accountId, checkBalanceRequest)
            //start validation of transaction response iccData - ARPC validation at device
            if (checkBalanceResponse.isSuccessful) {
                checkBalanceResponse.body().let { cardReceiptResponse ->
                    //balance check transactions with status = pending or timeout(RC 68) are considered as failed transaction
                    if(cardReceiptResponse?.responseCode != "00"){
                        trySend(
                            EdcTransactionResponse.Failure(
                                type = FailureType.TRANSACTION_FAILURE,
                                code = cardReceiptResponse?.responseCode!!,
                                message = "",
                                stan = checkBalanceResponse.body()?.systemTraceAuditNumber.orEmpty()
                            )
                        )
                        return@let
                    }
                    /*
                     * if test on android device flag is enabled, device cannot validate iccData
                     * return backend response without executing further checks
                     */
                    if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE) ||
                        checkBalanceRequest.posEntryMode == Constants.POS_ENTRY_MODE_MAG_CARD ||
                        (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API) && !(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE) || Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC) || Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_REVERSAL)))){
                        edcTransactionUseCase.confirmBalanceCheckUseCase(
                            Utils.getPaymentAccountId(),
                            TransactionConfirmRequest(cardReceiptResponse?.id)
                        )
                        trySend(EdcTransactionResponse.Success(cardReceiptResponse))
                        return@let
                    }

                    if(cardReceiptResponse?.iccData == null){
                        submitTransactionReversal(
                            checkBalanceRequest,
                            checkBalanceRequest.iccData,
                            cardReceiptResponse!!
                        )
                        trySend(
                            EdcTransactionResponse.Failure(
                                type = FailureType.DEVICE_CONFIRMATION_FAILURE,
                                code = Constants.DEVICE_TRANSACTION_VALIDATION_ERROR,
                                message = "online result refused",
                                data = ""
                            )
                        )
                        return@let
                    }
                    /*
                     * Backend gets iccData (ARPC) from AJ after transaction is executed.
                     * Device has to validate this data to make sure card was not tempered
                     * EMV will take online transaction result as input and gives result in callback
                     */
                    cardReceiptResponse?.iccData?.let {
                        inputOnlineResultUseCase.invoke(
                            cardReceiptResponse.responseCode!!,
                            it
                        ).collect { it ->

                            val validationResult =
                                it as EdcResponse.Success<OnlineTransactionResult>

                            if (validationResult.data.result == ConstOnlineResultHandler.onProccessResult.result.TC) {
                                /*
                                 * after transaction is validated by device, backend is expecting confirmation of ARPC validation
                                 * send transaction confirmation request to backend if received iccData was valid
                                 */
                                edcTransactionUseCase.confirmBalanceCheckUseCase(
                                    Utils.getPaymentAccountId(),
                                    TransactionConfirmRequest(cardReceiptResponse.id)
                                )
                                trySend(EdcTransactionResponse.Success(cardReceiptResponse))
                            } else {
                                //transaction device check failed
                                val reversalData =
                                    validationResult.data.resultData?.getString("REVERSAL_DATA")
                                /*
                                 * if device rejects card transaction due to invalid backend iccData
                                 * device needs to submit transaction reversal instead of transaction confirmation
                                 */
                                submitTransactionReversal(
                                    checkBalanceRequest,
                                    checkBalanceRequest.iccData,
                                    cardReceiptResponse!!
                                )
                                /*
                                 * set E01 as failure reason if device rejected iccData
                                 */
                                trySend(
                                    EdcTransactionResponse.Failure(
                                        type = FailureType.DEVICE_CONFIRMATION_FAILURE,
                                        code = Constants.DEVICE_TRANSACTION_VALIDATION_ERROR,
                                        message = validationResult.data.message,
                                        data = reversalData
                                    )
                                )
                            }
                        }
                    }
                }
            } else {
                val httpErrorCode = checkBalanceResponse.code()
                /*
                 * if backend API failed to execute transaction, appropriate error code, message is expected from backend
                 * it has to be provided in Failure object
                 */
                if (httpErrorCode == Constant.UNPROCESSABLE_ENTITY) {
                    val errorBody = JSONObject(checkBalanceResponse.errorBody()?.string())
                    val apiError = Gson().fromJson(
                        errorBody.getString("error"),
                        PinCardErrorResponse::class.java
                    )
                    val stan: String? = errorBody.optString("system_trace_audit_number", null)
                    trySend(
                        EdcTransactionResponse.Failure(
                            type = FailureType.TRANSACTION_FAILURE,
                            code = apiError.code,
                            message = apiError.message,
                            stan = stan
                        )
                    )
                } else {
                    trySend(
                        EdcTransactionResponse.Failure(
                            type = FailureType.HTTP_FAILURE,
                            code = httpErrorCode.toString(),
                            message = checkBalanceResponse.message(),
                            data = checkBalanceResponse.errorMessage()
                        )
                    )
                }
                Utils.clearIncompleteTransaction()
            }
        } catch (e: Exception) {
            // cannot throw exception from callbackFlow
            e.printStackTrace()
            trySend(
                EdcTransactionResponse.Failure(
                    FailureType.HTTP_FAILURE,
                    ErrorMapping.noInternetErrorCode[0], e.message
                )
            )
        }
        catch (e: NoConnectivityException) {
            // cannot throw exception from callbackFlow
            trySend(
                EdcTransactionResponse.Failure(
                    FailureType.HTTP_FAILURE,
                    ErrorMapping.noInternetErrorCode[0], e.message
                )
            )
        }
        finally {
            awaitClose {
                //flow closed
            }
        }
    }

    private suspend fun submitTransactionReversal(
        checkBalanceRequest: CheckBalanceRequest,
        reversalData: String?,
        cardReceiptResponse: CardReceiptResponse
    ): Response<Void> {
        val reversalRequest = TransactionReversalRequest(
            stan = cardReceiptResponse.systemTraceAuditNumber,
            transactionDate = DateTimeUtils.getCurrentDateYYYYMMDD(),
            terminalId = Utils.getTerminalId(),
            merchantId = Utils.getMerchantId(),
            transactionType = TransactionType.BALANCE_INQUIRY.type,
            reversalIccData = reversalData,
            cardNumber = checkBalanceRequest.cardNumber,
            cardExpiry = checkBalanceRequest.cardExpiry,
            pinBlock = checkBalanceRequest.pinBlock,
            track2Data = checkBalanceRequest.track2Data,
            accountType = cardReceiptResponse.balanceInformation?.accountType
        )
        return edcTransactionUseCase.submitTransactionReversal(
            Utils.getPaymentAccountId(),
            reversalRequest
        )
    }

}