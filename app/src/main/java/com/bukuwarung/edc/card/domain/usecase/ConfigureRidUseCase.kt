package com.bukuwarung.edc.card.domain.usecase

import com.bukuwarung.edc.card.domain.model.EmvCapkParam
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import javax.inject.Inject

class ConfigureRidUseCase @Inject constructor(private val deviceService: IEdcDeviceService) {

    val TAG = "ConfigureRidUseCase"

    operator fun invoke() {
        //clear RID
        Utils.setLoadedConfig(false)
        var status = deviceService.updateRID(3, null)
        bwLog(TAG, "Clear RID: $status")
        val listOfCapks = getCapkParamList()
        for (capk in listOfCapks) {
            status = deviceService.updateRID(capk.flagAppendRemoveClear, capk.tlvString)
            bwLog(TAG, "Set RID [status=$status] [${capk.comment}] rid=[${capk.tlvString}]")
        }
    }

    private fun getCapkParamList(): MutableList<EmvCapkParam> {

        val capkLive1408 = EmvCapkParam()
        capkLive1408.comment = "NSICCS LiveKey1 [1408 Bit]"
        capkLive1408.flagAppendRemoveClear = ConstIPBOC.updateRID.operation.append
        capkLive1408.append(EmvCapkParam.TAG_RID_9F06, "A000000602")
        capkLive1408.append(EmvCapkParam.TAG_Index_9F22, "05")
        capkLive1408.append(EmvCapkParam.TAG_ExpiryDate_DF05, "311226")
        capkLive1408.append(EmvCapkParam.TAG_Algorithm_DF07, "01")
        capkLive1408.append(
            EmvCapkParam.TAG_KEY_DF02,
            "B48CC63D71A486DFC920608A3E42D7C305472BF76B8E50C8C02FB8387E788F72931A29DC15F913E7D69E43AD4C38A5C4317E36D15DE5F49FA2327D9754799D2484A6E156941ACA9632417E5C92931A85E1BB5F2A2C1B847D5008C7B30591F1ACBF3B98DFB0CF2849B6C7CDC7435AEA85F3A58BAC3B8C990416A5E19EC4EA08DC91CEF2FBE5940FA6622926D2AD0523D109A7024EB1035BBE37260B30F41AA52EEB36E60DD37120B9401C3850920F0E03"
        )
        capkLive1408.append(EmvCapkParam.TAG_Exponent_DF04, "03")
        capkLive1408.append(
            EmvCapkParam.TAG_Hash_DF03,
            "1CAB162A1BE81492BB952C2846617B756F833C07"
        )

        val capkLive1984 = EmvCapkParam()
        capkLive1984.comment = "NSICCS LiveKey2 [1984 Bit]"
        capkLive1984.flagAppendRemoveClear = ConstIPBOC.updateRID.operation.append
        capkLive1984.append(EmvCapkParam.TAG_RID_9F06, "A000000602")
        capkLive1984.append(EmvCapkParam.TAG_Index_9F22, "09")
        capkLive1984.append(EmvCapkParam.TAG_ExpiryDate_DF05, "311230")
        capkLive1984.append(EmvCapkParam.TAG_Algorithm_DF07, "01")
        capkLive1984.append(
            EmvCapkParam.TAG_KEY_DF02,
            "A517A338854E0856EE4AFDBF4BDA5DD3F9EB3895CBD8971B1E58A8EB167BF9935E0752DAEA7EAFB25E79D601EB201895A93F8B0A16D95A230366C05FEC55858C94D6097B2FB1EDDD2C6A3647DD0B71BC1DCDDC68B4E9ECC919FB544070952443159733471292993AB23E5B8C00E6A8526DF04A0B6E65E0F9D0378F71497E12FA83540B49FC05D0A86DC3D66FC4BB291A69B2EBB98D057C8F1EE7CB8E942FD05E9E4FAD0361BC184C13418C313C042C547DEF41310BA1850EF59CAF8CC7B14DAEE72FA4689C1047434024D565A3FA46EDCA3F53E236235268C893F268AA24AB2D20EB7AE06FF3123318041CB23E30839C58DFD4991D7C88CB"
        )
        capkLive1984.append(EmvCapkParam.TAG_Exponent_DF04, "03")
        capkLive1984.append(
            EmvCapkParam.TAG_Hash_DF03,
            "E78686DB119C1CBFAD2149EF3CBE9CF54AC6321E"
        )


        val capkTest1408 = EmvCapkParam()
        capkTest1408.comment = "NSICCS TestKey1 [1408 Bit]"
        capkTest1408.flagAppendRemoveClear = ConstIPBOC.updateRID.operation.append
        capkTest1408.append(EmvCapkParam.TAG_RID_9F06, "A000000602")
        capkTest1408.append(EmvCapkParam.TAG_Index_9F22, "F5")
        capkTest1408.append(EmvCapkParam.TAG_ExpiryDate_DF05, "311226")
        capkTest1408.append(EmvCapkParam.TAG_Algorithm_DF07, "01")
        capkTest1408.append(
            EmvCapkParam.TAG_KEY_DF02,
            "9F2E972FF3F00759772AF850CBCD0B8BE411FE2D104CEB1B218E91F9F008DCC30DE61F42224C6A6BE07DAC048DA16CF24C43E600CEC4BBD4C5674494788F703AA4469E7DBC3B9923C2C54B50D4971D176A0E62F6875611B9CE5E247D24AC26926F22D0705D39A65808790232702E8C1A80291CA2C8AD890925B0EA20069761DF863B374FFB41A53ECB229867FBC6475C054B407C41E170265F08634E61324228A32466EF26E6BD5A2C150740A2F5D7B5"
        )
        capkTest1408.append(EmvCapkParam.TAG_Exponent_DF04, "03")
        capkTest1408.append(
            EmvCapkParam.TAG_Hash_DF03,
            "72454EB38A360FB9735963E8A17E8256C92816DB"
        )
//        Don't remove this commented code-> Test key2 doesn't work due to length. need to calculate separately
//        val capkTest1948 = EmvCapkParam()
//        capkTest1948.comment = "NSICCS TestKey2 [1984 Bit]"
//        capkTest1948.flagAppendRemoveClear = ConstIPBOC.updateRID.operation.append
//        capkTest1948.append(EmvCapkParam.TAG_RID_9F06, "A000000602")
//        capkTest1948.append(EmvCapkParam.TAG_Index_9F22, "F9")
//        capkTest1948.append(EmvCapkParam.TAG_ExpiryDate_DF05, "311230")
//        capkTest1948.append(EmvCapkParam.TAG_Algorithm_DF07, "01")
//        capkTest1948.append(
//            EmvCapkParam.TAG_KEY_DF02,
//            "A517A338854E0856EE4AFDBF4BDA5DD3ABF23E79FDEDFE8CE087353D65C2F8906AE55698C73605EEDC572A0C6B9CE7266253DDB4314E9051C84E42E7CF417DE44C288C584DA7F5D2A82E58EA343A7A23F4C2E561F318E5A65007F2CC71139713F8629EAFA5ED19D5D92451EE0DEB08F7D269741C4D791E6548BECBC2499DC23A062921A22256557B48713213B5AEF78824C37E786094C4AD3E0FB501D6C394B3A0443EF584520F63B6DDAA8245C83C43F958B8E9E0673E77987EAA17E4CFAD3BD667D888DA147D35C833BB0B645D9215604061DDF7E01C780479D0CCAB0726C0DF35AF1119B8773BE9959DC97CED2AA30887B404DDDEC135146A6F243E1D16106DCA929B35613C3D"
//        )
//        capkTest1948.append(EmvCapkParam.TAG_Exponent_DF04, "03")
//        capkTest1948.append(
//            EmvCapkParam.TAG_Hash_DF03,
//            "3C822B8A2BEC48181ED08E08EC5C9C9D4B7F8792"
//        )

        val capkParamList: MutableList<EmvCapkParam> = ArrayList<EmvCapkParam>()
        capkParamList.add(capkLive1408)
        capkParamList.add(capkLive1984)
        capkParamList.add(capkTest1408)
//        capkParamList.add(capkTest1984);
        return capkParamList
    }

}