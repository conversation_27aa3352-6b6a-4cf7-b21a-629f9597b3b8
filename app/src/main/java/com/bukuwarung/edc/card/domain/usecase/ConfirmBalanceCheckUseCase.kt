package com.bukuwarung.edc.card.domain.usecase

import com.bukuwarung.edc.card.data.model.TransactionConfirmRequest
import com.bukuwarung.edc.card.data.repository.EdcTransactionRepository
import com.bukuwarung.edc.util.Utils
import javax.inject.Inject

class ConfirmBalanceCheckUseCase @Inject constructor(private val edcTransactionRepository: EdcTransactionRepository) {
    suspend operator fun invoke(
        accountId: String,
        transactionConfirmRequest: TransactionConfirmRequest
    ) {

        val confirmTransactionResponse = edcTransactionRepository.confirmBalanceCheckTransaction(accountId, transactionConfirmRequest)
        if(confirmTransactionResponse.isSuccessful){
            Utils.clearIncompleteTransaction()
        }
    }
}