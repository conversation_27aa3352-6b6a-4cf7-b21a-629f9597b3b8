package com.bukuwarung.edc.card.domain.usecase

data class EdcDeviceUseCase(
    val checkCard: CheckCardUseCase,
    val stopCheckCard: StopCheckCardUseCase,
    val startEvm: StartEvmCardReadUseCase,
    val printReceipt: PrintCardReceiptUseCase,
    val printTest: PrinterTestUseCase,
    val loadKeys: LoadDeviceKeyUseCase,
    val getPinBlock: GeneratePinBlockUseCase,
    val printPaymentReceipt: PrintPaymentReceiptUseCase,
    val importCardConfirmResult: ImportCardConfirmResultUseCase,
    val importPin: ImportPinUseCase,
    val configureAidUseCase: ConfigureAidUseCase,
    val configureRidUseCase: ConfigureRidUseCase,
    val inputOnlineResult: EvmInputOnlineResultUseCase,
    val abortEmv: AbortEmvUseCase,
    val getEmvTagData: EmvTagDataUseCase,
    val checkCardAvailability: CheckCardAvailabilityUseCase,
    val initPinpad: InitPinpadUseCase,
    val startPinpadPinInput: StartPinpadPinInputUseCase,
    val stopPinpadPinInput: StopPinpadPinInputUseCase,
    val beeperSound: BeeperSoundUseCase
)
