package com.bukuwarung.edc.card.domain.usecase

import com.bukuwarung.edc.card.data.model.IncompleteTransactionRequest
import com.bukuwarung.edc.card.data.repository.EdcTransactionRepository
import javax.inject.Inject

class SubmitIncompleteTransactionUseCase @Inject constructor(private val edcTransactionRepository: EdcTransactionRepository) {
    suspend operator fun invoke(
        accountId: String,
        incompleteTransactionRequest: IncompleteTransactionRequest
    ) = edcTransactionRepository.submitIncompleteTransaction(accountId, incompleteTransactionRequest)
}