package com.bukuwarung.edc.card.domain.usecase.tms

import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.edc.card.data.model.EdcActivation
import com.bukuwarung.edc.card.data.repository.TerminalManagementRepository
import com.bukuwarung.edc.card.data.util.TripleDESUtil
import com.bukuwarung.edc.card.domain.model.TerminalSystemException
import com.bukuwarung.edc.card.domain.model.TmsError
import com.bukuwarung.edc.card.domain.model.TmsFailureType
import com.bukuwarung.edc.card.domain.model.TmsOperationResponse
import com.bukuwarung.edc.card.domain.usecase.LoadMasterKeyUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.put
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.Gson
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class ActivateTerminalUseCase @Inject constructor(
    private val repository: TerminalManagementRepository,
    private val loadMasterKeyUseCase: LoadMasterKeyUseCase
) {
    suspend operator fun invoke(serialNumber: String, appDeviceId: String, fcmToken: String) = callbackFlow {
        try {
            val response = repository.activateTerminal(serialNumber, appDeviceId, fcmToken)
            if (response.isSuccessful) {
                val activationResponse = response.body()?.data
                val documentRef = FirebaseFirestore.getInstance().collection("edc_activation_data")
                    .document(activationResponse!!.activationToken)

                documentRef.get()
                    .addOnSuccessListener { document ->
                        if (document != null) {
                            // Handle the document data
                            val data = document.toObject(EdcActivation::class.java)
                            val tmkVal = TripleDESUtil.decrypt(
                                data!!.encryptedToken,
                                repository.getPairedDeviceMacAddress()
                            )
                            val status = loadMasterKeyUseCase.invoke(
                                tmkVal
                            )
                            if (status) {
                                Utils.setConnectedDeviceMasterKey(tmkVal,data.serialNumber)
                                trySend(TmsOperationResponse.Success(true))
                            } else {
                                trySend(TmsOperationResponse.Failure(TmsError(TmsFailureType.DEVICE_FAILED_LOADING_KEY)))
                                Utils.trackDeviceActivationError(TmsFailureType.DEVICE_FAILED_LOADING_KEY)
                            }
                            FirebaseFirestore.getInstance().collection("edc_activation_data")
                                .document(activationResponse!!.activationToken).delete()
                        } else {
                            trySend(TmsOperationResponse.Failure(TmsError(TmsFailureType.FIREBASE_FAILED_LOADING_KEY)))
                            Utils.trackDeviceActivationError(TmsFailureType.FIREBASE_FAILED_LOADING_KEY)
                        }
                    }
                    .addOnFailureListener { exception ->
                        trySend(TmsOperationResponse.Failure(TmsError(TmsFailureType.ACTIVATION_FAILURE)))
                    }
            } else {
                val errorBody = response.errorBody()?.string()
                if (errorBody != null && errorBody.contains("errorCode")) {
                    val exception: TerminalSystemException = try {
                        Gson().fromJson(errorBody, TerminalSystemException::class.java)
                    } catch (e: Exception) {
                        TerminalSystemException(false, "E20", response.errorMessage())
                    }
                    trySend(TmsOperationResponse.Failure(TmsError(TmsFailureType.ACTIVATION_FAILURE)))
                    bwLog("TerminalSystemException: ${exception.messages}")
                }else {
                    trySend(TmsOperationResponse.Failure(TmsError(TmsFailureType.ACTIVATION_FAILURE)))
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            trySend(TmsOperationResponse.Failure(TmsError(TmsFailureType.UNEXPECTED_ERROR)))
            Utils.trackDeviceActivationError(TmsFailureType.UNEXPECTED_ERROR)
        } finally {
            awaitClose {
                // flow closed
            }
        }
    }
}
