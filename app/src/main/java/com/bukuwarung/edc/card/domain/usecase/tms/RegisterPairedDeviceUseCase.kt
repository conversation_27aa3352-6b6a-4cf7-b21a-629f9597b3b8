package com.bukuwarung.edc.card.domain.usecase.tms

import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.edc.card.data.model.TerminalPairedDevice
import com.bukuwarung.edc.card.data.repository.TerminalManagementRepository
import com.bukuwarung.edc.card.domain.model.TmsError
import com.bukuwarung.edc.card.domain.model.TmsFailureType
import com.bukuwarung.edc.card.domain.model.TmsOperationResponse
import com.bukuwarung.edc.card.domain.model.TmsResponse
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.put
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

class RegisterPairedDeviceUseCase @Inject constructor(private val repository: TerminalManagementRepository) {
    suspend operator fun invoke(serialNumber: String,appDeviceId: String, androidId: String, btName: String, btAddress: String) = callbackFlow {
        try{
            val response = repository.pairBluetoothDevice(serialNumber,appDeviceId, androidId, btName, btAddress)
            if (response.isSuccessful) {
                trySend(TmsOperationResponse.Success(response.body()?.data as TerminalPairedDevice))
            } else {
                if(response.code() == 401) {
                    trySend(
                        TmsOperationResponse.Failure(TmsError(TmsFailureType.UNAUTHORIZED))
                    )
                }else {
                    Utils.sharedPreferences.put(
                        "pair" + BluetoothDevices.getPairedCardReader(),
                        true
                    )
                }
            }
        } catch (e: Exception) {
//            trySend(TmsOperationResponse.Failure(TmsError(TmsFailureType.PAIRING_FAILURE)))
//            Utils.trackDeviceActivationError(TmsFailureType.PAIRING_FAILURE)
        } finally {
            awaitClose {
                //flow closed
            }
        }
    }
}