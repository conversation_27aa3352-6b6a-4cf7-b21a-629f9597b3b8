package com.bukuwarung.edc.card.transfermoney.constants

import com.bukuwarung.edc.card.transfermoney.model.PendingInfoHighlight
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken

object CheckBalancePinConfig {
    const val EDC_DYNAMIC_KEYBOARD = "edc_dynamic_keyboard"
    const val IS_FROM_FIREBASE = "IS_FROM_FIREBASE"
    const val DYNAMIC_KEYS = "DYNAMIC_KEYS"
    const val KEYBOARD_TYPE = "KEYBOARD_TYPE"
    const val PENDING_TRANSACTION_TNC_URL = "edc_pending_transaction_tnc_url"
    const val PENDING_TRANSACTION_INFO_HIGHLIGHT = "pending_transaction_info_highlight"
    const val EDC_ORDER_WARRANTY_NUDGE = "edc_order_warranty_nudge"

    fun getDynamicKeyboard() = RemoteConfigUtils.remoteConfig.getString(EDC_DYNAMIC_KEYBOARD)

    fun getEdcPendingTransactionTncUrl() =
        RemoteConfigUtils.remoteConfig.getString(PENDING_TRANSACTION_TNC_URL)

    private fun getEdcPendingInfoHighLight() = RemoteConfigUtils.remoteConfig.getString(PENDING_TRANSACTION_INFO_HIGHLIGHT)

    fun getEdcOrderWarrantyNudge() = RemoteConfigUtils.remoteConfig.getString(EDC_ORDER_WARRANTY_NUDGE)

    const val PENDING_TRANSACTION_TNC_URL_VALUE =
        "Ya, saya telah membaca dan menyetujui <a href=\"https://bukuwarung.com/syarat-dan-ketentuan-penggunaan-edc-bukuwarung/\">Syarat dan Ketentuan Penggunaan</a> Mesin EDC BukuAgen."

    const val EDC_DYNAMIC_KEYBOARD_BODY = """
       {
        "isFromFirebase":true,
        "keyboardType":0,
        "row1": [
                "0",
                "2",
                "3"
                ],
      "row2": [
                "4",
                "5",
                "6"
                 ],
      "row3": [
                "7",
                "8",
                "9"
                ],
      "row4": [
                "",
                "1",
                ""
                ]
        }
    """

    const val PENDING_TRANSACTION_INFO_HIGHLIGHT_VALUE = """
        {
            "targetErrorCode": "68",
            "title": "Jaminan Transaksi Sukses",
            "message": "Dapatkan kompensasi Komisi Agen, jika transaksi ini pending lebih dari 10 menit. S&K berlaku."
        }
    """

    const val EDC_ORDER_WARRANTY_NUDGE_VALUE = """
        {
          "is_visible": true,
          "image_url": "",
          "text": "Hati makin tenang dengan Garansi Ekstra EDC Saku!",
          "redirection_url": "mx-mweb/edc/dynamic-order/EDC_MOREFUN_INSURANCE",
          "button_text": "Beli Sekarang"
        }
    """

    fun fetchPendingTransactionInfoHighlightValue(): PendingInfoHighlight? {
        var stringJson = getEdcPendingInfoHighLight()
        val type = object : TypeToken<PendingInfoHighlight>() {}.type
        var highlightData: PendingInfoHighlight?
        try {
            highlightData = GsonBuilder().create().fromJson(stringJson, type)
        } catch (e: Exception) {
            stringJson = RemoteConfigUtils.remoteConfig.getString(PENDING_TRANSACTION_INFO_HIGHLIGHT_VALUE)
            highlightData = GsonBuilder().create().fromJson(stringJson, type)
        }
        return highlightData
    }
}