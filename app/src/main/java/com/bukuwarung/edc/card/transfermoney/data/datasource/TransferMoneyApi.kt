package com.bukuwarung.edc.card.transfermoney.data.datasource

import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.transfermoney.model.EdcBankResponse
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.util.Utils
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface TransferMoneyApi {

    @POST("edc-adapter/transfer/inquiry/{accountId}")
    suspend fun transferMonetEnquireApi(
        @Header("x-edc-serial-number") serialNumber: String,
        @Path("accountId") accountId: String,
        @Body enquireTransferMoneyRequest: TransferMoneyRequestResponseBody
    ): Response<TransferMoneyRequestResponseBody>

    @POST("edc-adapter/transfer/posting/{accountId}")
    suspend fun transferMonetApi(
        @Header("x-edc-serial-number") serialNumber: String,
        @Path("accountId") accountId: String,
        @Body transferMoneyRequest: TransferMoneyRequestResponseBody,
        @Header("app-instance-installation-id") insallationId: String = Utils.getFcmDeviceId()
    ): Response<CardReceiptResponse>

    /**
     * Retrieves a list of banks available for EDC transactions.
     * @param accountId The payment account ID of the account.
     * @param usecase An optional use case identifier.
     * @param usecase = settlement, will filter banks that have mapping to payment bank code and used for listing banks for settlement
     * @return A [Response] object containing the [EdcBankResponse] with the list of banks.
     */
    @GET("edc-adapter/{accountId}/bank/list")
    suspend fun getEdcBanks(
        @Path("accountId") accountId: String,
        @Query("usecase") usecase: String = "",
        @Query("statuses") statuses: String
    ): Response<EdcBankResponse>

    @POST("edc-adapter/cash-withdrawal/inquiry/{account_id}")
    suspend fun cashWithdrawalInquiry(
        @Header("x-edc-serial-number") serialNumber: String,
        @Path("account_id") accountId: String,
        @Body request: TransferMoneyRequestResponseBody
    ): Response<TransferMoneyRequestResponseBody>

    @POST("edc-adapter/cash-withdrawal/posting/{account_id}")
    suspend fun cashWithdrawalPosting(
        @Header("x-edc-serial-number") serialNumber: String,
        @Path("account_id") accountId: String,
        @Body request: TransferMoneyRequestResponseBody,
        @Header("app-instance-installation-id") insallationId: String = Utils.getFcmDeviceId()
    ): Response<CardReceiptResponse>
}