package com.bukuwarung.edc.card.transfermoney.data.repository

import com.bukuwarung.edc.card.transfermoney.data.datasource.TransferMoneyApi
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import javax.inject.Inject

class TransferMoneyRepository @Inject constructor(private val transferMoneyApi: TransferMoney<PERSON>pi) {

    suspend fun enquireTransferMoney(accountId: String, requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.transferMonetEnquireApi(accountId, requestBody)

    suspend fun transferMoney(accountId: String,requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.transferMonetApi(accountId,requestBody)

    suspend fun cashWithdrawalInquiry(accountId: String, requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.cashWithdrawalInquiry(accountId, requestBody)

    suspend fun cashWithdrawalPosting(accountId: String,requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.cashWithdrawalPosting(accountId,requestBody)

    suspend fun getEdcBanks(accountId: String, useCase: String, statuses: String) =
        transferMoneyApi.getEdcBanks(accountId, useCase, statuses)
}