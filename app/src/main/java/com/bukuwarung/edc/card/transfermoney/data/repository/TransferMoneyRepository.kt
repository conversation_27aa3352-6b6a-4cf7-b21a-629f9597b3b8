package com.bukuwarung.edc.card.transfermoney.data.repository

import com.bukuwarung.edc.card.transfermoney.data.datasource.TransferMoneyApi
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import javax.inject.Inject

class TransferMoneyRepository @Inject constructor(private val transferMoneyApi: TransferMoneyApi) {

    suspend fun enquireTransferMoney(serialNumber: String,accountId: String, requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.transferMonetEnquireApi(serialNumber,accountId, requestBody)

    suspend fun transferMoney(serialNumber: String, accountId: String, requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.transferMonetApi(serialNumber,accountId,requestBody)

    suspend fun cashWithdrawalInquiry(serialNumber: String,accountId: String, requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.cashWithdrawalInquiry(serialNumber,accountId, requestBody)

    suspend fun cashWithdrawalPosting(serialNumber: String,accountId: String,requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyApi.cashWithdrawalPosting(serialNumber,accountId,requestBody)

    suspend fun getEdcBanks(accountId: String, useCase: String, statuses: String) =
        transferMoneyApi.getEdcBanks(accountId, useCase, statuses)
}