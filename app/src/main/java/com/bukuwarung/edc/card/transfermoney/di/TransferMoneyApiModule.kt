package com.bukuwarung.edc.card.transfermoney.di

import com.bukuwarung.edc.card.transfermoney.data.datasource.TransferMoneyApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class TransferMoneyApiModule {

    @Singleton
    @Provides
    fun provideTransferMoneyApi(@Named("normal") retrofit: Retrofit): TransferMoneyApi =
        retrofit.create(TransferMoneyApi::class.java)
}