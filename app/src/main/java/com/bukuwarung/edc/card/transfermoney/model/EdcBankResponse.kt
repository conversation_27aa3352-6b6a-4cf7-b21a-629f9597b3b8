package com.bukuwarung.edc.card.transfermoney.model

import androidx.annotation.Keep
import com.bukuwarung.edc.payments.data.model.Bank.Metadata
import com.google.gson.annotations.SerializedName

@Keep
data class EdcBankResponse(

    @field:SerializedName("banks")
    val banks: List<BanksItem?>? = null
)

@Keep
data class BanksItem(

    @field:SerializedName("bank_code")
    val bankCode: String? = null,

    @field:SerializedName("logo_url")
    val logoUrl: String? = null,

    @field:SerializedName("bank_name")
    val bankName: String? = null,

    @field:SerializedName("id")
    val id: String? = null,

    @field:SerializedName("bank_swift_code")
    val bankSwiftCode: String? = null,

    @field:SerializedName("status")
    val status: String? = null,

    @field:SerializedName("payment_bank_code")
    val paymentBankCode: String? = null,

    @field:SerializedName("metadata")
    val metadata: Metadata? = null
)

@Keep
data class Metadata(
	@field:SerializedName("message")
	val message: MessageMetadata? = null
)

@Keep
data class MessageMetadata(
	@field:SerializedName("message_id")
	val messageId: String? = null
)