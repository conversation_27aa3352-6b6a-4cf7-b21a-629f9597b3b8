package com.bukuwarung.edc.card.ui

import android.os.Build
import android.os.Bundle
import android.util.Log
import com.bukuwarung.edc.card.CardPinDynamicActivity
import com.bukuwarung.edc.card.ExternalPinpadActivity
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.ui.AddBankAccountMoneyTransferActivity
import com.bukuwarung.edc.databinding.LayoutCardInfoBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.ui.addbank.AddBankAccountActivity
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity.Companion.IS_MONEY_TRANSFER
import com.bukuwarung.edc.util.*
import com.bukuwarung.edc.util.DateTimeUtils.getFormattedExpiryDate
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CardInfoActivity : BaseCardActivity() {

    private lateinit var binding: LayoutCardInfoBinding
    private var bundle: Bundle? = null
    private var isTransfer = false;
    private var transactionType = ""


    override fun setViewBinding() {
        binding = LayoutCardInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {

        if (intent.hasExtra("data")) {
            bundle = intent.getBundleExtra("data")
            isTransfer = bundle?.getString(Constant.INTENT_KEY_TRANSACTION_TYPE)
                .equals(TransactionType.TRANSFER_INQUIRY.type)
            transactionType = bundle!!.getString(Constant.INTENT_KEY_TRANSACTION_TYPE)!!
            binding.apply {
                tvCardNum.text = Utils.formatPanNumber(bundle?.getString("PAN"))
                tvExpiryValue.text =
                    getFormattedExpiryDate(bundle?.getString(KEY_EXPIRED_DATE_String))
            }
        }

        val map = HashMap<String, String>()
        map[CardAnalyticsConstants.TRANSACTION_TYPE] = Utils.getTransactionTypeAnalytics(transactionType)
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_VISIT_INFORMATION_CARD, map)

        binding.tbCardInfo.apply {
            if(transactionType == TransactionType.TRANSFER_INQUIRY.type){
                tvTitle.text = "Transfer Via Kartu"
            }else if(transactionType == TransactionType.CASH_WITHDRAWAL.type){
                tvTitle.text = "Tarik Tunai Via Kartu"
            }else {
                tvTitle.text = "Cek Saldo Via Kartu"
            }
            btnBack.setOnClickListener {
                trackBackPressEvent(CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON)
                edcCardViewModel.onEventReceived(EdcCardViewModel.Event.OnCardReadConfirm(
                    ConstIPBOC.importCardConfirmResult.pass.refused))
                onBackPressed()
            }
        }

        binding.btnSubmit.singleClick {
            edcCardViewModel.onEventReceived(EdcCardViewModel.Event.OnCardReadConfirm(true))
            //to avoid duplicate emv flow
            edcCardViewModel.abortEmv()
            redirectToTransactionFlow()
            Analytics.trackEvent(CardAnalyticsConstants.EVENT_INFO_CARD_CONFIRM, map)
        }
        setupInactivityDialog()
    }

    override fun subscribeState() {

    }

    override fun onBackPressed() {
        trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
        checkForCardRemove(this)
    }

    override fun onResume() {
        super.onResume()
        startInactivityTimer()
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] =
            if (isTransfer) CardAnalyticsConstants.TRANSFER else CardAnalyticsConstants.BALANCE_CHECK
        eventProperties[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CARD_INFO_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
    }

    private fun redirectToTransactionFlow() {
        Log.d("account", bundle.toString())
        when (transactionType) {
            TransactionType.TRANSFER_INQUIRY.type,TransactionType.TRANSFER_POSTING.type -> {
                openActivity(SelectBankActivity::class.java) {
                    putBundle("data", bundle)
                    putBoolean(IS_MONEY_TRANSFER, true)
                }
            }
            TransactionType.CASH_WITHDRAWAL.type -> {
                //fetch account information
                val bankAccount = Utils.getSettlementBankAccount()
                if(bankAccount == null){
                    openActivity(
                        AddSettlementBankAccountActivity::class.java,
                    ) {
                        putString(AddSettlementBankAccountActivity.ENTRY_POINT, <EMAIL>)
                    }
                }else {
                    openActivity(AddBankAccountMoneyTransferActivity::class.java) {
                        putBundle("data", bundle)
                        putParcelable(AddBankAccountActivity.SELECTED_BANK, bankAccount)
                        putBoolean(IS_MONEY_TRANSFER, false)
                    }
                }
            }
            else -> {
                openActivity(Utils.getCardPinActivity(bundle?.getString(CardPinDynamicActivity.PAN).orEmpty())) {
                    putBundle("data", bundle)
                    putBoolean(IS_MONEY_TRANSFER, false)
                }
            }
        }
    }
}