package com.bukuwarung.edc.card.ui

import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.activity.viewModels
import com.bukuwarung.bluetooth_printer.utils.hideView
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.cardreader.contant.CardConstants
import com.bukuwarung.cardreader.tianyu.util.TianyuDataUtils
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.data.model.Terminal
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_CHECK_STATUS_ERROR
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_CHECK_STATUS_SUCCESS
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_CHECK_STATUS_TIMEOUT
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_CHECK_STATUS_UNEXPECTED_ERROR
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_EXPIRED
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_UNSUPPORTED
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_UNSUPPORTED_MAG_CARD
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_STATUS_VALID
import com.bukuwarung.edc.databinding.ActivityInsertCardBinding
import com.bukuwarung.edc.databinding.LayoutOverlayBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant.INTENT_KEY_TRANSACTION_TYPE
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.homepage.ui.home.HomePageViewModel
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.*
import dagger.hilt.android.AndroidEntryPoint
import kotlin.collections.set

@AndroidEntryPoint
class CardReaderInstructionActivity: BaseCardActivity() {

    private var cardReadingStartTime: Long? = null
    private lateinit var binding: ActivityInsertCardBinding
    private lateinit var overlayLayout: LayoutOverlayBinding
    private val homePageViewModel: HomePageViewModel by viewModels()
    private val handler = Handler(Looper.getMainLooper())
    private var transactionType = TransactionType.BALANCE_INQUIRY.type
    private val btCardReaderViewModel: TerminalManagementViewModel by viewModels()

    val TIMEOUT = 60
    var isTransfer = false
    var data: Bundle? = null
    var cardErrorDialog: CardErrorDialog? = null

    override fun setViewBinding(){

        binding = ActivityInsertCardBinding.inflate(layoutInflater)
        overlayLayout = LayoutOverlayBinding.bind(binding.root)
        setContentView(binding.root)
    }

    override fun setupView() {

        binding.tbInsertCard.apply {
            if(intent.hasExtra("mode") && intent.getStringExtra("mode")?.equals(TransactionType.TRANSFER_INQUIRY.type) == true) {
                isTransfer = true
                transactionType = TransactionType.TRANSFER_INQUIRY.type
                Utils.setTestingFlow(transType = TransactionType.TRANSFER_INQUIRY.type)
                tvTitle.text = "Transfer Via Kartu"
            }else if(intent.hasExtra("mode") && intent.getStringExtra("mode")?.equals(TransactionType.CASH_WITHDRAWAL.type) == true) {
                transactionType = TransactionType.CASH_WITHDRAWAL.type
                tvTitle.text = "Tarik Tunai Via Kartu"
            } else {
                transactionType = TransactionType.BALANCE_INQUIRY.type
                Utils.setTestingFlow(transType = TransactionType.BALANCE_INQUIRY.type)
                tvTitle.text = "Cek Saldo Via Kartu"
            }
            btnBack.setOnClickListener {
                trackBackPressEvent(CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON)
                if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
                    onBackPressed()
                } else {
                    super.onBackPressed()
                }
            }
            val eventProperties = HashMap<String, String>()
            eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] = Utils.getTransactionTypeAnalytics(transactionType)
            Analytics.trackEvent(CardAnalyticsConstants.EVENT_INSERT_CARD_PAGE_VISIT, eventProperties)
        }

        Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, true)
        if(Utils.isMagCardEnabled()) {
            binding.ivInsertCard.setImageDrawable(getDrawableCompat(R.drawable.ic_instruction_mag_and_chip))
            binding.tvInsertCardHeader.text = getString(R.string.insert_or_swipe_card)
            binding.tvInsertCardInfo.text = getString(R.string.insert_or_swipe_card_instruction_message)
        }
        edcCardViewModel.configureEmv();
        Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, true)
        setupInactivityDialog()
        if(Utils.getActivationMasterKey(Utils.getTerminalId()).isNullOrEmpty() || Utils.getMasterKey().isNullOrEmpty()){
            requestReactivationKey()
        }
    }

    override fun onResume() {
        super.onResume()
        startInactivityTimer()
        if(Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)){
            Toast.makeText(applicationContext, "[Simulate Card] reading debug card in 2sec", Toast.LENGTH_SHORT).show()
            edcCardViewModel.onEventReceived(
                EdcCardViewModel.Event.SimulateInsertCard(
                    2000
                )
            )
        } else {
            edcCardViewModel.onEventReceived(
                EdcCardViewModel.Event.OnStartCheckCard(
                    TIMEOUT, false
                )
            )
        }
    }

    override fun subscribeState() {
        observeLogonData()
        edcCardViewModel.checkCardResult.observe(this) {
            if (it?.inputMode == CARD_ENTRY_MODE_IC && it?.status == CARD_CHECK_STATUS_SUCCESS) {
                if (Utils.sharedPreferences.getBoolean(Utils.CARD_INSERT_SOUND, false)) {
                    homePageViewModel.doBeep()
                }
                showProgressOverlay()
                val map = HashMap<String, String>()
                cardReadingStartTime = System.currentTimeMillis()
                map[CardAnalyticsConstants.TRANSACTION_TYPE] = Utils.getTransactionTypeAnalytics(transactionType)
                Analytics.trackEvent(CardAnalyticsConstants.EVENT_CARD_READING_START, map)
                edcCardViewModel.onEventReceived(
                    EdcCardViewModel.Event.OnStartReadCard(
                        TIMEOUT
                    )
                )
            } else if( it?.status == CARD_CHECK_STATUS_TIMEOUT){
                if (cardErrorDialog != null) {
                    goToDestination(HomePageActivity::class.java)
                }
                CardErrorDialog(this, CardErrorType.REQUEST_TIMEOUT){ errorDialog ->
                    errorDialog.dismiss()
                    goToDestination(HomePageActivity::class.java)
                }.show()
            } else if( it?.status == CARD_CHECK_STATUS_ERROR){
                if (cardErrorDialog == null) {
                    cardErrorDialog = CardErrorDialog(this, CardErrorType.UNABLE_TO_READ) { errorDialog ->
                        checkForCardRemove(this)
                    }
                    cardErrorDialog?.show()
                } else {
                    goToDestination(HomePageActivity::class.java)
                }
            } else if (it?.status == CARD_STATUS_UNSUPPORTED) {
                goToDestination(HomePageActivity::class.java)
            }
        }

        edcCardViewModel.cardReaderResult.observe(this) {

            var failedReason = ""
            if(it.cardStatus == CARD_STATUS_EXPIRED){
                failedReason = CardAnalyticsConstants.CARD_EXPIRED
                showCardErrorDialog(CardErrorType.EXPIRED_CARD)
            } else if(it.cardStatus == CARD_STATUS_UNSUPPORTED){
                failedReason = CardAnalyticsConstants.CARD_UNSUPPORTED
                showCardErrorDialog(CardErrorType.CARD_NOT_SUPPORTED)
            } else if(it.cardStatus == CARD_STATUS_VALID && it.data != null) {
                edcCardViewModel.stopCheckCard()
                data = it.data
                homePageViewModel.fetchLogonData(Utils.getTerminalId())
                if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)) {
                    it.iccData = Utils.getDummyIccData()
                    data?.putString("icc_data", it.iccData)
                }
            }else if(it.cardStatus == CARD_STATUS_UNSUPPORTED_MAG_CARD){
                showUnsupportedMagCardError()
            }else if(it.cardStatus == CARD_CHECK_STATUS_UNEXPECTED_ERROR){
                showUnexpectedMagCardError()
            }

            val map = HashMap<String, String>()
            val cardReadingCompletedTime = System.currentTimeMillis()
            map[CardAnalyticsConstants.TIME_TAKEN_TO_READ_CARD] =
                (cardReadingCompletedTime - cardReadingStartTime.orNil).milliSecondsToSeconds()
                    .toString() + " secs"
            map[CardAnalyticsConstants.TRANSACTION_TYPE] = Utils.getTransactionTypeAnalytics(transactionType)
            map[CardAnalyticsConstants.CARD_READING_STATUS] =
                if (failedReason.isBlank()) CardAnalyticsConstants.SUCCESS else CardAnalyticsConstants.FAIL
            if (failedReason.isNotBlank()) map[CardAnalyticsConstants.FAIL_REASON] = failedReason
            try {
                map["type_card"] = "${it.inputMode}"
                val prefix = it.data?.getString(CardConstants.ResultBundleConst.KEY_PAN_String)
                    ?.let { if (it.length >= 6) it.substring(0, 6) else it }
                map["prefix"] = "$prefix"
            } catch (e: Exception) {
                e.printStackTrace()
            }
            Analytics.trackEvent(CardAnalyticsConstants.EVENT_CARD_READING_COMPLETED, map)
        }
    }
    private fun showProgressOverlay(){
        overlayLayout.progressOverlay.showView()
//        overlayLayout.root.hideView()
    }

    private fun hideProgressOverlay(){
        overlayLayout.progressOverlay.hideView()
        overlayLayout.root.showView()
    }

    private fun showCardRemovalDialog() {
        checkForCardRemove(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        edcCardViewModel.stopCheckCard()
        if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
            edcCardViewModel.abortEmv()
        }
    }

    private fun observeLogonData() {
        btCardReaderViewModel.tmsResponse.observe(this) { response ->
            when (response.status) {
                Status.SUCCESS -> handleReactivationSuccessResponse(response.data)
                else -> {
                    //ignore as reactive is fallback method, not impacting core flow
                }
            }
        }
        homePageViewModel.logonData.observe(this) {
            when (it.status) {
                Status.LOADING -> {
                    overlayLayout.progressOverlay.showView()
                }
                Status.SUCCESS -> {
                    redirectToChooseAccountType()
                }
                Status.ERROR -> {
                    if ((it.message?.contains("TMK", ignoreCase = true) == true) ||
                        (it.message?.contains("422", ignoreCase = true) == true)
                    ) {
                        showAuthenticationError(it.message)
                    } else {
                        showSystemTrouble(it.message)
                    }
                }
                Status.NO_INTERNET -> {
                    overlayLayout.progressOverlay.hideView()
                    BukuDialog(
                        context = this@CardReaderInstructionActivity,
                        title = getString(R.string.failed_to_load),
                        subTitle = getString(R.string.checking_internet_connection_message)+"[${it.message}]",
                        image = null,
                        isLoader = false,
                        btnLeftListener = {
                            openActivity(HomePageActivity::class.java)
                            finishAffinity()

                        },
                        btnRightListener = {
                            homePageViewModel.fetchLogonData(Utils.getTerminalId())
                        },
                        btnLeftText = getString(R.string.back),
                        btnRightText = getString(R.string.retry)
                    ).show()
                }
            }
        }
    }

    override fun onBackPressed() {
        if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
            super.onBackPressed()
        }
        trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
        showCardRemovalDialog()
    }

    override fun onCardRemove() {
        edcCardViewModel.stopCheckCard()
        if(hasShownInactivityTimer || Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN){
            goToDestination(HomePageActivity::class.java)
        }else {
            edcCardViewModel.onEventReceived(
                EdcCardViewModel.Event.OnStartCheckCard(
                    TIMEOUT, false
                )
            )
        }
    }

    private fun showCardErrorDialog(errorType: CardErrorType){
        hideProgressOverlay()
        if (cardErrorDialog != null) {
            cardErrorDialog?.dismiss()
        }
        cardErrorDialog = null
        cardErrorDialog = CardErrorDialog(this, errorType) {
            checkForCardRemove(this)
        }
        cardErrorDialog?.show()
    }

    private fun showUnsupportedMagCardError(){
        hideProgressOverlay()
        if (cardErrorDialog != null) {
            cardErrorDialog?.dismiss()
        }
        cardErrorDialog = null
        cardErrorDialog = ErrorMapping.showErrorDialog(
            this,
            null,
            ErrorMapping.magCardNotSupportedErrorCode[0],
            dismissListener = {
                edcCardViewModel.onEventReceived(
                    EdcCardViewModel.Event.OnStartCheckCard(
                        TIMEOUT, false
                    )
                )
            }

        )
        cardErrorDialog?.show()
    }

    private fun showUnexpectedMagCardError(){
        hideProgressOverlay()
        if (cardErrorDialog != null) {
            cardErrorDialog?.dismiss()
        }
        cardErrorDialog = null
        cardErrorDialog = ErrorMapping.showErrorDialog(
            this,
            null,
            ErrorMapping.magCardUnexpectedErrorCode[0],
            dismissListener = {
                edcCardViewModel.onEventReceived(
                    EdcCardViewModel.Event.OnStartCheckCard(
                        TIMEOUT, false
                    )
                )
            }

        )
        cardErrorDialog?.show()
    }

    private fun redirectToChooseAccountType() {
        openActivity(ChooseAccountTypeActivity::class.java) {
            data?.putString(
                INTENT_KEY_TRANSACTION_TYPE,
                transactionType
            )
            putBundle("track", data)
        }
        hideProgressOverlay()
        finish()
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] = Utils.getTransactionTypeAnalytics(transactionType)
        eventProperties[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CARD_READING_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }

    private fun showSystemTrouble(errorCode: String?) {
        BukuDialog(
            context = this@CardReaderInstructionActivity,
            title = getString(R.string.system_error),
            subTitle = getString(R.string.system_trouble_message, errorCode),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {
                openActivity(HomePageActivity::class.java)
                finish()
            },
            btnRightListener = {},
            btnLeftText = getString(com.bukuwarung.edc.R.string.back),
            btnRightText = ""
        ).show()
    }

    private fun showAuthenticationError(errorCode: String?){
        BukuDialog(
            context = this@CardReaderInstructionActivity,
            title = getString(R.string.authentication_trouble),
            subTitle = getString(R.string.authentication_trouble_message, errorCode),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {},
            btnRightListener = {
                ZohoChat.openZohoChat("card_reader_instruction")
                finish()
            },
            btnLeftText = "",
            btnRightText = getString(R.string.contact_customer_care)
        ).show()
    }

    private fun requestReactivationKey() {
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }
            val versionName = packageInfo.versionName
            val additionalDetails = mapOf(
                "Manufacturer" to Build.MANUFACTURER,
                "Model" to Build.MODEL,
                "AppVersion" to versionName
            )
            val terminal = Terminal(
                serialNumber = Utils.getDeviceSerialNumber(),
                btName = Utils.getDeviceSerialNumber(),
                btAddress = Utils.getDeviceSerialNumber(),
                appDeviceId = Analytics.getDeviceId(),
                appVersion = versionCode.toString(),
                androidId = DeviceUtils.getAndroidId(this),
                hardwareVersion = "-",
                softwareVersion = "-",
                status = "Active",
                currentLocationId = "0",
                manufacturer = Build.MANUFACTURER,
                model = Build.MODEL,
                reactivationKey = "",
                additionalDetails = additionalDetails
            )
            btCardReaderViewModel.updateTerminal(terminal.serialNumber, terminal)
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun handleReactivationSuccessResponse(data: Any?) {
        try {
            if (data !is Terminal) {
                return
            }

            val reactivationKey = data.reactivationKey
            if (!reactivationKey.isNullOrBlank()) {
                injectMasterKey(reactivationKey, data.serialNumber)
            }
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun injectMasterKey(key: String, serialNumber: String) {
        try {
            val decodeKey = TianyuDataUtils.getDecodedKey(key);
            Utils.setConnectedDeviceMasterKey(decodeKey, serialNumber)
            Utils.setActivationMasterKey(Utils.getTerminalId(),decodeKey)
            Utils.getActivationMasterKey(Utils.getTerminalId())
                ?.let { Utils.setTerminalMasterKey(it) }
            btCardReaderViewModel.updateTerminalStatus(
                serialNumber,"Active"
            )
        }catch (e:Exception){
            e.printStackTrace()
        }
    }
}