package com.bukuwarung.edc.card.ui

import Resource
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.edc.card.CardUseCase
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import com.bukuwarung.edc.card.data.model.PinCardErrorResponse
import com.bukuwarung.edc.card.domain.model.EdcTransactionResponse
import com.bukuwarung.edc.card.domain.usecase.CheckCardBalanceUseCase
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.card.ui.ErrorMapping.errorOccurredInSystemErrorCode
import com.bukuwarung.edc.card.ui.ErrorMapping.noInternetErrorCode
import com.bukuwarung.edc.card.ui.ErrorMapping.transactionFailedErrorCode
import com.bukuwarung.edc.card.ui.ErrorMapping.transactionTimeOutErrorCode
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.util.NetworkUtils
import com.bukuwarung.edc.util.Utils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject
import java.net.HttpURLConnection
import javax.inject.Inject

@HiltViewModel
class CheckCardViewModel @Inject constructor(
    private val cardUseCase: CardUseCase,
    private val checkCardBalanceUseCase: CheckCardBalanceUseCase,
) : ViewModel() {

    private val _cardBalance = MutableLiveData<Resource<Any>>()

    val cardBalance: LiveData<Resource<Any>>
        get() = _cardBalance

    val _transferMoneyInquire = MutableLiveData<Resource<Any>>()

    val transferMoneyInquire: LiveData<Resource<Any>>
        get() = _transferMoneyInquire

    fun fetchCardBalance(checkBalanceRequest: CheckBalanceRequest) =
        viewModelScope.launch {
            //start loading screen
            _cardBalance.postValue(Resource.loading(null))
            try {
                val accountId = Utils.getPaymentAccountId()
                val serialNumber =
                    Utils.getHardwareSerialNumber().ifEmpty { Utils.getDeviceSerialNumber() }
                //execute balance check transaction
                checkCardBalanceUseCase.invoke(serialNumber, accountId, checkBalanceRequest)
                    .collect { response ->
                    //transaction flow is completed, incomplete-txn data should be cleared
                    when (response) {
                        //Success = balance check API returned true and device EMV has verified backend response icc data
                        is EdcTransactionResponse.Success<*> -> {
                            bwLog("BALANCE-data $response.data")
                            _cardBalance.postValue(Resource.success(response.data))
                            if(Utils.isCardReader()) {
                                try {
                                    val status: Boolean =
                                        CardReaderHelper.getInstance()
                                            ?.confirmTransaction("Transaksi Berhasil") == true
                                    Log.d("BALANCE", "Transaction Complete: $status")
                                } catch (e: Exception) {
                                    bwLog("BALANCE-Transaction Complete: ${e.message}")
                                    bwLog(e)
                                }
                            }
                        }
                        //check balance transaction failed = http failure or device validation faliure or backend rejected transaction
                        is EdcTransactionResponse.Failure -> {
                            bwLog("BALANCE-error $response")
                            _cardBalance.postValue(
                                Resource.error(
                                    response.message ?: "",
                                    PinCardErrorResponse(
                                        response.code,
                                        response.message ?: "",
                                        response.stan.orEmpty()
                                    )
                                )
                            )
                            if(Utils.isCardReader()) {
                                try {
                                    val status: Boolean =
                                        CardReaderHelper.getInstance()
                                            ?.confirmTransaction("Transaksi Gagal") == true
                                    bwLog("BALANCE-Transaction Failed: $status")
                                } catch (e: Exception) {
                                    bwLog("BALANCE-Transaction Failed: ${e.message}")
                                    bwLog(e)
                                }
                            }
                        }
                    }
                }

            } catch (e: NoConnectivityException) {
                bwLog(e)
                handleServerError(e.message, noInternetErrorCode[0])
                NetworkUtils.checkInternetHealthAndLog(e)
            } catch (e: Exception) {
                bwLog(e)
                handleServerError(e.message.toString(), errorOccurredInSystemErrorCode[0])
                NetworkUtils.checkInternetHealthAndLog(e)
            }
        }

    fun inquireMoneyTransfer(
        transactionType: TransactionType,
        enquireMoneyTransferBody: TransferMoneyRequestResponseBody
    ) = viewModelScope.launch {
        _transferMoneyInquire.postValue(Resource.loading(null))
        try {
            val accountId = Utils.getPaymentAccountId()
            val serialNumber =
                Utils.getHardwareSerialNumber().ifEmpty { Utils.getDeviceSerialNumber() }
            val response =
                if (transactionType == TransactionType.CASH_WITHDRAWAL) cardUseCase.cashWithdrawalInquiry(
                    serialNumber,
                    accountId,
                    enquireMoneyTransferBody
                ) else cardUseCase.inquireTransferMoney(
                    serialNumber,
                    accountId,
                    enquireMoneyTransferBody
                )
            if (response.isSuccessful) {
                response.body()?.let {
                    if (it.responseCode.equals(transactionTimeOutErrorCode[0])) {
                        _transferMoneyInquire.postValue(
                            Resource.error(
                                "",
                                PinCardErrorResponse(
                                    code = transactionTimeOutErrorCode[0],
                                    message = "",
                                    stan = response.body()?.stan.orEmpty()
                                )
                            )
                        )
                    } else {
                        _transferMoneyInquire.postValue(Resource.success(it))
                    }
                }
            } else {
                val errorBody = JSONObject(response.errorBody()?.string())
                var message = if (errorBody.has("errorMessage")) {
                    errorBody.getString("errorMessage")
                } else {
                    "Some Unknown Error occurred"
                }
                val stan = try {
                    errorBody.optString("system_trace_audit_number", "").orEmpty()
                } catch (e: JSONException) {
                    ""
                }
                var pinCardError: PinCardErrorResponse? = null
                val httpErrorCode = response.code()
                if (httpErrorCode == HttpURLConnection.HTTP_BAD_REQUEST) {
                    pinCardError = PinCardErrorResponse(
                        code = transactionFailedErrorCode[0],
                        message = message,
                        stan = stan
                    )
                } else if (ErrorMapping.httpErrorCode.contains(httpErrorCode.toString())) {
                    pinCardError = PinCardErrorResponse(
                        code = httpErrorCode.toString(),
                        message = message,
                        stan = stan
                    )
                } else {
                    if (errorBody.has("error")) {
                        pinCardError = Gson().fromJson(
                            errorBody.getString("error"),
                            PinCardErrorResponse::class.java
                        ).copy(stan = stan)
                    }
                    message = pinCardError?.message.orEmpty()
                }
                bwLog("Error occurred in inquire money transfer: $message, errorBody:$errorBody")
                _transferMoneyInquire.postValue(Resource.error(message, pinCardError))
            }
        } catch (e: NoConnectivityException) {
            bwLog(e)
            handleServerError(e.message, noInternetErrorCode[0])
            NetworkUtils.checkInternetHealthAndLog(e)
        } catch (e: Exception) {
            bwLog(e)
            handleServerError(e.message.toString(), errorOccurredInSystemErrorCode[0])
            NetworkUtils.checkInternetHealthAndLog(e)
        }
    }

    private fun handleServerError(msg: String, code: String) {
        _transferMoneyInquire.postValue(
            Resource.error(
                msg,
                PinCardErrorResponse(
                    code = code,
                    message = msg,""
                )
            )
        )
    }
}