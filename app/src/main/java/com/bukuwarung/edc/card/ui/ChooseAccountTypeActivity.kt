package com.bukuwarung.edc.card.ui

import android.app.Activity
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.card.ExternalPinpadActivity
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity.Companion.ADDED_BANK_ACCOUNT
import com.bukuwarung.edc.card.cashwithdrawal.viewmodel.CashWithdrawAddBankAccountViewModel
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.transfermoney.ui.AddBankAccountMoneyTransferActivity
import com.bukuwarung.edc.databinding.ActivityChooseAccountTypeBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.enums.toZohoEntryPoint
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.addbank.AddBankAccountActivity
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.StoreManagerUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityAndFinish
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint
import kotlin.collections.set

@AndroidEntryPoint
class ChooseAccountTypeActivity : BaseCardActivity() {

    private lateinit var binding: ActivityChooseAccountTypeBinding
    private var bundle: Bundle? = null
    private var transactionType = ""
    var cardErrorDialog: CardErrorDialog? = null
    private val settlementAccountViewModel: CashWithdrawAddBankAccountViewModel by viewModels()

    private val bankAddedActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val bankAccount = result.data?.getParcelableExtra<SettlementBankAccount>(
                    ADDED_BANK_ACCOUNT
                )
                binding.clAccountTypeContainer.showView()
                binding.clAccountConfirmationContainer.hideView()
            } else {
                finish()
            }
        }

    override fun setViewBinding() {
        binding = ActivityChooseAccountTypeBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        cardErrorDialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.REMOVE_CARD,
            errorCode = ErrorMapping.removeCardErrorCode[0]
        )

        if (intent.hasExtra("track")) {
            bundle = intent.getBundleExtra("track")
            transactionType = bundle!!.getString(Constant.INTENT_KEY_TRANSACTION_TYPE).toString()
            bundle?.putString(Constant.INTENT_KEY_TRANSACTION_TYPE, transactionType)
        } else {
            super.onBackPressed()
        }
        if (transactionType == TransactionType.CASH_WITHDRAWAL.type) {
            binding.clAccountTypeContainer.hideView()
            settlementAccountViewModel.processIntent(CashWithdrawAddBankAccountViewModel.AddSettlementBankAccountIntent.GetSettlementBankList)
        } else {
            binding.clAccountTypeContainer.showView()
        }

        binding.tbAccountType.apply {
            if (transactionType == TransactionType.TRANSFER_INQUIRY.type) {
                tvTitle.text = "Transfer Via Kartu"
            } else if (transactionType == TransactionType.CASH_WITHDRAWAL.type) {
                tvTitle.text = "Tarik Tunai Via Kartu"
            } else {
                tvTitle.text = "Cek Saldo Via Kartu"
            }
            btnBack.setOnClickListener {
                trackBackPressEvent(CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON)
                checkCardAndShowDialog()
            }
        }

        binding.apply {
            tvSaving.singleClick {
                showCardInformation(PaymentConst.TYPE_SAVINGS)
            }

            tvCurrent.singleClick {
                showCardInformation(PaymentConst.TYPE_CHECKING)
            }
        }
        StoreManagerUtil.setStoreState(true)
        setupInactivityDialog()

        setSupportActionBar(binding.tbAccountType.widgetToolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
    }

    override fun subscribeState() {
        observeSettlementAccount()
    }

    override fun onResume() {
        super.onResume()
        startInactivityTimer()
    }

    override fun onBackPressed() {
        trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
        checkCardAndShowDialog()
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] =
            Utils.getTransactionTypeAnalytics(transactionType)
        eventProperties[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CHOOSE_ACCOUNT_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }

    private fun showCardInformation(accountType: String) {
        val analyticsData = hashMapOf(
            CardAnalyticsConstants.SELECTED_ACCOUNT_TYPE to accountType.lowercase(),
            CardAnalyticsConstants.TRANSACTION_TYPE to Utils.getTransactionTypeAnalytics(
                transactionType
            )
        )
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_CHOOSE_ACCOUNT, analyticsData)

        bundle?.let { currentBundle ->
            currentBundle.putString("account_type", accountType)
            Log.d("account", bundle.toString())

            if (Utils.isFixedTerminal()) {
                currentBundle.putString(Constant.INTENT_KEY_TRANSACTION_TYPE, transactionType)
                openActivity(CardInfoActivity::class.java) {
                    putBundle("data", bundle)
                }
            } else {
                bundle = Bundle().apply {
                    putString("account_type", accountType)
                    putString(Constant.INTENT_KEY_TRANSACTION_TYPE, transactionType)
                }
                redirectToTransactionFlow()
            }
        }
    }

    private fun redirectToTransactionFlow() {
        Log.d("account", bundle.toString())
        when (transactionType) {
            TransactionType.TRANSFER_INQUIRY.type, TransactionType.TRANSFER_POSTING.type -> {
                openActivity(SelectBankActivity::class.java) {
                    putBundle("data", bundle)
                    putBoolean(SelectBankActivity.IS_MONEY_TRANSFER, true)
                }
            }

            TransactionType.CASH_WITHDRAWAL.type -> {
                //fetch account information
                val bankAccount = Utils.getSettlementBankAccount()
                if (bankAccount == null) {
                    openActivity(AddSettlementBankAccountActivity::class.java) {
                        putString(
                            AddSettlementBankAccountActivity.ENTRY_POINT,
                            <EMAIL>
                        )
                    }
                } else {
                    openActivity(AddBankAccountMoneyTransferActivity::class.java) {
                        putBundle("data", bundle)
                        putParcelable(AddBankAccountActivity.SELECTED_BANK, bankAccount)
                    }
                }
            }

            else -> {
                openActivity(ExternalPinpadActivity::class.java) {
                    putBundle("data", bundle)
                }
            }
        }
    }

    private fun checkCardAndShowDialog() {
        if (Utils.isFixedTerminal()) {
            checkForCardRemove(this)
        } else {
            onCardRemove()
        }
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
        finishAffinity()
    }

    private fun observeSettlementAccount() {
        lifecycleScope.launchWhenStarted {
            settlementAccountViewModel.viewEvent.collect { event ->
                when (event) {
                    is CashWithdrawAddBankAccountViewModel.Event.BankListLoaded -> {
                        handleBankAccountListResponse(event.bankList)
                    }

                    else -> {
                        Log.d("settlement_bank", "Event not handled: $event")
                    }
                }
            }
        }
    }

    private fun handleBankAccountListResponse(bankList: List<BankAccount>) {
        if (bankList.isEmpty()) {
            binding.clAccountTypeContainer.hideView()
            val prop = hashMapOf("entry_point" to "cash_withdrawal")
            Analytics.trackEvent(CardAnalyticsConstants.POP_UP_SET_SETTLEMENT_BANK, prop)
            val dialog = BukuDialog(
                context = this,
                title = "Anda Belum Memiliki\n" +
                        "Rekening Tujuan",
                subTitle = "Untuk menggunakan fitur Tarik Tunai, silakan tambahkan rekening sebagai tujuan transfer uang dari pelanggan.",
                image = R.drawable.ic_cash_withdraw,
                isLoader = false,
                btnLeftListener = {
                    openActivityAndFinish(HomePageActivity::class.java)
                },
                btnRightListener = {
                    Analytics.trackEvent(CardAnalyticsConstants.ADD_SETTLEMENT_BANK_ACCOUNT)
                    openActivityForResult(
                        AddSettlementBankAccountActivity::class.java,
                        bankAddedActivityForResult
                    ) {
                        putString(
                            AddSettlementBankAccountActivity.ENTRY_POINT,
                            <EMAIL>
                        )
                    }
                },
                btnLeftText = "Nanti Saja",
                btnRightText = "Tambah Rekening"
            )
            Utils.showDialogIfActivityAlive(this, dialog)
        } else {
            binding.clAccountTypeContainer.showView()
            val bankAccount = bankList.firstOrNull()
            Utils.setSettlementBankAccount(
                SettlementBankAccount(
                    bankCode = bankAccount?.bankCode.orEmpty(),
                    bankName = bankAccount?.bankName,
                    accountNumber = bankAccount?.accountNumber,
                    beneficiaryName = bankAccount?.accountHolderName,
                    bankLogo = bankAccount?.logo
                )
            )
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_help, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_help) {
            ZohoChat.openZohoChat(transactionType.toZohoEntryPoint())
            return true
        }
        return super.onOptionsItemSelected(item)
    }
}