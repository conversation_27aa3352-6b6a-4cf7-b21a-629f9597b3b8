package com.bukuwarung.edc.card.ui

import Resource
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.data.model.Terminal
import com.bukuwarung.edc.card.data.model.TerminalLocation
import com.bukuwarung.edc.card.domain.model.TmsError
import com.bukuwarung.edc.card.domain.model.TmsFailureType
import com.bukuwarung.edc.card.domain.model.TmsOperationResponse
import com.bukuwarung.edc.card.domain.usecase.tms.*
import com.bukuwarung.edc.global.Analytics
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TerminalManagementViewModel @Inject constructor(private val updateTerminalUseCase: UpdateTerminalUseCase,
                                                      private val updateTerminalStatusUseCase: UpdateTerminalStatusUseCase,
                                                      private val registerPairedDeviceUseCase: RegisterPairedDeviceUseCase,
                                                      private val activateTerminalUseCase: ActivateTerminalUseCase,
                                                      private val updateTerminalLocationUseCase: UpdateTerminalLocationUseCase
) : ViewModel() {

    val TAG = "TerminalManagementViewModel"

    private val _tmsResponse = MutableLiveData<Resource<Any>>()
    val tmsResponse: LiveData<Resource<Any>> get() = _tmsResponse

    private val _tmsError = MutableLiveData<TmsOperationResponse.Failure?>()

    val tmsError: LiveData<TmsOperationResponse.Failure?>
        get() = _tmsError


    fun updateTerminal(serialNumber:String, terminal: Terminal) = viewModelScope.launch {
        try {
            val terminal = updateTerminalUseCase.invoke(serialNumber, terminal).collect{ response ->
                when (response) {
                    is TmsOperationResponse.Success<*> -> {
                        _tmsResponse.postValue(Resource.success(response.data))
                    }
                    is TmsOperationResponse.Failure -> {
                        _tmsError.postValue(response)
                    }
                }
            }
        }catch (e:Exception){
            _tmsError.postValue(TmsOperationResponse.Failure(TmsError(TmsFailureType.UNKNOWN_FAILURE)))
        }
    }

    fun updateTerminalLocation(serialNumber:String, terminalLocation: TerminalLocation) = viewModelScope.launch {
        updateTerminalLocationUseCase.invoke(serialNumber,terminalLocation)
    }

    fun registerPairedDevice(serialNumber:String,btName:String, btAddress:String, androidId:String ) = viewModelScope.launch {
        registerPairedDeviceUseCase.invoke(
            serialNumber,
            Analytics.getDeviceId(),
            androidId,
            btName!!,
            btAddress!!
        ).collect { response ->
            //transaction flow is completed, incomplete-txn data should be cleared
            when (response) {
                is TmsOperationResponse.Success<*> -> {
                    _tmsResponse.postValue(Resource.success(response.data))
                }
                is TmsOperationResponse.Failure -> {
                    _tmsError.postValue(response)
                }
            }
        }
    }

    fun activateTerminal(serialNumber: String, fcmToken:String ) = viewModelScope.launch {
        activateTerminalUseCase.invoke(
            serialNumber,
            Analytics.getDeviceId(),
            fcmToken,
        ).collect { response ->
            when (response) {
                is TmsOperationResponse.Success<*> -> {
                    try {
                        val terminal = updateTerminalStatusUseCase.invoke(
                            serialNumber,
                            "Active",
                            Analytics.getDeviceId(),
                        );
                        _tmsResponse.postValue(Resource.success(terminal))
                    }catch (e:Exception){
                        _tmsError.postValue(TmsOperationResponse.Failure(TmsError(TmsFailureType.UNKNOWN_FAILURE)))
                    }
                }
                is TmsOperationResponse.Failure -> {
                    _tmsError.postValue(response)
                }
            }
        };
    }


    fun updateTerminalStatus(serialNumber: String, status: String) = viewModelScope.launch {
        try {
            val terminal = updateTerminalStatusUseCase.invoke(
                serialNumber,
                status,
                Analytics.getDeviceId(),
            );
        }catch (e:Exception){
            _tmsError.postValue(TmsOperationResponse.Failure(TmsError(TmsFailureType.UNKNOWN_FAILURE)))
        }
    }

}