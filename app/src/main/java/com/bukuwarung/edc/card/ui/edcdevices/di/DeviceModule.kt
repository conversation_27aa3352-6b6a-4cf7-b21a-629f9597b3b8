package com.bukuwarung.edc.card.ui.edcdevices.di

import com.bukuwarung.edc.card.ui.edcdevices.api.DeviceListApi
import com.bukuwarung.edc.card.ui.edcdevices.repo.DeviceListRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named


@Module
@InstallIn(SingletonComponent::class)
class DeviceModule {

    @Provides
    fun provideDeviceListRepository(deviceListApi: DeviceListApi): DeviceListRepository {
        return DeviceListRepository(deviceListApi)
    }

    @Provides
    fun provideDeviceListApi(@Named("normal") retrofit: Retrofit): DeviceListApi {
        return retrofit.create(DeviceListApi::class.java)
    }
}