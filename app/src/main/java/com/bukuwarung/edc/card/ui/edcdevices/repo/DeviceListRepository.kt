package com.bukuwarung.edc.card.ui.edcdevices.repo

import com.bukuwarung.edc.card.ui.edcdevices.api.DeviceListApi
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceListResponse
import com.bukuwarung.network.utils.ResourceState
import com.bukuwarung.network.utils.safeApiCall
import javax.inject.Inject

class DeviceListRepository @Inject constructor(private val deviceListApi: DeviceListApi) {

    suspend fun getDevice(devicePlan: String): ResourceState<DeviceListResponse> {
        return safeApiCall { deviceListApi.getDeviceList(devicePlan) }
    }
}