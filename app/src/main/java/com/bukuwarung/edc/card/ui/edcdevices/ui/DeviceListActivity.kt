package com.bukuwarung.edc.card.ui.edcdevices.ui

import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceItem
import com.bukuwarung.edc.card.ui.edcdevices.ui.adapter.DeviceListAdapter
import com.bukuwarung.edc.card.ui.edcdevices.viewmodel.DeviceListViewModel
import com.bukuwarung.edc.databinding.ActivityDevicesListBinding
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

@AndroidEntryPoint
class DeviceListActivity : AppCompatActivity(), DeviceListAdapter.OnDeviceItemClickListener {

    private val deviceListViewModel: DeviceListViewModel by viewModels()

    private lateinit var binding: ActivityDevicesListBinding

    private lateinit var deviceListAdapter :DeviceListAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDevicesListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setUpToolBar()
        setUpAdapter()
        setupRecyclerView()
        observeDeviceList()
        deviceListViewModel.getDeviceList("ALL")
    }

    private fun setUpAdapter() {
        val pairedDevices = BluetoothDevices.getPairedCardReaderList()
        Log.d("--->Device", pairedDevices.toString())
        deviceListAdapter = DeviceListAdapter(pairedDevices, this)
    }

    private fun setUpToolBar() {
        with(binding.toolbar) {
            tvTitle.text = getString(R.string.card_history)
            btnBack.singleClick { onBackPressed() }
        }
    }

    private fun setupRecyclerView() {
        binding.rvDeviceList.apply {
            layoutManager = LinearLayoutManager(this@DeviceListActivity)
            adapter = deviceListAdapter
        }
    }

    private fun observeDeviceList() {
        lifecycleScope.launch {
            deviceListViewModel.devicesFlow.collectLatest {
                if (it.isEmpty()) {
                    binding.ivEmptyView.showView()
                    binding.rvDeviceList.hideView()
                } else {
                    binding.ivEmptyView.hideView()
                    binding.rvDeviceList.showView()
                    deviceListAdapter.setDeviceList(it)
                }
            }
        }
    }

    override fun onDeviceItemClick(deviceItem: DeviceItem) {
        Utils.setPaymentAccountId(deviceItem.paymentAccountId.orEmpty())
        openActivity(CardTransactionHistoryActivity::class.java) {
            putString("device_vendor", deviceItem.vendor)
            putString("terminal_id", deviceItem.terminalId)
            putString("payment_account_id", deviceItem.paymentAccountId)
            putString("serial_number", deviceItem.serialNumber)
        }

    }
}