package com.bukuwarung.edc.card.ui.receipt

import android.graphics.Bitmap
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.card.constant.PrintState
import com.bukuwarung.edc.card.constant.ReceiptType
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.domain.usecase.EdcDeviceUseCase
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.orDefault
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CardReceiptViewModel @Inject constructor(private val deviceUseCase: EdcDeviceUseCase) :
    ViewModel() {

    var nextPendingCopy: ReceiptType = ReceiptType.RECEIPT_TYPE_BANK
    val TAG = "EDC_RECEIPT_VIEW_MODEL"
    private var triggerType = "automatic"

    sealed class State {
        data class SetPrintSuccess(val status: Boolean, val printState: PrintState) : State()
        data class SetPrintStart(val status: Boolean, val printState: PrintState) : State()
        data class SetPrintFinish(val status: Boolean) : State()
        data class SetPrintError(val errorStatus: ErrorStatus) : State()
        data class SetPrintComplete(val status: Boolean, val printState: PrintState) : State()
        data class SetAutoPrintStart(val status: Boolean, val receiptType: ReceiptType) : State()
    }

    private var handler: Handler = Handler(Looper.getMainLooper())
    private var runnable: Runnable? = null
    private val _state = MutableLiveData<State>()
    val state: LiveData<State> = _state
    private var cardReceiptData: CardReceiptResponse? = null
    private var panNumber: String = ""
    private var notes: String = ""
    private var accountType = ""
    private var hasStartedManualPrint: Boolean = false
    private var hasError: Boolean = false
    private var transactionType: Int = PrintConst.TRANSACTION_TYPE_TRANSFER
    private var cardEntryMode: Int = CARD_ENTRY_MODE_IC
    private var terminalId:String = Utils.getTerminalId()

    enum class Destination {
        TRANSACTION_FLOW,
        TRANSACTION_LISTING_FLOW
    }

    sealed class Event {
        data class OnPrintButtonClicked(
            val receiptType: ReceiptType,
            val destination: Destination,
            val bitmap: ByteArray?,
            val bukuAgenLogo: ByteArray?,
            val headerBitmap: Bitmap?,
            val bukuAgenBitmap: Bitmap?
        ) : Event()
        data class OnAutoPrint(
            val receiptType: ReceiptType,
            val destination: Destination,
            val header: ByteArray?,
            val bukuAgenLogo: ByteArray?,
            val headerBitmap: Bitmap?,
            val bukuAgenBitmap: Bitmap?
        ) : Event()
        data class StartAutoPrintTimer(val time: Long) : Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnPrintButtonClicked -> handlePrint(
                event.receiptType,
                event.destination.name,
                event.bitmap,
                event.bukuAgenLogo,
                event.headerBitmap,
                event.bukuAgenBitmap,
                false
            )

            is Event.OnAutoPrint -> handlePrint(
                event.receiptType,
                event.destination.name,
                event.header,
                event.bukuAgenLogo,
                event.headerBitmap,
                event.bukuAgenBitmap,
                true
            )

            is Event.StartAutoPrintTimer -> startAutoPrintTimer(event.time)
        }
    }

    fun init(
        cardReceiptData: CardReceiptResponse?,
        pan: String,
        cardTransactionType: Int?,
        notes: String,
        accountType: String,
        cardEntryMode: Int,
        selectedTerminalId:String = Utils.getTerminalId()
    ) {
        this.cardReceiptData = cardReceiptData
        this.terminalId = selectedTerminalId
        this.panNumber = pan
        this.nextPendingCopy = ReceiptType.RECEIPT_TYPE_BANK
        hasStartedManualPrint = false
        if (cardTransactionType != null) {
            this.transactionType = cardTransactionType
        }
        this.cardEntryMode = cardEntryMode
        this.notes = notes
        this.accountType = accountType
    }

    private fun handlePrint(
        receiptType: ReceiptType,
        destination: String,
        header: ByteArray?,
        bukuAgenLogo: ByteArray?,
        headerBitmap: Bitmap?,
        bukuAgenBitmap: Bitmap?,
        isAutoPrint: Boolean
    ) = viewModelScope.launch {
        var printingErrorMessage = ""
        triggerType = if (isAutoPrint) "automatic" else "user_action"
        val map = HashMap<String, String>()
        map[CardAnalyticsConstants.TRANSACTION_TYPE] =
            if (cardReceiptData?.balanceInformation != null) CardAnalyticsConstants.BALANCE_CHECK else CardAnalyticsConstants.TRANSFER
        map[CardAnalyticsConstants.TRANSACTION_STATUS] =
            cardReceiptData?.status.orDefault("success")
        map[CardAnalyticsConstants.TRIGGER] = triggerType
        map[CardAnalyticsConstants.ENTRY_POINT] =
            if (cardReceiptData?.balanceInformation != null) "balance_check" else "payment_page"
        map[CardAnalyticsConstants.RECEIPT_COPY] = receiptType.message
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_PRINT_RECEIPT_START, map)
        bwLog(TAG, CardAnalyticsConstants.EVENT_PRINT_RECEIPT_START)
        runnable?.let { handler.removeCallbacks(it) }
        var currentState: PrintState = getCurrentPrintStateOnPrintRequest(receiptType)
        _state.postValue(
            State.SetPrintStart(true, currentState)
        )
        if (!Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_PRINT)) {
            val maskedPan = Utils.maskCardNo(panNumber)
            // ReceiptPrintCommand.receipt is not null but ReceiptPrintCommand.orderResponse is null
            val printCommand = ReceiptPrintCommand(
                cardReceiptData,
                maskedPan,
                notes,
                receiptType,
                transactionType,
                accountType,
                destination,
                cardEntryMode,
                terminalId = terminalId
            )

            deviceUseCase.printReceipt(
                printCommand,
                header,
                bukuAgenLogo,
                headerBitmap,
                bukuAgenBitmap
            ).collect { response ->
                bwLog(TAG, "printer response $response")
                when (response) {
                    is EdcResponse.Success -> {
                        var printerResult = response.data
                        if (printerResult.success) {
                            var nextState: PrintState =
                                getNextPrintStateAfterPrintFinish(receiptType)
                            if (nextState == PrintState.PRINT_RECEIPT_COMPLETE) {
                                bwLog(TAG, "[mock print] on finish SetPrintFinish")
                                //show card removal screen, if print button clicked again after print flow completion.
                                nextPendingCopy = nextState.receiptType
                                _state.postValue(
                                    State.SetPrintFinish(true)
                                )
                            } else {
                                //current slip copy is printer, start from next copy if button clicked after dismissing confirm dialog
                                if(receiptType == ReceiptType.RECEIPT_TYPE_CUSTOMER && destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name){
                                    nextPendingCopy = PrintState.PRINT_RECEIPT_COMPLETE.receiptType
                                    _state.postValue(
                                        State.SetPrintFinish(true)
                                    )
                                }else {
                                    nextPendingCopy = nextState.receiptType
                                    bwLog(TAG, "finish SetPrintSuccess: $nextPendingCopy")
                                    _state.postValue(
                                        State.SetPrintSuccess(true, nextState)
                                    )
                                }
                            }
                        } else {
                            printingErrorMessage = printerResult.status.msg.orDefault("print_error")
                            bwLog(TAG, "finish SetPrintError: $printingErrorMessage")
                            _state.postValue(
                                State.SetPrintError(printerResult.status)
                            )
                        }
                        val map = HashMap<String, String>()
                        map[CardAnalyticsConstants.TRANSACTION_TYPE] =
                            if (cardReceiptData?.balanceInformation != null) CardAnalyticsConstants.BALANCE_CHECK else CardAnalyticsConstants.TRANSFER
                        map[CardAnalyticsConstants.TRANSACTION_STATUS] =
                            cardReceiptData?.status.orDefault("success")
                        map[CardAnalyticsConstants.TRIGGER] = triggerType
                        map[CardAnalyticsConstants.ENTRY_POINT] =
                            if (cardReceiptData?.balanceInformation != null) "balance_check" else "payment_page"
                        map[CardAnalyticsConstants.RECEIPT_COPY] = receiptType.message
                        map[CardAnalyticsConstants.PRINTING_STATUS] =
                            if (printingErrorMessage.isBlank()) "success" else "fail"
                        if (printingErrorMessage.isNotBlank()) map[CardAnalyticsConstants.FAIL_REASON] =
                            printingErrorMessage
                        Analytics.trackEvent(
                            CardAnalyticsConstants.EVENT_PRINT_RECEIPT_COMPLETED,
                            map
                        )
                        bwLog(e = Exception(CardAnalyticsConstants.EVENT_PRINT_RECEIPT_COMPLETED))
                    }

                    is EdcResponse.Failure -> {
                        //do nothing
                    }
                }
            }
        } else {

            val timer = object : CountDownTimer(3000, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    Log.d(TAG, "[mock print] printing..")
                }

                override fun onFinish() {
                    Log.d(TAG, "[mock print] on finish SetPrintComplete")
                    if (hasError) return
                    _state.postValue(
                        State.SetPrintComplete(true, currentState)
                    )
                    var nextState: PrintState = getNextPrintStateAfterPrintFinish(receiptType)
                    if (nextState == PrintState.PRINT_RECEIPT_COMPLETE) {
                        Log.d(TAG, "[mock print] on finish SetPrintFinish")
                        nextPendingCopy = ReceiptType.RECEIPT_TYPE_NONE
                        _state.postValue(
                            State.SetPrintFinish(true)
                        )
                    } else {
                        if(receiptType == ReceiptType.RECEIPT_TYPE_CUSTOMER && destination == CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name){
                            nextPendingCopy = PrintState.PRINT_RECEIPT_COMPLETE.receiptType
                            _state.postValue(
                                State.SetPrintFinish(true)
                            )
                        }else {
                            Log.d(TAG, "[mock print] on finish SetPrintSuccess")
                            nextPendingCopy = nextState.receiptType
                            _state.postValue(
                                State.SetPrintSuccess(true, nextState)
                            )
                        }
                    }
                }
            }
            timer.start()
        }
    }

    private fun startAutoPrintTimer(time: Long) = viewModelScope.launch {
        Log.d(TAG, "manual print wait finish")
        runnable = Runnable {
            if (!hasStartedManualPrint) {
                _state.postValue(
                    State.SetAutoPrintStart(true, ReceiptType.RECEIPT_TYPE_MERCHANT)
                )
            }
        }
        runnable?.let { handler.postDelayed(it, time) }
    }

    private fun getCurrentPrintStateOnPrintRequest(printReceiptType: ReceiptType): PrintState {
        when (printReceiptType) {
            ReceiptType.RECEIPT_TYPE_BANK -> {
                return PrintState.PRINTING_BANK_RECEIPT
            }

            ReceiptType.RECEIPT_TYPE_CUSTOMER -> {
                return PrintState.PRINTING_CUSTOMER_RECEIPT
            }

            ReceiptType.RECEIPT_TYPE_MERCHANT -> {
                return PrintState.PRINTING_MERCHANT_RECEIPT
            }

            ReceiptType.RECEIPT_TYPE_NONE -> {

            }
        }
        return PrintState.PRINTING_BANK_RECEIPT
    }

    private fun getNextPrintStateAfterPrintFinish(currentPrintReceiptType: ReceiptType): PrintState {
        when (currentPrintReceiptType) {
            ReceiptType.RECEIPT_TYPE_BANK -> {
                return PrintState.CONFIRM_PRINT_CUSTOMER_RECEIPT
            }

            ReceiptType.RECEIPT_TYPE_CUSTOMER -> {
                return PrintState.CONFIRM_PRINT_MERCHANT_RECEIPT
            }

            ReceiptType.RECEIPT_TYPE_MERCHANT -> {
                return PrintState.PRINT_RECEIPT_COMPLETE
            }

            ReceiptType.RECEIPT_TYPE_NONE -> {
                return PrintState.PRINT_RECEIPT_COMPLETE
            }
        }
        return PrintState.PRINT_RECEIPT_COMPLETE
    }

    private fun getNextPrintReceiptType(currentPrintReceiptType: ReceiptType): ReceiptType {
        when (currentPrintReceiptType) {
            ReceiptType.RECEIPT_TYPE_BANK -> {
                return ReceiptType.RECEIPT_TYPE_CUSTOMER
            }

            ReceiptType.RECEIPT_TYPE_CUSTOMER -> {
                return ReceiptType.RECEIPT_TYPE_MERCHANT
            }

            ReceiptType.RECEIPT_TYPE_MERCHANT -> {
                return ReceiptType.RECEIPT_TYPE_NONE
            }

            ReceiptType.RECEIPT_TYPE_NONE -> {
                return ReceiptType.RECEIPT_TYPE_BANK
            }
        }
        return ReceiptType.RECEIPT_TYPE_BANK
    }


}
