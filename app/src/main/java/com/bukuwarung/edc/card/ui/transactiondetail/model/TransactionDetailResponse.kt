package com.bukuwarung.edc.card.ui.transactiondetail.model

import com.bukuwarung.edc.card.transfermoney.model.BankDetails
import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class TransactionDetailResponse(

	@field:SerializedName("account_type")
	val accountType: String? = null,

	@field:SerializedName("response_code")
	val responseCode: String? = null,

	@field:SerializedName("dest_details")
	val destDetails: BankDetails? = null,

	@field:SerializedName("trace_number")
	val traceNumber: String? = null,

	@field:SerializedName("card_number")
	val cardNumber: String? = null,

	@field:SerializedName("balance")
	val balance: Double? = null,

	@field:SerializedName("amount")
	val amount: Double? = null,

	@field:SerializedName("source_details")
	val sourceDetails: BankDetails? = null,

	@field:SerializedName("notes")
	val notes: String? = null,

	@field:SerializedName("status")
	val status: String? = "",

	@field:SerializedName("end_user_status")
	val endUserStatus: String? = null,

	@field:SerializedName("pos_entry_mode")
	val posEntryMode: String? = "",


	@field:SerializedName("created_at")
	val createdAt: String? = "",

    @field:SerializedName("rrn")
    val rrn: String? = "",
){
	fun getTransactionAmount():Double{
		return balance ?: (amount ?: 0.0)
	}
}
