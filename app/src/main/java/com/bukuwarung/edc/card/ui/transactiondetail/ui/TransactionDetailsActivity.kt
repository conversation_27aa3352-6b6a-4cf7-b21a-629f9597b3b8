package com.bukuwarung.edc.card.ui.transactiondetail.ui

import android.annotation.SuppressLint
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity
import com.bukuwarung.edc.card.constant.CardHistoryAnalyticsConstants
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.card.constant.PrintConst.TRANSACTION_TYPE_CHECK_BALANCE
import com.bukuwarung.edc.card.constant.PrintConst.TRANSACTION_TYPE_TRANSFER
import com.bukuwarung.edc.card.constant.ReceiptType
import com.bukuwarung.edc.card.data.model.BalanceInformation
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.ui.EdcCardViewModel
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.card.ui.ErrorMapping.transactionDeclined
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.card.ui.transactiondetail.model.TransactionDetailResponse
import com.bukuwarung.edc.card.ui.transactiondetail.viewmodel.TransactionDetailViewModel
import com.bukuwarung.edc.databinding.ActivityTransactionDetailsBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.Constant.INTENT_KEY_TRANSACTION_TYPE
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.ImageUtils
import com.bukuwarung.edc.util.ToastUtil.setToast
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.util.textHTML
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import dagger.hilt.android.AndroidEntryPoint
import java.io.ByteArrayOutputStream
import kotlin.collections.set

@AndroidEntryPoint
class TransactionDetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityTransactionDetailsBinding
    private var dialog: BukuDialog? = null

    private var isPrintingCompleted = false
    private var isPrintingStarted = false

    private val transactionType by lazy {
        intent?.getStringExtra(INTENT_KEY_TRANSACTION_TYPE) ?: BALANCE_INQUIRY
    }
    private val transactionId by lazy {
        intent?.getStringExtra(INTENT_KEY_TRANSACTION_ID) ?: "12345"
    }
    //to avoid experience degrade. default value was Utils.getTerminalId() previously as well.
    private var terminalId = ""

    private val entryPoint by lazy {
        intent?.getStringExtra(INTENT_KEY_ENTRY_POINT) ?: ENTRY_POINT_TRANSACTION_HISTORY
    }

    private val transactionDetailsViewModel: TransactionDetailViewModel by viewModels()
    private val cardReceiptViewModel: CardReceiptViewModel by viewModels()
    private val edcDeviceViewModel: EdcCardViewModel by viewModels()


    companion object {
        const val INTENT_KEY_TRANSACTION_ID = "transaction_id"
        const val INTENT_KEY_TERMINAL_ID = "terminal_id"
        const val INTENT_KEY_TRANSACTION_TYPE = "type"
        const val INTENT_KEY_TRANSFER_AMOUNT = "transfer_amount"
        const val INTENT_KEY_TRANSACTION_DATE = "transaction_date"
        const val INTENT_KEY_ENTRY_POINT = "entry_point"
        const val INTENT_KEY_REFERENCE_NUMBER = "reference_number"
        const val BALANCE_INQUIRY = "BALANCE_INQUIRY"
        const val TRANSFER_POSTING = "TRANSFER_POSTING"
        const val WITHDRAW_CASH = "CASH_WITHDRAWAL_POSTING"
        const val ENTRY_POINT_TRANSACTION_HISTORY = "TRANSACTION_HISTORY"
        const val ENTRY_POINT_HOMEPAGE = "HOMEPAGE"
        const val ENTRY_POINT_PUSH_NOTIF = "PUSH_NOTIF"
    }

    @SuppressLint("WrongThread")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTransactionDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        showToolbarTitle()

        terminalId = intent?.getStringExtra(INTENT_KEY_TERMINAL_ID) ?: Utils.getTerminalId()
        val drawable = ContextCompat.getDrawable(this, R.drawable.bersama_logo1)
        val bitmapHeader = drawable?.toBitmap()
        var baos: ByteArrayOutputStream? = null
        baos = ByteArrayOutputStream()
        bitmapHeader?.compress(Bitmap.CompressFormat.PNG, 100, baos)
        val headerImage = baos.toByteArray()

        val drawableAgen = ContextCompat.getDrawable(this, R.drawable.buku_agent_print_1)
        var bitmapAgen = drawableAgen?.toBitmap()
        var baosAgen: ByteArrayOutputStream? = null
        baosAgen = ByteArrayOutputStream()
        bitmapAgen = bitmapAgen?.let { Bitmap.createScaledBitmap(it, 120, 72, true) }
        bitmapAgen?.compress(Bitmap.CompressFormat.PNG, 100, baosAgen)
        val bukuAgenLogo = baosAgen.toByteArray()

        trackTransactionDetailsData()
        initData()
        observeData()
        with(binding) {
            btnBack.singleClick {
                onBackPressed()
            }
            btnHelp.singleClick {
                ZohoChat.openZohoChat("transaction_detail")
            }
            btnPrint.singleClick {
                bwLog("TransactionDetailsActivity","btnPrint.singleClick")
                if(Utils.hasPairedPrinter()) {
                    isPrintingStarted = true
                    cardReceiptViewModel.onEventReceived(
                        CardReceiptViewModel.Event.OnPrintButtonClicked(
                            ReceiptType.RECEIPT_TYPE_CUSTOMER,
                            CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW,
                            headerImage,
                            bukuAgenLogo,
                            bitmapHeader,
                            bitmapAgen
                        )
                    )
                }else{
                    showPrinterConnectionDialog()
                }
            }
            layoutEmptyView.btnEmptyCta.singleClick {
                initData()
            }
        }
    }

    private fun trackTransactionDetailsData() {
        val map = HashMap<String, String>()
        map[CardHistoryAnalyticsConstants.EVENT_PROPERTY_TRANSACTION_TYPE] = transactionType
        Analytics.trackEvent(CardHistoryAnalyticsConstants.EVENT_VISIT_HISTORY_DETAILS, map)
    }

    private fun showToolbarTitle() {
        binding.tvTitle.text =
            if (transactionType == BALANCE_INQUIRY) getString(R.string.payment_detail) else getString(
                R.string.transaction_detail_toolbar_title
            )
    }

    private fun initData() {
        val accountId = Utils.getPaymentAccountId()
        transactionDetailsViewModel.getTransactionDetails(accountId, transactionId, transactionType)
    }

    private fun observeData() {
        val errorDialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.REMOVE_CARD,
            errorCode = ErrorMapping.removeCardErrorCode[0]
        )

        transactionDetailsViewModel.transactionDetailsStatus.observe(this) {
            when (it.status) {
                Status.LOADING -> {
                    binding.progressBar.showView()
                    binding.nsvDetails.hideView()
                    binding.layoutEmptyView.root.hideView()
                }

                Status.ERROR -> {
                    binding.layoutEmptyView.root.showView()
                    binding.layoutEmptyView.btnEmptyCta.text = getString(R.string.retry)
                    binding.progressBar.hideView()
                }

                Status.SUCCESS -> {
                    binding.nsvDetails.showView()
                    binding.layoutEmptyView.root.hideView()
                    binding.progressBar.hideView()
                    val response = it.data as? TransactionDetailResponse
                    terminalId = response?.terminalId.orEmpty().ifBlank { Utils.getTerminalId() }
                    showDetails(response)
                    loadReceiptPrintingData(response)
                }

                Status.NO_INTERNET -> {
                    binding.layoutEmptyView.root.showView()
                    binding.progressBar.hideView()
                }
            }
        }

        cardReceiptViewModel.state.observe(this) {
            val drawable = ContextCompat.getDrawable(this, R.drawable.bersama_logo1)
            val bitmapHeader = drawable?.toBitmap()

            var baos: ByteArrayOutputStream? = null
            baos = ByteArrayOutputStream()
            bitmapHeader?.compress(Bitmap.CompressFormat.PNG, 100, baos)
            val headerImage = baos.toByteArray()

            val drawableAgen = ContextCompat.getDrawable(this, R.drawable.buku_agent_print_1)
            var bitmapAgen = drawableAgen?.toBitmap()
            var baosAgen: ByteArrayOutputStream? = null
            baosAgen = ByteArrayOutputStream()
            bitmapAgen = bitmapAgen?.let { Bitmap.createScaledBitmap(it, 120, 72, true) }
            bitmapAgen?.compress(Bitmap.CompressFormat.PNG, 100, baosAgen)
            val bukuAgenLogo = baosAgen.toByteArray()

            when (it) {
                is CardReceiptViewModel.State.SetPrintStart -> {
                    bwLog(Constant.TAG_PRINT, "Print start: ${it.printState.receiptType}")
                    dialog = BukuDialog(
                        context = this@TransactionDetailsActivity,
                        title = getString(it.printState.messageTitle),
                        subTitle = getString(it.printState.messageBody),
                        image = R.drawable.ic_hourglass,
                        isLoader = true,
                        btnLeftListener = {
                            dialog?.dismiss()
                        },
                        btnRightListener = {
                        },
                        btnLeftText = getString(R.string.batal),
                        btnRightText = getString(R.string.print)
                    )
                    Utils.showDialogIfActivityAlive(this,dialog)
                }

                is CardReceiptViewModel.State.SetPrintComplete -> {
                    bwLog(Constant.TAG_PRINT, "Print complete: ${it.printState.receiptType}")
                    dialog?.dismiss()
                    isPrintingCompleted = true
                }

                is CardReceiptViewModel.State.SetPrintError -> {
                    bwLog(
                        Constant.TAG_PRINT,
                        "Print error [${it.errorStatus.errorCode}]: ${it.errorStatus.msg}"
                    )
                    dialog?.dismiss()
                    setToast(
                        this,
                        it.errorStatus.errorLevel,
                        it.errorStatus.msg,
                        this.findViewById(R.id.btn_back)
                    )
                    isPrintingCompleted = true
                }

                is CardReceiptViewModel.State.SetAutoPrintStart -> {
                    bwLog(Constant.TAG_PRINT, "Auto print start: ${it.receiptType}")
                    if(Utils.hasPairedPrinter()) {
                        cardReceiptViewModel.onEventReceived(
                            CardReceiptViewModel.Event.OnAutoPrint(
                                it.receiptType,
                                CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW,
                                headerImage,
                                bukuAgenLogo,
                                bitmapHeader,
                                bitmapAgen
                            )
                        )
                    }else{
                        showPrinterConnectionDialog()
                    }
                }

                is CardReceiptViewModel.State.SetPrintSuccess -> {
                    bwLog(Constant.TAG_PRINT, "Confirm printing : ${it.printState.receiptType}")
                    dialog?.dismiss()
                }

                is CardReceiptViewModel.State.SetPrintFinish -> {
                    dialog?.dismiss()
                    setToast(
                        this,
                        PrintConst.ALERT_INFO,
                        getString(R.string.print_finish),
                        this.findViewById(R.id.btn_back)
                    )
                    isPrintingCompleted = true
                    bwLog(Constant.TAG_PRINT, "Print complete remove card")
                    finishPrinting()
                }
            }
        }
        binding.btnShare.visibility = Utils.isCardReader().asVisibility()
        binding.btnShare.setOnClickListener {
            val bitmap = getBitmapFromView(binding.receiptLayout)
            bitmap?.let {
                if (ImageUtils.isAppInstalled(this, "com.whatsapp")) {
                    ImageUtils.shareImage(this, it, Utils.getBusinessName())
                } else {
                    Toast.makeText(this, "WhatsApp is not installed on this device", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun getBitmapFromView(view: View): Bitmap? {
        val totalHeight = view.height
        val totalWidth = view.width

        val bitmap = Bitmap.createBitmap(totalWidth, totalHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        view.draw(canvas)

        ImageUtils.addWatermark(canvas, totalWidth, totalHeight)

        return bitmap
    }

    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    private fun showPrinterConnectionDialog() {
            var dialog:BukuDialog? = null
            dialog = BukuDialog(
                context = this,
                title = "Printer Belum Terhubung",
                subTitle = "Silakan sambungkan aplikasi ke printer Anda untuk lanjut mencetak struk transaksi.",
                image = R.drawable.ic_printing,
                isLoader = false,
                btnLeftListener = {
                    dialog?.dismiss()
                },
                btnRightListener = {
                    dialog?.dismiss()
                    openActivity(SetupBluetoothDeviceActivity::class.java){
                        putString(
                            SetupBluetoothDeviceActivity.DEVICE_TYPE,
                            SetupBluetoothDeviceActivity.PRINTER
                        )
                    }
                },
                btnLeftText = getString(R.string.later),
                btnRightText = "Hubungkan"
            )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    private fun loadReceiptPrintingData(response: TransactionDetailResponse?) {
        val balanceInfoData = BalanceInformation(
            balance = response?.getTransactionAmount().toString(),
            accountType = response?.accountType
        )
        val cardReceiptData = CardReceiptResponse().apply {
            sourceDetails = response?.sourceDetails
            destDetails = response?.destDetails
            terminalId = terminalId
            merchantId = Utils.getMerchantId()
            cardNumber = response?.cardNumber
            rrn = response?.rrn
            transactionDate = response?.createdAt
            responseCode = response?.responseCode
            amount = response?.getTransactionAmount()
            balanceInformation = balanceInfoData
            systemTraceAuditNumber = response?.traceNumber
            endUserStatus = response?.endUserStatus
            transactionType = <EMAIL>
        }
        cardReceiptViewModel.init(
            cardReceiptData,
            response?.cardNumber.orEmpty(),
            if (transactionType == BALANCE_INQUIRY) TRANSACTION_TYPE_CHECK_BALANCE else TRANSACTION_TYPE_TRANSFER,
            response?.notes.orEmpty(),
            response?.accountType.orEmpty(),
            if(response?.posEntryMode?.equals("021") == true) Constants.CARD_ENTRY_MODE_MAG else Constants.CARD_ENTRY_MODE_IC,
            terminalId
        )
    }

    private fun showDetails(data: TransactionDetailResponse?) {
        showTransactionStatus(data)
        showBusinessDetails()
        data?.let {
            showTopDetails(it)
            showPendingStatusCard(it)
            showCardDetails(it)
            showCodes(it)
            showSourceBankDetails(it)
            if (transactionType == BALANCE_INQUIRY) hideDestinationBankDetails() else showDestinationBankDetails(it)
            if (it.endUserStatus == EndUserStatusValues.FAILED){
                binding.btnShare.hideView()
                binding.btnPrint.hideView()
            } else {
                if(Utils.isCardReader()) {
                    binding.btnShare.showView()
                }
                binding.btnPrint.showView()
            }
            showNotes(it)
            showTransactionDeclined(it)
        }
    }

    private fun showNotes(data: TransactionDetailResponse?) {
        if (transactionType == BALANCE_INQUIRY) {
            binding.tvNoteTitle.hideView()
            binding.tvNoteValue.hideView()
            binding.vwDivider4.hideView()

        } else {
            binding.tvNoteValue.text = data?.notes ?: "-"
        }
    }

    private fun hideDestinationBankDetails() {
        binding.layoutPaymentCode.root.hideView()
        binding.layoutPaymentCode.tvMount.hideView()
    }

    //info
    private fun showPendingStatusCard(data: TransactionDetailResponse) {
        val text = when(data.endUserStatus){
            EndUserStatusValues.PENDING_SETTLEMENT -> {
                "Pencairan uang diproses. Jangan khawatir, transaksi akan sukses."
            }

            EndUserStatusValues.PENDING_REFRESH -> {
                if (transactionType == WITHDRAW_CASH) {
                    "Transaksi Pending. Jangan menyerahkan uang terlebih dahulu jika Anda sedang melayani transaksi tarik tunai."
                } else {
                    "Transaksi Pending. Cek berkala untuk mengetahui status akhir transaksi."
                }
            }

            EndUserStatusValues.PENDING -> {
                if (transactionType == WITHDRAW_CASH) {
                    "Transaksi Pending. Jangan menyerahkan uang terlebih dahulu jika Anda sedang melayani transaksi tarik tunai."
                } else {
                    "Transaksi Pending. Cek berkala untuk mengetahui status akhir transaksi."
                }
            }

            else -> {
                ""
            }
        }
        if (text.isNotNullOrEmpty()) {
            binding.info.root.showView()
            binding.info.tvInfoText.textHTML(text)
            binding.info.tvInfoText.singleClick {
                val link: String? = Utils.extractLinkFromHtml(text)
                if (link.isNotNullOrEmpty() && (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX || Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE)) {
                    openActivity(WebviewActivity::class.java) {
                        putString(
                            ClassConstants.WEBVIEW_URL,
                            link
                        )
                    }
                } else {
                    binding.info.tvInfoText.movementMethod = LinkMovementMethod.getInstance()
                }
            }
        } else {
            binding.info.root.hideView()
        }
        binding.tvPendingTransactionFooter.hideView()
    }

    // note
    private fun showDestinationBankDetails(data: TransactionDetailResponse?) {
        data?.destDetails?.let {
            binding.layoutPaymentCode.tvAccountName.text = it.name
            binding.layoutPaymentCode.tvAccountNumber.text = it.bankName.plus(" - ").plus(it.accountNumber)
        }
        binding.layoutPaymentCode.clInvoiceCode.visibility = (transactionType == TRANSFER_POSTING).asVisibility()

        var statusWording: String? = null
        var statusIcon: Int? = null
        var guaranteeMessage: String? = null
        when (data?.endUserStatus) {
            EndUserStatusValues.SUCCESS -> {
                statusWording = "Transfer Berhasil"
                statusIcon = R.drawable.ic_check_black_bg
            }

            EndUserStatusValues.PENDING_SETTLEMENT -> {
                statusWording = "Pencairan Diproses"
                statusIcon = R.drawable.ic_hourglass_black
                guaranteeMessage = "Pencairan uang diproses. Jangan khawatir, transaksi akan sukses."
            }

            EndUserStatusValues.PENDING_REFRESH -> {
                statusWording = "Transaksi Pending"
                statusIcon = R.drawable.ic_hourglass_black
                guaranteeMessage = "Transaksi Pending. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda."
            }

            EndUserStatusValues.PENDING -> {
                statusWording = "Transaksi Pending"
                statusIcon = R.drawable.ic_hourglass_black
                guaranteeMessage = "Transaksi Pending. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda."
            }
            EndUserStatusValues.FAILED -> {
                statusWording = "Gagal"
                statusIcon = R.drawable.ic_cancle_x
                guaranteeMessage = "Silakan cek saldo secara berkala atau hubungi bank asal, jika transaksi ini terdebit."
            }
        }
        statusIcon?.let { binding.layoutPaymentCode.ivStatus.setImageDrawable(getDrawableCompat(it)) }
        statusWording?.let { binding.layoutPaymentCode.tvStatus.text = it }
        binding.layoutPaymentCode.tvMount.text = Utils.formatAmount(data?.getTransactionAmount())
        binding.layoutPaymentCode.tvMount.showView()
        guaranteeMessage?.let {
            binding.layoutPaymentCode.tvPendingInfoGuarantee.text = it
            binding.layoutPaymentCode.tvPendingInfoGuarantee.showView()
        }
    }

    private fun showSourceBankDetails(data: TransactionDetailResponse?) {
        if (transactionType == BALANCE_INQUIRY) {
            binding.tvSourceAccountBankTitle.text = getString(R.string.filter_history_saldo)
            binding.tvSourceAccountBankValue.text = Utils.formatAmount(data?.getTransactionAmount())
            binding.tvSourceAccountBankValue.setTypeface(null, Typeface.BOLD)
            binding.tvSourceAccountBankValue.setTextColor(
                ContextCompat.getColor(
                    binding.tvSourceAccountBankValue.context,
                    R.color.cta_button_text
                )
            )
            if(data?.responseCode == "E02") {
                binding.tvSourceAccountBankTitle.hideView()
                binding.tvSourceAccountBankValue.hideView()
            }

        } else {
            data?.sourceDetails?.let {
                binding.tvSourceAccountBankValue.text = "${it.bankName}-${Utils.maskSensitiveInfo(it.name.orEmpty())}-${Utils.maskSensitiveInfo(it.accountNumber.orEmpty())}"
            }
        }
    }

    private fun showCodes(data: TransactionDetailResponse?) {
        binding.tvTraceNumberRcValue.text = "${data?.traceNumber}/${data?.responseCode}"
        binding.tvDateValue.text = DateTimeUtils.getUTCTimeToLocalDateTime(
            data?.createdAt,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        binding.tvRefNumberValue.text = data?.rrn
    }

    private fun showCardDetails(data: TransactionDetailResponse?) {
        binding.tvTerminalIdValue.text = terminalId
        binding.tvMerchantIdValue.text = Utils.getMerchantId()
        binding.tvCardTypeValue.text = "DEBIT TABUNGAN"
        binding.tvCardNumberValue.text = Utils.maskCardNo(data?.cardNumber)
        binding.tvPosEntryMode.text = if (data?.posEntryMode?.equals("021") == true) "Magnetic " else "Chip"
    }

    @SuppressLint("SuspiciousIndentation")
    private fun showTopDetails(data: TransactionDetailResponse?) {
        binding.tvCurDate.text = DateTimeUtils.getUTCTimeToLocalDateTime(
            data?.createdAt,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        binding.layoutCardNumber.tvTitle.text = getString(R.string.card_number)
        binding.layoutCardNumber.tvValue.text = Utils.maskCardNo(data?.cardNumber.orEmpty())
        binding.layoutRefNumber.tvTitle.text = getString(R.string.reference_number)
        binding.layoutRefNumber.tvValue.text = data?.rrn

        binding.layoutTransactionType.tvTitle.text = getString(R.string.transaction_type)
        if (transactionType == BALANCE_INQUIRY) {
            binding.tvPaymentAmount.hideView()
            binding.layoutDestinationBank.tvTitle.hideView()
            binding.layoutDestinationBank.tvValue.hideView()
            binding.layoutTransactionType.tvValue.text = "Cek Saldo"
        } else {
            binding.tvPaymentAmount.showView()
            binding.tvPaymentAmountValue.text = Utils.formatAmount(data?.getTransactionAmount())
            binding.layoutDestinationBank.tvTitle.text = getString(R.string.beneficiary_account)
            binding.layoutDestinationBank.tvValue.text =
                data?.destDetails?.bankName.plus(" - ").plus(data?.destDetails?.accountNumber)
            if (transactionType == TRANSFER_POSTING) {
                binding.layoutTransactionType.tvValue.text = "Transfer"
            } else if (transactionType == WITHDRAW_CASH) {
                binding.layoutTransactionType.tvValue.text = "Tarik Tunai"
            } else {
                binding.layoutTransactionType.tvValue.text = transactionType
            }

        }
    }

    private fun showTransactionStatus(data: TransactionDetailResponse?) {
        var background: Int? = null
        var ivStatus: Int? = null
        var statusWording: String? = null
        when (data?.endUserStatus) {
            EndUserStatusValues.SUCCESS -> {
                statusWording = "Transfer Berhasil"
                background = R.drawable.bg_solid_green100_corner_8dp
                ivStatus = R.drawable.vector_tick_white
            }

            EndUserStatusValues.PENDING_SETTLEMENT -> {
                statusWording = "Pencairan Diproses"
                background = R.drawable.bg_solid_yellow_corner_8dp
                ivStatus = R.drawable.ic_order_in_process
            }

            EndUserStatusValues.PENDING_REFRESH -> {
                statusWording = "Pending"
                background = R.drawable.bg_solid_yellow_corner_8dp
                ivStatus = R.drawable.ic_order_in_process
            }

            EndUserStatusValues.PENDING -> {
                statusWording = "Pending"
                background = R.drawable.bg_solid_yellow_corner_8dp
                ivStatus = R.drawable.ic_order_in_process
            }
            else ->{
                statusWording = "Transaksi Gagal"
                background = R.drawable.bg_solid_red_corner_8dp
                ivStatus = R.drawable.ic_order_failed
            }
        }

        binding.clStatus.background = ContextCompat.getDrawable(binding.clStatus.context,background)

        binding.ivStatus.setImageDrawable(getDrawableCompat(ivStatus))

        binding.tvStatus.text = statusWording
    }

    private fun showBusinessDetails() {
        binding.tvStoreName.text = Utils.getBusinessNameForSelectedSerialNumber()
        binding.tvStoreName.visibility = Utils.getBusinessName().isNotBlank().asVisibility()
        binding.tvStoreAddress.text = Utils.getBusinessAddress()
        binding.tvStoreAddress.visibility = Utils.getBusinessAddress().isNotBlank().asVisibility()
    }

    private fun finishPrinting() {
        setToast(
            this,
            PrintConst.ALERT_INFO,
            getString(R.string.print_finish),
            this.findViewById(R.id.btn_back)
        )
        isPrintingCompleted = true
        Log.d(Constant.TAG_PRINT, "Print complete remove card")
    }

    override fun onBackPressed() {
        if (entryPoint.equals(ENTRY_POINT_HOMEPAGE)) {
            openActivity(CardTransactionHistoryActivity::class.java)
            finish()
        } else {
            super.onBackPressed()
        }
    }

    private fun showTransactionDeclined(data: TransactionDetailResponse){
        if (transactionDeclined.contains(data.responseCode) && data.endUserStatus == EndUserStatusValues.FAILED) {
            val text = getString(R.string.transaction_declined_message)
            binding.info.tvInfoText.textHTML(text)
            binding.info.root.showView()
        }
    }

    override fun onDestroy() {
        bwLog(e = Exception("TransactionDetailActivity-onDestroy"))
        super.onDestroy()
    }
}