package com.bukuwarung.edc.card.ui.transactiondetail.usecase

import com.bukuwarung.edc.card.ui.transactiondetail.data.repository.TransactionDetailsRepository
import javax.inject.Inject

class TransactionDetailsUseCase @Inject constructor(private val transactionDetailsRepository: TransactionDetailsRepository) {

    suspend fun getTransactionDetails(accountId: String, transactionId: String, type: String) =
        transactionDetailsRepository.getTransactionDetails(accountId, transactionId, type)
}