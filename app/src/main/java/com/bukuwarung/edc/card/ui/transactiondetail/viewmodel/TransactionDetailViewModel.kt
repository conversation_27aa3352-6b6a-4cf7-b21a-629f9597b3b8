package com.bukuwarung.edc.card.ui.transactiondetail.viewmodel

import Resource
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.ui.transactiondetail.usecase.TransactionDetailsUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TransactionDetailViewModel @Inject constructor(private val transactionDetailsUseCase: TransactionDetailsUseCase) :
    ViewModel() {

    private var _transactionDetailsStatus = MutableLiveData<Resource<Any>>()
    val transactionDetailsStatus: LiveData<Resource<Any>> get() = _transactionDetailsStatus


    fun getTransactionDetails(accountId: String, transactionId: String, type: String) {
        _transactionDetailsStatus.postValue(Resource.loading(null))
        viewModelScope.launch {
            try {
                val response =
                    transactionDetailsUseCase.getTransactionDetails(accountId, transactionId, type)
                if (response.isSuccessful) {
                    _transactionDetailsStatus.postValue(Resource.success(response.body()))
                } else {
                    _transactionDetailsStatus.postValue(Resource.error("", null))
                }

            } catch (e: Exception) {
                _transactionDetailsStatus.postValue(Resource.error(e.message.toString(), null))
            }
        }
    }
}