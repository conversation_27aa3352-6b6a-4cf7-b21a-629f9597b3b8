package com.bukuwarung.edc.di

import android.content.SharedPreferences
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.network.utils.AppProvider
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    fun providesSharedPreferences(): SharedPreferences {
        return Utils.sharedPreferences
    }

    @Provides
    fun provideConnectivity(): AppProvider = AppProviderImpl()
}