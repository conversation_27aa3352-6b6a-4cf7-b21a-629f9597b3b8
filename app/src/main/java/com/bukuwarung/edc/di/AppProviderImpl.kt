package com.bukuwarung.edc.di

import android.os.Build
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.utils.AppProvider

class AppProviderImpl : AppProvider {
    override fun isConnected(): Boolean {
        return Utils.isInternetAvailable()
    }

    override fun testingMock(): Boolean {
        return Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API)
    }

    override fun getDeviceBrand(): String {
        return Utils.getDeviceBrand().orDefault(Build.BRAND)
    }

    override fun getDeviceModel(): String {
        return Utils.getDeviceModel().orDefault(Build.MODEL)
    }

    override fun getAppVersionName(): String {
        return BuildConfig.VERSION_NAME
    }

    override fun getAppVersionCode(): String {
        return BuildConfig.VERSION_CODE.toString()
    }

    override fun bLog(msg: String?, ex: Exception?) {
        bwLog(msg, ex)
    }

    override fun getAuthToken(): String {
        return EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, "")
    }

    override fun getUserId(): String {
        return Utils.getUserId()
    }

    override fun getClientId(): String {
        return Constant.clientID
    }

    override fun getClientSecret(): String {
        return Constant.clientSecret
    }
}