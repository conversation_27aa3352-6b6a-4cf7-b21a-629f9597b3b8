package com.bukuwarung.edc.global

import com.bukuwarung.edc.BuildConfig

object Constant {

    const val NO_INTERNET_ERROR_MESSAGE = "Te<PERSON><PERSON><PERSON> kesalahan dengan permintaanmu, silakan cek koneksi internetmu"
    const val clientID = BuildConfig.CLIENT_ID
    const val START_TOKEN_REFRESH = "START_TOKEN_REFRESH"
    const val TOKEN_REFRESH_SUCCESS = "TOKEN_REFRESH_SUCCESS"
    const val TOKEN_REFRESH_FAIL = "TOKEN_REFRESH_FAIL"
    const val clientSecret = BuildConfig.CLIENT_SECRET
    const val EDC_ZOHO_APP_KEY = BuildConfig.BUKUWARUNG_EDC_ZOHO_APP_KEY
    const val EDC_ZOHO_ACCESS_KEY = BuildConfig.BUKUWARUNG_EDC_ZOHO_ACCESS_KEY
    const val BUKUAGEN_ZOHO_APP_KEY = BuildConfig.BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY
    const val BUKUAGEN_ZOHO_ACCESS_KEY = "BuildConfig.BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY"

    const val paxStoreClientId = BuildConfig.PAX_CLIENT_ID
    const val paxStoreClientSecret = BuildConfig.PAX_CLIENT_SECRET

    const val POSTMAN_URL = "https://60c8e33b-f999-4a33-9089-38e9c239231a.mock.pstmn.io"
    const val BASE_URL = BuildConfig.API_BASE_URL+"/"

    const val MIXPANEL_TOKEN = BuildConfig.MIXPANEL_TOKEN
    const val INTENT_KEY_TRANSACTION_TYPE = "bw_transaction_type"
    //dev - 4eeb93e63b515fc12d7a5d575306f4b0
    const val APP_LAUNCH = "app_launch"

    const val SESSION_TIME = 30 * 60 * 1000

    const val ACCESS_LOCATION = 212
    const val BLUETOOTH_PERMISSION = 214

    const val SMS = "SMS"
    const val WA = "WA"
    const val ANDROID = "android"
    const val ONE_SECOND = 1000L
    const val PIN_DIGITS = 4
    const val COUNTRY_CODE = "+62"
    const val DEVICE_ID = "EDC"
    const val USER_PROP_DEVICE = "device"
    const val USER_PROP_EDC_BRAND = "edc_brand"
    const val USER_PROP_TERMINAL_ID = "terminal_id"
    const val USER_PROP_SERIAL_NUMBER = "serial_number"
    const val VERIFONE = "verifone"
    const val PAX = "pax"
    const val MOREFUN_ANDROID = "morefun_android"
    const val EDC_SAKU = "Edc-Saku"
    const val USER_PROP_BW_USER_ID = "bw_user_id"
    const val USER_PROP_KYC_TIER = "kyc_tier"

    const val X_TOTAL_COUNT = "X-Total-Count"

    const val PAYMENT_ASSIST_URL = "https://bukuwarung1.zohodesk.com/portal/id/kb/bayar-dan-tagih"
    const val ZOHODESK_HOME_URL = "https://bukuwarung1.zohodesk.com/portal/id/home"

    var FAQ_MATCHING_INFO_URL = "https://bukuwarung.com/verifikasi-akun-rekening-bank/"
    const val ABOUT_ACCOUNT_VERIFICATION = "https://bukuwarung.com/yuk-verifikasi-data/"
    const val SALDO_BONUS_URL = "https://bukuwarung.com/bukupoin-berubah-jadi-saldo-bonus/"
    const val KYC_TIER_INFO_URL = "https://bukuwarung.com/verifikasi-data/"
    const val FAQ_USED_ACCOUNT_BW_URL = "https://bukuwarung.com/verifikasi-rekening-penerima/"
    const val FAQ_BLOCKED_ACCOUNT_BW_URL = "https://bukuwarung.com/rekening-penerima-terkunci/"

    const val BUKUWARUNG_EDC = "bukuwarung-edc"

    const val DEVICE_SERVICE_VERIFONE = "VERIFONE_SERVICE"
    const val DEVICE_SERVICE_PAX = "PAX_SERVICE"
    const val DEVICE_SERVICE_MF_ANDROID = "MF_SERVICE_ANDROID"

    const val UNPROCESSABLE_ENTITY = 422
    const val TAG_PRINT = "EDC_PRINT"
    const val MOBILE_PHONE = "mobile"

    var PStyle_align_left = 0x100
    var PStyle_align_center = 0x200
    var PStyle_align_right = 0x400
    var PStyle_align_remove = -0x701
    var PStyle_image_contrast_light = 0x1000
    var PStyle_image_contrast_normal = 0x2000
    var PStyle_image_contrast_heavy = 0x4000
    var PStyle_image_revert = 0x8000
    var PStyle_not_print = 0x10000000 // not print to paper

    var PStyle_not_show = 0x20000000 // not show on canvas


    var Font_default = "/system/fonts/DroidSansMono.ttf" // not set will use the system default

    var Font_Bold = "/system/fonts/DroidSans-Bold.ttf"

    const val IMAGE_QUALITY_DEFAULT = 80
    const val SD = "sd"
    const val LOW = "low"
}