package com.bukuwarung.edc.global

import android.annotation.SuppressLint
import android.app.Application
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.os.Build
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import cn.verifone.VeristoreMsgTool.client.manager.receiver.VeristoreManager
import com.bukuwarung.analytic.BtAnalytics
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.card.data.util.VeristoreBroadcastListener
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.global.Constant.DEVICE_SERVICE_MF_ANDROID
import com.bukuwarung.edc.global.Constant.DEVICE_SERVICE_PAX
import com.bukuwarung.edc.global.messaging.worker.PushFCMTokenWorkerFactory
import com.bukuwarung.edc.global.messaging.worker.scheduleWeeklyFCMTokenPush
import com.bukuwarung.edc.receiver.PackageInstallReceiver
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.network.refreshtoken.TokenManager
import com.bukuwarung.webview.BtWebView
import com.bukuwarung.zoho.BtZohoChat
import com.bureau.apponomics.ApponomicsModule
import com.bureau.base.Environment
import com.bureau.base.models.BureauConfig
import com.bureau.devicefingerprint.BureauAPI
import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.FirebaseAppCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.morefun.yapi.engine.DeviceServiceEngine
import com.morefun.yapi.engine.DeviceInfoConstrants
import com.pax.dal.IDAL
import com.pax.dal.entity.ETermInfoKey
import com.pax.market.android.app.sdk.BaseApiService
import com.pax.market.android.app.sdk.StoreSdk
import com.pax.neptunelite.api.NeptuneLiteUser
import com.vfi.smartpos.deviceservice.aidl.IDeviceService
import com.zoho.commons.InitConfig
import com.zoho.livechat.android.listeners.InitListener
import com.zoho.salesiqembed.ZohoSalesIQ
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.Executors
import javax.inject.Inject


@HiltAndroidApp
class EdcApplication: Application(), Configuration.Provider {

    @Inject
    lateinit var hiltWorkerFactory: HiltWorkerFactory

    @Inject
    lateinit var edcService: IEdcDeviceService

    @Inject
    lateinit var pushFCMTokenWorkerFactory: PushFCMTokenWorkerFactory


    companion object {
        lateinit var instance: EdcApplication
            private set
        var dal: IDAL? = null
        //if needed implement to get verifone device edcService.device
        var verifoneDevice: IDeviceService? = null
        var isVerifoneDevice: Boolean = false
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        val executorService = Executors.newCachedThreadPool()

        executorService.execute {
            RemoteConfigUtils.initialize()
        }
        FirebaseApp.initializeApp(this)
        FirebaseAppCheck.getInstance().installAppCheckProviderFactory(
            PlayIntegrityAppCheckProviderFactory.getInstance()
        )
        EncryptedPreferencesHelper.initialize(this)
        initTokenManager()
        if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE || Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX) {
            dal = getDAL()
            isVerifoneDevice = edcService.connectService()
        } else if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
            bindMfDeviceService()
            registerMfBroadcast()
        }
        Analytics.initialize(this)
        Analytics.trackEvent(Constant.APP_LAUNCH)
        Analytics.checkAppUpdate()

        try {
            initStoreSdk()
            setCrashlyticsIdentifier()
        }catch (e:Exception){
            e.printStackTrace()
        }
        Utils.getFcmDeviceId()
        initZoho()
        scheduleWeeklyFCMTokenPush(this)
        initialiseBureau()
    }

    private fun initialiseBureau() {
        BureauAPI.init(
            BureauConfig(
                credentialId = BuildConfig.BUREAU_CREDENTIAL_ID,
                environment =
                if (BuildConfig.FLAVOR_env.equals("prod", true)) Environment.ENV_PRODUCTION
                else Environment.ENV_SANDBOX,
                application = instance
            )
        ).addModule(ApponomicsModule())
    }

    private fun setCrashlyticsIdentifier(){
        FirebaseCrashlytics.getInstance().setUserId(Utils.getPhoneNumber())
        FirebaseCrashlytics.getInstance().setCustomKey("terminalId",Utils.getTerminalId())
    }

    private fun initStoreSdk() {
        Log.d("Application", "start app store")
        if(Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE) {
            VeristoreManager.startAppStore(this)
            Log.d("Application", "set listener")
            VeristoreManager.setVeristoreBroadcastReceiverListener(VeristoreBroadcastListener())
        }else if(Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX) {
            StoreSdk.getInstance()
                .init(
                    applicationContext,
                    Constant.paxStoreClientId,
                    Constant.paxStoreClientSecret,
                    object : BaseApiService.Callback {
                        override fun initSuccess() {
                            Log.i("pax_initialize", "initSuccess.")
                        }

                        override fun initFailed(e: RemoteException?) {
                            Log.i("pax_initialize", "initFailed: " + e?.message)
                        }
                    })
        } else {
            BluetoothDevices.init(this)
            CardReaderHelper.getInstance(this)
//        cardReader?.initDevice(ICommunication.BLUETOOTH_DEVICE)
            Utils.sharedPreferences.put("card_reader_iniatiated", false)
            isVerifoneDevice = false
            BtZohoChat.initialize(
                cb = { ZohoChat.openZohoChat("bluetooth_help") }
            )
            BtWebView.initialize(
                webView = { url, context ->
                    context.openActivity(WebviewActivity::class.java) {
                        putString(ClassConstants.WEBVIEW_URL, url)
                    }
                }
            )
        }
        BtAnalytics.initialize(
            cb = { name, props -> Analytics.trackEvent(name, props) },
            type = { deviceName -> Analytics.changeDeviceName(deviceName) },
            log = { msg, e -> bwLog(msg, e) }
        )
    }
    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(hiltWorkerFactory)
            .setWorkerFactory(pushFCMTokenWorkerFactory)
            .build()

    private fun getDAL(): IDAL? {
        if (dal == null) {
            try {
                val start = System.currentTimeMillis()
                dal = NeptuneLiteUser.getInstance().getDal(instance)
                val deviceSn = dal?.getSys()?.getTermInfo()?.get(ETermInfoKey.SN)
                deviceSn?.let {
                    if(it!=null && it!="") {
                        Utils.setHardwareSerialNumber(it)
                    }
                };
                Utils.sharedPreferences.put("DEVICE_SERVICE",DEVICE_SERVICE_PAX)
                Log.i("Test","get dal cost:"+(System.currentTimeMillis() - start)+" ms")
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(instance, "error occurred,DAL is null.", Toast.LENGTH_LONG).show()
            }
        }
        return dal
    }

    private fun initZoho() {
        val initConfig = InitConfig()
        val zohoAppKey = if(BuildConfig.APPLICATION_ID == "com.bukuwarung.bukuagen") BuildConfig.BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY else BuildConfig.BUKUWARUNG_EDC_ZOHO_APP_KEY
        val zohoAccessKey = if(BuildConfig.APPLICATION_ID == "com.bukuwarung.bukuagen") BuildConfig.BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY else BuildConfig.BUKUWARUNG_EDC_ZOHO_ACCESS_KEY
        ZohoSalesIQ.init(
            instance,
            zohoAppKey,
            zohoAccessKey,
            initConfig,
            object : InitListener {
                override fun onInitSuccess() {
                    ZohoSalesIQ.showLauncher(false)
                }

                override fun onInitError(errorCode: Int, errorMessage: String) {
                    //your code
                }
            })
    }

    private val TAG: String = "Emv-EdcApplication"
    private val SERVICE_ACTION = "com.morefun.ysdk.service"
    private val SERVICE_PACKAGE = "com.morefun.ysdk"

    private var deviceServiceEngine: DeviceServiceEngine? = null

    fun getDeviceService(): DeviceServiceEngine? {
        return deviceServiceEngine
    }

    fun bindMfDeviceService() {
        CoroutineScope(Dispatchers.IO).launch {
            while (true) {
                if (deviceServiceEngine != null) {
                    return@launch
                }

                val intent = Intent().apply {
                    action = SERVICE_ACTION
                    setPackage(SERVICE_PACKAGE)
                }
                Log.i(TAG, "======bindService======")
                bindService(intent, mfConnection, BIND_AUTO_CREATE)
                delay(1000)
            }
        }
    }

    private val mfConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName) {
            deviceServiceEngine = null
            bwLog(msg = "[$TAG] ======onServiceDisconnected======")
        }

        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            deviceServiceEngine = DeviceServiceEngine.Stub.asInterface(service)
            Utils.sharedPreferences.put("DEVICE_SERVICE",DEVICE_SERVICE_MF_ANDROID)
            Analytics.changeDeviceName(DEVICE_SERVICE_MF_ANDROID)
            bwLog(msg = "[$TAG] ======onServiceConnected======")

            try {
                DeviceHelper.reset()
                DeviceHelper.initDevices(this@EdcApplication)
                val devInfo = DeviceHelper.getDeviceService().devInfo
                devInfo.getString(DeviceInfoConstrants.COMMOM_SN)
                    ?.let {
                        Utils.setHardwareSerialNumber(it)
                        Analytics.trackSuperProperty("edc_brand", Constant.MOREFUN_ANDROID)
                        Analytics.trackSuperProperty("serial_number", it)
                    }
            } catch (e: RemoteException) {
                bwLog(e = e)
            }

            linkToDeath(service)
        }

        private fun linkToDeath(service: IBinder) {
            try {
                service.linkToDeath({
                    bwLog(msg = "[$TAG] ======binderDied======")
                    deviceServiceEngine = null
                    bindMfDeviceService()
                }, 0)
            } catch (e: RemoteException) {
                bwLog(e = e)
            }
        }
    }

    private fun registerMfBroadcast() {
        bwLog(msg = "[$TAG] registerBroadcast")

        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_PACKAGE_ADDED)
        intentFilter.addAction(Intent.ACTION_PACKAGE_REMOVED)
        intentFilter.addAction(Intent.ACTION_PACKAGE_REPLACED)
        intentFilter.addAction(Intent.ACTION_BOOT_COMPLETED)
        intentFilter.addAction("com.morefun.scancode.broadcast")
        intentFilter.addDataScheme("package")

        ContextCompat.registerReceiver(
            this,
            PackageInstallReceiver(),
            intentFilter,
            ContextCompat.RECEIVER_EXPORTED
        )
    }

    private fun initTokenManager() {
        TokenManager.init(
            getEncryptedString = { key, default -> EncryptedPreferencesHelper.get(key, default) },
            putEncryptedString = { key, value -> EncryptedPreferencesHelper.put(key, value) },
            getPrefString = { key, default -> Utils.sharedPreferences.getString(key, default) },
            putPrefString = { key, value -> Utils.sharedPreferences.put(key, value) }
        )
    }
}