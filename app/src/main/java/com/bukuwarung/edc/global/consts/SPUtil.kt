package com.bukuwarung.edc.global.consts

import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.put
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

//to store result cross process, replace shared preferences.
class SPUtil {
    fun setString(tag: String?, value: String?) {
        if (tag != null) {
            Utils.sharedPreferences.put(tag, value)
        }
    }

    fun getString(tag: String?): String {
        return Utils.sharedPreferences.getString(tag, "").toString()
    }

    /**
     * save List
     * @param tag
     * @param datalist
     */
    fun <T> setDataList(tag: String?, datalist: List<T>?) {
        if (null == datalist || datalist.size <= 0) return
        val gson = Gson()
        //转换成json数据，再保存
        val strJson = gson.toJson(datalist)
        if (tag != null) {
            Utils.sharedPreferences.put(tag, strJson)
        }
    }

    /**
     * get List
     * @param tag
     * @return
     */
    fun <T> getDataList(tag: String?): List<T> {
        var datalist: List<T> = ArrayList()
        val strJson: String = Utils.sharedPreferences.getString(tag, null)
            ?: return datalist
        val gson = Gson()
        datalist = gson.fromJson(strJson, object : TypeToken<List<T>?>() {}.type)
        return datalist
    }
}