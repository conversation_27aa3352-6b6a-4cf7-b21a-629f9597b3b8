package com.bukuwarung.edc.global.enums

import com.bukuwarung.edc.ZohoChatEntryPoint

/**
 * Transaction Names
 **/
enum class TransactionType(val type: String) {
    BALANCE_INQUIRY("BALANCE_INQUIRY"),
    TRANSFER_INQUIRY("TRANSFER_INQUIRY"),
    TRANSFER_POSTING("TRANSFER_POSTING"),
    CASH_WITHDRAWAL("CASH_WITHDRAWAL"),
    EDC_ORDER("EDC_ORDER"),
    CASH_WITHDRAWAL_POSTING("CASH_WITHDRAWAL_POSTING"),
    RNL_WITHDRAWAL("<PERSON>NL_W<PERSON>HDRAWAL")
}

fun String?.toZohoEntryPoint(): String {
    return when (this) {
        TransactionType.BALANCE_INQUIRY.type -> ZohoChatEntryPoint.BALANCE_INQUIRY
        TransactionType.TRANSFER_INQUIRY.type -> ZohoChatEntryPoint.TRANSFER
        TransactionType.TRANSFER_POSTING.type -> ZohoChatEntryPoint.TRANSFER
        TransactionType.CASH_WITHDRAWAL.type -> ZohoChatEntryPoint.CASH_WITHDRAWAL
        TransactionType.EDC_ORDER.type -> ZohoChatEntryPoint.EDC_ORDER
        TransactionType.RNL_WITHDRAWAL.type -> ZohoChatEntryPoint.RNL_WITHDRAWAL
        else -> ""
    }
}