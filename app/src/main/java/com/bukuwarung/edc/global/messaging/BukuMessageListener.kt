package com.bukuwarung.edc.global.messaging

import android.app.Notification.DEFAULT_ALL
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import androidx.core.app.NotificationCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.global.messaging.usecase.PostFcmTokenUseCase
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.util.Utils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.zoho.livechat.android.ZohoLiveChat
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.runBlocking
import javax.inject.Inject

@AndroidEntryPoint
class BukuMessageListener: FirebaseMessagingService() {

    @Inject
    lateinit var postFcmTokenUseCase: PostFcmTokenUseCase

    companion object {
        const val NOTIFICATION_CHANNEL_TITLE = "bukuagen_notification"
        const val SILENT_NOTIFICATION_ACTION = "silent_notification_action"
        const val FCM_DEEPLINK_KEY = "deeplink"
        const val FCM_WEB_URL_KEY = "weburl"
        const val LOGOUT_ACTION: String = "logout"
    }

    override fun onMessageReceived(message: RemoteMessage) {
        sendPushNotification(message.notification?.title, message.notification?.body, message.toIntent())
    }

    override fun onNewToken(token: String) {
        ZohoLiveChat.Notification.enablePush(token, false)
        runBlocking {
            postFcmTokenUseCase.invoke()
        }
    }

    /*
    notification from firebase console will handle by system, onMessageReceived will never
    called except when app is in foreground. so we need to handle background task in this function
     */
    override fun handleIntent(intent: Intent?) {
        try {
            intent?.extras?.let {
                if (it.getString(SILENT_NOTIFICATION_ACTION) == LOGOUT_ACTION) {
                    try {
                        Utils.clearDataBeforeLogout()
                    } catch (ex: Exception) {
                        FirebaseCrashlytics.getInstance().recordException(ex)
                    }
                    return
                }
            }
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
        super.handleIntent(intent)
    }

    private fun sendPushNotification(
        title: String?,
        description: String?,
        remoteIntent: Intent
    ) {
        val notificationIntent = Intent(applicationContext, HomePageActivity::class.java)
        remoteIntent.extras?.let { notificationIntent.putExtras(it) }
        notificationIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        val pendingIntent = PendingIntent.getActivity(
            applicationContext,
            generateUniqueNumber(),
            notificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        showDefaultNotification(
            context = application.applicationContext,
            localNotificationData = LocalNotificationData(
                title = title,
                message = description
            ),
            pendingIntent = pendingIntent
        )
    }

    private fun showDefaultNotification(
        context: Context,
        localNotificationData: LocalNotificationData,
        style: LocalNotificationStyle = LocalNotificationStyle.DEFAULT,
        pendingIntent: PendingIntent,
        notificationChannelId: String = NOTIFICATION_CHANNEL_TITLE
    ) {
        try {
            val soundUri: Uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
            val priority: Int = NotificationCompat.PRIORITY_MAX
            localNotificationData.title ?: return
            localNotificationData.message ?: return

            val iconSmall = R.drawable.ic_notif
            val builder = NotificationCompat.Builder(context, notificationChannelId)
                .setSmallIcon(iconSmall)
                .setContentTitle(localNotificationData.title)
                .setContentText(localNotificationData.message)
                .setContentIntent(pendingIntent)
                .setPriority(priority)
                .setSound(soundUri)
                .setAutoCancel(true)
                .setDefaults(DEFAULT_ALL)

            when (style) {
                LocalNotificationStyle.DEFAULT -> {
                }
                LocalNotificationStyle.BIG_TEXT -> {
                    builder.setStyle(
                        NotificationCompat.BigTextStyle().bigText(localNotificationData.message)
                    )
                }
            }

            val notification = builder.build()

            val notificationManager: NotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channelId = NOTIFICATION_CHANNEL_TITLE
                val channelName = "Bukuagen Notifications"
                val channelDescription = "Channel for Bukuagen Notifications"
                val channel = NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_DEFAULT).apply {
                    description = channelDescription
                    enableLights(true)
                    enableVibration(true)
                }
                notificationManager.createNotificationChannel(channel)
            }
            notificationManager.notify(generateUniqueNumber(), notification)
        } catch (ex: Exception) {
            // can't show notification somehow
            FirebaseCrashlytics.getInstance().recordException(ex)
            ex.printStackTrace()
        }
    }

    private fun generateUniqueNumber(): Int {
        val randomPart = (Math.random() * 1000).toInt()
        val timePart = System.currentTimeMillis()
        return (timePart + randomPart).toInt()
    }
}