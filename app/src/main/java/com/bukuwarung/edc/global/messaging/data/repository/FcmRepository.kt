package com.bukuwarung.edc.global.messaging.data.repository

import com.bukuwarung.edc.global.messaging.data.datasource.FcmRemoteDataStore
import com.bukuwarung.edc.global.messaging.data.model.FcmTokenRequest
import com.bukuwarung.network.utils.ResourceState
import com.bukuwarung.network.utils.safeApiCall


class FcmRepository(private val remoteDataSource: FcmRemoteDataStore) {

    suspend fun postToken(requestBody: FcmTokenRequest): ResourceState<Any> {
        return safeApiCall { remoteDataSource.postToken(requestBody)}
    }
}
