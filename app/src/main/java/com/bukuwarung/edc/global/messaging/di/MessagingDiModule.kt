package com.bukuwarung.edc.global.messaging.di

import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.messaging.data.datasource.FcmRemoteDataStore
import com.bukuwarung.edc.global.messaging.data.repository.FcmRepository
import com.bukuwarung.edc.global.messaging.usecase.PostFcmTokenUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class MessagingDiModule {

    @Singleton
    @Provides
    fun provideFcmRemoteDataStore(
        @Named("normal") okHttpClient: OkHttpClient,
        gsonFactory: GsonConverterFactory
    ): FcmRemoteDataStore {
        val retrofit = Retrofit.Builder().baseUrl(Constant.BASE_URL)
            .addConverterFactory(gsonFactory)
            .client(okHttpClient)
            .build()
        return retrofit.create(FcmRemoteDataStore::class.java)
    }

    @Provides
    fun provideFcmRepository(remoteDataSource: FcmRemoteDataStore) = FcmRepository(remoteDataSource)

    @Singleton
    @Provides
    fun providePostFcmTokenUseCase(repo: FcmRepository) = PostFcmTokenUseCase(repo)
}