package com.bukuwarung.edc.global.messaging.di

import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.messaging.data.datasource.FcmRemoteDataStore
import com.bukuwarung.edc.global.messaging.data.repository.FcmRepository
import com.bukuwarung.edc.global.messaging.usecase.PostFcmTokenUseCase
import com.bukuwarung.edc.global.network.ApiResponseCallAdapterFactory
import com.bukuwarung.edc.global.network.NullOnEmptyConverterFactory
import com.google.gson.GsonBuilder
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class MessagingDiModule {

    @Singleton
    @Provides
    fun provideFcmRemoteDataStore(@Named("normal") okHttpClient: OkHttpClient): FcmRemoteDataStore {
        val retrofit = Retrofit.Builder().baseUrl(Constant.BASE_URL)
            .addCallAdapterFactory(ApiResponseCallAdapterFactory())
            .addConverterFactory(NullOnEmptyConverterFactory())
            .addConverterFactory(GsonConverterFactory.create(GsonBuilder().setLenient().create()))
            .client(okHttpClient)
            .build()
        return retrofit.create(FcmRemoteDataStore::class.java)
    }

    @Provides
    fun provideFcmRepository(remoteDataSource: FcmRemoteDataStore) = FcmRepository(remoteDataSource)

    @Singleton
    @Provides
    fun providePostFcmTokenUseCase(repo: FcmRepository) = PostFcmTokenUseCase(repo)
}