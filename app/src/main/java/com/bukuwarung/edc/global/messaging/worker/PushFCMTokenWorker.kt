package com.bukuwarung.edc.global.messaging.worker

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.Worker
import androidx.work.WorkerFactory
import androidx.work.WorkerParameters
import com.bukuwarung.edc.global.messaging.usecase.PostFcmTokenUseCase
import com.bukuwarung.edc.util.Utils
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.runBlocking
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@HiltWorker
class PushFCMTokenWorker@AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val postFcmTokenUseCase: PostFcmTokenUseCase
)  : Worker(context, workerParams) {

    override fun doWork(): Result {
        return try {
            runBlocking {
                postFcmTokenUseCase.invoke()
            }
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

class PushFCMTokenWorkerFactory @Inject constructor(
    private val postFcmTokenUseCase: PostFcmTokenUseCase
) : WorkerFactory() {
    override fun createWorker(
        appContext: Context,
        workerClassName: String,
        workerParameters: WorkerParameters
    ): ListenableWorker? {
        return when (workerClassName) {
            PushFCMTokenWorker::class.java.name ->
                PushFCMTokenWorker(appContext, workerParameters, postFcmTokenUseCase)
            else -> null
        }
    }
}

fun scheduleWeeklyFCMTokenPush(context: Context) {
    if (Utils.isPaxVerifoneDevice()) {
        return
    }
    val workRequest = PeriodicWorkRequestBuilder<PushFCMTokenWorker>(14, TimeUnit.DAYS)
        .build()

    WorkManager.getInstance(context)
        .enqueueUniquePeriodicWork(
            "push_fcm_token_work",
            ExistingPeriodicWorkPolicy.KEEP,
            workRequest
        )
}