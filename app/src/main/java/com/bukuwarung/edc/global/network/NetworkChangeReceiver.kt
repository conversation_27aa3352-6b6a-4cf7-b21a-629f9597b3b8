package com.bukuwarung.edc.global.network

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkInfo
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity

class NetworkChangeReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork: NetworkInfo? = cm.activeNetworkInfo
        val isConnected: Boolean = activeNetwork?.isConnectedOrConnecting == true

        if (!isConnected) {
            // (context as? HomePageActivity)?.onInternetDisconnected()
        }else{
            (context as? HomePageActivity)?.onInternetConnected()
        }
    }
}

