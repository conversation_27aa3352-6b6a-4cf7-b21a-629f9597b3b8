package com.bukuwarung.edc.global.network

import android.content.Context
import android.util.Log
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.network.interceptors.HeadersInterceptor
import com.bukuwarung.edc.global.network.interceptors.NetworkConnectionInterceptor
import com.bukuwarung.edc.global.network.interceptors.SecuredInterceptor
import com.moczul.ok2curl.CurlInterceptor
import com.moczul.ok2curl.logger.Logger
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.Protocol
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    fun retrofit(
        baseUrl: String = provideBaseUrl(),
        isWithToken: Boolean = true,
        retryOnConnectionFailure: Boolean = true
    ): Retrofit {
        val retrofit = Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(baseUrl)

        val client = provideOkHttpClient()
        retrofit.client(client)

        return retrofit.build()
    }


    @Provides
    fun providesGsonConverterFactory(): GsonConverterFactory {
        return GsonConverterFactory.create()
    }

    @Provides
    fun provideBaseUrl() = Constant.BASE_URL

    @Provides
    @Named("postman")
    fun providePostmanUrl() = Constant.POSTMAN_URL

    @Provides
    fun provideContext(@ApplicationContext context: Context) = context

    // adding Timeout for testing.
    @Singleton
    @Provides
    @Named("normal")
    fun provideOkHttpClient() =
        OkHttpClient.Builder()
            .connectTimeout(120, TimeUnit.SECONDS)
            .readTimeout(120, TimeUnit.SECONDS)
            .callTimeout(120, TimeUnit.SECONDS)
            .writeTimeout(120, TimeUnit.SECONDS)
            .addInterceptor(NetworkConnectionInterceptor())
            .addInterceptor(HeadersInterceptor())
            .addInterceptor(SecuredInterceptor())
            .authenticator(TokenAuthenticator())
            .retryOnConnectionFailure(false)
            .addInterceptor(CurlInterceptor(object : Logger {
                override fun log(message: String) {
                    Log.d("Ok2Curl", message)
                }
            }))
            .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
            .build()

    @Singleton
    @Provides
    @Named("payment_pin")
    fun provideOkHttpClientForPin(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(NetworkConnectionInterceptor())
            .addInterceptor(HeadersInterceptor())
            .addInterceptor(SecuredInterceptor())
            .authenticator(TokenAuthenticator())
            .addInterceptor(CurlInterceptor(object : Logger {
                override fun log(message: String) {
                    Log.d("Ok2Curl", message)
                }
            }))
            .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
            .build()
    }

    @Singleton
    @Provides
    @Named("location")
    fun provideOkHttpClientForLocation() =
        OkHttpClient.Builder()
            .addInterceptor(NetworkConnectionInterceptor())
            .addInterceptor(HeadersInterceptor())
            .addInterceptor(SecuredInterceptor())
            .addInterceptor(CurlInterceptor(object : Logger {
                override fun log(message: String) {
                    Log.d("Ok2Curl", message)
                }
            }))
            .authenticator(TokenAuthenticator())
            .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
            .build()

    @Singleton
    @Provides
    @Named("normal")
    fun provideRetrofit(@Named("normal") okHttpClient: OkHttpClient, BASE_URL: String): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .build()

    @Singleton
    @Provides
    @Named("postman")
    fun provideRetrofitPostman(
        @Named("normal") okHttpClient: OkHttpClient,
        @Named("postman") postmanUrl: String
    ): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(postmanUrl)
            .client(okHttpClient)
            .build()

    @Singleton
    @Provides
    @Named("location")
    fun provideRetrofitForLocation(@Named("location") okhttpClient: OkHttpClient, BASE_URL: String): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(BASE_URL)
            .client(okhttpClient)
            .build()

    @Singleton
    @Provides
    @Named("payment_pin")
    fun provideRetrofitForPin(@Named("payment_pin") okhttpClient: OkHttpClient, BASE_URL: String): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(BASE_URL)
            .client(okhttpClient)
            .build()

    @Provides
    @Singleton
    @Named("accounting-retrofit")
    fun provideRetrofitAccounting(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.ACCOUNTING_API_BASE_URL+"/")
            .client(OkHttpClient.Builder()
                .addInterceptor(NetworkConnectionInterceptor())
                .addInterceptor(HeadersInterceptor())
                .addInterceptor(SecuredInterceptor())
                .addInterceptor(CurlInterceptor(object : Logger {
                    override fun log(message: String) {
                        Log.d("Ok2Curl", message)
                    }
                }))
                .authenticator(TokenAuthenticator())
                .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
                .build())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
}