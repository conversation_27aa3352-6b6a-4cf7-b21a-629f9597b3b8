package com.bukuwarung.edc.global.network.model.request

import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.util.Utils
import com.google.gson.annotations.SerializedName

data class SessionRequest(
    @SerializedName("deviceId")
    private val deviceId: String,
    @SerializedName("token")
    private val token: String,
    @SerializedName("register")
    private val register: <PERSON><PERSON><PERSON>,
    @SerializedName("deviceModel")
    private val deviceModel: String,
    @SerializedName("deviceBrand")
    private val deviceBrand: String,
    @SerializedName("userId")
    private val userId: String? = Utils.getUserId(),
    @SerializedName("client")
    val clientId: String? = Constant.clientID,
    @SerializedName("clientSecret")
    val clientSecret: String? = Constant.clientSecret
)
