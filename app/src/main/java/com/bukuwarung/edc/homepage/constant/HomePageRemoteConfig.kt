package com.bukuwarung.edc.homepage.constant

import com.bukuwarung.edc.homepage.data.model.EdcLeaderboardConfigContents
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.Utils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

object HomePageRemoteConfig {

    const val EDC_HOMEPAGE_SCHEMA = "edc_homepage_schema"
    const val EDC_SAKU_HOMEPAGE_SCHEMA = "edc_saku_homepage_schema"
    const val HOMEPAGE_SCHEMA_FAILSAFE = "homepage_Schema_failsafe"
    const val EDC_HOMEPAGE_SALDO = "edc_homepage_saldo"
    const val EDC_HOMEPAGE_TICKER = "edc_homepage_ticker"
    const val EDC_HOMEPAGE_HISTORY = "edc_homepage_history"
    const val EDC_HOMEPAGE_CARD = "edc_homepage_card"
    const val EDC_HOMEPAGE_TILES_PER_ROW = "edc_homepage_tiles_per_row"
    const val TICKER_FRAGMENT = "ticker_fragment_edc"
    const val HOUR = "hour"
    const val MINS = "mins"
    const val EDC_LEADERBOARD_CONFIG_NAME = "edc_leaderboard"
    const val SAKU_APP_UPDATE_VERSION_CODE = "saku_app_update_version_code"
    const val EDC_SAKU_ORDER_WELCOME_SCREEN = "edc_saku_order_welcome_screen"

    const val HOMEPAGE_SCHEMA_VAL_FAILSAFE = """
        [
  {
    "block_name": "edc_homepage_saldo",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 1
  },
  {
    "block_name": "edc_homepage_history",
    "block_start_version": 5,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 2
  },
  {
    "block_name": "edc_homepage_card",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 3
  },
  {
    "block_name": "edc_homepage_ppob",
    "block_start_version": 7,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 4
  }
]
    """

    const val EDC_LEADERBOARD_CONFIG_VAL = """
        {
  "campaign_name": "EDC_LEADERBOARD_CAMPAIGN",
  "banner_url": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/leaderboard_banner.png",
  "redirection_url": "https://api-v4.bukuwarung.com/mx-mweb/edc/campaign/leaderboard?campaign=EDC_LEADERBOARD_CAMPAIGN"
}
    """

    const val HOMEPAGE_SCHEMA_VAL = """
        [
  {
    "block_name": "edc_homepage_saldo",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 1
  },
  {
    "block_name": "edc_homepage_history",
    "block_start_version": 5,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 2
  },
  {
    "block_name": "edc_homepage_card",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 3
  },
  {
    "block_name": "edc_homepage_ppob",
    "block_start_version": 25,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 4
  }
]
    """

    const val HOMEPAGE_TICKER = """
        {
  "body_name": "edc_homepage_ticker",
  "body_analytics_name": "ticker",
  "body_title": "",
  "body_subtitle": "",
  "body_rank": 1,
  "body_type": 3,
  "body_content": [
    {
      "display_name": "",
      "analytics_name": "",
      "icon": "",
      "rank": 1,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "",
      "is_new": true
    }
  ]
}
    """

    const val HOMEPAGE_HISTORY = """
        {
  "body_name": "edc_homepage_history",
  "body_analytics_name": "history",
  "body_title": "Riwayat Transaksi",
  "body_subtitle": "",
  "body_rank": 3,
  "body_type": 4,
  "body_content": [
    {
      "display_name": "Transaksi Digital",
      "analytics_name": "view_digital_transactions",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/clock.png",
      "rank": 1,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.payments.ui.history.OrderHistoryActivity",
      "show_on_verifone": true,
      "show_on_pax": true,
      "is_new": true
    },
    {
      "display_name": "Transaksi MiniATM",
      "analytics_name": "view_card_transactions",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/credit-card.png",
      "rank": 2,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "show_on_verifone": true,
      "show_on_pax": true,
      "deeplink_type": "app",     
      "deeplink_url": "com.bukuwarung.edc.card.ui.edcdevices.ui.DeviceListActivity",
      "is_new": true
    },
    {
      "display_name": "Transaksi MiniATM",
      "analytics_name": "view_card_transactions",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/credit-card.png",
      "rank": 3,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "show_on_verifone": false,
      "show_on_pax": true,
      "deeplink_type": "mweb",
      "deeplink_url": "https://api-dev.bukuwarung.com/mx-mweb/edc/dashboard",
      "is_new": true
    }
  ],
  "deeplink_type": "app",
  "deeplink_url": "com.bukuwarung.edc.homepage.ui.history.HistoryActivity"
}
    """

    const val HOMEPAGE_CARD = """
{
  "body_name": "edc_homepage_card",
  "body_analytics_name": "cards",
  "body_title": "MiniATM",
  "body_subtitle": "",
  "body_rank": 3,
  "body_type": 2,
  "body_content": [
    {
      "display_name": "Transfer",
      "analytics_name": "transfer",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/ic_transfer.webp",
      "rank": 1,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.card.ui.CardReaderInstructionActivity?mode=TRANSFER_INQUIRY?redirect=com.bukuwarung.edc.card.ui.CardReaderInstructionActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Cek Saldo",
      "analytics_name": "balance_check",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/ic_check.webp",
      "rank": 2,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.card.ui.CardReaderInstructionActivity?mode=BALANCE_INQUIRY?redirect=com.bukuwarung.edc.card.ui.CardReaderInstructionActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Riwayat Transaksi Kartu",
      "analytics_name": "card_transaction_history",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/ic_history.webp",
      "rank": 3,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Tarik Tunai",
      "analytics_name": "cash_withdrawal",
      "icon": "https://storage.googleapis.com/temp-image-test/cash_withdraw.png",
      "rank": 4,
      "is_visible": true,
      "start_version": 23300,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.card.ui.CardReaderInstructionActivity?mode=CASH_WITHDRAWAL",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Pengaturan",
      "analytics_name": "settings",
      "icon": "https://storage.googleapis.com/ledger-fcc1e.appspot.com/edc/edc_homepage_setting.webp",
      "rank": 5,
      "is_visible": true,
      "start_version": 23300,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.settings.ui.setting.SettingActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    }
  ]
}
    """

    // homepage section bodies
    const val HOMEPAGE_SALDO_BODY_CONTENT = """
        {
  "body_name": "edc_homepage_saldo",
  "body_analytics_name": "saldo",
  "body_title": "Transaksi Tanpa Kartu",
  "body_subtitle": "",
  "body_rank": 1,
  "body_type": 1,
  "no_of_columns": 2,
  "body_content": [
    {
      "display_name": "Top Up",
      "analytics_name": "saldo_topup",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/plus-circle.png",
      "rank": 1,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.payments.ui.saldo.SaldoTopupActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Bayar",
      "analytics_name": "payment_out",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/trailing-icon.png",
      "rank": 2,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    }
  ]
}
    """

    const val HOMEPAGE_TILE_BODY_CONTENT = """
        {
            "body_name": "tile",
            "body_analytics_name": "tiles",
            "body_title": "Menu",
            "body_subtitle": "",
            "body_rank": 3,
            "body_type": 2,
            "body_content": [
                {
                    "display_name": "Pembayaran",
                    "analytics_name": "pembayaran",
                    "icon": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/EDC%2FPembayaran.webp?alt=media&token=f3048295-2da4-434f-ac8a-dae6bc22f00f&_gl=1*y9tplk*_ga****************************.*_ga_CW55HF8NVT***********************************************..",
                    "rank": 1,
                    "is_visible": true,
                    "start_version": 0,
                    "end_version": -1,
                    "deeplink_type": "app",
                    "deeplink_url": "com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity",
                    "is_new": true
                },
                {
                    "display_name": "Saldo Top-up",
                    "analytics_name": "saldo_top_up",
                    "icon": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/EDC%2Fsaldo.webp?alt=media&token=488deccf-4700-42db-9a3f-e922abc9a086&_gl=1*nog6t2*_ga****************************.*_ga_CW55HF8NVT***********************************************..",
                    "rank": 2,
                    "is_visible": true,
                    "start_version": 0,
                    "end_version": -1,
                    "deeplink_type": "app",
                    "deeplink_url": "com.bukuwarung.edc.payments.ui.saldo.SaldoTopupActivity",
                    "is_new": true
                },
                {
                    "display_name": "Riwayat Transaksi Kartu",
                    "analytics_name": "riwayat",
                    "icon": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/EDC%2Fedc%20(1).webp?alt=media&token=753d7aa4-8bff-4bd8-a00b-eab007913a7e&_gl=1*1nf04wi*_ga****************************.*_ga_CW55HF8NVT*MTY5ODgyMTk1NC4xODQuMS4xNjk4ODIyNDAwLjUzLjAuMA..",
                    "rank": 3,
                    "is_visible": true,
                    "start_version": 0,
                    "end_version": -1,
                    "deeplink_type": "app",
                    "deeplink_url": "com.bukuwarung.edc.webview.WebviewActivity?webview_url=https://api-v4.bukuwarung.com/mx-mweb/edc/dashboard",
                    "is_new": true
                },
                {
                    "display_name": "Riwayat Pembayaran",
                    "analytics_name": "riwayat_pembayaran",
                    "icon": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/EDC%2Friwayat.png?alt=media&token=27d55a51-62d8-4e3f-a814-95b3e19f4b95&_gl=1*11lhvi4*_ga****************************.*_ga_CW55HF8NVT*MTY5ODgyMTk1NC4xODQuMS4xNjk4ODIyMzMxLjYwLjAuMA..",
                    "rank": 4,
                    "is_visible": true,
                    "start_version": 0,
                    "end_version": -1,
                    "deeplink_type": "app",
                    "deeplink_url": "com.bukuwarung.edc.payments.ui.history.OrderHistoryActivity",
                    "is_new": true
                },
                {
                    "display_name": "Pengaturan",
                    "analytics_name": "pengaturan",
                    "icon": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/EDC%2Fpengaturan.webp?alt=media&token=0dc8faf3-8227-4bfc-8b13-77f29a0864de&_gl=1*ms7gyl*_ga****************************.*_ga_CW55HF8NVT*MTY5ODgyMTk1NC4xODQuMS4xNjk4ODIyMzY5LjIyLjAuMA..",
                    "rank": 5,
                    "is_visible": true,
                    "start_version": 0,
                    "end_version": -1,
                    "deeplink_type": "app",
                    "deeplink_url": "com.bukuwarung.edc.settings.ui.setting.SettingActivity",
                    "is_new": true
                }
            ]
        }
    """
    const val SAKU_APP_UPDATE_VERSION_CODE_VAL =
        """
            {
                "hardUpdateVersionCode": 0,
                 "softUpdateVersionCode": 0
            }
        """

    const val EDC_SAKU_ORDER_WELCOME_SCREEN_FAILSAFE =
            """
            {
      "edc_screen_title": "Saatnya mulai #BebasTambahCuan Pakai EDC BukuAgen!",
      "edc_screen_image": "",
      "edc_screen_bullet_points": [
        {
          "icon_url": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/ic_transfer.webp",
          "description": "<b>Transfer</b>・Layani transaksi transfer ke sesama atau beda bank"
        },
        {
          "icon_url": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/ic_check.webp",
          "description": "<b>Tarik Tunai &amp; Cek Saldo・</b>Layani transaksi tarik tunai &amp; cek saldo dari rekening pelanggan"
        },
        {
          "icon_url": "https://storage.googleapis.com/temp-image-test/cash_withdraw.png",
          "description": "<b>PKH・</b>Layani transaksi pencarian &amp; cek saldo dana bantuan PKH"
        }
      ],
      "edc_screen_learn_more": {
        "text": "Pelajari lebih lanjut",
        "redirection_url": "https://bukuwarung.com/bukuagen/edc-saku/"
      },
      "edc_screen_buttons": [
        {
          "text": "Beli EDC",
          "redirection_url": "https://api-dev.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN",
          "is_visisble": true
        },
        {
          "text": "Saya Sudah Beli",
          "redirection_url": "https://api-dev.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN/BUKUWARUNG",
          "is_visible": false
        }
      ]
    }
        """

    fun getHomePageSchema() = if(Utils.isCardReader()) RemoteConfigUtils.remoteConfig.getString(EDC_SAKU_HOMEPAGE_SCHEMA) else RemoteConfigUtils.remoteConfig.getString(EDC_HOMEPAGE_SCHEMA)

    fun getAppUpdateVersionCode() = RemoteConfigUtils.remoteConfig.getString(SAKU_APP_UPDATE_VERSION_CODE)


    fun getLeaderboardInfo(): EdcLeaderboardConfigContents {
        return try {
            val type = object : TypeToken<EdcLeaderboardConfigContents>() {}.type
            val json =
                RemoteConfigUtils.remoteConfig.getString(EDC_LEADERBOARD_CONFIG_NAME)
            Gson().fromJson(json, type)
        } catch (ex: Exception) {
            EdcLeaderboardConfigContents()
        }

    }

    fun getHomePageBody(bodyName: String) = RemoteConfigUtils.remoteConfig.getString(bodyName)

    fun getNumberOfColumns() = RemoteConfigUtils.remoteConfig.getString(EDC_HOMEPAGE_TILES_PER_ROW)

    fun getTickerFragmentData() : String =
        RemoteConfigUtils.remoteConfig.getString(TICKER_FRAGMENT)

    fun getLocationSendDurationHours() = RemoteConfigUtils.remoteConfig.getString(HOUR)

    fun getLocationSendMins() = RemoteConfigUtils.remoteConfig.getString(MINS)

    fun getEdcOrderWelcome() = RemoteConfigUtils.remoteConfig.getString(EDC_SAKU_ORDER_WELCOME_SCREEN)
}