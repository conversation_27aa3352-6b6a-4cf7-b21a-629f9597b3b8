package com.bukuwarung.edc.homepage.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class EdcWelcomeScreenData(
    @SerializedName("edc_screen_title")
    val edcScreenTitle: String,
    @SerializedName("edc_screen_image")
    val edcScreenImage: String,
    @SerializedName("edc_screen_bullet_points")
    val bulletPoints: List<BulletPoint>,
    @SerializedName("edc_screen_learn_more")
    val learnMore: LearnMore,
    @SerializedName("edc_screen_buttons")
    val buttons: List<Button>
)

@Keep
data class BulletPoint(
    @SerializedName("icon_url")
    val iconUrl: String,

    val description: String
)

@Keep
data class LearnMore(
    val text: String,
    @SerializedName("redirection_url")
    val redirectionUrl: String
)

@Keep
data class Button(
    val text: String,
    @SerializedName("redirection_url")
    val redirectionUrl: String,
    @SerializedName("is_visible")
    val isVisible: Boolean
)
