package com.bukuwarung.edc.homepage.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class HomePageBody (
    @SerializedName("body_name")
    val bodyName: String?,
    @SerializedName("show_on_verifone")
    val shouldShowOnVerifone: Boolean = true,
    @SerializedName("show_on_pax")
    val shouldShowOnPax: Boolean = true,
    @SerializedName("body_analytics_name")
    val bodyAnalyticsName: String?,
    @SerializedName("body_title")
    val bodyTitle: String?,
    @SerializedName("body_subtitle")
    val bodySubtitle: String?,
    @SerializedName("body_rank")
    val bodyRank: Int = 1,
    @SerializedName("body_type")
    val bodyType: Int = 1,
    @SerializedName("body_content")
    var bodyContent: List<HomePageBodyContents?>? = null,
    @SerializedName("deeplink_type")
    val deepLinkType: String? = null,
    @SerializedName("deeplink_url")
    val deepLinkUrl: String? = null,
    @SerializedName("no_of_columns")
    val noOfColumns: Int? = null,
    @SerializedName("auto_rotate_banners")
    val autoRotateBanners: Boolean? = null,
    @SerializedName("banner_duration")
    val bannerDuration: Long? = null
) : Parcelable
