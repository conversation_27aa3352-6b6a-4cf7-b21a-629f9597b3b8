package com.bukuwarung.edc.homepage.data.repository

import com.bukuwarung.edc.homepage.data.datasource.HomePageDataSource
import com.bukuwarung.edc.homepage.data.model.LogonDataRequest
import javax.inject.Inject

class HomePageRepository @Inject constructor(private val homePageDataSource: HomePageDataSource) {

    suspend fun fetchUserDetails(serialNumber:String?) = homePageDataSource.fetchUserDetails(serialNumber)

    suspend fun fetchLogonData(accountId: String, logonDataRequest: LogonDataRequest) = homePageDataSource.fetchLogonData(accountId, logonDataRequest)

    suspend fun userWhitelistedForLeaderboard(campaign: String, checkWhitelist: Boolean) = homePageDataSource.userWhitelistedForLeaderboard(campaign, checkWhitelist)

    suspend fun getDevices(devicePlan: String) = homePageDataSource.getDeviceList(devicePlan)

}