package com.bukuwarung.edc.homepage.data.repository

import com.bukuwarung.edc.homepage.data.datasource.HomePageDataSource
import com.bukuwarung.edc.homepage.data.model.LogonDataRequest
import com.bukuwarung.edc.login.data.model.UserDetail
import com.bukuwarung.network.utils.ResourceState
import com.bukuwarung.network.utils.safeApiCall
import javax.inject.Inject

class HomePageRepository @Inject constructor(private val homePageDataSource: HomePageDataSource) {

    suspend fun fetchUserDetails(serialNumber: String?): ResourceState<UserDetail> {
        return safeApiCall { homePageDataSource.fetchUserDetails(serialNumber) }
    }

    suspend fun fetchLogonData(
        serialNumber: String,
        accountId: String,
        logonDataRequest: LogonDataRequest
    ) = homePageDataSource.fetchLogonData(serialNumber, accountId, logonDataRequest)

    suspend fun userWhitelistedForLeaderboard(campaign: String, checkWhitelist: Boolean) = homePageDataSource.userWhitelistedForLeaderboard(campaign, checkWhitelist)

    suspend fun getDevices(devicePlan: String) = homePageDataSource.getDeviceList(devicePlan)

}