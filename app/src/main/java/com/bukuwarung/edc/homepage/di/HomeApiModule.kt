package com.bukuwarung.edc.homepage.di

import com.bukuwarung.edc.homepage.data.datasource.HomePageDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class HomeApiModule {

    @Provides
    @Singleton
    fun provideHomePageDataSource(@Named("normal") retrofit: Retrofit): HomePageDataSource
        = retrofit.create(HomePageDataSource::class.java)
}