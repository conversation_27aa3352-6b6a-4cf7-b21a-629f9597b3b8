package com.bukuwarung.edc.homepage.ui.banners

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.BannerItemBinding
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.util.listen
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable


class HomePageBannersAdapter(
    private var itemList: List<TileData?>,
    private val context: Context,
    val getOnClickData: (TileData?, Int) -> Unit
) : RecyclerView.Adapter<HomePageBannersAdapter.HomePaymentsBannerViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): HomePaymentsBannerViewHolder {
        return HomePaymentsBannerViewHolder(
            BannerItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        ).listen { position, type ->
            getOnClickData(itemList[position], position)
        }
    }

    override fun onBindViewHolder(holder: HomePaymentsBannerViewHolder, position: Int) {
        holder.bind(itemList[position])
    }

    override fun getItemCount(): Int {
        return itemList.size
    }

    inner class HomePaymentsBannerViewHolder(private val binding: BannerItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()

        private val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }

        fun bind(body: TileData?) {
            with(binding) {
                if (isValidContextForGlide(context))
                    Glide.with(context).load(body?.image).placeholder(shimmerDrawable)
                        .into(ivBanner)
            }
        }
    }

    private fun isValidContextForGlide(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        if (context is Activity) {
            if (context.isDestroyed || context.isFinishing) {
                return false
            }
        }
        return true
    }
}