package com.bukuwarung.edc.homepage.ui.history

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.bukuwarung.edc.databinding.FragmentHistoryHomepageBinding
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.ui.home.HomePageViewModel
import com.bukuwarung.edc.payments.constant.PaymentConst.STATUS_PAID
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeHistoryFragment : Fragment() {

    companion object {
        private const val TYPE = "type"
        private const val URL = "url"
        private const val CONTENTS = "contents"
        const val SHOW_RED_DOT ="SHOW_RED_DOT"
    }

    lateinit var binding: FragmentHistoryHomepageBinding
    private val homepageViewModel: HomePageViewModel by activityViewModels()
    // show red dot when there are orders for which KYC,KYB is not done.
    private var showRedDot = false


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentHistoryHomepageBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val bodyContents = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(CONTENTS, HomePageBody::class.java)
        } else {
            arguments?.getParcelable(CONTENTS)
        }

        observeDate()

        binding.root.singleClick {
            requireContext().openActivity(Utils.getRouterClass()) {
                putString(TYPE, bodyContents?.deepLinkType)
                putString(URL, bodyContents?.deepLinkUrl)
                putParcelable(CONTENTS, bodyContents)
                putBoolean(SHOW_RED_DOT, showRedDot)
            }
        }

    }

    private fun observeDate(){
        // get orders for which KYC,KYB is not done.
        homepageViewModel.orderDetails.observe(viewLifecycleOwner){
            val isPaid = it?.data?.paymentDetails?.status?.equals(STATUS_PAID, true).isTrue
            val isKycDone = it?.data?.checks?.kybDone.isTrue
            val isKybDone = it?.data?.checks?.kybDone.isTrue
            val isBankAdded = it?.data?.checks?.bankAccountAdded.isTrue
            if ( it?.result.isTrue && isPaid && (!isKybDone || !isKycDone || !isBankAdded)
            ) {
                binding.viewRedDot.showView()
                showRedDot = true
            }
        }
    }

}