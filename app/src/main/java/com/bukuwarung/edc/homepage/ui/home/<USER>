package com.bukuwarung.edc.homepage.ui.home

import DynamicKeyboardBody
import android.annotation.SuppressLint
import android.content.Intent
import android.content.IntentFilter
import android.content.IntentSender
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.bluetooth_printer.activities.print.setup.BluetoothDeviceScanActivity.AppType
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.ZohoChatEntryPoint
import com.bukuwarung.edc.card.activation.ui.ActivatedStatus
import com.bukuwarung.edc.card.activation.ui.EdcActivationBottomSheet
import com.bukuwarung.edc.card.activation.ui.constant.CardActivationConstants
import com.bukuwarung.edc.card.data.model.ReversalStatus
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.DYNAMIC_KEYS
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.EDC_ORDER_WARRANTY_NUDGE
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.IS_FROM_FIREBASE
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.KEYBOARD_TYPE
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.card.ui.ErrorMapping.terminalIdOrDeviceSerialBlockedCodes
import com.bukuwarung.edc.card.ui.transactiondetail.ui.TransactionDetailsActivity
import com.bukuwarung.edc.databinding.ActivityHomepageBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.messaging.BukuMessageListener.Companion.FCM_DEEPLINK_KEY
import com.bukuwarung.edc.global.messaging.BukuMessageListener.Companion.FCM_WEB_URL_KEY
import com.bukuwarung.edc.global.messaging.usecase.PostFcmTokenUseCase
import com.bukuwarung.edc.global.network.NetworkChangeReceiver
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.homepage.data.model.AppUpdateConfigContents
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.data.model.HomePageSchema
import com.bukuwarung.edc.homepage.data.model.TickerFragmentData
import com.bukuwarung.edc.homepage.ui.BTRouterActivity
import com.bukuwarung.edc.homepage.ui.banners.HomePageBannersFragment
import com.bukuwarung.edc.homepage.ui.history.HomeHistoryFragment
import com.bukuwarung.edc.homepage.ui.home.bottomsheet.KycKybBottomSheet
import com.bukuwarung.edc.homepage.ui.home.bottomsheet.NoSakuDeviceRegisteredBS
import com.bukuwarung.edc.homepage.ui.home.dialog.KomisiAgenInfoDialog
import com.bukuwarung.edc.homepage.ui.order.EdcOrderFragment
import com.bukuwarung.edc.homepage.ui.saldo.HomeKomisiAgenFragment
import com.bukuwarung.edc.homepage.ui.saldo.HomeSaldoFragment
import com.bukuwarung.edc.homepage.ui.ticker.HomeTickerFragment
import com.bukuwarung.edc.homepage.ui.tile.HomePageTileFragment
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.onboarding.ui.WelcomeActivity
import com.bukuwarung.edc.order.orderdetail.ui.EdcOrderDetailsActivity
import com.bukuwarung.edc.payments.constant.PaymentConst.STATUS_PAID
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.payments.ui.history.KomisiAgenHistoryActivity
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.StoreManagerUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.clear
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.getClassTag
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.util.withArgs
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.webview.constant.ClassConstants.HIDE_TOOLBAR
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.interceptors.NoConnectivityException
import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.refreshtoken.TokenManager
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.SocketTimeoutException
import javax.inject.Inject
import kotlin.collections.set

@AndroidEntryPoint
class HomePageActivity : AppCompatActivity() {

    companion object{
        const val REQUEST_CODE_UPDATE = 100
        const val EDC_SAKU_HOMEPAGE_CARD = "edc_saku_homepage_card"
        const val EDC_HOMEPAGE_PPOB = "edc_homepage_ppob"
    }

    @Inject
    lateinit var postFcmTokenUseCase: PostFcmTokenUseCase

    private lateinit var binding: ActivityHomepageBinding

    private val handler = Handler(Looper.getMainLooper())

    private lateinit var networkChangeReceiver: NetworkChangeReceiver

    private val fragmentPool = mutableListOf<Fragment>()

    private val viewModel: HomePageViewModel by viewModels()

    private val versionCode by lazy { BuildConfig.VERSION_CODE }

    private var haveSakuDevice: Boolean? = null
    private var ppobViewPosition = 0
    private var cardViewPosition = 0

    private var lastBackPressTime: Long = 0
    private val DOUBLE_BACK_PRESS_THRESHOLD: Long = 2000 // 2 seconds
    private val kycTier by lazy {
        Utils.getKycTierFromToken(
            EncryptedPreferencesHelper.get(
                BUKUWARUNG_TOKEN,
                ""
            )
        )
    }

    var isTerminalActive = true
    var isDeviceActivationPending = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StoreManagerUtil.setStoreState(false)

        Analytics.trackSuperProperty("app_version_name", BuildConfig.VERSION_NAME)
        val appFlow = if(Utils.isAtmPro()) "MiniATMpro" else "BukuAgen"
        Analytics.trackSuperProperty("app_flow", appFlow)
        if(Utils.getHardwareSerialNumber().isNotNullOrBlank()){
            Analytics.trackSuperProperty("edc_brand", Utils.getDeviceTypeBySerialNumber())
        }
        binding = ActivityHomepageBinding.inflate(layoutInflater)
        binding.tbHomepage.tvTitle.text = "BukuAgen EDC"
        setContentView(binding.root)

        binding.tbHomepage.ivSettings.visibility = Utils.isCardReader().asVisibility()

        networkChangeReceiver = NetworkChangeReceiver()

        // Register the receiver with the intent filter for connectivity changes
        val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
        registerReceiver(networkChangeReceiver, filter)
        val callTrackAuthEvent = intent.getBooleanExtra("call_track_event", false).isTrue

        viewModel.apply {
            lifecycleScope.launch {
                try {
                    setJanusAccount()
                    if (callTrackAuthEvent) trackAuthEvent()
                    val campaignName = HomePageRemoteConfig.getLeaderboardInfo().campaignName.orEmpty()
                    val isValidateFromBackend = HomePageRemoteConfig.getLeaderboardInfo().isValidateFromBackend.isTrue
                    val hideBanner = HomePageRemoteConfig.getLeaderboardInfo().hideBanner.isTrue
                    if (hideBanner.not()) userWhitelistedForLeaderboard(campaignName, isValidateFromBackend)
                } catch (t: NoConnectivityException) {
                    withContext(Dispatchers.Main) { Toast.makeText(this@HomePageActivity, t.message, Toast.LENGTH_LONG).show() }
                } catch (t: SocketTimeoutException) {
                    withContext(Dispatchers.Main) { Toast.makeText(this@HomePageActivity, "Terjadi kesalahan, coba lagi nanti", Toast.LENGTH_LONG).show() }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) { Toast.makeText(this@HomePageActivity, "Terjadi kesalahan, coba lagi nanti", Toast.LENGTH_LONG).show() }
                }

                withContext(Dispatchers.Main) {
                    if (savedInstanceState == null) {
                        fetchHomeData()
                    }
                }
                postFcmTokenUseCase.invokeIfNeeded()
            }
        }

        startUpdateCheck()
        callZohoCustomerCare()
        fetchDynamicKeyboard()
        fetchWarrantyConfig()
        subscribeState()

//        LocationUtil.getLocation(this)
//removing temporarily for verifone testing as it's causing crash
        if (!EdcApplication.isVerifoneDevice) {
            // Pax device
//            fetchLocationAndSendToBackend()
        }
        handleDeeplink(intent)
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (intent.hasExtra("IS_ACTIVATED") == true) {
            val activationStatus = if (intent.getBooleanExtra(
                    "IS_ACTIVATED",
                    false
                )
            ) ActivatedStatus.COMPLETE else ActivatedStatus.START
            EdcActivationBottomSheet.createInstance(
                supportFragmentManager,
                activationStatus,
                intent.extras ?: bundleOf()
            )
        }
        handleDeeplink(intent)
    }

    private fun startUpdateCheck() {
        if(Utils.isCardReader()) {
            val appUpdateString = HomePageRemoteConfig.getAppUpdateVersionCode()
            val type = object : TypeToken<AppUpdateConfigContents>() {}.type
            val gson: Gson = GsonBuilder().create()
            var appUpdateData: AppUpdateConfigContents? = null
            appUpdateData = try {
                gson.fromJson(appUpdateString, type)
            } catch (e: Exception) {
                gson.fromJson(HomePageRemoteConfig.SAKU_APP_UPDATE_VERSION_CODE_VAL, type)
            }
            handler.post {
                checkForAppUpdate(appUpdateData)
            }
        }else{
            //app update check for verifone/pax
        }
    }

    private fun checkForAppUpdate(appUpdateData: AppUpdateConfigContents?) {
        var flexibleUpdate = false
        var immediateUpdate = false
        if (appUpdateData != null) {
            flexibleUpdate = BuildConfig.VERSION_CODE <= appUpdateData.softUpdateVersionCode
            immediateUpdate = BuildConfig.VERSION_CODE <= appUpdateData.hardUpdateVersionCode
        }
        val actionType: Int = if (immediateUpdate) {
            AppUpdateType.IMMEDIATE
        } else if (flexibleUpdate) {
            AppUpdateType.FLEXIBLE
        } else {
            return  // both are false, no update needed, return!
        }

        val appUpdateManager = AppUpdateManagerFactory.create(this)
        appUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                try {
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        actionType,
                        this,
                        REQUEST_CODE_UPDATE
                    )
                } catch (e: IntentSender.SendIntentException) {
                    e.printStackTrace();
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        appUpdateManager.registerListener {state->
            if (state.installStatus() == InstallStatus.DOWNLOADED) {
                // Prompt user to restart the app to complete the update
                Snackbar.make(
                    findViewById(android.R.id.content),
                    "An update has just been downloaded.",
                    Snackbar.LENGTH_INDEFINITE
                ).setAction("RESTART") {
                    appUpdateManager.completeUpdate()
                }.show()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_UPDATE) {
            if (resultCode != RESULT_OK) {
                // Handle the failure of the update flow
                startUpdateCheck()
                Log.e("Update", "Update flow failed! Result code: $resultCode")

            }
        }
    }

    private fun callZohoCustomerCare() {
        binding.fbHelp.visibility = BuildConfig.DEBUG.not().asVisibility()
        binding.fbHelp.singleClick {
            ZohoChat.openZohoChat("homepage_floating_btn")
        }
    }

    override fun onResume() {
        super.onResume()
        callOnResumeOnceTokenValid()
        //below scrolling logic is user to anchor card/ppob section from mweb.
        val scrollTo = intent.extras?.getString("scroll_to").orEmpty()
        if (scrollTo.isNotBlank()){
            lifecycleScope.launch {
                delay(1000)
                scrollToView(scrollTo)
            }
        }
    }

    private fun callOnResumeOnceTokenValid() {
        viewModel.apply {
            viewModelScope.launch {
                fetchUserAndOrderDetails()
                processIncompleteTransactions()
                fetchUserDevices()
                withContext(Dispatchers.Main){
                    checkKycTier()
                }
            }
        }
    }

    private fun checkKycTier() {
        with(binding.tbHomepage.ivSettings) {
            when {
                kycTier.isNonKyc() -> {
                    singleClick {
                        KycKybBottomSheet.createInstance()
                            .show(supportFragmentManager, KycKybBottomSheet.getClassTag())
                    }
                    setImageDrawable(getDrawableCompat(R.mipmap.ic_kyc_badge_standard))
                }

                kycTier.isAdvanced() -> {
                    singleClick {
                        KycKybBottomSheet.createInstance()
                            .show(supportFragmentManager, KycKybBottomSheet.getClassTag())
                    }
                    setImageDrawable(getDrawableCompat(R.mipmap.ic_kyc_badge_premium))
                }

                kycTier.isSupreme() -> {
                    singleClick { }
                    setImageDrawable(getDrawableCompat(R.mipmap.ic_kyc_badge_priority))
                }
            }
        }
    }

    private fun subscribeState() {
        viewModel.userAndOrderData.observe(this) { (userDetails, orderDetails) ->
            userDetails?.let { userResult ->
                when (userResult.status) {
                    Status.SUCCESS -> {
                        Log.d("user_details", userResult.message.toString())
                        userResult.data?.data?.let { userData ->
                            //have to add check for serial number and phone number mapping
                            if (Utils.isFixedTerminal()) {
                                if (userData.serialNumber.isNotNullOrEmpty() && userData.userId.isNotNullOrEmpty() && userData.userId != Utils.getPhoneNumber()) {
                                    showTerminalActivationError(
                                        userData.serialNumber,
                                        userData.userId
                                    )
                                }
                            }
                            binding.tbHomepage.tvTitle.text = userData.storeName
                            viewModel.saveUserDetails(userData)
                            showSakuOnboardingDialog()
                        }
                        if (userResult.data?.data == null) {
                            isTerminalActive = false
                        } else {
                            isTerminalActive = true
                        }
                        if (userResult.data?.data == null && Utils.isCardReader()) {
                            showNoSakuRegisteredDevicesDialog()
                        } else if (userResult.data?.data == null && Utils.isFixedTerminal()) {
                            val isPaid = orderDetails?.data?.paymentDetails?.status?.equals(
                                STATUS_PAID,
                                true
                            ).isTrue
                            isDeviceActivationPending =
                                orderDetails?.result.isTrue && orderDetails?.data?.status.equals(
                                    "Waiting_for_ops",
                                    false
                                )
                            if (isDeviceActivationPending) {
                                showEdcDeviceActivationBS()
                            } else {
                                showWrongDeviceLoggedInDialog()
                            }
                        }
                    }

                    Status.ERROR -> {
                        Log.d("user_details", userResult.message.toString())
                    }

                    else -> {
                        Log.d("user_details", "${userResult.status}")
                    }
                }
            }
        }

        viewModel.deviceList.observe(this) {
            if (haveSakuDevice != true) {
                haveSakuDevice = it.any {
                    it.vendor.equals(Constants.DEVICE_MANUFACTURER_VERIFONE, true).not() &&
                            it.vendor.equals(Constants.DEVICE_MANUFACTURER_PAX, true).not()
                }
            }
            if (haveSakuDevice == false && Utils.isCardReader()) {
                showNoSakuRegisteredDevicesDialog()
            }
        }

        observeIncompleteTransaction()

        viewModel.errorFetchUserDevicesState.observe(this) {
            if (it) { showWhenErrorFetchUserDevices() }
        }

        viewModel.activateDeviceState.observe(this) {
            val map = HashMap<String, String>()
            map["order_type"] = "non_partnerships"
            when (it.status) {
                com.bukuwarung.network.model.Status.SUCCESS -> {
                    map["status"] = "success"
                    map["sn_activated"] = it.data?.serialNumber.orEmpty()
                    map["tid"] = it?.data?.tid.orEmpty()
                    map["store_name"] = it?.data?.storeName.orEmpty()
                    showDeviceActivationSuccessDialog(isAfterActivation = true)
                }

                com.bukuwarung.network.model.Status.LOADING -> {

                }

                com.bukuwarung.network.model.Status.ERROR -> {
                    map["status"] = "failure"
                    map["failed_reason"] = it.message.orEmpty()
                    showDeviceActivationFailedDialog()
                }

                com.bukuwarung.network.model.Status.NO_INTERNET -> {

                }
            }
            Analytics.trackEventMobile(CardActivationConstants.EVENT_EDC_ACTIVATION_RESULT, map)
        }
    }

    private fun fetchDynamicKeyboard() {
        var dynamicKeys = CheckBalancePinConfig.getDynamicKeyboard()
        val type = object : TypeToken<DynamicKeyboardBody>() {}.type
        var keyboardData: DynamicKeyboardBody?
        try {
            keyboardData = GsonBuilder().create().fromJson(dynamicKeys, type)
        } catch (e: Exception) {
            dynamicKeys =
                RemoteConfigUtils.remoteConfig.getString(CheckBalancePinConfig.EDC_DYNAMIC_KEYBOARD_BODY)
            keyboardData = GsonBuilder().create().fromJson(dynamicKeys, type)
        }
        val isFromFirebase: Boolean = keyboardData?.isFromFirebase ?: false
        val keyboardType: Int = keyboardData?.keyboardType ?: 0
        Utils.sharedPreferences.put(DYNAMIC_KEYS, dynamicKeys)
        Utils.sharedPreferences.put(IS_FROM_FIREBASE, isFromFirebase)
        Utils.sharedPreferences.put(KEYBOARD_TYPE, keyboardType)
    }
    private fun fetchWarrantyConfig() {
        lateinit var edcOrderWarrantyNudgeConfig: String
        try {
            edcOrderWarrantyNudgeConfig = CheckBalancePinConfig.getEdcOrderWarrantyNudge()

        } catch (e: Exception) {
            Log.d("--->warranty", "fetch failsafe data")
            edcOrderWarrantyNudgeConfig =
                RemoteConfigUtils.remoteConfig.getString(CheckBalancePinConfig.EDC_ORDER_WARRANTY_NUDGE_VALUE)

        }
        Utils.sharedPreferences.put(EDC_ORDER_WARRANTY_NUDGE, edcOrderWarrantyNudgeConfig)
    }

    private fun fetchHomeData() {
        // Fetch and populate from remote config
        var homePageSchema = HomePageRemoteConfig.getHomePageSchema()
        val type = object : TypeToken<List<HomePageSchema>>() {}.type
        val gson: Gson = GsonBuilder().create()
        var homeData: List<HomePageSchema> = listOf()

        try {
            Log.d("homepage","fetch homepage json $homePageSchema")
            homeData = gson.fromJson(homePageSchema, type)
        } catch (e: Exception) {
            Log.d("homepage","fetch failsafe data")
            homePageSchema =
                RemoteConfigUtils.remoteConfig.getString(HomePageRemoteConfig.HOMEPAGE_SCHEMA_FAILSAFE)
            homeData = gson.fromJson(homePageSchema, type)
        }

        if (EdcApplication.isVerifoneDevice) {
            homeData = homeData.filter { it.shouldShowOnVerifone }
        } else{
            homeData = homeData.filter { it.shouldShowOnPax }
        }

        homeData = homeData.filter {
            it.isVisible && (it.blockEndVersion >= versionCode
                    || it.blockEndVersion == -1) && it.blockStartVersion <= versionCode
        }.sortedBy { it.rank }

        fragmentPool.clear()

        val linearLayout = binding.frameHomepage
        if (linearLayout.childCount > 0) {
            linearLayout.removeAllViews()
        }

        val fragmentManager = supportFragmentManager
        var transaction: FragmentTransaction?

        for (data: HomePageSchema in homeData) {
            binding.apply {
                transaction = fragmentManager?.beginTransaction()
                var fragment = Fragment()

                var bodyContents: HomePageBody? = null
                val bodyName = data.blockName
                if (bodyName?.contains("ppob", true).isTrue) ppobViewPosition = data.rank.orNil
                else if (bodyName?.contains("card", true).isTrue) cardViewPosition = data.rank.orNil

                bodyName?.let {
                    bodyContents = retrieveBodyContent(bodyName)
                }

                when (bodyContents?.bodyType) {
                    -1 -> {
                        fragment = Fragment()
                    }
                    1 -> {
                        fragment = HomeSaldoFragment.newInstance(bodyContents, ::redirectBasedOnTier)
                    }

                    2 -> {
                        fragment = HomePageTileFragment.newInstance(bodyName, bodyContents, ::handleTileFragmentOnItemClickListener)
                    }
                    3 -> {
                        if (shouldShowTicker()) {
                            fragment = HomeTickerFragment()
                        }
                    }
                    4 -> {
                        fragment = HomeHistoryFragment().withArgs {
                            putParcelable("contents", bodyContents)
                        }
                    }
                    5 -> {
                        fragment = HomePageBannersFragment().withArgs {
                            putParcelable("contents", bodyContents)
                        }
                    }

                    6 -> {
                        fragment = EdcOrderFragment().withArgs {
                            putParcelable("contents", bodyContents)
                        }
                    }

                    7 -> {
                        fragment = HomeKomisiAgenFragment.newInstance(bodyContents, ::komisiAgenViewClickListener)
                    }
                }

                fragmentPool.add(fragment)
                transaction?.add(linearLayout.id, fragment, data.blockName)
                    ?.commitAllowingStateLoss()

            }
        }
    }

    private fun handleTileFragmentOnItemClickListener(bodyName: String?, type: String, url: String, checkIfRegistered: Boolean){
        when(bodyName){
            EDC_SAKU_HOMEPAGE_CARD->{
                checkSakuDeviceRegisteredOROpenRouterActivity(type, url, checkIfRegistered)
            }
            EDC_HOMEPAGE_PPOB->{
                redirectBasedOnTier(type, url)
            }
            else->{
                openActivity(Utils.getRouterClass()) {
                    putString("type", type)
                    putString("url", url)
                }
            }

        }
    }


    private fun retrieveBodyContent(bodyName: String): HomePageBody {
        val type = object : TypeToken<HomePageBody>() {}.type
        val gson: Gson = GsonBuilder().create()
        val bodyData: HomePageBody

        val bodyContents: String =
            HomePageRemoteConfig.getHomePageBody(bodyName)
        bwLog("In HomePageActivity, getting body contents: $bodyContents of body name: $bodyName")
        bodyData = gson.fromJson(bodyContents, type)

        if (EdcApplication.isVerifoneDevice) {
            bodyData.bodyContent = bodyData.bodyContent?.filter { it?.shouldShowOnVerifone.isTrue }
        }else{
            bodyData.bodyContent = bodyData.bodyContent?.filter { it?.shouldShowOnPax.isTrue }
        }

        bodyData.bodyContent = bodyData.bodyContent?.filter {
            it?.isVisible!! && (it.endVersion >= versionCode
                    || it.endVersion == -1) && it.startVersion <= versionCode
        }

        return bodyData
    }

    override fun onBackPressed() {
        super.onBackPressed()
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastBackPressTime < DOUBLE_BACK_PRESS_THRESHOLD) {
            showExitConfirmationDialog()
        } else {
            lastBackPressTime = currentTime
            Toast.makeText(this, "Press back again to exit", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showExitConfirmationDialog() {
        val builder = AlertDialog.Builder(this)
        builder.setTitle("Exit Application")
        builder.setMessage("Are you sure you want to exit?")
        builder.setPositiveButton("Yes") { dialog, _ ->
            dialog.dismiss()
            super.onBackPressed()
            finishAffinity() // This will close all activities and exit the app
        }
        builder.setNegativeButton("No") { dialog, _ ->
            dialog.dismiss()
            lastBackPressTime = 0 // Reset the back press time to prevent immediate re-exit
        }
        builder.create().show()
    }

    private fun observeIncompleteTransaction() {
        viewModel.incompleteTransaction.observe(this) { resource ->

            when (resource.status) {
                Status.SUCCESS -> {
                    val incompleteTransaction = resource.data

                    when (incompleteTransaction?.status) {
                        ReversalStatus.IN_REVERSAL -> {
                            if (incompleteTransaction.request?.transactionType == TransactionType.TRANSFER_POSTING.type) {
                                binding.includePaymentLoading.tvPaymentStatus.setText(R.string.processing_incomplete_transfer_transaction)
                            } else {
                                binding.includePaymentLoading.tvPaymentStatus.setText(R.string.processing_incomplete_balance_transaction)
                            }
                            binding.includePaymentLoading.root.showView()
                            binding.includePaymentLoading.ivClose.hideView()
                            binding.includePaymentLoading.tvClose.hideView()
                            Log.d("REVERSAL", "Incomplete transaction in progress ${incompleteTransaction}")
                        }
                        ReversalStatus.PROCESSED -> {
                            binding.includePaymentLoading.root.hideView()
                            if (incompleteTransaction.transactionId.isNotNullOrBlank()) {
                                openActivity(TransactionDetailsActivity::class.java) {
                                    putString(
                                        Constant.INTENT_KEY_TRANSACTION_TYPE,
                                        incompleteTransaction.request?.transactionType
                                    )
                                    // backend will provide transaction_id
                                    putString(
                                        TransactionDetailsActivity.INTENT_KEY_TRANSACTION_ID,
                                        incompleteTransaction.transactionId
                                    )
                                    putString(
                                        TransactionDetailsActivity.INTENT_KEY_ENTRY_POINT,
                                        TransactionDetailsActivity.ENTRY_POINT_HOMEPAGE
                                    )
                                }
                            }
                            Log.d("REVERSAL", "Incomplete transaction reversal complete: $resource")
                        }

                        null -> {
                            Log.d("REVERSAL", "Transaction status is null")
                        }
                    }

                }
                Status.ERROR -> {
                    binding.includePaymentLoading.root.hideView()
                    Utils.clearIncompleteTransaction()
                    val errorCode = resource.data?.error
                    when {
                        terminalIdOrDeviceSerialBlockedCodes.contains(errorCode) -> {
                            cardErrorDialog = ErrorMapping.showErrorDialog(
                                context = this,
                                errorCode = errorCode,
                                stan = ErrorMapping.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED,
                                positiveListener = {
                                    if (terminalIdOrDeviceSerialBlockedCodes.contains(errorCode)) {
                                        ZohoChat.openZohoChat(ZohoChatEntryPoint.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED)
                                    }
                                },
                                dismissListener = {
                                    if (terminalIdOrDeviceSerialBlockedCodes.contains(errorCode)) {
                                        openActivity(HomePageActivity::class.java)
                                        finishAffinity()
                                    }
                                }
                            )
                            cardErrorDialog?.show()
                        }
                    }
                }

                Status.LOADING -> {
                    Log.d("INCOMPLETE_TX_OBSERVER", "LOADING")
                }
                Status.NO_INTERNET -> {
                    Log.d("INCOMPLETE_TX_OBSERVER", "NO_INTERNET")
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(networkChangeReceiver)
    }

    private var cardErrorDialog: CardErrorDialog? = null

    fun onInternetConnected() {
        cardErrorDialog?.takeIf { it.isShowing }?.dismiss()
        viewModel.processIncompleteTransactions()
    }

    private fun komisiAgenViewClickListener(redirectionUrl: String) {
        var map = HashMap<String, String>()
        map.put(HomePageAnalyticsConstants.BUTTON_NAME, HomePageAnalyticsConstants.KOMISI_AGEN)
        Analytics.trackEvent(HomePageAnalyticsConstants.HOMEPAGE_BUTTON_CLICK, map)

        if (Utils.sharedPreferences.get("show_komisi_agen_dialog", true)) {
            val komisiAgenInfoDialog = KomisiAgenInfoDialog(this)
            Utils.showDialogIfActivityAlive(this, komisiAgenInfoDialog)
        } else {
            openActivity(WebviewActivity::class.java) {
                putString(WEBVIEW_URL, redirectionUrl)
                putBoolean(HIDE_TOOLBAR, true)
            }
        }
    }

    private fun redirectBasedOnTier(type: String, url: String){
        if(Utils.isFixedTerminal() || kycTier.isSupreme()){
            openActivity(Utils.getRouterClass()) {
                putString("type", type)
                putString("url", url)
            }
        } else {
            KycKybBottomSheet.createInstance().show(supportFragmentManager, KycKybBottomSheet.getClassTag())
        }
    }

    private fun checkSakuDeviceRegisteredOROpenRouterActivity(type: String, url: String, checkIfRegistered: Boolean) {
        if (checkIfRegistered && Utils.isCardReader() && haveSakuDevice == false) {
            showNoSakuRegisteredDevicesDialog(isFromCardSection = true)
        } else {
            openActivity(Utils.getRouterClass()) {
                putString("type", type)
                putString("url", url)
            }
        }
    }

    private fun showNoSakuRegisteredDevicesDialog(isFromCardSection: Boolean = false) {
        if (!Utils.isNoSakuDeviceRegisteredBTShown || isFromCardSection) {
            val dialog = NoSakuDeviceRegisteredBS()
            dialog.show(supportFragmentManager, "no_device_registered_dialog")
        }
    }

    fun showWrongDeviceLoggedInDialog() {
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(R.string.nomor_hp_tidak_cocok),
            subTitle = getString(R.string.nomor_hp_yang_digunakan_tidak_terdaftar),
            image = R.drawable.ic_wrong_phone_number,
            isLoader = false,
            btnLeftListener = {
                //open Zoho live chat
            },
            btnRightListener = {
                // logout and open login page
                Utils.clearDataAndLogout(false)
            },
            btnLeftText = getString(R.string.contact_customer_care),
            btnRightText = "Masuk Ulang"
        )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    fun showDeviceActivationFailedDialog() {
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(R.string.device_activation_failed),
            subTitle = getString(R.string.device_activation_failed_description),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {
                //open Zoho live chat
                ZohoChat.openZohoChat("device_activation_failed")
            },
            btnRightListener = {
                dialog?.dismiss()
            },
            btnLeftText = getString(R.string.contact_customer_care),
            btnRightText = getString(R.string.retry)
        )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    fun showDeviceActivationSuccessDialog(isAfterActivation: Boolean = false) {
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(R.string.selamat_datang_di_bukuagen),

            subTitle = getString(R.string.connect_saku_to_bluetooth_to_start_transaction),
            image = R.drawable.ic_first_app_open_welcome,
            isLoader = false,
            btnLeftListener = {

            },
            btnRightListener = {
                if (isAfterActivation) {
                    viewModel.apply {
                        viewModelScope.launch {
                            viewModel.fetchUserAndOrderDetails()
                        }
                    }
                }
                dialog?.dismiss()
            },
            btnLeftText = "",
            btnRightText = "Coba Sekarang"
        )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    fun showEdcDeviceActivationBS() {
        if (supportFragmentManager.findFragmentByTag(EdcActivationBottomSheet.getClassTag()) != null) {
            return

        }
        EdcActivationBottomSheet.createInstance(
            supportFragmentManager,
            ActivatedStatus.START,
            bundleOf()
        ) {
            // activate edc
            Log.d(
                "--->activation",
                "activate edc serial number = ${Utils.getHardwareSerialNumber()}"
            )
            val request = ActivateDeviceRequest(
                serialNumber = Utils.getHardwareSerialNumber(),
                type = AppType.NON_PARTNERSHIP.name
            )
            viewModel.activateDevice(request)
        }
    }

    fun checkEdcDeviceActivationAndError(serialNumber: String = "", phoneNumber: String = "") {
        if (isDeviceActivationPending) {
            showEdcDeviceActivationBS()
        } else {
            showTerminalActivationError(serialNumber, phoneNumber)
        }
    }

    fun showTerminalActivationError(serialNumber: String = "", phoneNumber: String = "") {
        //for bluetooth terminals, device activation check is done after connecting to the terminal in BluetoothConnectionActivity
        if (!Utils.isFixedTerminal())
            return

        isTerminalActive = (isTerminalActive == false) && (Utils.getPaymentAccountId()
            .isNotNullOrEmpty() && Utils.getTerminalId().isNotNullOrEmpty())
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(
                com.bukuwarung.bluetooth_printer.R.string.different_phone_number
            ),
            subTitle = getString(
                com.bukuwarung.bluetooth_printer.R.string.different_phone_number_message,
                serialNumber,
                phoneNumber
            ),
            image = R.drawable.ic_wrong_phone_number,
            isLoader = false,
            btnLeftListener = {

            },
            btnRightListener = {
                Utils.clearDataAndLogout(false)
            },
            btnLeftText = "",
            btnRightText = getString(com.bukuwarung.bluetooth_printer.R.string.login_with_registered_phone_number)
        )
        Utils.showDialogIfActivityAlive(this, dialog)
        try {
            val props = HashMap<String, String>()
            props["error_code"] = "E06"
            props["source"] = "homepage"
            props["reason"] = "no_edc_order"
            Analytics.trackEventMobile("device_mapping_mismatch", props)
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun showSakuOnboardingDialog() {
        if (Utils.getBooleanConfig("onboarding_dialog_shown") || Utils.isFixedTerminal()) {
            return
        }
        Utils.sharedPreferences.put("onboarding_dialog_shown", true)
        showDeviceActivationSuccessDialog()
    }

    fun openSetupBluetoothDeviceActivityForCardReader(){
        val device = Utils.getUserRegisteredDevices()
        val deviceSerialNumberList = device.map { it.serialNumber }
        openActivity(SetupBluetoothDeviceActivity::class.java) {
            putString(
                SetupBluetoothDeviceActivity.DEVICE_TYPE,
                SetupBluetoothDeviceActivity.CARD_READER
            )
            putStringArrayList(SetupBluetoothDeviceActivity.DEVICE_SN_LIST, ArrayList(deviceSerialNumberList))
        }
    }

    private fun showWhenErrorFetchUserDevices(){
        val snackbar = Snackbar.make(
            binding.root,
            getString(R.string.fail_fetch_user_device_message),
            Snackbar.LENGTH_SHORT
        )
        snackbar.setAction(getString(R.string.retry)) {
            viewModel.apply {
                viewModelScope.launch {
                    viewModel.fetchUserDevices()
                }
            }
        }
        snackbar.show()
    }

    private fun handleDeeplink(intent: Intent) {
        if (EncryptedPreferencesHelper.get("uuid", "").isEmpty()) {
            openActivity(WelcomeActivity::class.java)
            finish()
        }
        val uri = when {
            intent.action == Intent.ACTION_VIEW -> intent.data

            intent.extras?.containsKey(FCM_DEEPLINK_KEY) == true ->
                Uri.parse(intent.extras?.getString(FCM_DEEPLINK_KEY))

            intent.extras?.containsKey(FCM_WEB_URL_KEY) == true ->
                Uri.parse(intent.extras?.getString(FCM_WEB_URL_KEY))

            else -> null
        }

        uri?.let {
            if (it.scheme == "http" || it.scheme == "https") {
                val url = it.toString()
                val type = BTRouterActivity.mweb
                openActivity(Utils.getRouterClass()) {
                    putString(BTRouterActivity.type, type)
                    putString(BTRouterActivity.url, url)
                }
            } else {
                val path = it.path
                when {
                    path?.startsWith("/transactiondetail") == true -> {
                        val transactionId = it.getQueryParameter("transaction_id")
                        val type = it.getQueryParameter("type")
                        openActivity(TransactionDetailsActivity::class.java) {
                            putString(TransactionDetailsActivity.INTENT_KEY_TRANSACTION_ID, transactionId)
                            putString(TransactionDetailsActivity.INTENT_KEY_TRANSACTION_TYPE, type)
                            putString(TransactionDetailsActivity.INTENT_KEY_ENTRY_POINT, TransactionDetailsActivity.ENTRY_POINT_PUSH_NOTIF)
                        }
                    }
                    path?.startsWith("/komisiAgenHistory") == true -> {
                        openActivity(KomisiAgenHistoryActivity::class.java)
                    }
                    path?.startsWith("/webview") == true -> {
                        val url = it.getQueryParameter("web_url")
                        openActivity(WebviewActivity::class.java){
                            putString(ClassConstants.WEBVIEW_URL, url)
                            putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                        }
                    }
                    path?.startsWith("/edcOrderDetail").isTrue -> {
                        val phoneNumber = it.getQueryParameter("phone_number")
                        val orderId = it.getQueryParameter("order_id")
                        if (phoneNumber.equals(Utils.getPhoneNumber())) {
                            openActivity(EdcOrderDetailsActivity::class.java) {
                                putString(EdcOrderDetailsActivity.ORDER_ID, orderId)
                            }
                        } else {
                            BukuDialog(
                                context = this,
                                title = getString(R.string.activation_not_found_title),
                                subTitle = getString(
                                    R.string.activation_not_found_subtitle,
                                    phoneNumber.orEmpty()
                                ),
                                image = R.drawable.not_found,
                                isLoader = false,
                                btnLeftText = getString(R.string.close),
                                btnRightText = getString(R.string.sign_out),
                                btnLeftListener = {},
                                btnRightListener = {
                                    Utils.clearDataAndLogout(false)
                                }
                            ).show()
                        }
                    }
                    else -> {
                        //deeplink is not handled
                    }
                }
            }
        }
    }

    private fun scrollToView(scrollTo: String) {
        val position = when (scrollTo) {
            "ppob" -> ppobViewPosition
            "card" -> cardViewPosition
            else -> 1
        }
        val targetView = binding.frameHomepage.getChildAt(position - 1)
        binding.svHomepage.post {
            binding.svHomepage.scrollTo(0, targetView?.top.orNil)
        }
    }

    private fun shouldShowTicker(): Boolean {
        return try {
            val tickerDataBlock = HomePageRemoteConfig.getTickerFragmentData()
            val type = object : TypeToken<TickerFragmentData>() {}.type
            val gson: Gson = GsonBuilder().create()
            val tickerData: TickerFragmentData = gson.fromJson(tickerDataBlock, type)

            val hours = 24 / tickerData.frequency.orNil
            val tickerDataInterval = hours.times(60).times(60).times(1000).toLong()

            val currentTime = System.currentTimeMillis()
            val lastClosedTime = Utils.getTickerCloseTime()
            val startTime = Utils.getTime(tickerData.startTime)
            val endTime = Utils.getTime(tickerData.endTime)

            val shouldShow = currentTime - lastClosedTime > tickerDataInterval &&
                    currentTime > startTime &&
                    currentTime < endTime &&
                    !tickerData.tickerHeader.isNullOrEmpty()

            shouldShow
        } catch (e: Exception) {
            bwLog("Exception while checking ticker visibility = ${e.message.toString()}")
            false
        }
    }
}
