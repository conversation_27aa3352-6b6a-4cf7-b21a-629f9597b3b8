package com.bukuwarung.edc.homepage.ui.home

import Resource
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.activities.print.setup.GetMappedPhoneNumberUseCase
import com.bukuwarung.data.business.implementation.mapper.toBusinessEntity
import com.bukuwarung.edc.card.data.model.IncompleteTransaction
import com.bukuwarung.edc.card.data.model.IncompleteTransactionRequest
import com.bukuwarung.edc.card.data.model.ReversalStatus
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.usecase.EdcDeviceUseCase
import com.bukuwarung.edc.card.domain.usecase.EdcTransactionUseCase
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceItem
import com.bukuwarung.edc.card.ui.edcdevices.usecase.DeviceListUseCase
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.homepage.data.model.Data
import com.bukuwarung.edc.homepage.data.model.LeaderboardWhitelistedResponse
import com.bukuwarung.edc.homepage.data.model.LogonDataRequest
import com.bukuwarung.edc.homepage.data.model.User
import com.bukuwarung.edc.homepage.usecase.HomePageUseCase
import com.bukuwarung.edc.login.data.model.*
import com.bukuwarung.edc.login.usecase.EnablePaymentUseCase
import com.bukuwarung.edc.login.usecase.JanusUseCase
import com.bukuwarung.edc.order.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.edc.order.orderdetail.usecase.EdcOrderDetailUseCase
import com.bukuwarung.edc.session.SessionManager
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.*
import com.bukuwarung.edc.webview.WebviewUseCase
import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.ActivateDeviceResponse
import com.bukuwarung.network.utils.ResourceState
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.FirebaseFirestore
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.SocketException
import java.util.*
import javax.inject.Inject
import kotlin.collections.set

@HiltViewModel
class HomePageViewModel @Inject constructor(
    private val homePageUseCase: HomePageUseCase,
    private val deviceUseCase: EdcDeviceUseCase,
    private val edcTransactionUseCase: EdcTransactionUseCase,
    private val deviceListUseCase: DeviceListUseCase,
    private val janusUseCase: JanusUseCase,
    private val edcOrderUseCase: EdcOrderDetailUseCase,
    private val enablePaymentUseCase: EnablePaymentUseCase,
    private val getMappedPhoneNumberUseCase: GetMappedPhoneNumberUseCase

): ViewModel() {

    private val _userWhitelistedForLeaderboard = MutableLiveData<Resource<LeaderboardWhitelistedResponse>>()
    private val _userDetails = MutableLiveData<Resource<UserDetail>>()
    private val _logonData = MutableLiveData<Resource<LogonResponse>>()
    private val _incompleteTransaction = MutableLiveData<IncompleteTransaction>()

    val userWhitelistedForLeaderboard: LiveData<Resource<LeaderboardWhitelistedResponse>>
        get() = _userWhitelistedForLeaderboard
    val userDetails: LiveData<Resource<UserDetail>>
        get() = _userDetails
    val logonData: LiveData<Resource<LogonResponse>>
        get() = _logonData
    val incompleteTransaction: LiveData<IncompleteTransaction>
        get() = _incompleteTransaction
    val errorFetchUserDevicesState = MutableLiveData<Boolean>()

    private var _orderDetails = MutableLiveData<EdcOrderDetailResponse?>()
    val orderDetails: LiveData<EdcOrderDetailResponse?> = _orderDetails

    private val _deviceList = MutableLiveData<List<DeviceItem>>()
    val deviceList: LiveData<List<DeviceItem>> get() = _deviceList


    private val _userAndOrderData =
        MutableLiveData<Pair<Resource<UserDetail>?, EdcOrderDetailResponse?>>()
    var userAndOrderData: LiveData<Pair<Resource<UserDetail>?, EdcOrderDetailResponse?>> =
        _userAndOrderData

    suspend fun fetchUserAndOrderDetails()= coroutineScope {
        try {
            val userJob = async { fetchUserDetails() }
            val orderJob = async { getEdcOrderDetailByPhoneNumber() }
            val userDetails = userJob.await()
            val orderDetails = orderJob.await()
            _userAndOrderData.value = Pair(userDetails, orderDetails)
        } catch (e: Exception) {
            _userAndOrderData.value = Pair(
                Resource.error("Failed to fetch details: ${e.message}", null),
                EdcOrderDetailResponse(result = false, data = null)
            )
            bwLog(e)
        }
    }

    private val _activateDeviceState = MutableLiveData<com.bukuwarung.network.model.Resource<ActivateDeviceResponse>>()
    val activateDeviceState: LiveData<com.bukuwarung.network.model.Resource<ActivateDeviceResponse>>
        get() = _activateDeviceState

    suspend fun getEdcOrderDetailByPhoneNumber(): EdcOrderDetailResponse? {
        return try {
            when (val response = edcOrderUseCase.getEdcOrderDetailByPhoneNumber()){
                is ResourceState.Success->{
                    _orderDetails.value = response.data
                    response.data
                }
                else->{
                    _orderDetails.value = EdcOrderDetailResponse(result = false, data = null)
                    EdcOrderDetailResponse(result = false, data = null)
                }
            }
        } catch (e: Exception) {
            bwLog(e)
            EdcOrderDetailResponse(result = false, data = null)
        }
    }

    suspend fun fetchUserDetails(): Resource<UserDetail> {
        return try {
            val hardwareSerial = Utils.getHardwareSerialNumber()
            val targetSerialNumber = if (hardwareSerial.isNullOrEmpty()) {
                Utils.getDeviceSerialNumber()
            } else {
                hardwareSerial
            }

            if (targetSerialNumber.isNullOrEmpty()) {
                val errorMsg = "Device serial number is unavailable"
                val errorResource = Resource.error<UserDetail>(errorMsg, null)
                _userDetails.postValue(errorResource)
                return errorResource
            }

            when (val result = homePageUseCase.fetchUserDetails(targetSerialNumber)) {
                is ResourceState.Success -> {
                    val userData = result.data
                    val successResource = Resource.success(userData)
                    _userDetails.postValue(successResource)
                    successResource
                }
                is ResourceState.Failure -> {
                    val errorMsg = result.message ?: "Unknown error"
                    bwLog(Exception(result.throwable))
                    val errorResource = Resource.error<UserDetail>(errorMsg, null)
                    _userDetails.postValue(errorResource)
                    errorResource
                }
                is ResourceState.Loading -> {
                    val loadingResource = Resource.loading<UserDetail>(null)
                    _userDetails.postValue(loadingResource)
                    loadingResource
                }
            }
        } catch (e: Exception) {
            bwLog(e)
            val errorMsg = "Exception: ${e.localizedMessage ?: "Unexpected error"}"
            val errorResource = Resource.error<UserDetail>(errorMsg, null)
            _userDetails.postValue(errorResource)
            return errorResource
        }
    }


    suspend fun fetchUserDevices() {
        try {
            deviceListUseCase.getDeviceList("ALL").let {
                when (it) {
                    is ResourceState.Loading -> {

                    }

                    is ResourceState.Success -> {
                        errorFetchUserDevicesState.postValue(false)
                        val devices: List<DeviceItem>? = it.data.data
                        saveTerminalAccountDetails(devices)
                        _deviceList.postValue(devices?: emptyList())
                        devices?.let { Utils.saveUserRegisteredDevices(it) }
                        Utils.bwLog("device-list", Exception("device-list ${Utils.getPhoneNumber()}: $it => ${it.data.data}"))
                    }

                    is ResourceState.Failure -> {
                        bwLog("Error-fetchUserDevices(): ${it.message}")
                        if (Utils.getPaymentAccountId().isEmpty()) {
                            errorFetchUserDevicesState.postValue(true)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            bwLog(e)
        }
    }

    fun setJanusAccount() = viewModelScope.launch {
        try {
            if (Utils.sharedPreferences.get("kyc_account_id", "").isEmpty()) {
                janusUseCase.createKycAccount().let {
                    if (it.isSuccessful) {
                        Utils.sharedPreferences.put(
                            "kyc_account_id",
                            it.body()?.accountId.orEmpty()
                        )
                    }
                }
            }
        } catch (e: NoConnectivityException) {
            bwLog(e)
        } catch (e: Exception) {
            bwLog(e)
        }
    }

    private fun saveTerminalAccountDetails(devices: List<DeviceItem>?) {
        try {
            if (devices != null && devices.size == 1) {
                Utils.sharedPreferences.apply {
                    put("t_id", devices[0].terminalId)
                    put("serial_number", devices[0].serialNumber)
                    EncryptedPreferencesHelper.put("payment_account_id", devices[0].paymentAccountId)
                }
                bwLog("saveTerminal[0] ${devices[0]}")
            } else if (devices != null && devices.isNotEmpty() && BluetoothDevices.hasPairedCardReader() && BluetoothDevices.getPairedCardReaderList()
                    ?.isNotEmpty() == true
            ) {
                val connectedDeviceItem =
                    BluetoothDevices.getPairedCardReaderList()?.get(0)?.name?.let { it1 ->
                        Utils.findConnectedDeviceItem(
                            it1, devices
                        )
                    }
                Utils.sharedPreferences.apply {
                    if (connectedDeviceItem != null) {
                        put("t_id", connectedDeviceItem.terminalId)
                        put("serial_number", connectedDeviceItem.serialNumber)
                        EncryptedPreferencesHelper.put("payment_account_id", connectedDeviceItem.paymentAccountId.orEmpty())
                    }
                }
                bwLog("saveTerminalConnectedDevice $connectedDeviceItem")
            } else {
                val tmsTerminalId = Utils.getTmsTerminalId()
                val hardwareSerialNumber = Utils.getHardwareSerialNumber()

                val connectedDeviceItem = devices?.firstOrNull {
                    it.serialNumber == hardwareSerialNumber || (hardwareSerialNumber.isNullOrBlank() && it.terminalId == tmsTerminalId)
                }

                connectedDeviceItem?.let { device ->
                    Utils.sharedPreferences.apply {
                        put("t_id", device.terminalId)
                        put("serial_number", device.serialNumber)
                    }
                    EncryptedPreferencesHelper.put("payment_account_id", device.paymentAccountId)
                }
                bwLog("saveTerminal[$tmsTerminalId][$hardwareSerialNumber] $connectedDeviceItem")
            }
            //users who have not purchased any edc device and never opened bukuagen app. In this case, we need to create a default business for them.
            if( (!SessionManager.getInstance().hasExistingBusiness() && devices.isNullOrEmpty() && Utils.getPaymentAccountId().isNullOrEmpty())
                || (devices?.isNotEmpty().isTrue && Utils.getPaymentAccountId().isNullOrEmpty() && SessionManager.getInstance().paymentAccountIdForExistingBusinessCheck.isNullOrEmpty())){
                val business = createDefaultBusiness()
                if (business != null) {
                    updateBook(business)
                }
                //user never purchased edc device, but opened bukuagen app in the past and default payment account id was created. Re-use same payment account id.
            }else if( (devices.isNullOrEmpty() && Utils.getPaymentAccountId().isNullOrEmpty() && SessionManager.getInstance().paymentAccountIdForExistingBusinessCheck.isNotNullOrEmpty())
                || (SessionManager.getInstance().hasExistingBusiness() && devices.isNullOrEmpty() && Utils.getPaymentAccountId().isNullOrEmpty())
                //user purchased edc device, and opened bukuagen app without connecting edc saku exisitng payment accountid can be used as default.
                || (devices?.isNotEmpty().isTrue && Utils.getPaymentAccountId().isNullOrEmpty() && SessionManager.getInstance().paymentAccountIdForExistingBusinessCheck.isNotNullOrEmpty()))
            {
                Utils.setPaymentAccountId(SessionManager.getInstance().paymentAccountIdForExistingBusinessCheck)
                Utils.sharedPreferences.apply {
                    put("store_name", "BukuAgen EDC")
                }
                //user has not purchased a device, using default payment accountId.
            }else if(SessionManager.getInstance().hasExistingBusiness() && devices.isNullOrEmpty() && Utils.getPaymentAccountId().isNotNullOrEmpty()){
                Utils.sharedPreferences.apply {
                    put("store_name", "BukuAgen EDC")
                }
            }
        }catch (e:Exception){
            bwLog(e)
        }
    }

    fun doBeep(){
        deviceUseCase.beeperSound.invoke(50)
    }

    fun saveUserDetails(userData: UserData) {
        Analytics.trackSuperProperty(Constant.USER_PROP_TERMINAL_ID, userData.tid)
        Analytics.trackSuperProperty(Constant.USER_PROP_SERIAL_NUMBER, Utils.getDeviceSerialNumber())
        Utils.sharedPreferences.apply {
            userData.apply {
                put("user_id", userId)
                put("bank_account", bankAccount)
                put("bank_name", bankName)
                put("device_id", deviceId)
                put("beneficiary_name", beneficiaryName)
                put("janus_account_id", janusAccountId)
                put("store_name", storeName)
                put("store_name_sn", storeName)
                put("store_address", storeAddress)
                FirebaseCrashlytics.getInstance().setCustomKey("backendTerminalId", tid)
                FirebaseCrashlytics.getInstance().setCustomKey("backendSerialNo", serialNumber)
                //in case backend dto has error
                FirebaseCrashlytics.getInstance().setCustomKey("backendUserId", userId)
            }
            if(Utils.isFixedTerminal()){
                userData.apply {
                    put("t_id", tid)
                    EncryptedPreferencesHelper.put("payment_account_id", paymentAccountId)
                    put("serial_number", serialNumber)
                }
            }
        }
    }

    fun fetchLogonData(terminalId: String) = viewModelScope.launch {
        try{
            if(terminalId != Utils.getTmsTerminalId()){
                val eventProp = hashMapOf<String, String>()
                eventProp["tms_tid"] = Utils.getTmsTerminalId()
                eventProp["backend_tid"] = terminalId
                Analytics.trackEvent("tid_mismatch",eventProp)
            }
        }catch (e:Exception){
            e.printStackTrace()
        }
        _logonData.postValue(Resource.loading(null))
        try {
            val accountId = Utils.getPaymentAccountId()
            val logonDataRequest = LogonDataRequest(terminalId)
            val serialNumber =
                Utils.getHardwareSerialNumber().ifEmpty { Utils.getDeviceSerialNumber() }
            homePageUseCase.fetchLogonData(serialNumber, accountId, logonDataRequest).let {
                if (it.isSuccessful) {
//                    val isWorkingKeyChanged = Utils.getWorkKey() == it.body()?.workingKeyValue
                    Utils.sharedPreferences.apply {
                        put("terminal_id", it.body()?.terminalId)
                        put("trace_number", it.body()?.traceNumber)
                        EncryptedPreferencesHelper.put("TWK", it.body()?.workingKeyValue)
                        put("logon_sync_time", System.currentTimeMillis())
                    }
                    if(Utils.isFixedTerminal()){
                        if(Utils.getMasterKey().isNullOrEmpty() || Utils.getMasterKey().length<5){
                            val eventProp = hashMapOf<String, String>()
                            eventProp["backend_tid"] = terminalId
                            Analytics.trackEvent("tmk_not_loaded",eventProp)
                            _logonData.postValue(Resource.error("TMK", null))
                        }else {
                            if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API)) {
                                _logonData.postValue(Resource.success(it.body()))
                            } else {
                                if (loadKeys()) _logonData.postValue(Resource.success(it.body()))
                                else _logonData.postValue(Resource.error("", null))
                            }
                        }
                    }else{
                        if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API)) {
                            _logonData.postValue(Resource.success(it.body()))
                        } else {
                            if (loadKeys()) _logonData.postValue(Resource.success(it.body()))
                            else _logonData.postValue(Resource.error("", null))
                        }
                    }

                } else {
                    _logonData.postValue(Resource.error(it.code().toString(), null))
                }
            }
        } catch (e: SocketException) {
            NetworkUtils.checkInternetHealthAndLog(e)
            bwLog(e)
            _logonData.postValue(Resource.error("SE", null))
            NetworkUtils.checkInternetHealthAndLog(e)
        } catch (e: Exception) {
            NetworkUtils.checkInternetHealthAndLog(e)
            bwLog(e)
            _logonData.postValue(Resource.error("", null))
        }
    }

    private fun loadKeys(isWorkingKeyChanged: Boolean = true): Boolean{
        // to avoid frequent loading of keys following condition is written.
        //if no change in working keys and the working key is already loaded then exit the fn.
//        if (isWorkingKeyChanged.not() && Utils.sharedPreferences.get("is_working_key_loaded", false)) return true
        when(deviceUseCase.loadKeys(Utils.getMasterKey(), Utils.getWorkKey())){
            is EdcResponse.Success -> {
                Utils.sharedPreferences.put("is_working_key_loaded", true)
                return true
            }
            is EdcResponse.Failure -> {
                Utils.sharedPreferences.put("is_working_key_loaded", false)
                return false
            }
        }
    }

    /*
     * get most recent incomplete transaction from sharedPref
     * send incomplete transaction details to backend for reversal
     * Note: User is not allowed to make any transaction unless previous incomplete card transaction is not sent to backend.
     */
    suspend fun processIncompleteTransactions() {
        try {
            val reversalRequest = Utils.getIncompleteTransaction()
            if (reversalRequest != null) {
                _incompleteTransaction.postValue(
                    IncompleteTransaction(
                        null,
                        reversalRequest,
                        ReversalStatus.IN_REVERSAL
                    )
                )
                Log.d("incomplete_transaction", "incomplete transaction details $reversalRequest")
                val incompleteTransactionRequest = IncompleteTransactionRequest(reversalRequest.terminalId, reversalRequest.transactionType)
                Log.d("incomplete_transaction", "incomplete transaction request body $incompleteTransactionRequest")
                edcTransactionUseCase.submitIncompleteTransaction(
                    Utils.getPaymentAccountId(),
                    incompleteTransactionRequest
                ).let {
                    if(it.isSuccessful) {
                        val responseBody: Array<String>? = it.body()
                        var transactionId: String? = try {
                            if (responseBody != null) responseBody?.get(0) else null
                        }catch(e:Exception){
                            e.printStackTrace()
                            null
                        }
                        Utils.clearIncompleteTransaction()
                        _incompleteTransaction.postValue(
                            IncompleteTransaction(
                                transactionId,
                                reversalRequest,
                                ReversalStatus.PROCESSED
                            )
                        )

                    }else{
                        val httpErrorCode = it.code()
                        Log.d("incomplete_transaction", "incomplete transaction response: $httpErrorCode req:$reversalRequest")
                        if (httpErrorCode == HttpURLConnection.HTTP_BAD_REQUEST) {
                            Utils.clearIncompleteTransaction()
                            Log.e("incomplete_transaction", "bad request $reversalRequest")
                        } else if (httpErrorCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                            //might require retry explicitly. depending on backend
                        } else {
                            //auto retry till getIncompleteTransaction doesn't give empty response
                        }
                    }
                }
            }
        }catch(ex: NoConnectivityException){
            bwLog(ex)
        }catch(e: Exception){
            _incompleteTransaction.postValue(
                IncompleteTransaction(
                    null,
                    null,
                    ReversalStatus.PROCESSED
                )
            )
            bwLog(e)
        }
    }

    fun userWhitelistedForLeaderboard(campaignName: String, isValidateFromBackend: Boolean) =
        viewModelScope.launch(Dispatchers.IO) {
            try {
                if (isValidateFromBackend) {
                    //checkWhitelist should be sent as true. In case of null for this value backend is sending top 10 users in the leaderboard.
                    try {
                        val response =
                            homePageUseCase.userWhitelistedForLeaderboard(campaignName, true)
                        if (response.isSuccessful) {
                            _userWhitelistedForLeaderboard.postValue(Resource.success(response.body()))
                        } else {
                            _userWhitelistedForLeaderboard.postValue(Resource.error("", null))
                        }
                    } catch (e: Exception) {
                        _userWhitelistedForLeaderboard.postValue(Resource.error("", null))
                    }
                } else {
                    _userWhitelistedForLeaderboard.postValue(
                        Resource.success(
                            LeaderboardWhitelistedResponse(
                                result = true,
                                data = Data(
                                    user = User(
                                        isWhitelisted = true
                                    )
                                )
                            )
                        )
                    )
                }
            } catch (e: NoConnectivityException) {
                bwLog(e)
            }
        }

    private fun createDefaultBusiness(): Business? {
        if(SessionManager.getInstance().hasExistingBusiness())
            return null;
        val userId = Utils.getUserId()
        val deviceId = Utils.getDeviceId()
        SessionManager.getInstance().setExistingBusinessFlag(true)
        return Business.default.copy(
            businessId = UUID.randomUUID().toString(),
            ownerId = userId,
            ownerName = "BukuAgen",
            category = Business.Category.default.copy(
                categoryId = "-1",
                name = "",
            ),
            name = "BukuAgen EDC",
            phone = userId,
            createdByDevice = deviceId,
            modifiedByDevice = "bukuagen-edc-order",
            createdAt = Date(),
            lastModifiedAt = Date(),
            temp = Business.Temp.default.copy(
                enableSmsAlerts = true,
                businessImageUploadPending = 0,
                createdByUser = userId,
                updatedByUser = userId,
                dirty = 1,
                serverSeq = 0L,
                isGuest = false,
            )
        )
    }

    fun updateBook(business: Business) {
        try {
            var book = business.toBusinessEntity()
            val mFirestore = FirebaseFirestore.getInstance()
            book.dirty = 1
            mFirestore.collection("book_store")
                .document(book.bookId)
                .set(book)
                .addOnSuccessListener {
                    // Launch a coroutine to handle the suspend function
                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            // Enable payment asynchronously
                            book.businessName?.let { businessName ->
                                enablePaymentUseCase.enablePayment(book.bookId, businessName)
                            }

                            // Set enabledPayment and update Firestore
                            book.enabledPayment = 1
                            Utils.setPaymentAccountId(book.bookId)
                            SessionManager.getInstance().paymentAccountIdForExistingBusinessCheck=book.bookId
                            Utils.sharedPreferences.apply {
                                put("user_id", book.ownerId)
                                put("book_name", book.bookName)
                                put("store_name", book.bookName)
                            }
                            mFirestore.collection("book_store")
                                .document(book.bookId)
                                .set(book)
                                .await()  // Await to handle async update

                        } catch (e: Exception) {
                            e.printStackTrace()
                            FirebaseCrashlytics.getInstance().recordException(e)
                        }
                    }
                }
                .addOnFailureListener { e ->
                    e.printStackTrace()
                    FirebaseCrashlytics.getInstance().recordException(e)
                }
        } catch (e: Exception) {
            e.printStackTrace()
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    fun activateDevice(request: ActivateDeviceRequest) = viewModelScope.launch(Dispatchers.IO) {
        try {
            val response = getMappedPhoneNumberUseCase.activateDevice(request)
            if (response.isSuccessful) {
                _activateDeviceState.postValue(com.bukuwarung.network.model.Resource.success(response.body()?.data))
            } else {
                val errorBody = JSONObject(response.errorBody()?.string().orEmpty())
                var message = if (errorBody.has("message")) {
                    errorBody.getString("message")
                } else {
                    "Some Unknown Error occurred"
                }
                _activateDeviceState.postValue(
                    com.bukuwarung.network.model.Resource.error(
                        message,
                        null
                    )
                )
            }
        } catch (e : Exception){
            bwLog(e = e)
        }

    }
}