package com.bukuwarung.edc.homepage.ui.home.dialog

import android.content.Context
import android.os.Bundle
import com.bukuwarung.edc.databinding.DialogKomisiAgenInfoBinding
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.addQuery
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants.HIDE_TOOLBAR
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL

class KomisiAgenInfoDialog(
    context: Context
): BaseDialog(context, BaseDialogType.POPUP_ROUND_CORNERED) {

    override val resId: Int = 0
    private val binding by lazy { 
        DialogKomisiAgenInfoBinding.inflate(layoutInflater).also { 
            setupViewBinding(it.root)
        }
    }

    init {
        setUseFullWidth(false)
        setCancellable(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)

        Utils.sharedPreferences.put("show_komisi_agen_dialog", false)
        binding.tvTermsAndConditions.singleClick {
            dismiss()
            openWebView(
                HomePageRemoteConfig.getKomisiAgenTermsAndConditionsUrl()
                    .addQuery("entryPoint=BUKUAGEN")
            )
        }
        binding.btnUnderstand.singleClick {
            dismiss()
            openWebView(
                HomePageRemoteConfig.getKomisiAgenDashboardUrl().addQuery("entryPoint=BUKUAGEN")
            )
        }
    }

    private fun openWebView(redirectionUrl: String){
        context.openActivity(WebviewActivity::class.java) {
            putString(WEBVIEW_URL, redirectionUrl)
            putBoolean(HIDE_TOOLBAR, true)
        }
    }
}