package com.bukuwarung.edc.homepage.ui.home.bottomsheet

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentNoDeviceRegisteredBinding
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.homepage.data.model.EdcWelcomeScreenData
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.util.textHTML
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL
import com.bukuwarung.webview.BtWebView
import com.google.gson.reflect.TypeToken

class NoSakuDeviceRegisteredBS : DialogFragment() {

    private var _binding: FragmentNoDeviceRegisteredBinding? = null
    private val binding get() = _binding!!

    private var welcomeScreenData: EdcWelcomeScreenData? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialogTheme)
        isCancelable = false
        fetchConfig()
    }

    private fun fetchConfig() {
        var welcomeScreenConfig = HomePageRemoteConfig.getEdcOrderWelcome()
        val type = object : TypeToken<EdcWelcomeScreenData>() {}.type
        val gson = com.google.gson.Gson()
        try {
            Log.d("--->welcome", "fetch welcome screen config json $welcomeScreenConfig")
            welcomeScreenData = gson.fromJson(welcomeScreenConfig, type)
        } catch (e: Exception) {
            Log.d("--->welcome", "fetch failsafe data")
            welcomeScreenConfig =
                RemoteConfigUtils.remoteConfig.getString(HomePageRemoteConfig.EDC_SAKU_ORDER_WELCOME_SCREEN_FAILSAFE)
            Log.d("--->welcome", "fetch welcome screen config local json $welcomeScreenConfig")
            welcomeScreenData = gson.fromJson(welcomeScreenConfig, type)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentNoDeviceRegisteredBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tvTitle.text = welcomeScreenData?.edcScreenTitle
        binding.tvTransfer.textHTML(welcomeScreenData?.bulletPoints?.get(0)?.description)
        binding.tvCheckBalance.textHTML(welcomeScreenData?.bulletPoints?.get(1)?.description)
        binding.tvPkh.textHTML(welcomeScreenData?.bulletPoints?.get(2)?.description)
        binding.tvRedirectWebpage.text = welcomeScreenData?.learnMore?.text

        binding.btnOne.isVisible = welcomeScreenData?.buttons?.get(0)?.isVisible == true
        binding.btnOne.singleClick {
            dismiss()
            Utils.isNoSakuDeviceRegisteredBTShown = true
            requireActivity().openActivity(WebviewActivity::class.java)
            {
                putString(
                    WEBVIEW_URL,
                    BuildConfig.API_BASE_URL + welcomeScreenData?.buttons?.get(0)?.redirectionUrl.orEmpty()
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
            dismiss()
        }

        binding.btnTwo.isVisible = welcomeScreenData?.buttons?.get(1)?.isVisible == true
        binding.btnTwo.singleClick {
            dismiss()
            Utils.isNoSakuDeviceRegisteredBTShown = true
            requireActivity().openActivity(WebviewActivity::class.java)
            {
                putString(
                    WEBVIEW_URL,
                    BuildConfig.API_BASE_URL + welcomeScreenData?.buttons?.get(1)?.redirectionUrl.orEmpty()
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
            dismiss()
        }

        binding.tvRedirectWebpage.singleClick {
            dismiss()
            BtWebView.redirectToWebViewActivity(
                welcomeScreenData?.learnMore?.redirectionUrl.orEmpty(),
                requireContext()
            )
        }

        binding.tvLewati.singleClick {
            Utils.isNoSakuDeviceRegisteredBTShown = true
            dismiss()
        }
    }
}