package com.bukuwarung.edc.homepage.ui.saldo

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.viewModelScope
import com.bukuwarung.bluetooth_printer.utils.singleClick
import com.bukuwarung.edc.databinding.LayoutHomeSaldoBonusBinding
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.payments.ui.saldo.SaldoViewModel
import com.bukuwarung.edc.util.Utils
import kotlin.getValue
import com.bukuwarung.edc.R
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.network.utils.ResourceState
import kotlinx.coroutines.launch

class HomeKomisiAgenFragment : Fragment() {

    companion object {
        private const val CONTENTS = "contents"

        fun newInstance(
            bodyContents: HomePageBody?,
            viewClickListener: ((redirectionUrl: String) -> Unit)?
        ): HomeKomisiAgenFragment {
            val fragment = HomeKomisiAgenFragment()
            fragment.arguments = Bundle().apply {
                putParcelable("contents", bodyContents)
            }
            fragment.viewClickListener = viewClickListener
            return fragment
        }
    }

    private var viewClickListener: ((redirectionUrl: String) -> Unit)? = null
    private lateinit var binding: LayoutHomeSaldoBonusBinding
    private val viewModel: SaldoViewModel by activityViewModels()
    private var homePageBody: HomePageBody? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = LayoutHomeSaldoBonusBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        homePageBody = if (Build.VERSION.SDK_INT >= 33) {
            arguments?.getParcelable(CONTENTS, HomePageBody::class.java)
        } else {
            arguments?.getParcelable(CONTENTS)
        }
        binding.tvTitle.text = homePageBody?.bodyTitle
        binding.root.singleClick{
            viewClickListener?.invoke(homePageBody?.deepLinkUrl.orEmpty())
        }
        viewModel.checkSaldoBalance()
        viewModel.saldo.observe(viewLifecycleOwner) {
            when (it) {
                is ResourceState.Success -> {
                    binding.tvDescription.text = Utils.formatAmount(it.data?.subBalance?.cashback)
                    binding.tvDescription.setTextColor(requireContext().getColorCompat(R.color.colorPrimary))
                }
                else -> {
                    binding.tvDescription.text = getString(R.string.komisi_agen_error)
                    binding.tvDescription.setTextColor(requireContext().getColorCompat(R.color.red_40))
                }
            }
        }
    }
}