package com.bukuwarung.edc.homepage.ui.tile

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.listen
import com.bumptech.glide.Glide
import javax.inject.Inject

class HomePageTileAdapter @Inject constructor(): RecyclerView.Adapter<HomePageTileAdapter.HomePageTileViewHolder>() {

    private var tileData: List<TileData> = mutableListOf()
    var itemClickListener: ((type: String?, url: String?, analyticsName: String?, checkIfRegistered: Boolean) -> Unit)? = null

    inner class HomePageTileViewHolder(view: View): RecyclerView.ViewHolder(view) {
        val tileImage: AppCompatImageView
        val tileText: AppCompatTextView

        init {
            tileImage = view.findViewById(R.id.iv_tile_image)
            tileText = view.findViewById(R.id.tv_tile_text)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HomePageTileViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.homepage_tile_item, parent, false)
        val holder = HomePageTileViewHolder(view).listen { position, type ->
            itemClickListener?.invoke(tileData[position].type, tileData[position].deeplink, tileData[position].analyticsName, tileData[position].checkIfRegistered.isTrue)
        }


        return holder
    }

    override fun onBindViewHolder(holder: HomePageTileViewHolder, position: Int) {
        val tile = tileData[position]
        holder.apply {
            Glide.with(tileImage.context)
                .load(tile.image)
                .placeholder(R.drawable.ic_bukuagen_logo)
                .centerInside()
                .into(tileImage)

            tileText.text = tile.text
        }
    }

    override fun getItemCount(): Int {
        return tileData.size
    }

    fun populateTiles(tiles: List<TileData>) {
        tileData = tiles
        notifyDataSetChanged()
    }
}