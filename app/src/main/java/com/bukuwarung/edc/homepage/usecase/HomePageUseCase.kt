package com.bukuwarung.edc.homepage.usecase

import com.bukuwarung.edc.homepage.data.model.LogonDataRequest
import com.bukuwarung.edc.homepage.data.repository.HomePageRepository
import javax.inject.Inject

class HomePageUseCase @Inject constructor(private val homePageRepository: HomePageRepository) {

    suspend fun fetchUserDetails(serialNumber:String?) = homePageRepository.fetchUserDetails(serialNumber)

    suspend fun fetchLogonData(accountId: String, logonDataRequest: LogonDataRequest) = homePageRepository.fetchLogonData(accountId, logonDataRequest)

    suspend fun userWhitelistedForLeaderboard(campaign: String, checkWhitelist: Boolean) = homePageRepository.userWhitelistedForLeaderboard(campaign, checkWhitelist)

    suspend fun getDeviceList(devicePlan: String) = homePageRepository.getDevices(devicePlan)

}