package com.bukuwarung.edc.login.data.model

import android.os.Parcelable
import com.bukuwarung.edc.payments.data.model.KycTier
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class BasicKyc(
        @SerializedName("status")
        val status: KycStatus? = null,
        @SerializedName("checks")
        val checks: List<KycChecks>?=null,
        @SerializedName("kyc_tier")
        val kycTier: KycTier? = null,
        @SerializedName("name")
        val name: String? = null,
) : Parcelable
