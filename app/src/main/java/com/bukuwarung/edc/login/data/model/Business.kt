package com.bukuwarung.edc.login.data.model

import java.io.Serializable
import java.util.Date

data class Business(
    val id: Long?,
    val businessId: String?,
    val ownerId: String?,
    val ownerName: String?,
    val category: Category?,
    val name: String?,
    val phone: String?,
    val email: String?,
    val address: Address?,
    val image: String?,
    val logo: String?,
    val tagLine: String?,
    val operational: Operational?,
    val totalEmployee: Int?,
    val totalOutlet: Int?,
    val year: String?,
    val isBnpl: Boolean?,
    val profileCompletion: Int?,
    val deleted: Boolean?,
    val createdByDevice: String?,
    val modifiedByDevice: String?,
    val createdAt: Date?,
    val lastModifiedAt: Date?,
    @Deprecated(
        message = "Fields are no longer supported by internal service. " +
                "Reason why they still exist is to support current local usages. " +
                "If you still have dependencies on them, please refactor current logic as it's no longer relevant. " +
                "For any doubt, please consult with service maintainer regarding this refactoring decision."
    )
    val temp: Temp?,
) : Serializable {
    companion object {
        val default: Business
            get() = Business(
                id = null,
                businessId = null,
                ownerId = null,
                ownerName = null,
                category = Category.default,
                name = null,
                phone = null,
                email = null,
                address = Address.default,
                image = null,
                logo = null,
                tagLine = null,
                operational = Operational.default,
                totalEmployee = null,
                totalOutlet = null,
                year = null,
                isBnpl = null,
                profileCompletion = null,
                deleted = null,
                createdByDevice = null,
                modifiedByDevice = null,
                createdAt = null,
                lastModifiedAt = null,
                temp = Temp.default,
            )
    }

    data class Category(
        val id: Long?,
        val categoryId: String?,
        val ownerId: String?,
        val name: String?,
        val displayName: Language?,
        val image: String?,
        val displayOrder: Int?,
        val deleted: Boolean?,
        val createdAt: Date?,
        val lastModifiedAt: Date?,
    ) {
        companion object {
            val default: Category
                get() = Category(
                    id = null,
                    categoryId = null,
                    ownerId = null,
                    name = null,
                    displayName = Language.default,
                    image = null,
                    displayOrder = null,
                    deleted = null,
                    createdAt = null,
                    lastModifiedAt = null,
                )
        }

        data class Language(
            val id: String?,
            val en: String?,
        ) {
            companion object {
                val default: Language
                    get() = Language(
                        id = null,
                        en = null,
                    )
            }
        }
    }

    data class Address(
        val name: String?,
        val province: String?,
        val city: String?,
        val district: String?,
        val subDistrict: String?,
        val postalCode: String?
    ) {
        companion object {
            val default: Address
                get() = Address(
                    name = null,
                    province = null,
                    city = null,
                    district = null,
                    subDistrict = null,
                    postalCode = null,
                )
        }
    }

    data class Operational(
        val startHour: String?,
        val endHour: String?,
        val days: String?,
    ) {
        companion object {
            val default: Operational
                get() = Operational(
                    startHour = null,
                    endHour = null,
                    days = null,
                )
        }
    }

    data class Temp(
        val enableSmsAlerts: Boolean?,
        val businessImageUploadPending: Int?,
        val createdByUser: String?,
        val updatedByUser: String?,
        val dirty: Int?,
        val serverSeq: Long?,
        val enabledPayment: Boolean?,
        val isGuest: Boolean?,
        val isDailyBusinessUpdateSeen: Boolean?,
        val production: String?,
        val productBuyer: String?,
        val monthlyTurnover: Int?,
    ) {
        companion object {
            val default: Temp
                get() = Temp(
                    enableSmsAlerts = null,
                    businessImageUploadPending = null,
                    createdByUser = null,
                    updatedByUser = null,
                    dirty = null,
                    serverSeq = null,
                    enabledPayment = null,
                    isGuest = null,
                    isDailyBusinessUpdateSeen = null,
                    production = null,
                    productBuyer = null,
                    monthlyTurnover = null,
                )
        }
    }
}
