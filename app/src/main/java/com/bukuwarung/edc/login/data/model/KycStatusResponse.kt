package com.bukuwarung.edc.login.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class KycStatusResponse(
        @SerializedName("status")
        val status: String? = null,
        @SerializedName("account_id")
        val accountId: String? = null,
        @SerializedName("partner_user_id")
        val partnerUserId: String? = null,
        @SerializedName("basic_kyc")
        val basicKyc: BasicKyc? = null,
        @SerializedName("kyb_data")
        val kybData: KybData? = null
) : Parcelable
