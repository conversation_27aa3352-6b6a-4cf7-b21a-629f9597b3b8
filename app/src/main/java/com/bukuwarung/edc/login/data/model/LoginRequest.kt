package com.bukuwarung.edc.login.data.model

import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.enums.AuthActions
import com.bukuwarung.edc.global.enums.CommunicationChannel
import com.bukuwarung.edc.util.Utils
import com.google.gson.annotations.SerializedName

data class LoginRequest(
    @SerializedName("countryCode")
    var countryCode: String? = null,
    @SerializedName("phone")
    var phone: String? = null,
    @SerializedName("method")
    var method: String? = CommunicationChannel.SMS.channel,
    @SerializedName("action")
    var action: String? = AuthActions.LOGIN_OTP.action,
    @SerializedName("deviceId")
    var deviceId: String? = Utils.generatedAppId(),
    @SerializedName("clientId")
    val clientId: String? = Constant.clientID,
    @SerializedName("clientSecret")
    val clientSecret: String? = Constant.clientSecret
)
