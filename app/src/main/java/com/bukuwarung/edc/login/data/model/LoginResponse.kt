package com.bukuwarung.edc.login.data.model

import com.bukuwarung.edc.global.enums.OtpStatus
import com.google.gson.annotations.SerializedName

data class LoginResponse (
    @SerializedName("status")
    var status: OtpStatus? = null,
    @SerializedName("token")
    var token: String? = null,
    @SerializedName("message")
    var message: String? = null,
    @SerializedName("recipient")
    var recipient: String? = null,
    @SerializedName("channel")
    var channel: List<String>? = null
)