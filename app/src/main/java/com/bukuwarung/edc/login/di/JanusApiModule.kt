package com.bukuwarung.edc.login.di

import com.bukuwarung.edc.login.data.repository.JanusRemoteDataSource
import com.bukuwarung.edc.login.data.repository.JanusRepository
import com.bukuwarung.edc.login.usecase.JanusUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
class JanusApiModule {

    @Provides
    fun provideJanusRemoteDataSource(@Named("normal") retrofit: Retrofit): JanusRemoteDataSource = retrofit.create(JanusRemoteDataSource::class.java)

    @Provides
    fun provideJanusRepository(dataSource: JanusRemoteDataSource) = JanusRepository(dataSource)

    @Provides
    fun provideJanusUseCase(repository: JanusRepository) = JanusUseCase(repository)

}