package com.bukuwarung.edc.login.di

import com.bukuwarung.edc.login.data.datasource.LoginDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class LoginApiModule {

    @Singleton
    @Provides
    fun provideLoginDataSource(@Named("normal") retrofit: Retrofit): LoginDataSource = retrofit.create(LoginDataSource::class.java)

}