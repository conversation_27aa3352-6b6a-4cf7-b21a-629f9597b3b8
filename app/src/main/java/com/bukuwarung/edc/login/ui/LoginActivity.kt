package com.bukuwarung.edc.login.ui

import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityLoginBinding
import com.bukuwarung.edc.global.enums.CommunicationChannel
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.enums.AuthActions
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.login.constant.LoginAnalyticsConstants
import com.bukuwarung.edc.login.constant.LoginRemoteConfig
import com.bukuwarung.edc.login.util.TncPrivacyDialog
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.*
import com.bukuwarung.edc.verifyotp.constant.ClassConstants
import com.bukuwarung.edc.verifyotp.ui.VerifyOtpActivity
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils.bwLog
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.zoho.livechat.android.ZohoLiveChat
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class LoginActivity : AppCompatActivity() {

    companion object {
        const val BUNDLE_KEY_DATA = "DATA"
        const val BUNDLE_KEY_SHOW_SESSION_ENDED_DIALOG = "show_session_ended_dialog"
    }

    private var _binding: ActivityLoginBinding? = null
    private val binding get() = _binding

    private val loginViewModel: LoginViewModel by viewModels()
    private var phoneNumber: String = ""

    lateinit var span1: ClickableSpan
    lateinit var span2: ClickableSpan

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        _binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding?.root)

        val showSessionEndedDialog =
            intent.getBundleExtra(BUNDLE_KEY_DATA)?.getBoolean(BUNDLE_KEY_SHOW_SESSION_ENDED_DIALOG).isTrue
        if (showSessionEndedDialog) showSessionEndedDialog()

        if (EncryptedPreferencesHelper.get("uuid", "").isNotEmpty()) {
            openActivity(HomePageActivity::class.java)
            finish()
        }

        binding?.pbLogin?.hideView()

        span1 = object : ClickableSpan() {
            override fun onClick(textView: View) {
                TncPrivacyDialog(
                    "Syarat & Ketentuan",
                    LoginRemoteConfig.getTncUrl(),
                    this@LoginActivity,
                    callback = {

                    }).show()
            }
        }

        span2 = object : ClickableSpan() {
            override fun onClick(textView: View) {
                TncPrivacyDialog(
                    "Kebijakan Privasi",
                    LoginRemoteConfig.getPrivacyPolicyUrl(),
                    this@LoginActivity,
                    callback = {

                    }).show()
            }
        }

        setTncText()

        binding?.etLoginPhone?.doAfterTextChanged {
            binding?.btnLogin?.isEnabled = Utils.validatePhoneNumber(it.toString())
        }
        if (Utils.getTerminalMerchantPhone().isNotNullOrBlank()) {
            binding?.etLoginPhone?.setText(Utils.getTerminalMerchantPhone())
            phoneNumber = Utils.getTerminalMerchantPhone();
            binding?.btnLogin?.isEnabled = true
            binding?.etLoginPhone?.isEnabled = false
            val spannableString =
                SpannableString(getString(R.string.you_can_only_use_registered_call_number))
            val clickableText = getString(R.string.contact_customer_care)
            val clickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    ZohoLiveChat.Chat.setLanguage("in")
                    ZohoLiveChat.Chat.show()
                    ZohoLiveChat.Visitor.setContactNumber(
                        Utils.getTerminalMerchantPhone()
                    )
                    ZohoLiveChat.Visitor.setEmail(
                        Utils.getTerminalMerchantPhone().plus("@merchant.com")
                    )
                    ZohoLiveChat.Visitor.setName("No Name")
                }
            }
            val colorSpan = ForegroundColorSpan(ContextCompat.getColor(this, R.color.colorPrimary))
            val startIndex = spannableString.indexOf(clickableText)
            val endIndex = startIndex + clickableText.length
            spannableString.setSpan(
                clickableSpan,
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannableString.setSpan(
                colorSpan,
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding?.tvLoginPrompt?.text = spannableString
            binding?.tvLoginPrompt?.movementMethod = LinkMovementMethod.getInstance()
        }

        binding?.btnLogin?.apply {
            singleClick {
                phoneNumber = binding?.etLoginPhone?.text.toString().cleanPhoneNumber()
                binding?.tvWarning?.hideView()
                loginViewModel.requestOtp(phoneNumber, CommunicationChannel.SMS, AuthActions.LOGIN_OTP)
                val map = HashMap<String, String>()
                map["phone_number"] = phoneNumber
                Analytics.trackEvent(LoginAnalyticsConstants.REGISTRATION_ASK_OTP, map)
            }
        }

        loginViewModel.login.observe(this) {
            when(it.status) {
                Status.SUCCESS -> {
                    binding?.pbLogin?.hideView()
                    binding?.tvWarning?.hideView()
                    openActivity(VerifyOtpActivity::class.java) {
                            putString(ClassConstants.VERIFY_OTP_PARAM_PHONE, phoneNumber)
                            putString(ClassConstants.VERIFY_OTP_PARAM_COUNTRY_CODE, "+62")
                    }
                }

                Status.ERROR -> {
                    binding?.pbLogin?.hideView()
                    binding?.tvWarning?.showView()
                    binding?.tvWarning?.text = it.message
                    bwLog(e = Exception("Login Exception"))
                }

                Status.LOADING -> {
                    binding?.pbLogin?.showView()
                }
                Status.NO_INTERNET -> {
                    binding?.pbLogin?.hideView()
                    binding?.btnLogin?.apply {
                        text = getString(R.string.no_internet_error)
                        showView()
                    }
                    bwLog(e = Exception("Login NO_INTERNET"))
                }
            }
        }
    }

    private fun setTncText() {
        try {
            val ss = SpannableString(getString(R.string.tnc_text))

            ss.setSpan(span1, 36, 57, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            ss.setSpan(span2, 65, 81, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

            binding?.tvLoginTnc.also {
                it?.text = ss
                it?.movementMethod = LinkMovementMethod.getInstance()
            }
        }catch (e:Exception){
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    private fun showSessionEndedDialog() {
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(R.string.session_ended),
            subTitle = getString(R.string.session_ended_description),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {},
            btnRightListener = {},
            btnLeftText = "",
            btnRightText = getString(R.string.login_again)
        )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

}