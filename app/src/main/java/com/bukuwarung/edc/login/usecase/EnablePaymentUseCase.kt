package com.bukuwarung.edc.login.usecase

import com.bukuwarung.edc.login.data.model.LoginRequest
import com.bukuwarung.edc.login.data.model.LoginResponse
import com.bukuwarung.edc.login.data.repository.LoginRepository
import com.bukuwarung.edc.payments.data.model.EnableCustomerResponse
import com.bukuwarung.edc.payments.data.repository.PaymentRepository
import retrofit2.Response
import javax.inject.Inject

class EnablePaymentUseCase @Inject constructor(private val paymentRepository: PaymentRepository) {

    suspend fun enablePayment(accountId:String, businessName:String): Response<EnableCustomerResponse>
        = paymentRepository.enablePaymentAccount(accountId, mapOf(Pair("business_name", businessName)))
}