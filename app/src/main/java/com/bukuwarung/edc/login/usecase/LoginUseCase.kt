package com.bukuwarung.edc.login.usecase

import com.bukuwarung.edc.login.data.model.LoginRequest
import com.bukuwarung.edc.login.data.model.LoginResponse
import com.bukuwarung.edc.login.data.repository.LoginRepository
import retrofit2.Response
import javax.inject.Inject

class LoginUseCase @Inject constructor(private val loginRepository: LoginRepository) {

    suspend fun fetchOtp(loginRequest: LoginRequest, phone: String): Response<LoginResponse> =
        loginRepository.fetchOtp(loginRequest, phone)
}