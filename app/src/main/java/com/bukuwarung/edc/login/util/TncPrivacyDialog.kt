package com.bukuwarung.edc.login.util

import android.content.Context
import android.graphics.Color
import android.graphics.Outline
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewOutlineProvider
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.google.android.material.button.MaterialButton

class TncPrivacyDialog(
    private val title: String,
    private val urlWebView: String,
    context: Context,
    private val callback: () -> Unit
) : BaseDialog(context, BaseDialogType.FULL_SCREEN) {

    init {
        setUseFullWidth(false)
        setCancelable(true)
    }

    override val resId: Int
        get() {
            return R.layout.bottomsheet_privacy_tnc
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val webView: WebView = findViewById(R.id.wv_content)
        webView.apply {
            webViewClient = WebViewClient()
            loadUrl(urlWebView)
        }

        findViewById<MaterialButton>(R.id.btn_okay).setOnClickListener {
            callback()
            dismiss()
        }

        this.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val provider: ViewOutlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 40f)
                }
            }

            val clTnc: ConstraintLayout = findViewById(R.id.cl_tnc)
            clTnc.apply {
                outlineProvider = provider
                clipToOutline = true
            }
        }

        findViewById<AppCompatTextView>(R.id.tv_title).text = title
    }


}