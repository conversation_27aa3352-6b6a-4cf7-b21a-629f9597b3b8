package com.bukuwarung.edc.order.orderdetail.api

import com.bukuwarung.edc.order.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.edc.order.orderdetail.model.EdcProductDetails
import com.bukuwarung.edc.order.orderdetail.model.OrderCancelResponse
import com.bukuwarung.edc.order.orderdetail.model.RefundRequest
import com.bukuwarung.edc.order.orderdetail.model.RefundResponse
import com.bukuwarung.edc.order.orderdetail.model.RegeneratePaymentLinkResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface EdcOrderDetailApi {

    @GET("ac/api/v2/edc/order/v2")
    suspend fun getEdcOrderDetailByPhoneNumber(): Response<EdcOrderDetailResponse>

    @GET("ac/api/v2/edc/order/{order_id}")
    suspend fun getEdcOrderDetailByOrderId(@Path("order_id") orderId: String): Response<EdcOrderDetailResponse>

    @GET("ac/api/v2/cart/view/{order_id}")
    suspend fun getEdcProductDetails(@Path("order_id") orderId: String): Response<EdcProductDetails>

    @POST("ac/api/v2/edc/order/{order_id}/cancel")
    suspend fun cancelEdcOrder(@Path("order_id") orderId: String): Response<OrderCancelResponse>

    @POST("ac/api/v2/edc/payment/regenerate-link/order/{orderId}")
    suspend fun regeneratePaymentLink(@Path("orderId") orderId: String): Response<RegeneratePaymentLinkResponse>

    @POST("ac/api/v2/edc/payment/refund/{orderId}")
    suspend fun refundPayment(
        @Path("orderId") orderId: String,
        @Body request: RefundRequest
    ): Response<RefundResponse>
}