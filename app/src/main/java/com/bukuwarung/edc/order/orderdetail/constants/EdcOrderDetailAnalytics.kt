package com.bukuwarung.edc.order.orderdetail.constants

object EdcOrderDetailAnalytics {
    const val EVENT_CANCEL_ORDER_CLICKED = "cancel_order_clicked"
    const val EVENT_PAY_AGAIN = "i_want_to_pay_back_clicked"
    const val EVENT_EDC_ORDER_DETAIL_VIEWED = "edc_order_detail_viewed"
    const val EVENT_EDC_REFUND_BANK_ACCOUT_SELECTED = "edc_refund_bank_account_selected"

    const val PLAN = "plan"
    const val STATUS = "status"
    const val PAYMENT_STATUS = "payment_status"
    const val TRANSACTION_ID = "transaction_id"
    const val ORDER_ID = "order_id"
    const val BANK_ACCOUNT_NUMBER = "bank_account_number"
    const val BANK_NAME = "bank_name"
    const val SKU_PRICE = "sku_price"
    const val PRICE_AFTER_DISCOUNT = "price_after_discount"
    const val PAYMENT_METHOD = "payment_method"
    const val IS_KYC_DONE = "is_kyc_done"
    const val IS_KYB_DONE = "is_kyb_done"
    const val IS_BANK_ACCOUNT_ADDED = "is_bank_account_added"
    const val ORDER_CHANNEL = "order_channel"

}