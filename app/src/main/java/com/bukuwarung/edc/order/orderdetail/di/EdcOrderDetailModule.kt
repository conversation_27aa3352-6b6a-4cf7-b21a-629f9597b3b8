package com.bukuwarung.edc.order.orderdetail.di

import com.bukuwarung.edc.order.orderdetail.api.EdcOrderDetailApi
import com.bukuwarung.edc.order.orderdetail.repo.EdcOrderDetailRepo
import com.bukuwarung.edc.order.orderdetail.usecase.EdcOrderDetailUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named


@Module
@InstallIn(SingletonComponent::class)
class EdcOrderDetailModule {

    @Provides
    fun provideEdcOrderDetailDataSource(@Named("normal") retrofit: Retrofit): EdcOrderDetailApi {
        return retrofit.create(EdcOrderDetailApi::class.java)
    }

    @Provides
    fun provideEdcOrderDetailRepository(api: EdcOrderDetailApi) = EdcOrderDetailRepo(api)

    @Provides
    fun provideEdcOrderDetailUseCase(repository: EdcOrderDetailRepo) =
        EdcOrderDetailUseCase(repository)

}