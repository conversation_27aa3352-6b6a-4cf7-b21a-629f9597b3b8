package com.bukuwarung.edc.order.orderdetail.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.bukuwarung.activities.edc.orderdetail.ui.EdcCancelOrderBS
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.bluetooth_printer.utils.StateDialog
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.activation.ui.EdcActivationFragment
import com.bukuwarung.edc.card.activation.ui.constant.CardActivationConstants
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity.Companion.ADDED_BANK_ACCOUNT
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity.Companion.ENTRY_POINT
import com.bukuwarung.edc.card.data.model.EdcOrderWarrantyNudgeBody
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.EDC_ORDER_WARRANTY_NUDGE
import com.bukuwarung.edc.databinding.ActivityEdcOrderDetailsBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.order.orderdetail.constants.EdcOrderDetailAnalytics
import com.bukuwarung.edc.order.orderdetail.model.Beneficiary
import com.bukuwarung.edc.order.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.edc.order.orderdetail.model.RefundRequest
import com.bukuwarung.edc.order.orderdetail.ui.EdcOrderDetailsActivity.OrderStatus
import com.bukuwarung.edc.order.orderdetail.ui.bottomsheet.BankAccountListBottomSheetFragment
import com.bukuwarung.edc.order.orderdetail.viewmodel.EdcOrderDetailViewModel
import com.bukuwarung.edc.order.orderhistory.enums.EdcOrderStatus
import com.bukuwarung.edc.order.orderhistory.enums.EdcOrderType
import com.bukuwarung.edc.order.orderhistory.enums.isPending
import com.bukuwarung.edc.order.orderhistory.enums.isRejected
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig.getPaymentConfigs
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.ToastUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.addQuery
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.colorText
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.getClassTag
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isFalse
import com.bukuwarung.edc.util.isFalseOrNull
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.orDash
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.setDrawableRightListener
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.zoho.livechat.android.ZohoLiveChat
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EdcOrderDetailsActivity : AppCompatActivity(),
    BankAccountListBottomSheetFragment.BtSheetBankAccountListener {

    private lateinit var binding: ActivityEdcOrderDetailsBinding
    private val viewModel: EdcOrderDetailViewModel by viewModels()
    private val orderId by lazy { intent.getStringExtra(ORDER_ID) }
    private var orderStatus: OrderStatus? = null
    private var edcOrderDetailResponse: EdcOrderDetailResponse? = null
    private var isBillingDetailsExpanded = true
    private var isOrderRejected = false
    private var merchantBankAccountsList: List<BankAccount>? = null
    private var hideBottomView = true
    private var showError = false
    private var edcPlan = ""
    private var isBukuWarungOrder = true
    private var isTikTokOrder = false
    private var isEZAOrder = false
    private var vendor: String = ""
    private var isMorefunInsurance = false
    private val entryPoint by lazy { if (Utils.isAtmPro()) "MINIATMPRO" else "BUKUAGEN" }
    private val warrantyNudge by lazy {
        Utils.sharedPreferences.get(EDC_ORDER_WARRANTY_NUDGE, "")
    }
    private var isKycKybInitiated = false
    private var dialog: StateDialog? = null

    companion object {
        const val ORDER_ID = "ORDER_ID"
        private const val CANCELLED = "Cancelled"
        private const val UNASSIGNED = "Unassigned"
        private const val STATUS_PAID = "PAID"
        const val STATUS_REFUNDING = "REFUNDING"
        const val STATUS_REFUNDED = "REFUNDED"
        const val STATUS_REFUNDING_FAILED = "REFUNDING_FAILED"
        private const val RENTAL = "RENTAL"
        const val EDC_ORDER_DETAIL = "EDC_ORDER_DETAIL"

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEdcOrderDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setToolbar()
        checkFragmentState()
        if(orderStatus ==OrderStatus.READY_FOR_ACTIVATION){
            getWarrantyNudge()
        }
    }

    private fun setToolbar() {
        binding.btnBack.singleClick { onBackPressed() }
    }

    private fun callZohoCustomerCare() {
        ZohoLiveChat.Chat.setLanguage("in")
        ZohoLiveChat.Chat.show()
        ZohoLiveChat.Visitor.setContactNumber(
            Utils.sharedPreferences.getString("phone_number", null).orEmpty().plus("-edc")
        )
        ZohoLiveChat.Visitor.setEmail(
            Utils.sharedPreferences.getString("phone_number", null).orEmpty()
                .plus("@merchant.com")
        )
        ZohoLiveChat.Visitor.setName("No Name")
    }


    override fun onResume() {
        super.onResume()
        getOrderDetailsByOrderId(orderId)
        if (isKycKybInitiated) {
            viewModel.refreshToken()
        }
        observer()
    }

    override fun onBackPressed() {
        goToDestination(HomePageActivity::class.java)
    }

    private fun getOrderDetailsByOrderId(orderId: String?) {
        viewModel.fetchMerchantBankAccounts()
        orderId?.let {
            viewModel.getEdcOrderDetailsByOrderId(it)
        }
    }

    private fun observer() {
        viewModel.deviceBlacklisted.observe(this) {
            if (it.isTrue) {
                Utils.clearDataAndLogout(true)
            }
        }
        viewModel.refundResponse.observe(this) {
            if (it?.success.isFalseOrNull) {
                ToastUtil.setToast(this, 3, "Adding Refund Bank Account Failed.", binding.tvTitle)
            }
        }
        viewModel.merchantBankAccounts.observe(this) {
            merchantBankAccountsList = it
        }
        viewModel.cancelOrder.observe(this) {
            if (it.not()) {
                ToastUtil.setToast(this, 3, "Cancelling Order Failed.", binding.tvTitle)
            }
        }
        viewModel.regeneratePaymentLink.observe(this) {
            if (it?.result.isTrue) {
                startWebview(it?.data?.paymentUrl.orEmpty())
            } else {
                ToastUtil.setToast(this, 3, "Regenerating Payment Link Failed.", binding.tvTitle)
            }
        }
        viewModel.showLoader.observe(this) {
            binding.pbLoader.visibility = it.asVisibility()
            binding.clBottom.visibility = (it?.not().isTrue && hideBottomView.not()).asVisibility()
            binding.nsvDetails.visibility = (it?.not().isTrue && showError.isFalse).asVisibility()
        }
        viewModel.combinedOrderProductResponse.observe(this) { (orderResponse, productResponse) ->
            val purchaseType =
                if (productResponse?.data?.products?.firstOrNull()?.plans?.firstOrNull()?.planType?.equals(
                        RENTAL
                    ).isTrue
                ) "Sewa" else getString(
                    R.string.buy
                )

            val orderChannel = when {
                isBukuWarungOrder -> "BUKUAGEN"
                isTikTokOrder -> "TIKTOK"
                isEZAOrder -> "EZA"
                else -> ""
            }
            // If the order is from EZA, then the vendor name will be fetched from the order response. AS the product response will be null, cartId not there
            vendor = if (orderChannel.equals(
                    "EZA",
                    false
                )
            ) orderResponse?.data?.deviceMappingDetails?.vendor.orEmpty() else productResponse?.data?.products?.firstOrNull()?.name.orEmpty()
            isMorefunInsurance =
                productResponse?.data?.products?.firstOrNull()?.plans?.firstOrNull()?.name?.contains(
                    "EDC_MOREFUN_INSURANCE", false
                ).isTrue

            val productName = productResponse?.data?.products?.firstOrNull()?.description.orEmpty()
            binding.tvDeviceTitle.text = "$purchaseType $productName"
            val map = HashMap<String, String>()
            edcPlan =
                productResponse?.data?.products?.firstOrNull()?.plans?.firstOrNull()?.name.orEmpty()
            map[EdcOrderDetailAnalytics.PLAN] = edcPlan
            map[EdcOrderDetailAnalytics.SKU_PRICE] =
                Utils.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.productPrice)
            val edcDiscounts = edcOrderDetailResponse?.data?.orderAmountDetails?.discount
            edcDiscounts?.map { edcDiscount ->
                map[edcDiscount.key] = Utils.formatAmount(edcDiscount.value)
            }
            map[EdcOrderDetailAnalytics.PRICE_AFTER_DISCOUNT] =
                Utils.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.totalPayableAmount)
            map[EdcOrderDetailAnalytics.PAYMENT_METHOD] =
                edcOrderDetailResponse?.data?.paymentDetails?.paymentMethod.orDash
            map[EdcOrderDetailAnalytics.IS_KYC_DONE] =
                edcOrderDetailResponse?.data?.checks?.kycDone.isTrue.toString()
            map[EdcOrderDetailAnalytics.IS_KYB_DONE] =
                edcOrderDetailResponse?.data?.checks?.kybDone.isTrue.toString()
            map[EdcOrderDetailAnalytics.IS_BANK_ACCOUNT_ADDED] =
                edcOrderDetailResponse?.data?.checks?.bankAccountAdded.isTrue.toString()
            map[EdcOrderDetailAnalytics.STATUS] = edcOrderDetailResponse?.data?.status.orEmpty()
            map[EdcOrderDetailAnalytics.PAYMENT_STATUS] =
                edcOrderDetailResponse?.data?.paymentDetails?.status.orEmpty()
            map[EdcOrderDetailAnalytics.ORDER_ID] = edcOrderDetailResponse?.data?.orderId.orEmpty()

            map[EdcOrderDetailAnalytics.ORDER_CHANNEL] = orderChannel
            Analytics.trackEvent(EdcOrderDetailAnalytics.EVENT_EDC_ORDER_DETAIL_VIEWED, map)

            if (orderResponse?.result.isTrue) {
                edcOrderDetailResponse = orderResponse
                showError = false
                isBukuWarungOrder = orderResponse?.data?.deviceMappingDetails?.type?.equals(
                    EdcOrderType.BUKUWARUNG.name,
                    true
                ).isTrue
                isTikTokOrder = orderResponse?.data?.deviceMappingDetails?.type?.equals(
                    EdcOrderType.TIKTOK.name,
                    true
                ).isTrue
                isEZAOrder = orderResponse?.data?.deviceMappingDetails?.type?.equals(
                    EdcOrderType.EZA.name,
                    true
                ).isTrue
                if (isBukuWarungOrder.isFalse) {
                    binding.tvDeviceTitle.text =
                        orderResponse?.data?.deviceMappingDetails?.description.orEmpty()
                }
                //cache the orderId so that it can be used for opening this screen after completing the kyc.
                Utils.edcOrderId = edcOrderDetailResponse?.data?.orderId.orEmpty()
                orderStatus = when {
                    orderResponse?.data?.status?.equals(
                        CANCELLED,
                        true
                    ).isTrue -> OrderStatus.CANCELLED

                    orderResponse?.data?.status?.equals(
                        UNASSIGNED,
                        true
                    ).isTrue && orderResponse?.data?.paymentDetails?.paymentLink?.isNotNullOrBlank().isTrue -> OrderStatus.PENDING

                    orderResponse?.data?.status?.equals(
                        UNASSIGNED,
                        true
                    ).isTrue && orderResponse?.data?.paymentDetails?.paymentLink.isNullOrBlank().isTrue -> OrderStatus.EXPIRED

                    orderResponse?.data?.paymentDetails?.status?.equals(
                        STATUS_PAID,
                        true
                    ).isTrue && orderResponse?.data?.status?.equals(
                        "Waiting_for_ops",
                        true
                    ).isTrue -> OrderStatus.READY_FOR_ACTIVATION

                    orderResponse?.data?.paymentDetails?.status?.equals(
                        STATUS_PAID,
                        true
                    ).isTrue -> OrderStatus.COMPLETED

                    orderResponse?.data?.paymentDetails?.status?.equals(
                        STATUS_REFUNDING,
                        true
                    ).isTrue -> OrderStatus.REFUNDING_IN_PROGRESS

                    orderResponse?.data?.paymentDetails?.status?.equals(
                        STATUS_REFUNDED,
                        true
                    ).isTrue -> OrderStatus.REFUNDING_SUCCESSFUL

                    orderResponse?.data?.paymentDetails?.status?.equals(
                        STATUS_REFUNDING_FAILED,
                        true
                    ).isTrue -> OrderStatus.REFUNDING_FAILED

                    else -> OrderStatus.PENDING
                }
                isOrderRejected =
                    edcOrderDetailResponse?.data?.status.equals(EdcOrderStatus.REJECTED.name, true)
                loadTopDetails()
                loadRefundInfo()
                loadRefundView()
                loadEdcOrderSteps()
                loadTrackOrder()
                loadMyOrders()
                loadBillingDetails()
                loadBottomView(vendor, isMorefunInsurance)
                if (orderResponse?.data?.status?.equals(EdcOrderStatus.WAITING_FOR_ADDITIONAL_DOC.name, true).isTrue) {
                    showDialogVerification(orderResponse?.data?.orderId.orEmpty())
                }
            } else {
                showErrorView()
            }
        }

    }

    private fun showErrorView() = with(binding) {
        hideBottomView = true
        showError = true
        clBottom.hideView()
        nsvDetails.hideView()
        bukuErrorView.showView()
        bukuErrorView.setErrorType(
            type = BaseErrorView.Companion.ErrorType.SERVER_UNREACHABLE,
            message = getString(R.string.loading_error_message)
        )
        bukuErrorView.addCallback(serverErrorViewCallBack)
    }

    private var serverErrorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            goToDestination(HomePageActivity::class.java)
        }

        override fun messageClicked() {}
    }

    private fun loadTopDetails() = with(binding) {
        tvDateValue.text = DateTimeUtils.getUTCTimeToLocalDateTime(
            edcOrderDetailResponse?.data?.paymentDetails?.paymentDate,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        tvOrderTypeValue.text = when{
            isBukuWarungOrder -> getString(R.string.bukuwarung_order_type)
            isTikTokOrder -> getString(R.string.tiktok_order_type)
            isEZAOrder -> getString(R.string.eza_order_type)
            else -> getString(R.string.bukuwarung_order_type)
        }
        tvInvoiceValue.text = edcOrderDetailResponse?.data?.invoiceNumber.orEmpty()
        ivInvoiceValue.singleClick {
            Utils.copyToClipboard(
                edcOrderDetailResponse?.data?.invoiceNumber.orEmpty(),
                this@EdcOrderDetailsActivity,
                getString(R.string.invoice_number_copied)
            )
        }
        when (orderStatus) {
            OrderStatus.PENDING -> {
                tvStatusValue.text = getString(R.string.in_process)
                tvStatusValue.setTextColor(getColorCompat(R.color.dark_yellow))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_yellow5_corner_4dp)
                tvStatusValue.setDrawable(0, 0, 0, 0)
            }

            OrderStatus.COMPLETED, OrderStatus.READY_FOR_ACTIVATION -> {
                tvStatusValue.text = getString(R.string.paid_label)
                tvStatusValue.setTextColor(getColorCompat(R.color.green_60))
                tvStatusValue.background =
                    getDrawableCompat(R.drawable.bg_solid_green5_stroke_neutral)
                tvStatusValue.setDrawable(
                    R.drawable.ic_tick_green_bg,
                    0,
                    0,
                    0
                )
            }

            OrderStatus.EXPIRED -> {
                tvStatusValue.text = getString(R.string.expired_status)
                tvStatusValue.setTextColor(getColorCompat(R.color.red_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_red5_corner_8dp)
                tvStatusValue.setDrawable(0, 0, 0, 0)
            }

            OrderStatus.CANCELLED -> {
                tvStatusValue.text = getString(R.string.cancelled_label)
                tvStatusValue.setTextColor(getColorCompat(R.color.red_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_red5_corner_8dp)
                tvStatusValue.setDrawable(0, 0, 0, 0)
            }

            OrderStatus.REFUNDING_IN_PROGRESS -> {
                tvStatusValue.text = getString(R.string.returns_processing)
                tvStatusValue.setTextColor(getColorCompat(R.color.dark_yellow))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_yellow5_corner_4dp)
                tvStatusValue.setDrawable(R.drawable.ic_clock_in_progress, 0, 0, 0)
            }

            OrderStatus.REFUNDING_FAILED -> {
                tvStatusValue.text = getString(R.string.refund_failed_1)
                tvStatusValue.setTextColor(getColorCompat(R.color.red_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_red5_corner_8dp)
                tvStatusValue.setDrawable(R.drawable.ic_cross_filled_circle, 0, 0, 0)
            }

            OrderStatus.REFUNDING_SUCCESSFUL -> {
                tvStatusValue.text = getString(R.string.refund_successful)
                tvStatusValue.setTextColor(getColorCompat(R.color.green_60))
                tvStatusValue.background =
                    getDrawableCompat(R.drawable.bg_solid_green5_stroke_neutral)
                tvStatusValue.setDrawable(
                    com.bukuwarung.bluetooth_printer.R.drawable.ic_checklist_rounded,
                    0,
                    0,
                    0
                )
            }

            else -> {

            }
        }
    }

    private fun loadRefundInfo() = with(binding) {
        refundInfo.tvSetRefund.text = getString(R.string.money_will_be_returned)
        refundInfo.tvSetReceiver.hideView()
        refundInfo.btnChooseRefundMethod.hideView()
        refundInfo.includeBankLayout.root.showView()
        Glide.with(this@EdcOrderDetailsActivity)
            .load(edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.logoUrl)
            .placeholder(R.drawable.ic_bank)
            .error(R.drawable.ic_bank)
            .into(refundInfo.includeBankLayout.ivBank)
        val bankName = edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.bankName.orEmpty()
        val beneficiaryName =
            edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.beneficiaryName.orEmpty()
        refundInfo.includeBankLayout.tvBankName.text = "$bankName - $beneficiaryName"
        refundInfo.includeBankLayout.tvAccountNumber.text =
            Utils.maskSensitiveInfo(edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.accountNumber.orEmpty())
        refundInfo.includeBankLayout.tvEdit.hideView()
        when {
            edcOrderDetailResponse?.data?.beneficiaryAccountDetails == null -> {
                refundInfo.root.hideView()
            }

            orderStatus == OrderStatus.REFUNDING_IN_PROGRESS -> {
                refundInfo.root.showView()
                refundInfo.clRefundLayout.background =
                    getDrawableCompat(R.drawable.bg_rounded_rectangle_blue_5)
            }

            orderStatus == OrderStatus.REFUNDING_SUCCESSFUL -> {
                refundInfo.root.showView()
                refundInfo.clRefundLayout.background =
                    getDrawableCompat(R.drawable.bg_rounded_rectangle_green_5)
                refundInfo.tvSetRefund.setTextColor(getColorCompat(R.color.green_60))
            }

            else -> {
                refundInfo.root.hideView()
            }
        }
    }

    private val startAddBankAccountActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.getParcelableExtra<SettlementBankAccount>(ADDED_BANK_ACCOUNT)
                    ?.let {
                        addRefundBankAccount(
                            BankAccount(
                                bankCode = it.bankCode,
                                accountNumber = it.accountNumber,
                                accountHolderName = it.beneficiaryName
                            )
                        )
                    }
            }
        }

    private fun addRefundBankAccount(bankAccount: BankAccount?) {
        val map = HashMap<String,String>()
        map[EdcOrderDetailAnalytics.STATUS] = edcOrderDetailResponse?.data?.status.orEmpty()
        map[EdcOrderDetailAnalytics.PAYMENT_STATUS] = edcOrderDetailResponse?.data?.paymentDetails?.status.orEmpty()
        map[EdcOrderDetailAnalytics.ORDER_ID] = edcOrderDetailResponse?.data?.orderId.orEmpty()
        map[EdcOrderDetailAnalytics.BANK_ACCOUNT_NUMBER] = bankAccount?.accountNumber.orEmpty()
        map[EdcOrderDetailAnalytics.BANK_NAME] = bankAccount?.bankName.orEmpty()
        Analytics.trackEvent(EdcOrderDetailAnalytics.EVENT_EDC_REFUND_BANK_ACCOUT_SELECTED, map)
        viewModel.refundPayment(
            orderId.orEmpty(),
            RefundRequest(
                Beneficiary(
                    type = "BANK",
                    code = bankAccount?.bankCode.orEmpty(),
                    number = bankAccount?.accountNumber.orEmpty(),
                    name = bankAccount?.accountHolderName.orEmpty()
                )
            )
        )
    }

    private fun loadRefundView() = with(binding) {
        val checks = edcOrderDetailResponse?.data?.checks
        when {
            isOrderRejected && orderStatus == OrderStatus.COMPLETED -> {
                if (isBukuWarungOrder){
                    tvRefundTitle.text = getString(R.string.verification_period_completed)
                    tvRefundDesc.text = getString(R.string.select_refund_method_for_edc_purchase)
                    btnSelect.showView()
                    btnSelect.singleClick {
                        if (merchantBankAccountsList?.isEmpty().isTrue) {
                            addNewRefundBankAccount(false)
                        } else {
                            val bankListBtSheet =
                                BankAccountListBottomSheetFragment.createBankAccountInstance(EDC_ORDER_DETAIL)
                            bankListBtSheet.show(supportFragmentManager, "bankListBtSheet")
                        }
                    }
                    btnHelp.hideView()
                } else {
                    tvRefundTitle.text = getString(R.string.verification_period_completed)
                    if (isTikTokOrder) {
                        tvRefundDesc.text = getString(R.string.tiktok_order_cancelled_message)
                    } else if (isEZAOrder){
                        tvRefundDesc.text = getString(R.string.eza_order_cancelled_message)
                    }
                    btnSelect.hideView()
                    btnHelp.hideView()
                }
            }

            orderStatus == OrderStatus.COMPLETED && (checks?.kycStatus.isRejected() || checks?.kybStatus.isRejected()) -> {
                clRefund.showView()
                tvRefundTitle.text = getString(R.string.account_verification_failed)
                tvRefundDesc.text = getString(R.string.reverify_account)
                btnSelect.showView()
                btnHelp.hideView()
                btnSelect.singleClick { startWebview(getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}&landing=BUKUAGEN") }
            }

            orderStatus == OrderStatus.REFUNDING_FAILED && isBukuWarungOrder -> {
                clRefund.showView()
                tvRefundTitle.text = getString(R.string.refund_failed)
                tvRefundDesc.text = getString(R.string.refund_failed_message)
                btnSelect.hideView()
                btnHelp.showView()
                btnHelp.singleClick {
                    callZohoCustomerCare()
                }
            }

            else -> {
                clRefund.hideView()
            }
        }
    }

    private fun loadEdcOrderSteps() = with(binding) {
        stepOne.setStepTitle(getString(R.string.type_of_payment))
        stepOne.setStepNumber("1")
        stepTwo.setStepTitle(getString(R.string.identity_verification))
        stepTwo.setStepNumber("2")
        if(!Utils.isAtmPro()){
            stepThree.setStepTitle(getString(R.string.priority_account_verification))
            stepThree.setStepNumber("3")
        }else{
            // As KYB is not required to complete partnership orders.
            stepThree.hideView()
        }
        stepFour.setStepTitle(getString(R.string.enter_bank_account))
        stepFour.setStepNumber("4")
        when (orderStatus) {
            OrderStatus.CANCELLED, OrderStatus.EXPIRED -> binding.clOrderSteps.hideView()
            OrderStatus.PENDING -> {
                clOrderSteps.showView()
                tvWarning.hideView()
                stepOne.setCurrentStep()
                stepOne.singleClick { startWebview(edcOrderDetailResponse?.data?.paymentDetails?.paymentLink.orEmpty()) }
                if (edcOrderDetailResponse?.data?.checks?.kycDone.isTrue) stepTwo.setDone() else stepTwo.setDisabledStep()
                if (edcOrderDetailResponse?.data?.checks?.kybDone.isTrue) stepThree.setDone() else stepThree.setDisabledStep()
                if (edcOrderDetailResponse?.data?.checks?.bankAccountAdded.isTrue) stepFour.setDone() else stepFour.setDisabledStep()
                stepTwo.singleClick { }//empty click listener
                stepTwo.singleClick { }//empty click listener
                stepFour.singleClick { }//empty click listener
            }

            else -> {
                binding.clOrderSteps.showView()
                val checks = edcOrderDetailResponse?.data?.checks
                ivSteps.setImageResource(R.drawable.ic_sparkles)
                when {
                    checks?.kycDone.isTrue && checks?.kybDone.isTrue && checks?.bankAccountAdded.isTrue -> {
                        tvWarning.hideView()
                        tvEdcStepTitle.text = getString(R.string.edc_is_ready_title)
                        tvEdcStepDesc.text = getString(R.string.edc_is_ready_desc)
                    }

                    orderStatus == OrderStatus.COMPLETED && isOrderRejected.isFalse -> {
                        tvWarning.visibility = isBukuWarungOrder.asVisibility()
                        val time = DateTimeUtils.getUTCTimeToLocalDateTime(
                            edcOrderDetailResponse?.data?.checks?.completionExpireDate,
                            DateTimeUtils.DD_MMM_YYYY_HH_MM
                        )
                        tvWarning.text = getString(R.string.complete_details_by, time)
                        tvWarning.setTextColor(getColorCompat(R.color.grey_91))
                        ivSteps.setImageResource(R.drawable.ic_alert_triangle)
                        if (checks?.kybStatus.isPending() && checks?.bankAccountAdded.isTrue) {
                            tvEdcStepTitle.text = getString(R.string.edc_purchase_verification_pending_title)
                            tvEdcStepDesc.text = getString(R.string.edc_purchase_verification_pending_subtitle)
                            ivSteps.setImageResource(R.drawable.ic_sparkles)
                        }
                    }

                    orderStatus == OrderStatus.COMPLETED && (checks?.kycStatus.isPending() || checks?.kybStatus.isPending()) && checks?.bankAccountAdded.isTrue -> {
                        tvWarning.hideView()
                        tvEdcStepTitle.text = getString(R.string.edc_purchase_verification_pending_title)
                        tvEdcStepDesc.text = getString(R.string.edc_purchase_verification_pending_subtitle)
                    }

                    orderStatus == OrderStatus.COMPLETED && (checks?.kycStatus.isRejected() || checks?.kybStatus.isRejected()) -> {
                        ivSteps.setImageResource(R.drawable.ic_alert_triangle)
                        val time = DateTimeUtils.getUTCTimeToLocalDateTime(
                            edcOrderDetailResponse?.data?.checks?.completionExpireDate,
                            DateTimeUtils.DD_MMM_YYYY_HH_MM
                        )
                        tvWarning.showView()
                        tvWarning.setTextColor(getColorCompat(R.color.red_60))
                        val completeText =
                            getString(R.string.edc_verification_failed) + "\n\n" + getString(
                                R.string.complete_details_by,
                                time
                            )
                        tvWarning.text = SpannableStringBuilder(completeText).colorText(
                            getString(R.string.complete_details_by, time),
                            getColorCompat(R.color.grey_91),
                            true
                        )
                        tvEdcStepDesc.text = getString(R.string.edc_purchase_verification_failed)
                    }
                    else -> {
                        tvWarning.showView()
                        tvWarning.text = when{
                            isBukuWarungOrder -> getString(R.string.edc_purchase_expired)
                            isTikTokOrder -> getString(R.string.tiktok_refund_error_message)
                            isEZAOrder -> getString(R.string.eza_refund_error_message)
                            else -> getString(R.string.edc_purchase_expired)
                        }
                        tvWarning.setTextColor(getColorCompat(R.color.red_60))
                    }
                }
                stepOne.setDone()
                stepOne.singleClick { }//empty click listener
                if (edcOrderDetailResponse?.data?.checks?.kycDone.isTrue) {
                    stepTwo.setDone()
                    stepTwo.singleClick { }//empty click listener
                } else {
                    when {
                        isOrderRejected -> {
                            stepTwo.setDisabledStep()
                            stepTwo.singleClick { }//empty click listener
                        }

                        checks?.kycStatus.isPending() -> {
                            stepTwo.singleClick { }
                            stepTwo.setPendingStep()
                        }

                        checks?.kycStatus.isRejected() -> {
                            isKycKybInitiated = true
                            stepTwo.singleClick { startWebview(getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}&landing=BUKUAGEN") }
                            stepTwo.setRejectedStep()
                        }

                        orderStatus == OrderStatus.COMPLETED && isOrderRejected.not() -> {
                            isKycKybInitiated = true
                            stepTwo.setCurrentStep()
                            stepTwo.singleClick { startWebview(getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}&landing=BUKUAGEN") }
                        }

                        else -> {
                            stepTwo.setDisabledStep()
                            stepTwo.singleClick { }//empty click listener
                        }
                    }
                }
                if (edcOrderDetailResponse?.data?.checks?.kybDone.isTrue) {
                    stepThree.setDone()
                    stepThree.singleClick { }//empty click listener
                } else {
                    when {
                        isOrderRejected -> {
                            stepThree.setDisabledStep()
                            stepThree.singleClick { }//empty click listener
                        }

                        checks?.kybStatus.isPending() -> {
                            stepThree.singleClick { }
                            stepThree.setPendingStep()
                        }

                        checks?.kybStatus.isRejected() -> {
                            isKycKybInitiated = true
                            stepThree.singleClick { startWebview(getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}&landing=BUKUAGEN") }
                            stepThree.setRejectedStep()
                        }

                        orderStatus == OrderStatus.COMPLETED && isOrderRejected.not() -> {
                            isKycKybInitiated = true
                            stepThree.setCurrentStep()
                            stepThree.singleClick { startWebview(getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}&landing=BUKUAGEN") }
                        }

                        else -> {
                            stepThree.setDisabledStep()
                            stepThree.singleClick { }//empty click listener
                        }
                    }
                }
                if (edcOrderDetailResponse?.data?.checks?.bankAccountAdded.isTrue) {
                    stepFour.setDone()
                    stepFour.singleClick { }//empty click listener
                } else {
                    if (orderStatus == OrderStatus.COMPLETED && isOrderRejected.not() && edcOrderDetailResponse?.data?.checks?.kycDone.isTrue) {
                        stepFour.setCurrentStep()

                        stepFour.singleClick {
                            startWebview(
                                getPaymentConfigs().merchantBanksUrl.addQuery(
                                    "entryPoint=$entryPoint"
                                )
                            )
                        }
                    } else {
                        stepFour.setDisabledStep()
                        stepFour.singleClick { }//empty click listener
                    }
                }
            }
        }
    }

    private fun loadTrackOrder(){
        binding.tvTrackOrderDesc.hideView()
        edcOrderDetailResponse?.data?.status?.let {
            if (it.equals(EdcOrderStatus.COMPLETED.name, true) || it.equals(EdcOrderStatus.WAITING_FOR_USER.name, true) || it.equals(EdcOrderStatus.WAITING_FOR_OPS.name, true)){
                binding.clTrackOrders.showView()
                val awb = edcOrderDetailResponse?.data?.deliveryDetails?.awb
                if (awb.isNotNullOrBlank()){
                    binding.apply {
                        tvReceiptNumberValue.setDrawable(right = R.drawable.ic_copy)
                        tvTrackOrderValue.showView()
                        tvTrackOrderValue.singleClick {
                            openActivity(WebviewActivity::class.java) {
                                putString(
                                    ClassConstants.WEBVIEW_URL,
                                    "https://jne.co.id/tracking-package"
                                )
                                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                            }
                        }
                        tvReceiptNumberValue.singleClick {
                            Utils.copyToClipboard(awb, this@EdcOrderDetailsActivity, "")
                        }
                        tvReceiptNumberValue.text = awb
                    }
                }
                else {
                    binding.apply {
                        tvTrackOrderDesc.showView()
                        tvTrackOrderDesc.text =
                            SpannableStringBuilder(getString(R.string.track_order_desc)).colorText(
                                getString(R.string.track_order_desc_highlight),
                                getColorCompat(R.color.green),
                                true,
                                true
                            )
                        tvTrackOrderValue.hideView()
                        tvReceiptNumberValue.text = getString(R.string.in_the_process)
                        tvReceiptNumberValue.setDrawable(right = 0)
                    }
                }
            } else if(it.equals(EdcOrderStatus.WAITING_FOR_ADDITIONAL_DOC.name, true)) {
                binding.apply {
                    tvTrackOrderDesc.showView()
                    clTrackOrders.showView()
                    tvTrackOrderValue.hideView()
                    if (edcOrderDetailResponse?.data?.checks?.kybStatus.isPending() && edcOrderDetailResponse?.data?.checks?.bankAccountAdded.isTrue) {
                        tvReceiptNumberValue.text = getString(R.string.in_the_process)
                        tvTrackOrderDesc.text = getString(R.string.verification_in_process)
                    } else {
                        tvReceiptNumberValue.text =
                            getString(R.string.complete_the_verification_process)
                    }
                    tvReceiptNumberValue.setDrawable(right = 0)
                }
            } else {
                binding.clTrackOrders.hideView()
            }
        }
    }

    private fun loadMyOrders() = with(binding) {
        tvOrderValue.singleClick { startWebview(getPaymentConfigs().edcRegistrationUrl.addQuery("entryPoint=$entryPoint")) }
        tvDeviceValue.visibility = isBukuWarungOrder.asVisibility()
        tvDeviceValue.text =
            Utils.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.totalPayableAmount)
    }

    private fun loadBillingDetails() = with(binding) {
        if (isBukuWarungOrder){
            clBillingDetails.showView()
            tvBillTitle.setDrawableRightListener {
                tvBillTitle.setDrawable(right = if (isBillingDetailsExpanded) R.drawable.ic_chevron_down else R.drawable.ic_chevron_up)
                isBillingDetailsExpanded = !isBillingDetailsExpanded
                grpBillDetails.visibility = isBillingDetailsExpanded.asVisibility()
            }
            tvTotalPurValue.text =
                Utils.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.productPrice)
            val edcDiscounts = edcOrderDetailResponse?.data?.orderAmountDetails?.discount
            llDiscounts.removeAllViews()
            edcDiscounts?.map {
                val llDiscountsView = LayoutInflater.from(this@EdcOrderDetailsActivity)
                    .inflate(R.layout.layout_card_item, llDiscounts, false)
                val tvTitle = llDiscountsView.findViewById<TextView>(R.id.tvTitle)
                val tvValue = llDiscountsView.findViewById<TextView>(R.id.tvValue)
                tvTitle.text = it.key
                tvValue.text = "-${Utils.formatAmount(it.value)}"
                llDiscounts.addView(llDiscountsView)
            }
            tvTotalPaymentValue.text =
                Utils.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.totalPayableAmount)
            tvPaymentModeValue.text =
                edcOrderDetailResponse?.data?.paymentDetails?.paymentMethod.orDash
        } else {
            clBillingDetails.hideView()
        }
    }

    private fun loadBottomView(vendor: String,isMorefunInsurance: Boolean) = with(binding) {
        when (orderStatus) {
            OrderStatus.PENDING -> {
                hideBottomView = false
                clBottom.showView()
                btnCancelOrder.hideView()
                btnPayBill.showView()
                btnPayBill.text = getString(R.string.pay_bill)
                btnPayBill.singleClick { startWebview(edcOrderDetailResponse?.data?.paymentDetails?.paymentLink.orEmpty()) }
            }

            OrderStatus.EXPIRED -> {
                hideBottomView = false
                clBottom.showView()
                btnCancelOrder.showView()
                btnCancelOrder.singleClick {
                    showCancelOrderBS()
                }
                btnPayBill.text = getString(R.string.repay_bill)
                btnPayBill.singleClick {
                    val map = HashMap<String,String>()
                    map[EdcOrderDetailAnalytics.PLAN] = edcPlan
                    map[EdcOrderDetailAnalytics.STATUS] = edcOrderDetailResponse?.data?.status.orEmpty()
                    map[EdcOrderDetailAnalytics.PAYMENT_STATUS] = edcOrderDetailResponse?.data?.paymentDetails?.status.orEmpty()
                    map[EdcOrderDetailAnalytics.ORDER_ID] = edcOrderDetailResponse?.data?.orderId.orEmpty()
                    Analytics.trackEvent(EdcOrderDetailAnalytics.EVENT_PAY_AGAIN, map)
                    viewModel.regeneratePaymentLink(edcOrderDetailResponse?.data?.orderId.orEmpty())
                }
            }

            OrderStatus.READY_FOR_ACTIVATION -> {
                hideBottomView = false
                btnCancelOrder.hideView()
                if (isMorefunInsurance) {
                    btnPayBill.hideView()
                } else {
                    btnPayBill.showView()
                }
                btnPayBill.text = getString(R.string.activate_edc)
                btnPayBill.singleClick {

                    activateEdc(vendor)
                }
            }

            else -> {
                hideBottomView = true
                clBottom.hideView()
            }
        }
    }

    private fun showCancelOrderBS() {
        EdcCancelOrderBS {
            val map = HashMap<String,String>()
            map[EdcOrderDetailAnalytics.PLAN] = edcPlan
            map[EdcOrderDetailAnalytics.STATUS] = edcOrderDetailResponse?.data?.status.orEmpty()
            map[EdcOrderDetailAnalytics.PAYMENT_STATUS] = edcOrderDetailResponse?.data?.paymentDetails?.status.orEmpty()
            map[EdcOrderDetailAnalytics.ORDER_ID] = edcOrderDetailResponse?.data?.orderId.orEmpty()
            Analytics.trackEvent(EdcOrderDetailAnalytics.EVENT_CANCEL_ORDER_CLICKED, map)
            viewModel.cancelOrder(edcOrderDetailResponse?.data?.orderId.orEmpty())
        }.show(supportFragmentManager, EdcCancelOrderBS.TAG)
    }

    private fun startWebview(url: String) {
        openActivity(
            WebviewActivity::class.java,
        ) {
            putString(
                ClassConstants.WEBVIEW_URL, url
            )
            putBoolean(ClassConstants.HIDE_TOOLBAR, true)
        }
    }

    private fun addNewRefundBankAccount(hasBankAccount: Boolean) {
        val bookId = Utils.getPaymentAccountId()
        openActivityForResult(
            AddSettlementBankAccountActivity::class.java,
            startAddBankAccountActivityForResult
        )
        {
            putString(ENTRY_POINT, EDC_ORDER_DETAIL)
        }
    }

    private fun checkFragmentState() {
        supportFragmentManager.registerFragmentLifecycleCallbacks(object :
            FragmentManager.FragmentLifecycleCallbacks() {

            override fun onFragmentAttached(fm: FragmentManager, f: Fragment, context: Context) {
                super.onFragmentAttached(fm, f, context)
                if (f is EdcActivationFragment) {
                    binding.clDetails.hideView()
                    binding.containerEdcAndroidActivation.showView()
                }
            }

            override fun onFragmentDetached(fm: FragmentManager, f: Fragment) {
                super.onFragmentDetached(fm, f)
                if (f is EdcActivationFragment) {
                    binding.clDetails.showView()
                    binding.containerEdcAndroidActivation.hideView()
                }
            }

        }, false)
    }

    private fun activateEdc(vendor: String) {
        val map = HashMap<String, String>()
        map.put("entry_point", "edc_order_detail_screen")
        if (!Utils.isSakuDeviceBasedOnVendor(vendor)) {
            //android
            map.put("redirection_to", "android_activation_education_screen")
            addEdcAndroidActivationFragment()
        } else {
            //saku
            map.put("redirection_to", "saku_connection_flow")
            val device = Utils.getUserRegisteredDevices()
            val deviceSerialNumberList = device.map { it.serialNumber }
            openActivityForResult(SetupBluetoothDeviceActivity::class.java, launcher) {
                putString(
                    SetupBluetoothDeviceActivity.DEVICE_TYPE,
                    SetupBluetoothDeviceActivity.CARD_READER
                )
                putStringArrayList(
                    SetupBluetoothDeviceActivity.DEVICE_SN_LIST,
                    ArrayList(deviceSerialNumberList)
                )
            }
        }
        Analytics.trackEvent(CardActivationConstants.EVENT_EDC_ACTIVATION_BUTTON_CLICKED, map)
    }

    val launcher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()){result: ActivityResult->
        if (result.resultCode == RESULT_OK) {
            if (result.resultCode == RESULT_OK) {
                val intent = Intent(this, HomePageActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                intent.putExtra("IS_ACTIVATED", true)
                startActivity(intent)
            }
        }
    }

    private fun addEdcAndroidActivationFragment(){
        val fragment = EdcActivationFragment.newInstance(type = EdcActivationFragment.EDCActivationFragmentType.Android)
        val transaction = supportFragmentManager.beginTransaction()
        transaction.add(R.id.container_edc_android_activation,fragment)
        transaction.addToBackStack(fragment.getClassTag())
        transaction.commit()
    }

    private enum class OrderStatus {
        PENDING, COMPLETED, EXPIRED, CANCELLED, REFUNDING_IN_PROGRESS, REFUNDING_FAILED, REFUNDING_SUCCESSFUL,READY_FOR_ACTIVATION
    }

    override fun onBankAccountSelected(bankAccount: BankAccount?) {
        addRefundBankAccount(bankAccount)
    }

    override fun addNewBankAccount() {
        addNewRefundBankAccount(true)
    }

    private fun getWarrantyNudge() {
        val typeToken = object : TypeToken<EdcOrderWarrantyNudgeBody>() {}.type
        val edcWarrantyNudge = Gson().fromJson<EdcOrderWarrantyNudgeBody>(warrantyNudge, typeToken)
        binding.warrantyLayout.root.visibility =
            (edcWarrantyNudge.isVisible == true && Utils.isCardReader() && Utils.hasAMoreFunSakuDevice()).asVisibility()
        val orderChannel = if (Utils.isAtmPro()) "MiniATMPro" else "BUKUAGEN"
        binding.warrantyLayout.btnRedirect.singleClick {
            openActivity(WebviewActivity::class.java) {
                putString(
                    WEBVIEW_URL,
                    BuildConfig.API_BASE_URL + edcWarrantyNudge.redirectionUrl?.addQuery("order_channel=$orderChannel")
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
        }
        binding.warrantyLayout.ivClose.singleClick {
            binding.warrantyLayout.root.hideView()
        }
    }

    private fun showDialogVerification(orderId: String){
        if(!Utils.isThisOrderHasShownVerificationDialog(orderId)) {
            dialog?.dismiss()
            dialog = StateDialog(this)
                .setImage(R.drawable.ic_verification_edc)
                .setTitle(getString(R.string.complete_edc_verification_title))
                .setSubTitle(getString(R.string.complete_edc_verification_subtitle))
                .setBtnRightText(getString(R.string.verify_now))
                .setDialogType(StateDialog.DialogType.NORMAL)
                .setBtnRightListener {}
                .setBtnLeftListener {}
                .showDialog()
            Utils.setThisOrderHasShownVerificationDialog(orderId)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dialog?.dismiss()
    }

}