package com.bukuwarung.edc.order.orderdetail.ui.adapter

import android.content.Context
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ItemListBankAccountsBinding
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.UrlType
import com.bukuwarung.edc.payments.data.model.getActiveAccount
import com.bukuwarung.edc.payments.data.model.getBankLogoIfAvailable
import com.bukuwarung.edc.payments.data.model.getInActiveAccount
import com.bukuwarung.edc.payments.data.model.getNotSupportedAccount
import com.bukuwarung.edc.payments.data.model.isAccountSelected
import com.bukuwarung.edc.payments.data.model.isManualMatchingInProgress
import com.bukuwarung.edc.payments.data.model.isManualMatchingRejected
import com.bukuwarung.edc.payments.data.model.isManualMatchingRequired
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.clearDrawables
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.showView
import com.bumptech.glide.Glide
import com.google.gson.Gson

class BankAccountListAdapter(
    private val isAddForSelfOnly: Boolean = false,
    private val isSelectOnly: Boolean = false,
    private val problematicBankIds: ArrayList<String>? = null,
    private val clickAction: (BankAccount) -> Unit,
    private val clickDeleteAction: (BankAccount) -> Unit,
    private val clickLink: (UrlType, String?) -> Unit,
    private val disableSelectedBank: Boolean = false,
    private val isQris: Boolean = false
) : RecyclerView.Adapter<BankAccountListAdapter.BankAccountViewHolder>() {

    private var list = emptyList<BankAccount>()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BankAccountViewHolder {
        val itemBinding = ItemListBankAccountsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BankAccountViewHolder(itemBinding, clickAction, clickDeleteAction, clickLink, problematicBankIds)
    }

    override fun onBindViewHolder(holder: BankAccountViewHolder, position: Int) {
        holder.bind(list[position], isAddForSelfOnly, isSelectOnly)
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<BankAccount>) {
        this.list = list
        notifyDataSetChanged()
    }

    inner class BankAccountViewHolder(private val binding: ItemListBankAccountsBinding, private val clickAction: (BankAccount) -> Unit,
                                      private val clickDeleteAction: (BankAccount) -> Unit, private val clickLink: (UrlType, String?) -> Unit,
                                      private val problematicBankIds: ArrayList<String>?) : RecyclerView.ViewHolder(binding.root) {
        fun bind(bankAccount: BankAccount, isAddForSelfOnly: Boolean = false, isSelectOnly: Boolean = false) {
            val context = binding.root.context
            binding.apply {
                tvError.movementMethod = LinkMovementMethod.getInstance()
                tvInfo.movementMethod = LinkMovementMethod.getInstance()
                when {
                    bankAccount.isManualMatchingRejected() -> {
                        vDisabled.showView()
                        txtBankTitle.setDrawable(left = R.drawable.ic_alert_warning)
                        tvInfo.hideView()
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(context.getColorCompat(R.color.black_60))
                        val rejectedReason = bankAccount.manualMatchingInfo?.rejectedReason
                        val errorMessage = when {
                            rejectedReason != null -> rejectedReason + " ${context.getString(R.string.to_learn)}."
                            bankAccount.message.isNotNullOrBlank() -> bankAccount.message + " ${context.getString(
                                R.string.to_learn)}."
                            else -> context.getString(R.string.name_matching_failed)
                        }
                        setErrorText(errorMessage, context.getString(R.string.to_learn), context, UrlType.MATCHING_INFO, bankAccount)
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        root.setOnClickListener(null)
                    }
                    bankAccount.isManualMatchingInProgress() -> {
                        vDisabled.showView()
                        txtBankTitle.setDrawable(left = R.drawable.ic_alert_warning)
                        tvError.hideView()
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(context.getColorCompat(R.color.black_60))
                        setInfoText(context.getString(R.string.name_matching_in_progress), context.getString(R.string.to_learn))
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        root.setOnClickListener(null)
                    }
                    bankAccount.isManualMatchingRequired() -> {
                        vDisabled.showView()
                        txtBankTitle.setDrawable(left = R.drawable.ic_alert_warning)
                        tvInfo.hideView()
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(context.getColorCompat(R.color.black_60))
                        if (isQris) {
                            tvError.showView()
                            tvError.text = context.getString(R.string.name_matching_error_qris)
                        } else {
                            setErrorText(
                                context.getString(R.string.name_matching_error),
                                context.getString(R.string.here),
                                context,
                                UrlType.APPEAL_FLOW,
                                bankAccount
                            )
                        }
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        root.setOnClickListener(null)
                    }
                    bankAccount.flag != null && !bankAccount.getActiveAccount() -> {
                        vDisabled.showView()
                        tvError.showView()
                        tvInfo.hideView()
                        when {
                            bankAccount.getInActiveAccount() -> {
                                var errorText = if (bankAccount.message == null) context.getString(R.string.used_info_text) else bankAccount.message
                                errorText = errorText + " " +  context.getString(R.string.bold_text)
                                setErrorText(errorText, context.getString(R.string.bold_text), context, if (bankAccount.getInActiveAccount()) UrlType.FAQ_USED_ACCOUNT else UrlType.FAQ_BLOCKED_ACCOUNT)
                                txtBankTitle.setDrawable(left = R.drawable.ic_blocked_icon)
                            }
                            bankAccount.getNotSupportedAccount() -> {
                                tvError.text = bankAccount.message
                                        ?: context.getString(R.string.not_supported_info_text)
                                txtBankTitle.setDrawable(left = R.drawable.ic_alert_warning)
                            }
                            else -> {
                                var errorText = if (bankAccount.message == null) context.getString(R.string.blocked_info_text) else bankAccount.message
                                errorText = errorText + " " +  context.getString(R.string.bold_text)
                                setErrorText(errorText, context.getString(R.string.bold_text), context, if (bankAccount.getInActiveAccount()) UrlType.FAQ_USED_ACCOUNT else UrlType.FAQ_BLOCKED_ACCOUNT)
                                txtBankTitle.setDrawable(left = R.drawable.ic_blocked_icon)
                            }
                        }
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(context.getColorCompat(R.color.black_60))
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        imageBank.alpha = 0.5F
                        root.setOnClickListener(null)
                    }
                    problematicBankIds?.contains(bankAccount.accountNumber).isTrue -> {
                        vDisabled.showView()
                        tvError.hideView()
                        tvInfo.hideView()
                        txtBankTitle.clearDrawables()
                        txtAccountNumber.text = context.getString(R.string.not_available)
                        txtAccountNumber.setTextColor(context.getColorCompat(R.color.red_80))
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        imageBank.alpha = 0.5F
                        root.setOnClickListener(null)
                    }
                    disableSelectedBank && bankAccount.isAccountSelected() -> {
                        vDisabled.showView()
                        tvError.hideView()
                        tvInfo.hideView()
                        txtBankTitle.clearDrawables()
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(context.getColorCompat(R.color.black_60))
                        radioDefault.isChecked = bankAccount.isAccountSelected()
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        imageBank.alpha = 0.5F
                        root.setOnClickListener(null)
                    }
                    else -> {
                        vDisabled.hideView()
                        tvError.hideView()
                        tvInfo.hideView()
                        txtBankTitle.clearDrawables()
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(context.getColorCompat(R.color.black_60))
                        radioDefault.isChecked = bankAccount.isAccountSelected()
                        radioDefault.isEnabled = true
                        clItem.isEnabled = true
                        imageBank.alpha = 1F
                        root.setOnClickListener { clickAction(bankAccount) }
                    }
                }
                radioDefault.visibility = if (isAddForSelfOnly) View.GONE else View.VISIBLE
                Glide.with(context)
                        .load(bankAccount.getBankLogoIfAvailable())
                        .placeholder(R.drawable.ic_bank)
                        .error(R.drawable.ic_bank)
                        .into(imageBank)
                txtBankTitle.text = Utils.dashDividedString(bankAccount.bankCode, bankAccount.accountHolderName)
                iconMenu.visibility = when {
                    bankAccount.isManualMatchingInProgress() -> View.GONE
                    isSelectOnly -> View.GONE
                    else -> View.VISIBLE
                }
                iconMenu.setOnClickListener {
                    PopupMenu(it.context, it).apply {
                        setOnMenuItemClickListener {
                            clickDeleteAction(bankAccount)
                            true
                        }
                        inflate(R.menu.menu_delete)
                        show()
                    }
                }
            }
        }

        private fun setErrorText(errorText: String, clickableText: String, context: Context, urlType: UrlType, bankAccount: BankAccount? = null) {
            with(binding) {
                tvError.showView()
                tvError.text = Utils.makeSectionOfTextClickable(errorText, clickableText, object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        clickLink(urlType, Gson().toJson(bankAccount))
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = true
                        ds.color = context.getColorCompat(R.color.red_80)
                    }
                })
            }
        }

        private fun setInfoText(text: String, clickableText: String) {
            with(binding) {
                tvInfo.showView()
                tvInfo.text = Utils.makeSectionOfTextClickable(text, clickableText, object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        clickLink(UrlType.MATCHING_INFO, null)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = true
                    }

                })
            }
        }
    }
}
