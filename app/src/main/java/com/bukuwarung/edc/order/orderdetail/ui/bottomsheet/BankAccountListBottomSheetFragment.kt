package com.bukuwarung.edc.order.orderdetail.ui.bottomsheet

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.observe
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import android.app.AlertDialog
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import com.bukuwarung.edc.databinding.BottomSheetBankAccountListBinding
import com.bukuwarung.edc.order.orderdetail.ui.adapter.BankAccountListAdapter
import com.bukuwarung.edc.order.orderdetail.viewmodel.BankAccountListViewModel
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PinType
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.UrlType
import com.bukuwarung.edc.payments.ui.core.NewPaymentPinActivity
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.openActivityForResult
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class BankAccountListBottomSheetFragment : BottomSheetDialogFragment() {

    companion object {
        const val TAG = "bankListBtSheet"
        private const val CUSTOMER_ID = "customerId"
        private const val SELECTED_ACCOUNT_ID = "selectedAccountId"
        private const val ENTRY_POINT = "ENTRY_POINT"
        private const val IS_REFUND = "IS_REFUND"
        private const val REFUND_AMOUNT = "refund_amount"
        private const val PAYMENT_TYPE = "payment_type"
        const val PIN_FOR = "pin_for"
        const val TYPE = "type"
        const val SHOW_BANK_ACCOUNT ="show_bank_account"
        const val DELETE_BANK_ACCOUNT = "delete_bank_account"
        private const val PROBLEMATIC_BANK_IDS = "problematic_bank_ids"
        private const val SELECTED_BANK_CODE = "selected_bank_code"
        private const val SELECTED_ACCOUNT_NUMBER = "selected_account_number"
        private const val RC_PIN_SELECT = 99
        private const val RC_PIN_DELETE = 98


        fun createBankAccountInstance(
            entryPoint: String
        ): BankAccountListBottomSheetFragment {
            val bt = BankAccountListBottomSheetFragment()
            val bundle = Bundle()
            bundle.putString(ENTRY_POINT, entryPoint)
            bt.arguments = bundle
            return bt
        }
    }

    @Inject
    lateinit var viewModel: BankAccountListViewModel
    private var _mBinding: BottomSheetBankAccountListBinding? = null
    private val mBinding get() = _mBinding!!
    private val customerId by lazy { arguments?.getString(CUSTOMER_ID) }
    private val selectedAccountId by lazy { arguments?.getString(SELECTED_ACCOUNT_ID) }
    private val problematicBankIds by lazy { arguments?.getStringArrayList(PROBLEMATIC_BANK_IDS) }
    private val selectedBankCode by lazy { arguments?.getString(SELECTED_BANK_CODE) }
    private val selectedAccountNumber by lazy { arguments?.getString(SELECTED_ACCOUNT_NUMBER) }
    private val entryPoint by lazy { arguments?.getString(ENTRY_POINT).orEmpty() }
    private lateinit var adapter: BankAccountListAdapter
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialogTheme)
    }

    interface BtSheetBankAccountListener {
        fun onBankAccountSelected(bankAccount: BankAccount?)
        fun addNewBankAccount()
    }

    private var listener: BtSheetBankAccountListener? = null

    private val addBankPaymentPinActivityResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {result: ActivityResult ->
            if(result.resultCode ==Activity.RESULT_OK){
                viewModel.getCurrentSelectedAccount()
            }
        }

    private val deleteBankPaymentPinActivityResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {result: ActivityResult ->
            if(result.resultCode ==Activity.RESULT_OK){
                viewModel.deleteBankAccount()
            }
        }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is BtSheetBankAccountListener) {
            listener = context
        }
    }

    private fun setRefundTitle() {
        mBinding.titleTxt.text = getText(R.string.refund_method)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        _mBinding = BottomSheetBankAccountListBinding.inflate(inflater, container, false)
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        mBinding.closeImg.setOnClickListener {
            dialog?.dismiss()
        }
//        dialog?.setOnShowListener {
//            Utilities.showBottomSheet(it, BottomSheetBehavior.STATE_EXPANDED)
//        }
        adapter = BankAccountListAdapter(false, false, problematicBankIds, {
                viewModel.setCurrentSelectedAccount(it)
            requireContext().openActivityForResult(NewPaymentPinActivity::class.java,addBankPaymentPinActivityResult) {
                putString(NewPaymentPinActivity.USECASE, PinType.PIN_CONFIRM.toString())
            }
        }, {
            showDeleteBankConfirmationDialog(it)
        }, { urlType, bankAccount ->
            clickLink(
                urlType,
                bankAccount,
                paymentType = PaymentConst.PAYMENT_IN
            )
        })
        mBinding.addBankAccountBtn.setOnClickListener {
            listener?.addNewBankAccount()
            activity?.setResult(Activity.RESULT_OK)
            dialog?.dismiss()
        }
        mBinding.rvBankAccounts.layoutManager = LinearLayoutManager(context)
        mBinding.rvBankAccounts.adapter = adapter

//        if (entryPoint == AnalyticsConst.PAYMENT_DETAILS || entryPoint == AnalyticsConst.EDC_ORDER_DETAIL)
        setRefundTitle()
        return mBinding.root
    }

    private fun showDeleteBankConfirmationDialog(bankAccount: BankAccount){
        AlertDialog.Builder(context)
            .setTitle(getString(R.string.transaction_back_dialog_title))
            .setMessage(getString(R.string.transaction_back_dialog_subtitle))
            .setPositiveButton(getString(R.string.yes)) { _, _ ->
                viewModel.setTempDeletedAccount(bankAccount)
                requireContext().openActivityForResult(NewPaymentPinActivity::class.java,deleteBankPaymentPinActivityResult) {
                    putString(NewPaymentPinActivity.USECASE, PinType.PIN_CONFIRM.toString())
                }
            }
            .setNegativeButton(getString(R.string.no), null)
            .show()
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel.init(
            if (customerId.isNullOrBlank()) PaymentConst.TYPE_PAYMENT_IN else PaymentConst.TYPE_PAYMENT_OUT,
            entryPoint,
            customerId,
            null,
            selectedAccountId,
            selectedBankCode,
            selectedAccountNumber
        )

        observeData()
    }

    private fun observeData() {
        viewModel.mediatorLiveData.observe(this) {
            adapter.setData(it)
        }
        viewModel.observeEvent.observe(this) {
            when (it) {
                is BankAccountListViewModel.Event.ShowBankList -> {
                    val selectedBank =
                        it.list.firstOrNull { (it.isSelected ?: 0) > 0 }
                    // Set selected bank in the activity
                    listener?.onBankAccountSelected(selectedBank)
                    adapter.setData(it.list)
                }

                is BankAccountListViewModel.Event.HasOngoingTransaction -> {
                    AlertDialog.Builder(context)
                        .setTitle("Error")
                        .setMessage(getString(R.string.delete_bank_error_exist))
                        .setPositiveButton("OK",{ _, _ ->
                            dialog?.dismiss()
                        })
                        .show()
                }
                is BankAccountListViewModel.Event.ReturnSelectedAccount -> {
                    listener?.onBankAccountSelected(it.currentSelectedAccount)
                    dialog?.dismiss()
                }
                else->{

                }
            }
        }
    }

    private fun clickLink(urlType: UrlType, bankAccount: String? = null, paymentType: String) {
            PaymentAuxilliary.getBankAccountRedirectionIntent(
                context = requireContext(),
                urlType = urlType,
                bankAccount = bankAccount,
                paymentType = paymentType,
                entryPoint = entryPoint
            )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _mBinding = null
    }
}
