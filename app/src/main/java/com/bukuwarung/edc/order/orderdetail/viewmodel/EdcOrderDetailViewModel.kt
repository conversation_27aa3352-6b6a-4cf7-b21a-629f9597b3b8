package com.bukuwarung.edc.order.orderdetail.viewmodel

import android.os.Build
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.network.model.request.SessionRequest
import com.bukuwarung.network.session.SessionRepository
import com.bukuwarung.edc.order.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.edc.order.orderdetail.model.EdcProductDetails
import com.bukuwarung.edc.order.orderdetail.model.RefundRequest
import com.bukuwarung.edc.order.orderdetail.model.RefundResponse
import com.bukuwarung.edc.order.orderdetail.model.RegeneratePaymentLinkResponse
import com.bukuwarung.edc.order.orderdetail.usecase.EdcOrderDetailUseCase
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.usecase.PaymentUseCase
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.put
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.NetworkConst.SESSION_TOKEN
import com.bukuwarung.network.utils.ResourceState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import org.json.JSONObject
import javax.inject.Inject

@HiltViewModel
class EdcOrderDetailViewModel @Inject constructor(
    private val edcOrderDetailUseCase: EdcOrderDetailUseCase,
    private val paymentUseCase: PaymentUseCase,
    private val sessionRepository: SessionRepository
) : ViewModel() {

    companion object {
        private const val BLACKLISTED_DEVICE_CODE = "40301"
    }

    private var _showLoader = MutableLiveData<Boolean>()
    val showLoader: LiveData<Boolean> = _showLoader

    private var _deviceBlacklisted = MutableLiveData<Boolean>(false)
    val deviceBlacklisted: LiveData<Boolean> = _deviceBlacklisted

    private var _orderDetails = MutableLiveData<EdcOrderDetailResponse?>()
    val orderDetails: LiveData<EdcOrderDetailResponse?> = _orderDetails

    private var _productDetails = MutableLiveData<EdcProductDetails?>()
    val productDetails: LiveData<EdcProductDetails?> = _productDetails

    private var _cancelOrder = MutableLiveData<Boolean>()
    val cancelOrder: LiveData<Boolean> = _cancelOrder

    private var _regeneratePaymentLink = MutableLiveData<RegeneratePaymentLinkResponse?>()
    val regeneratePaymentLink: LiveData<RegeneratePaymentLinkResponse?> = _regeneratePaymentLink

    private var _refundResponse = MutableLiveData<RefundResponse?>()
    val refundResponse: LiveData<RefundResponse?> = _refundResponse

    private var _merchantBankAccounts = MutableLiveData<List<BankAccount>?>()
    val merchantBankAccounts: LiveData<List<BankAccount>?> = _merchantBankAccounts

    private var edcOrderDetailResponse: EdcOrderDetailResponse? = null

    val combinedOrderProductResponse: LiveData<Pair<EdcOrderDetailResponse?, EdcProductDetails?>> by lazy {
        MediatorLiveData<Pair<EdcOrderDetailResponse?, EdcProductDetails?>>().apply {

            addSource(orderDetails) { orderDetails ->
                value = Pair(orderDetails, productDetails.value)
            }
            addSource(productDetails) { productDetails ->
                value = Pair(orderDetails.value, productDetails)
            }
        }
    }

    fun fetchMerchantBankAccounts() = viewModelScope.launch {
        val bookId = Utils.getPaymentAccountId()
        try {
            val result = paymentUseCase.getMerchantBankAccounts(bookId)
            if (result.isSuccessful) {
                val size = result.body()?.size.orNil
                Utils.setBankAccount = size > 0
                _merchantBankAccounts.value = result.body()
            } else {
                _merchantBankAccounts.value = arrayListOf()
            }
        } catch (e: Exception){
            bwLog(e = e)
        }
    }

    fun getEdcOrderDetailByPhoneNumber() = viewModelScope.launch {
        when (val response = edcOrderDetailUseCase.getEdcOrderDetailByPhoneNumber()) {
            is ResourceState.Success -> {
                _orderDetails.value = response.data
            }

            else -> {
                _orderDetails.value = EdcOrderDetailResponse(result = false, data = null)
            }
        }
    }

    private fun fetchBankNameAndLogo(bankCode: String) = viewModelScope.launch {
        try {
            val response = paymentUseCase.getBanks()
            if (response.isSuccessful) {
                response.body()?.forEach {
                    if (it.bankCode == bankCode) {
                        edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.bankName = it.bankName
                        edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.logoUrl = it.logo
                        _orderDetails.value = edcOrderDetailResponse
                    }
                }
            }
        } catch (e: Exception){
            bwLog(e = e)
        }
    }

    fun getEdcOrderDetailsByOrderId(orderId: String) = viewModelScope.launch {
        _showLoader.value = true
        when (val response = edcOrderDetailUseCase.getEdcOrderDetailsByOrderId(orderId)) {
            is ResourceState.Loading -> {
                _showLoader.value = true
            }

            is ResourceState.Success -> {
                _showLoader.value = false
                edcOrderDetailResponse = response.data
                _orderDetails.value = edcOrderDetailResponse
                if (edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.bankCode.isNotNullOrBlank()) {
                    fetchBankNameAndLogo(edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.bankCode.orEmpty())
                }
                fetchProductName(edcOrderDetailResponse?.data?.orderId.orEmpty())
            }

            is ResourceState.Failure -> {
                _orderDetails.value = EdcOrderDetailResponse(result = false, data = null)
                _showLoader.value = false
            }
        }
    }

    private fun fetchProductName(orderId: String) = viewModelScope.launch {
        when(val response = edcOrderDetailUseCase.getEdcProductDetails(orderId)){
            is ResourceState.Success ->{
                _productDetails.value = response.data
                _showLoader.value = false
            }
            is ResourceState.Loading,
            is ResourceState.Failure ->{
                _productDetails.value = null
                _showLoader.value = false
            }
        }
    }

    fun cancelOrder(orderId: String) = viewModelScope.launch {
        _showLoader.value = true
        when(val response = edcOrderDetailUseCase.cancelEdcOrder(orderId)) {
            is ResourceState.Loading -> {
                _showLoader.value = true
            }

            is ResourceState.Success -> {
                _cancelOrder.value = response.data.result.isTrue
                getEdcOrderDetailsByOrderId(orderId)//calling this fn to refresh the screen.
            }

            is ResourceState.Failure -> {
                _showLoader.value = false
                _cancelOrder.value = false
            }
        }
    }

    fun regeneratePaymentLink(orderId: String) = viewModelScope.launch {
        _showLoader.value = true
        when (val response = edcOrderDetailUseCase.regeneratePaymentLink(orderId)) {
            is ResourceState.Loading -> {
                _showLoader.value = true
            }

            is ResourceState.Success -> {
                _regeneratePaymentLink.value = response.data
                _showLoader.value = false
            }

            is ResourceState.Failure -> {
                _regeneratePaymentLink.value =
                    RegeneratePaymentLinkResponse(result = false, data = null)
                _showLoader.value = false
            }
        }

    }
    fun refreshToken() {
        viewModelScope.launch {
            val token: String? = sharedPreferences.getString(SESSION_TOKEN, null)

            token?.let {
                val newSessionRequest = SessionRequest(
                    token = token,
                    register = false,
                    deviceId = "",
                    deviceModel = Build.MODEL,
                    deviceBrand = Build.MANUFACTURER,
                    userId = Utils.getUserId(),
                    clientId = Constant.clientID,
                    clientSecret = Constant.clientSecret
                )
                val response =
                    sessionRepository.createNewSession(
                        EncryptedPreferencesHelper.get("bureau_event_id", ""),
                        newSessionRequest
                    )

                if (response.isSuccessful) {
                    val newSession = response.body()
                    EncryptedPreferencesHelper.put(BUKUWARUNG_TOKEN, newSession?.idToken)
                    sharedPreferences.put(SESSION_TOKEN, newSession?.refreshToken)
                    if (newSession?.idToken?.isBlank().isTrue || newSession?.refreshToken?.isBlank().isTrue) {
                        _deviceBlacklisted.value = true
                    }
                } else {
                    val json = JSONObject(response.errorBody()?.string() ?: "{}")
                    if (json.has("response_code") && json.getString("response_code") == BLACKLISTED_DEVICE_CODE) {
                        _deviceBlacklisted.value = true
                    }
                }
            }

        }
    }

    fun refundPayment(orderId: String, refundRequest: RefundRequest) = viewModelScope.launch {
        val response = edcOrderDetailUseCase.refundPayment(orderId, refundRequest)
        when(response){
            is ResourceState.Loading -> {}

            is ResourceState.Success -> {
                getEdcOrderDetailsByOrderId(orderId)//refreshing screen on successfully adding refund bank account
            }

            is ResourceState.Failure -> {
                _refundResponse.value = RefundResponse(success = false)
            }
        }
    }
}