package com.bukuwarung.edc.order.orderhistory.data.repository

import com.bukuwarung.edc.order.orderhistory.data.api.EdcOrderHistoryApi
import javax.inject.Inject

class EdcOrderHistoryRepo @Inject constructor(private val api: EdcOrderHistoryApi) {

    suspend fun getEdcOrderHistory(
        pageNumber: Int,
        pageSize: Int,
        order: String?,
        startDate: String?,
        endDate: String?,
        type: String?
    ) = api.getEdcOrderHistory(
        type,
        pageNumber,
        pageSize,
        order,
        startDate,
        endDate
    )
}
