package com.bukuwarung.activities.edc.orderhistory.di

import com.bukuwarung.edc.order.orderhistory.data.api.EdcOrderHistoryApi
import com.bukuwarung.edc.order.orderhistory.data.repository.EdcOrderHistoryRepo
import com.bukuwarung.edc.order.orderhistory.usecase.EdcOrderHistoryUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
class EdcOrderHistoryModule {

    @Provides
    fun provideEdcOrderHistoryDataSource(@Named("normal") retrofit: Retrofit) : EdcOrderHistoryApi =
        retrofit.create(EdcOrderHistoryApi::class.java)


    @Provides
    fun provideEdcOrderHistoryRepo(api: EdcOrderHistoryApi): EdcOrderHistoryRepo {
        return EdcOrderHistoryRepo(api)
    }

    @Provides
    fun provideEdcOrderHistoryUseCase(repo: EdcOrderHistoryRepo): EdcOrderHistoryUseCase {
        return EdcOrderHistoryUseCase(repo)
    }
}
