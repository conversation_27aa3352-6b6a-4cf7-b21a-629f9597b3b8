package com.bukuwarung.edc.order.orderhistory.usecase

import com.bukuwarung.edc.card.cardhistory.model.HistoryItem
import com.bukuwarung.edc.order.orderhistory.data.repository.EdcOrderHistoryRepo
import javax.inject.Inject

class EdcOrderHistoryUseCase @Inject constructor(private val repo: EdcOrderHistoryRepo) {

    suspend fun getTransactionHistory(
        pageNumber: Int,
        pageSize: Int,
        order: String?,
        startDate: String?,
        endDate: String?,
        type: String?
    ): ArrayList<HistoryItem> {
        try {
            val response = repo.getEdcOrderHistory(
                pageNumber,
                pageSize,
                order,
                startDate,
                endDate,
                type
            )
            if (response.isSuccessful) {
                return response.body()?.edcOrderData?.history ?: arrayListOf()
            } else {
                return arrayListOf()
            }

        } catch (e: Exception) {
            throw Exception()
        }
    }
}

