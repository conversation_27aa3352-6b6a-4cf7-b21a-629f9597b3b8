import androidx.lifecycle.MutableLiveData
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.datasource.OrdersPagedDataSource
import com.bukuwarung.edc.payments.data.model.OrderHistoryData
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.payments.ui.history.OrderHistoryViewModel
import com.bukuwarung.edc.payments.usecase.OrderUseCase
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.orNil

class OrdersPagingSource(
    private val ordersUseCase: OrderUseCase,
    private val pagingStatusLiveData: MutableLiveData<PagingStatus>,
    private val activeTab: PaymentConst.HISTORY_TABS,
    private val orderDates: HashMap<PaymentConst.HISTORY_TABS, ArrayList<String>>,
    private val queryParams: OrdersPagedDataSource.QueryParams
) : PagingSource<Int, OrderHistoryData>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, OrderHistoryData> {
        val page = params.key ?: 0
        val limit = queryParams.limit ?: params.loadSize

        queryParams.page = page
        queryParams.limit = limit

        return try {
            val response = ordersUseCase.getOrders(
                accountId = queryParams.accountId,
                customerId = queryParams.customerId,
                startDate = queryParams.startDate,
                endDate = queryParams.endDate,
                type = queryParams.type,
                status = queryParams.status,
                page = queryParams.page,
                limit = queryParams.limit,
                billerCode = queryParams.billerCode,
                sorting = queryParams.sorting,
                searchQuery = queryParams.searchQuery,
                bukuOrigin = queryParams.bukuOrigin,
                serialNumberSelected = queryParams.serialNumberSelected
            )

            if (response.isSuccessful) {
                val orders = response.body()?.let { OrderHistoryViewModel.sortByDescending(it) }
                val data = orders?.let {
                    OrderHistoryViewModel.addDateHeaders(
                        it,
                        OrderHistoryViewModel.getExistingOrderDates(activeTab, orderDates),
                        shouldAddDateHeaders = queryParams.searchQuery.isNullOrEmpty()
                    )
                }.orEmpty()

                val totalCount = response.headers()[com.bukuwarung.edc.global.Constant.X_TOTAL_COUNT]?.toInt().orNil
                pagingStatusLiveData.postValue(
                    if (data.isEmpty()) PagingStatus.Empty else PagingStatus.Loaded(totalCount)
                )

                LoadResult.Page(
                    data = data,
                    prevKey = if (page == 0) null else page - 1,
                    nextKey = if (data.isEmpty() || data.size + (page * limit) >= totalCount) null else page + 1
                )
            } else {
                pagingStatusLiveData.postValue(PagingStatus.Error(response.errorMessage()))
                LoadResult.Error(Exception(response.errorMessage()))
            }
        } catch (e: Exception) {
            pagingStatusLiveData.postValue(PagingStatus.Error(e.message.orEmpty()))
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, OrderHistoryData>): Int? {
        return state.anchorPosition?.let { anchor ->
            state.closestPageToPosition(anchor)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchor)?.nextKey?.minus(1)
        }
    }
}
