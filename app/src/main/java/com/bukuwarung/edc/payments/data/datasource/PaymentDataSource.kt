package com.bukuwarung.edc.payments.data.datasource

import com.bukuwarung.edc.payments.data.model.AddBankAccountDetail
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.Disbursement
import com.bukuwarung.edc.payments.data.model.DisbursementRequest
import com.bukuwarung.edc.payments.data.model.EnableCustomerResponse
import com.bukuwarung.edc.payments.data.model.PaymentCategory
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckRequest
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckResponse
import com.bukuwarung.edc.payments.data.model.PaymentTransactionLimits
import com.bukuwarung.edc.payments.data.model.UpdateFeeRequest
import com.bukuwarung.edc.payments.data.model.request.BankAccountRequest
import com.bukuwarung.edc.payments.data.model.request.BankValidationRequest
import com.bukuwarung.edc.payments.data.model.request.DisbursementOverviewRequest
import com.bukuwarung.edc.payments.data.model.response.DisbursementOverviewResponse
import com.bukuwarung.payments.data.model.PaymentMethodsResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query


interface PaymentDataSource {
    @GET("/api/payments/banks/disbursements")
    suspend fun getBanks(): Response<List<Bank>>

    @POST("/api/payments/{account_id}/bank_accounts/validate")
    suspend fun validateBankAccount(
        @Path("account_id") accountId: String,
        @Body bankValidationRequest: BankValidationRequest
    ): Response<BankAccount>

    @GET("/payments/api/categories")
    suspend fun getPaymentCategoryList(@Query("disbursableType") disbursableType: String): Response<List<PaymentCategory>>

    @GET("/api/payments/{account_id}/disbursements/{customer_id}/transaction_limit")
    suspend fun getPaymentOutLimits(
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
    ): Response<PaymentTransactionLimits>

    @POST("/api/payments/health")
    suspend fun doHealthCheck(
        @Body paymentHealthCheckRequest: PaymentHealthCheckRequest
    ): Response<PaymentHealthCheckResponse>

    @POST("/api/payments/{account_id}/disbursements/{customer_id}/overview")
    suspend fun getDisbursementOverview(
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
        @Body request: DisbursementOverviewRequest
    ): Response<DisbursementOverviewResponse>

    @POST("/api/payments/{account_id}/bank_accounts_v2/customer_accounts/add/{customer_id}")
    suspend fun validateAndAddCustomerBankAccount(
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
        @Body bankValidationRequest: BankValidationRequest
    ): Response<AddBankAccountDetail>

    @POST("/api/payments/{account_id}/disbursements/{customer_id}/")
    suspend fun createDisbursement(
        @Header("Idempotency-Key") idempotentKey: String,
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
        @Body request: DisbursementRequest
    ): Response<Disbursement>

    @POST("/api/payments/accounts/{account_id}")
    suspend fun enabledMerchantPayments(
        @Path("account_id") accountId: String,
        @Body businessNameMap: Map<String, String>)
    : Response<EnableCustomerResponse>

    @PUT("/api/payments/{account_id}/disbursements/{customer_id}/{disbursement_request_id}/agent_fee")
    suspend fun updatePaymentOutAgentFee(@Path("account_id") accountId: String, @Path("customer_id") customerId: String,@Path("disbursement_request_id") disbursementRequestId: String, @Body updateFeeRequest: UpdateFeeRequest):Response<Disbursement>

    @GET("/payments/api/{account_id}/checkout/methods")
    suspend fun getPaymentMethods(
        @Path("account_id") accountId: String,
        @Query("type") paymentType: String,
        @Query("receiver_bank_code") bankCode: String,
        @Query("transaction_amount") transactionAmount: Double
    ):Response<PaymentMethodsResponse>

    // we are using this endpoint to get customer bank accounts
    @GET("/api/payments/{account_id}/customers/{customer_id}/bank_accounts")
    suspend fun getCustomerBankAccounts(
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
        @Query("is_cashback_settlement_bank") isCashbackSettlementBank: Boolean? = null
    ): Response<List<BankAccount>>

    // for Payment In
    @GET("/api/payments/{account_id}/bank_accounts")
    suspend fun getMerchantBankAccounts(
        @Path("account_id") accountId: String,
        @Query("type") type: String?
    ): Response<List<BankAccount>>

    @DELETE("/api/payments/{account_id}/bank_accounts/{bank_account_id}")
    suspend fun deleteMerchantBankAccount(@Path("account_id") accountId: String, @Path("bank_account_id") bankAccountId: String): Response<Any>

    @POST("/api/payments/{account_id}/bank_accounts")
    suspend fun addMerchantBankAccount(@Path("account_id") accountId: String, @Body bankAccount: BankAccountRequest): Response<BankAccount>
}