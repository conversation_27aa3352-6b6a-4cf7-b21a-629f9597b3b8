package com.bukuwarung.edc.payments.data.datasource

import com.bukuwarung.edc.login.data.model.LoginRequest
import com.bukuwarung.edc.payments.data.model.request.*
import com.bukuwarung.edc.payments.data.model.response.*
import com.bukuwarung.edc.settings.data.model.PinChangeResponse
import com.bukuwarung.edc.util.Utils
import retrofit2.Response
import retrofit2.http.*

interface PaymentTwoFARemoteDataSource {
    @POST("/api/v2/auth/otp/send")
    suspend fun requestOTPForPIN(
        @Body pinOTPRequest: LoginRequest,
        @Query("phone_number") phone: String
    ): Response<OtpResponse>

    @POST("/api/v2/auth/otp/verify")
    suspend fun verifyOTP(
        @Header("x-ops-token") otpToken: String = "",
        @Body otpVerificationRequest: OtpVerifyRequest
    ): Response<OtpResponse>

    @POST("/api/v2/auth/twofa/verify")
    suspend fun verifyPin(
        @Body pinVerificationRequest: PinVerifyRequest
    ): Response<PinVerifyResponse>

    @GET("/api/v3/auth/twofa/status/{user_id}")
    suspend fun checkPinChangeRequest(
        @Path("user_id") userId: String
    ): Response<PinChangeResponse>


    @GET("/api/v3/auth/twofa/{user_id}")
    suspend fun checkPinLength(
        @Path("user_id") userId: String
    ): Response<CheckPinLengthResponse>

    @POST("/api/v3/auth/twofa/register")
    suspend fun createPinV3(
        @Header("x-ops-token") otpToken: String = "",
        @Body createPinRequest: PinSetupRequest): Response<PinSetupResponse>

    @PUT("/api/v3/auth/twofa/update")
    suspend fun updatePinV3(
        @Header("x-ops-token") otpToken: String = "",
        @Body updatePinRequest: PinUpdateRequest): Response<PinUpdateResponse>

    @PUT("/api/v3/auth/twofa/forgot")
    suspend fun forgotPinV3(
        @Header("x-ops-token") otpToken: String = "",
        @Body forgetPinRequest: PinForgetRequest,
        @Header("app-instance-installation-id") insallationId: String = Utils.getFcmDeviceId()): Response<PinForgetResponse>


}