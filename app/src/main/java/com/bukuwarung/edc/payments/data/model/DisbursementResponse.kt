package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.bukuwarung.edc.util.Utils
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.RawValue
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

@Parcelize
data class Disbursement(
    @SerializedName("disbursement_id")
    val disbursementId: String,
    @SerializedName("customer_id")
    val customerId: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("amount")
    val amount: BigDecimal,
    @SerializedName("fee")
    val fee: Float = 0.0f,
    @SerializedName("discount")
    val discount: Float = 0.0f,
    @SerializedName("virtual_account")
    val virtualAccount: @RawValue VirtualAccount,
    @SerializedName("invoice_url")
    val invoiceUrl: String?,
    @SerializedName("share_url_template")
    val shareUrlTemplate: String? = null,
    @SerializedName("payment_instruction_url")
    val paymentInstructionsUrl: String?,
    @SerializedName("checkout_url")
    val checkoutUrl: String?,
    @SerializedName("receiver_bank")
    val receiverBank: @RawValue ReceiverBank? = null,
    @SerializedName("transaction_id")
    val transactionId: String? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("payment_method")
    val paymentMethod: String? = null,
    @SerializedName("payment_channel")
    val paymentChannel: String? = null,
    @SerializedName("agent_fee")
    val agentFeeInfo: @RawValue AgentFeeInfo? = null,
    @SerializedName("transaction_type")
    val transactionType: String? = null,
    @SerializedName("payment_category")
    val paymentCategory: PaymentCategoryItem? = null,
    @SerializedName("bankAccount")
    val refundBankAccount: @RawValue DisbursementRefundAccount?,
    @SerializedName("progress")
    val progress: List<PaymentProgress>
) : Parcelable {
    fun isPaid() = status.equals(PaymentHistory.STATUS_PAID, ignoreCase = true)
    fun isCompleted() = status.equals(PaymentHistory.STATUS_COMPLETED, ignoreCase = true)

    fun getFormattedTotal(): String = Utils.formatAmount(amount.toDouble())
}

data class ReceiverBank(
    @SerializedName("bank_code")
    val bankCode: String? = null,
    @SerializedName("account_number")
    val accountNumber: String? = null,
    @SerializedName("owner_name")
    val accountHolderName: String? = null,
    val isSelected: Boolean = false
)

@Parcelize
data class AgentFeeInfo(
    @SerializedName("amount")
    val amount: Double = 0.0,
    @SerializedName("updated_at")
    val updatedAt: String? = null
): Parcelable

data class DisbursementRefundAccount(
    val account: Account? = null,
    val bankCode: String? = "",
    val accountNumber: String? = "",
    val customerId: String? = "",
    val ownerName: String? = "",
    val isRefundableBank: Boolean? = true
)

data class Account(
    val id: AccountId? = null
)

data class AccountId(
    val value: String? = ""
)