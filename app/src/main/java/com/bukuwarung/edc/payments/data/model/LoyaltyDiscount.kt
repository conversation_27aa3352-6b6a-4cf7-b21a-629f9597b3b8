package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class LoyaltyDiscount(
    @SerializedName("tier_discount")
    val tierDiscount: Double?,
    @SerializedName("tier_name")
    val tierName: String?,
    @SerializedName("subscription_discount")
    val subscriptionDiscount: Double?
): Parcelable