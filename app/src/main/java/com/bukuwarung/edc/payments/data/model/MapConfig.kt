package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.google.android.gms.maps.model.LatLng
import kotlinx.parcelize.Parcelize

@Parcelize
data class MapConfig(
    var interval: Long? = null,
    val fastestInterval: Long? = null,
    val maxWaitTime: Long? = null,
    val zoom: Float? = null,
    val defaultLocation: LatLng? = null,
    val priority: Int? = null
): Parcelable