package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.ppob.common.model.Campaign
import com.bukuwarung.edc.ppob.common.model.RefundBankAccount
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.payments.data.model.FinproError
import com.bukuwarung.payments.data.model.FinproRefunds
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.io.Serializable
import java.math.BigDecimal

@Parcelize
data class OrderResponse(
    @SerializedName("order_id")
    val orderId: String? = null,
    @SerializedName("transaction_id")
    val transactionId: String? = null,
    @SerializedName("created_at")
    val createdAt: String? = null,
    @SerializedName("updated_at")
    val updatedAt: String? = null,
    @SerializedName("transaction_type")
    val transactionType: String? = null,
    @SerializedName("payment_category")
    var paymentCategory: PaymentCategoryItem? = null,
    @SerializedName("agent_fee")
    var agentFeeInfo: AgentFeeInfo? = null,
    @SerializedName("purchase_type")
    val purchaseType: String? = null,
    @SerializedName("channel_pending_eta")
    val channelPendingEta: String? = null,
    @SerializedName("share_url_template")
    val shareUrlTemplate: String? = null,
    @SerializedName("template")
    val template: String? = null,
    @SerializedName("customer_profile")
    val customerProfile: CustomerProfile? = null,
    @SerializedName("invalid_disbursable_accounts")
    val invalidDisburableAccounts: List<String>? = null,
    @SerializedName("total_discount")
    val totalDiscount: Double? = null,
    @SerializedName("total_bonus")
    val totalBonus: Double? = null,
    @SerializedName("kyb_status")
    var kybStatus: String? = null,
    @SerializedName("metadata")
    val metadata: MetaData? = null,
    @SerializedName("campaigns")
    val campaigns: List<Campaign>? = null,
    @SerializedName("status")
    val status: String? = null,
    @SerializedName("progress")
    val progress: List<PaymentProgress>? = null,
    @SerializedName("amount")
    val amount: Double? = null,
    @SerializedName("fee")
    val fee: Double? = null,
    @SerializedName("cost")
    val cost: Double? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("payments")
    val payments: List<FinproPayments?>? = null,
    @SerializedName("customer")
    val customer: PaymentCustomer? = null,
    @SerializedName("available_refund_methods")
    val refunds: List<FinproRefunds?>? = null,
    @SerializedName("flags")
    var flags: Map<String, Boolean>? = null,
    @SerializedName("items")
    val items: List<OrderItem>? = null,
    @SerializedName("payment_collection")
    val paymentCollectionInfo: PaymentCollectionInfo? = null,
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("error")
    var finproError: FinproError? = null,
    @SerializedName("used_refund_bank")
    var usedRefundBank: RefundBankAccount? = null,
    @SerializedName("loyalty")
    val loyalty: LoyaltyDiscount? = null
) : Parcelable {
    fun isPaid() = status?.equals(PaymentConst.STATUS_PAID, ignoreCase = true) ?: false
    fun isCompleted() = status?.equals(PaymentConst.STATUS_COMPLETED, ignoreCase = true) ?: false
    fun isFailed() = status?.equals(PaymentConst.STATUS_FAILED, ignoreCase = true) ?: false
}

fun OrderResponse.getCompletedStatusDate(): String? {
    return if (isCompleted() && progress?.size.orNil != 5) {
        progress?.firstOrNull { it.state == "COMPLETED" }
            ?.getFormattedTimestamp()
    } else null
}

@Keep
@Parcelize
data class PaymentCollectionInfo(
    @SerializedName("cash_transaction")
    val paymentCollectionCashTransactionInfo: PaymentCollectionCashTransactionInfo? = null,
    @SerializedName("detail")
    val paymentCollection: PaymentCollection? = null
) : Parcelable

@Keep
@Parcelize
data class PaymentCollectionCashTransactionInfo(
    @SerializedName("transaction_id")
    val transactionId: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("amount")
    val amount: Double? = null
) : Parcelable

@Parcelize
data class PaymentCollection(
    @SerializedName("account_id")
    val accountId: String? = null,

    @SerializedName("customer_id")
    val customerId: String? = null,

    @SerializedName("customer_name")
    val customerName: String? = null,

    @SerializedName("transaction_id")
    val transactionId: String? = null,

    @SerializedName("payment_request_id")
    val paymentRequestId: String? = null,

    @SerializedName("amount")
    val amount: BigDecimal = BigDecimal.ZERO,

    @SerializedName("fee")
    val fee: Float = 0.0f,

    @SerializedName("discount")
    val discount: Float = 0.0f,

    @SerializedName("invoice_url")
    val invoiceUrl: String? = null,

    @SerializedName("share_url_template")
    val shareUrlTemplate: String? = null,

    @SerializedName("expired_at")
    val expiredAt: String? = null,

    @SerializedName("template")
    val template: String? = null,

    @SerializedName("status")
    val status: String? = null,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("bank_account_id")
    val bankAccountId: String? = null,

    @SerializedName("payment_method")
    val paymentMethod: String? = null,

    @SerializedName("payment_channel")
    val paymentChannel: String? = null,

    @SerializedName("progress")
    val progress: List<PaymentProgress> = emptyList(),

    @SerializedName("transaction_type")
    val transactionType: String? = null,

    @SerializedName("payment_category")
    val paymentCategory: PaymentCategoryItem? = null,

    @SerializedName("payment_category_id")
    val paymentCategoryId: String? = null,

    @SerializedName("reference_id")
    val referenceId: String? = null,

    @SerializedName("received_amount")
    val receivedAmount: BigDecimal?): Parcelable {

    companion object {
        fun newCollectionRequest(
            amount: Long,
            description: String,
            bankAccountId: String?,
            customerName: String?,
            extras: PaymentExtras? = null,
            paymentCategoryId: String? = null,
            referenceId: String? = null,
            receivedAmount: BigDecimal? = null
        ): PaymentCollection {
            return PaymentCollection(
                amount = BigDecimal(amount),
                description = description,
                bankAccountId = bankAccountId,
                customerName = customerName,
                paymentCategoryId = paymentCategoryId,
                referenceId = referenceId,
                receivedAmount = receivedAmount
            )
        }
    }
}

@Parcelize
data class FinproPayments(
    @SerializedName("payment_id")
    val paymentId: String,
    @SerializedName("payment_url")
    val paymentUrl: String? = null,
    @SerializedName("checkout_url")
    val checkoutUrl: String? = null,
    @SerializedName("amount")
    val amount: Double,
    @SerializedName("expired_at")
    val expiredAt: String? = null,
    @SerializedName("invoice_payment")
    val invoicePayment: Boolean? = null,
    @SerializedName("payment_method")
    val paymentMethod: FinproPaymentMethod?,
    @SerializedName("destination_bank_information")
    val destinationBankInformation: DestinationBankInformation? = null
): Parcelable

@Parcelize
data class CustomerProfile(
    @SerializedName("favorite_details")
    var favouriteDetails: FavouriteDetail? = null,
    @SerializedName("is_favorite")
    var isFavorite: Boolean? = null,
    @SerializedName("details")
    val details: Details? = null,
    @SerializedName("category")
    val category: String? = null
): Parcelable

@Parcelize
data class Details(
    @SerializedName("ACCOUNT_NUMBER")
    val accountNumber: String? = null,
    @SerializedName("PHONE_NUMBER")
    val phoneNumber: String? = null,
    @SerializedName("region")
    val region: String? = null,
    @SerializedName("zoneId")
    val zoneId: String? = null,
    @SerializedName("BILL_CUSTOMER_NAME")
    val billCustomerName: String? = null
): Parcelable


@Parcelize
data class PaymentCustomer(
    @SerializedName("customer_id")
    val customerId: String?,
    @SerializedName("customer_name")
    val customerName: String?
): Parcelable

@Parcelize
data class PaymentProgress(
    val state: String,
    val timestamp: String?,
    val description: String?,
    val additional_info: String?,
    var hasNextTimestamp: Boolean = false,
    var hasPreviousTimestamp: Boolean = false,
    val hasAdditionalInfo: Boolean = false
) : Parcelable, Serializable {
    fun getFormattedTimestamp(): String {
        return if (timestamp != null) {
            DateTimeUtils.getStringFromUtc(timestamp, DateTimeUtils.DD_MMM_YYYY_HH_MM)
        } else ""
    }
}

@Parcelize
data class OrderItem(
    @SerializedName("sku")
    val sku: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("amount")
    val amount: Double,
    @SerializedName("fee")
    val fee: Double,
    @SerializedName("beneficiary")
    val beneficiary: FinproBeneficiary,
    @SerializedName("disbursableType")
    val disbursableType: String? = null,
    @SerializedName("admin_fee")
    val adminFee: Double,
    @SerializedName("discounted_fee")
    val discountedFee: Double,
    @SerializedName("selling_price")
    val sellingPrice: Double,
    @SerializedName("details")
    val details: PpobDetailParam
): Parcelable

@Parcelize
data class FinproBeneficiary(
    @SerializedName("category")
    val category: String,
    @SerializedName("code")
    val code: String,
    @SerializedName("number")
    val number: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("flags")
    val flag: Flags? = null,
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("account_number")
    val accountNumber: String? = null,
    @SerializedName("phone_number")
    val phoneNumber: String? = null
): Parcelable

@Parcelize
data class MetaData(
    @SerializedName("biller_type")
    val billerType: String? = null,
    @SerializedName("biller_name")
    val billerName: String? = null,
    @SerializedName("infobox_text")
    val infoboxText: String? = null,
    @SerializedName("id_field_name")
    val idFieldName: String? = null,
    @SerializedName("warning_message")
    val warningMessage: String? = null,
    @SerializedName("id_field_value")
    val idFieldValue: String? = null,
    @SerializedName("redeem_faq_link")
    val redeemFaqLink: String? = null,
    @SerializedName("faq_link")
    val faq_link: String? = null,
    @SerializedName("faq_cta")
    val faq_cta: String? = null,
    @SerializedName("fine_amount")
    val fineAmount: Double? = null,
    @SerializedName("biller_icon")
    val logo: String? = null,
    @SerializedName("category_display_name")
    val categoryDisplayName: String? = null,
    @SerializedName("expiry_date")
    val expiryDate: String? = null,
    @SerializedName("expected_date")
    val expectedDate: String? = null,
    @SerializedName("transaction_code")
    val transactionCode: String? = null,
    @SerializedName("buku_origin")
    val bukuOrigin: String? = null
): Parcelable

@Parcelize
data class PpobDetailParam(
    @SerializedName("serial_number")
    val serialNumber: String?,
    @SerializedName("paid_at")
    val paidAt: String?,
    @SerializedName("tarif")
    val tarif: String?,
    @SerializedName("amount")
    val amount: Double?,
    @SerializedName("admin_fee")
    val adminFee: Double?,
    @SerializedName("no_meter")
    val noMeter: String?,
    @SerializedName("customer_name")
    val customerName: String?,
    @SerializedName("total_kwh")
    val totalKwh: String?,
    @SerializedName("reference_number")
    val referenceNumber: String?,
    @SerializedName("token")
    val token: String?,
    @SerializedName("total_lembar_tagihan")
    val totalLembarTagihan: String?,
    @SerializedName("periode")
    val periode: String?,
    @SerializedName("product_name")
    val productName: String?,
    @SerializedName("response_code")
    val responseCode: String?,
    @SerializedName("customer_number")
    val customerNumber: String?,
    @SerializedName("installment_number")
    val installmentNumber: String?,
    @SerializedName("fine")
    val fine: String?,
    @SerializedName("e_ticket")
    val eTicket: String?,
    @SerializedName("voucher_code")
    val voucherCode: String? = null,
    @SerializedName("member_count")
    val memberCount: String? = null,
    @SerializedName("period")
    val period: String? = null,
    @SerializedName("policy_number")
    val policyNumber: String? = null,
    @SerializedName("vehicle_name")
    val vehicleName: String? = null,
    @SerializedName("vehicle_type")
    val vehicleType: String? = null,
    @SerializedName("vehicle_brand")
    val vehicleBrand: String? = null,
    @SerializedName("vehicle_color")
    val vehicleColor: String? = null,
    @SerializedName("build_year")
    val buildYear: String? = null,
    @SerializedName("expiration_date")
    val expirationDate: String? = null,
    @SerializedName("machine_number")
    val machineNumber: String? = null,
    @SerializedName("frame_number")
    val frameNumber: String? = null,
    @SerializedName("address")
    val address: String? = null,
    @SerializedName("product_price")
    val productPrice: String? = null,
    @SerializedName("pkb")
    val pkb: String? = null,
    @SerializedName("late_fee")
    val lateFee: String? = null ,
    @SerializedName("customer_email")
    val customerEmail: String? = null,
    @SerializedName("train_origin_station_code")
    val trainOriginStationCode: String? = null,
    @SerializedName("train_destination_station_code")
    val trainDestinationStationCode: String? = null,
    @SerializedName("train_origin_station_name")
    val trainOriginStationName: String? = null,
    @SerializedName("train_destination_station_name")
    val trainDestinationStationName: String? = null,
    @SerializedName("train_arrival_time")
    val trainArrivalTime: String? = null,
    @SerializedName("train_departure_time")
    val trainDepartureTime: String? = null,
    @SerializedName("train_name")
    val trainName: String? = null,
    @SerializedName("train_wagon_name")
    val trainWagonName: String? = null,
    @SerializedName("train_passengers")
    val trainPassenger: List<TrainPassenger>? = null,
    @SerializedName("bnpl_admin_fee")
    val bnplAdminFee: Double?= null,
    @SerializedName("payment_expiration_timestamp")
    val paymentExpirationTimestamp: String? = null,
    @SerializedName("nik")
    val nik: String? = null,
    @SerializedName("phone_number")
    val phoneNumber: String? = null,
    @SerializedName("biller_name")
    val billerName: String? = null
) : Parcelable

@Parcelize
data class TrainPassenger(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("id_number")
    val idNumber: String? = null,
    @SerializedName("seat")
    val seat: String? = null,
    val viewType: Int = 0
) : Parcelable