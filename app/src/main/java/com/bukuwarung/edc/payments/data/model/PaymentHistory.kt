package com.bukuwarung.edc.payments.data.model

import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.DateTimeUtils.DD_MMM_YYYY_HH_MM
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class PaymentHistory(
    @SerializedName("order_id")
    val orderId: String? = null,

    @SerializedName("customer_id")
    val customerId: String? = null,

    @SerializedName("display_name")
    val displayName: String? = null,

    @SerializedName("amount")
    val amount: Double? = null,

    @SerializedName("timestamp")
    val timestamp: String? = null,

    @SerializedName("status")
    val status: String? = "",

    @SerializedName("type")
    val type: String? = "",

    val viewType: Int = 0,

    @SerializedName("customer_profile")
    val customerProfile: CustomerProfile? = null,

    @SerializedName("tags")
    val tags: Map<String, Tag>? = null,

    @SerializedName("ledger_account_id")
    val ledgerAccountId: String? = null,

    @SerializedName("linked_order_count")
    var linkedOrderCount: Int? = null,

    @SerializedName("serial_number")
    val serialNumber: String? = null,

    @SerializedName("detail_service")
    val detailService: String? = null,

    @SerializedName("original_transaction_type")
    val originalTransactionType: String? = null,

    var isLinkedOrder: Boolean = false
) : Parcelable {


    fun formattedDate(): String? {
        return DateTimeUtils.getLocalStringFromUtc(timestamp, DD_MMM_YYYY_HH_MM)
    }

    companion object {
        const val TYPE_PAYMENT_IN = "IN"
        const val TYPE_PAYMENT_OUT = "OUT"
        const val TYPE_CASHBACK_SETTLEMENT = "CASHBACK_SETTLEMENT"
        const val TYPE_SALDO_IN = "SALDO"
        const val TYPE_SALDO_REFUND = "SALDO_IN"
        const val TYPE_SALDO_OUT = "SALDO_OUT"
        const val TYPE_SALDO_REDEMPTION = "SALDO_REDEMPTION"
        const val TYPE_SALDO_CASHBACK = "SALDO_CASHBACK"
        const val TYPE_SALDO_BNPL = "SALDO_BNPL"
        const val STATUS_PENDING = "PENDING"
        const val STATUS_UNHOLD = "UNHOLD"
        const val STATUS_CREATED = "CREATED"
        const val STATUS_COMPLETED = "COMPLETED"
        const val STATUS_PAID = "PAID"
        const val STATUS_FAILED = "FAILED"
        const val STATUS_CANCELLED = "CANCELLED"
        const val STATUS_REFUNDING = "REFUNDING"
        const val STATUS_REFUNDED = "REFUNDED"
        const val STATUS_REFUNDING_FAILED = "REFUNDING_FAILED"
        const val STATUS_EXPIRED = "EXPIRED"
        const val STATUS_REJECTED = "REJECTED"
        const val STATUS_HOLD = "HOLD"
        const val SALDO_BNPL = "BNPL"
        const val SALDO = "SALDO"
        val STATUS_RED_LIST = arrayListOf(
            STATUS_FAILED.lowercase(),
            STATUS_REFUNDING_FAILED.lowercase(),
            STATUS_EXPIRED.lowercase(),
            STATUS_REJECTED.lowercase(),
            STATUS_CANCELLED.lowercase()
        )
    }
}

@Parcelize
data class Tag(
    @SerializedName("id")
    val id: String? = "",
    @SerializedName("display_text")
    val displayText: String? = "",
    @SerializedName("text_color")
    val textColor: String? = "",
    @SerializedName("background_color")
    val backgroundColor: String? = "",
):Parcelable


data class LinkedOrdersData(
    var linkedOrdersVisibility: Boolean = false,
    var linkedOrdersLoading: Boolean = false,
    var linkedOrders: ArrayList<OrderHistoryData>? = null
)