package com.bukuwarung.edc.payments.data.model

import com.google.gson.annotations.SerializedName


data class SaldoResponse(
    @SerializedName("amount")
    val amount: Double? = null,
    @SerializedName("visible")
    val visible: Boolean? = false,
    @SerializedName("limit")
    val limit: Double? = null,
    @SerializedName("sub_balance")
    val subBalance: SubBalance? = null,
    @SerializedName("debits_monthly")
    val debitMonthly: Double? = null,
    @SerializedName("debits_monthly_limit")
    val debitMonthlyLimit: Double? = null,
    @SerializedName("debits_daily")
    val debitDaily: Double? = null,
    @SerializedName("debits_daily_limit")
    val debitDailyLimit: Double? = null,
    @SerializedName("bms_registered")
    val bmsRegistered: Boolean? = null,
    @SerializedName("supreme_limit")
    val supremeLimit: SaldoTierLimits? = null,
    @SerializedName("advanced_limit")
    val advancedLimit: SaldoTierLimits? = null,
    @SerializedName("saldo_freeze")
    val isSaldoFreezed: Boolean? = null
)


data class SubBalance(
    @SerializedName("saldo")
    val saldo: Double? = null,
    @SerializedName("cashback")
    val cashback: Double? = null
)


data class SaldoTierLimits(
    @SerializedName("daily_debit")
    val dailyDebit: Double? = null,
    @SerializedName("monthly_debit")
    val monthlyDebit: Double? = null,
    @SerializedName("hold")
    val hold: Double? = null
)