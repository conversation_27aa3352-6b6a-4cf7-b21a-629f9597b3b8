package com.bukuwarung.edc.payments.data.model.request

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class FavouriteRequest(

    @SerializedName("alias")
    val alias: String? = null,

    @SerializedName("phone_number")
    val phoneNumber: String? = null,

    @SerializedName("order_id")
    val orderId: String? = null
) : Parcelable
