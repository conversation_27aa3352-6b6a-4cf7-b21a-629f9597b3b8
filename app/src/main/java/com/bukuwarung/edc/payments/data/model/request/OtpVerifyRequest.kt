package com.bukuwarung.edc.payments.data.model.request

import com.google.gson.annotations.SerializedName

data class OtpVerifyRequest(
    @SerializedName("userId")
    var userId: String? = null,
    @SerializedName("countryCode")
    var countryCode: String? = null,
    @SerializedName("phone")
    var phone: String? = null,
    @SerializedName("otp")
    var otp: String? = null,
    @SerializedName("deviceId")
    var deviceId: String? = null,
    @SerializedName("action")
    var action: String? = null,
    @SerializedName("clientId")
    var clientId: String? = null,
    @SerializedName("clientSecret")
    var clientSecret: String? = null
)