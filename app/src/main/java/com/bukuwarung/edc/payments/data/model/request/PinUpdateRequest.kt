package com.bukuwarung.edc.payments.data.model.request

import androidx.annotation.Keep
import com.bukuwarung.edc.util.Utils
import com.google.gson.annotations.SerializedName

@Keep
data class PinUpdateRequest (
    @SerializedName("deviceId")
    var deviceId: String = Utils.generatedAppId(),
    @SerializedName("pin")
    var pin: String? = null,
    @SerializedName("source")
    var source: String = "android",
    @SerializedName("oldPin")
    val oldPin: String? = null,
    @SerializedName("newPin")
    val newPin: String? = null
)