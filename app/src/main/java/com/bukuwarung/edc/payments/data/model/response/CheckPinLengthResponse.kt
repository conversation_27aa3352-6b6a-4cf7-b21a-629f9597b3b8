package com.bukuwarung.edc.payments.data.model.response

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class CheckPinLengthResponse(
    @SerializedName("success")
    val success: Boolean = false,
    @SerializedName("message")
    val message: String? = null,
    @SerializedName("pinLength")
    val pinLength: Int? = null,
    @SerializedName("token")
    val token: String? = null,
    @SerializedName("lastPinChangeDate")
    val lastPinChangeDate: String? = ""
)
