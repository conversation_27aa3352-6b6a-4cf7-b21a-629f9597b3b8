package com.bukuwarung.edc.payments.data.repository

import androidx.lifecycle.MutableLiveData
import androidx.paging.PageKeyedDataSource
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.orNil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class FavouriteDataSource(
    private val finproUseCase: FinproUseCase,
    private val coroutineScope: CoroutineScope,
    private val pagingStatusLiveData: MutableLiveData<PagingStatus>,
    private val category: String
) : PageKeyedDataSource<Int, ProfilesItem>() {

    private var totalItemCount: Int = 0

    override fun loadInitial(
            params: LoadInitialParams<Int>,
            callback: LoadInitialCallback<Int, ProfilesItem>
    ) {
        pagingStatusLiveData.postValue(PagingStatus.Loading)

        coroutineScope.launch {
            try {
                finproUseCase.getFavourites(Utils.getPaymentAccountId(), category, 0, 10).let {
                    if (it.isSuccessful) {
                        val data = it.body()?.profiles
                        totalItemCount += data?.size.orNil
                        if (data?.isNotEmpty() == true) {
                            pagingStatusLiveData.postValue(PagingStatus.Loaded(totalItemCount))
                            callback.onResult(
                                data,
                                0,
                                data.size,
                                0,
                                if (totalItemCount != it.body()?.metadata?.totalCount) {
                                    1
                                } else {
                                    null
                                }
                            )
                        } else {
                            pagingStatusLiveData.postValue(PagingStatus.Empty)
                        }
                    } else {
                        pagingStatusLiveData.postValue(PagingStatus.Error(it.errorMessage()))
                    }
                }
            } catch (e: Exception) {
                pagingStatusLiveData.postValue(PagingStatus.Error(""))
            }
        }
    }

    override fun loadBefore(params: LoadParams<Int>, callback: LoadCallback<Int, ProfilesItem>) {
        // no implementation required
    }

    override fun loadAfter(params: LoadParams<Int>, callback: LoadCallback<Int, ProfilesItem>) {
        coroutineScope.launch {
            try {
                finproUseCase.getFavourites(
                    Utils.getPaymentAccountId(),
                    category,
                    params.key,
                    10
                ).let {
                    if (it.isSuccessful) {
                        val data = it.body()?.profiles
                        totalItemCount += data?.size.orNil
                        if (data?.isNotEmpty() == true) {
                            callback.onResult(
                                data,
                                if (totalItemCount != it.body()?.metadata?.totalCount) {
                                    params.key + 1
                                } else {
                                    null
                                }
                            )
                        }
                    }
                }
            } catch (_: Exception) {}
        }
    }
}