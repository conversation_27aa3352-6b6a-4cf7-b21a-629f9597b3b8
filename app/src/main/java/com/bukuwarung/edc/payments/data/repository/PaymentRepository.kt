package com.bukuwarung.edc.payments.data.repository

import com.bukuwarung.edc.payments.data.datasource.PaymentDataSource
import com.bukuwarung.edc.payments.data.model.request.BankValidationRequest
import com.bukuwarung.edc.payments.data.model.request.DisbursementOverviewRequest
import com.bukuwarung.edc.payments.data.model.DisbursementRequest
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckRequest
import com.bukuwarung.edc.payments.data.model.UpdateFeeRequest
import com.bukuwarung.edc.payments.data.model.request.BankAccountRequest
import javax.inject.Inject

 class PaymentRepository @Inject constructor(private val paymentDataSource: PaymentDataSource) {

    suspend fun getBanks() = paymentDataSource.getBanks()

    suspend fun validateBankAccount(
        accountId: String,
        bankValidationRequest: BankValidationRequest
    ) = paymentDataSource.validateBankAccount(accountId, bankValidationRequest)

    suspend fun getPaymentCategoryList(disbursableType: String) =
        paymentDataSource.getPaymentCategoryList(disbursableType)

    suspend fun getPaymentOutLimits(accountId: String, customerId: String) =
        paymentDataSource.getPaymentOutLimits(accountId, customerId)

    suspend fun doHealthCheck(paymentHealthCheckRequest: PaymentHealthCheckRequest) =
        paymentDataSource.doHealthCheck(paymentHealthCheckRequest)

    suspend fun getDisbursementOverview(
        accountId: String,
        customerId: String,
        overviewRequest: DisbursementOverviewRequest
    ) = paymentDataSource.getDisbursementOverview(accountId, customerId, overviewRequest)

    suspend fun validateAndAddCustomerBankAccount(
        accountId: String,
        customerId: String,
        bankValidationRequest: BankValidationRequest
    ) = paymentDataSource.validateAndAddCustomerBankAccount(
        accountId,
        customerId,
        bankValidationRequest
    )

    suspend fun createDisbursement(
        idempotencyKey: String,
        accountId: String,
        customerId: String,
        request: DisbursementRequest
    ) = paymentDataSource.createDisbursement(idempotencyKey, accountId, customerId, request)

     suspend fun updatePaymentOutAgentFee(
         businessId: String, customerId: String,
         paymentRequestId: String, updateFeeRequest: UpdateFeeRequest
     ) = paymentDataSource.updatePaymentOutAgentFee(
         businessId, customerId, paymentRequestId, updateFeeRequest
     )

     suspend fun getPaymentMethods(
         accountId: String, paymentType: String, bankCode: String, transactionAmount: Double
     ) = paymentDataSource.getPaymentMethods(accountId, paymentType, bankCode, transactionAmount)

     suspend fun getCustomerBankAccounts(
         accountId: String,
         customerId: String,
         isCashbackSettlementBank: Boolean?
     ) = paymentDataSource.getCustomerBankAccounts(accountId, customerId, isCashbackSettlementBank)

     suspend fun getMerchantBankAccounts(
         accountId: String, type: String?
     ) = paymentDataSource.getMerchantBankAccounts(accountId, type)

     suspend fun deleteMerchantBankAccount(accountId: String, bankAccountId: String) =
         paymentDataSource.deleteMerchantBankAccount(accountId, bankAccountId)

     suspend fun addMerchantBankAccount(accountId: String, bankAccount: BankAccountRequest) =
         paymentDataSource.addMerchantBankAccount(accountId, bankAccount)

     suspend fun enablePaymentAccount(accountId: String, businessNameMap: Map<String, String>) = paymentDataSource.enabledMerchantPayments(accountId, businessNameMap)

 }