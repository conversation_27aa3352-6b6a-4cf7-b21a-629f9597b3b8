package com.bukuwarung.edc.payments.di

import com.bukuwarung.edc.payments.data.datasource.FinproDataSource
import com.bukuwarung.edc.payments.data.datasource.OrderDataSource
import com.bukuwarung.edc.payments.data.datasource.PaymentDataSource
import com.bukuwarung.edc.payments.data.datasource.PaymentTwoFARemoteDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class PaymentApiModule {

    @Singleton
    @Provides
    fun providePaymentDataSource(@Named("normal") retrofit: Retrofit): PaymentDataSource =
        retrofit.create(PaymentDataSource::class.java)

    @Singleton
    @Provides
    fun provideFinproDataSource(@Named("normal") retrofit: Retrofit): FinproDataSource =
        retrofit.create(FinproDataSource::class.java)

    @Singleton
    @Provides
    fun providePaymentTwoFARemoteSource(@Named("payment_pin") retrofit: Retrofit): PaymentTwoFARemoteDataSource =
        retrofit.create(PaymentTwoFARemoteDataSource::class.java)

    @Singleton
    @Provides
    fun provideOrderDataSource(@Named("normal") retrofit: Retrofit): OrderDataSource =
        retrofit.create(OrderDataSource::class.java)

}