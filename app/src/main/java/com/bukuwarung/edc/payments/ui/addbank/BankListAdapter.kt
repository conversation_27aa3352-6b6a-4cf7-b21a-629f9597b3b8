package com.bukuwarung.edc.payments.ui.addbank

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ListItemBankBinding
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity.Companion.BANK_STATUS_INACTIVE
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.showView
import com.bumptech.glide.Glide
import javax.inject.Inject


class BankListAdapter @Inject constructor(
    private val banks: List<Bank>,
    private var filteredBanks: List<Bank>,
    private val callback: Callback
) : RecyclerView.Adapter<BankListAdapter.BankViewHolder>(), Filterable {

    private var searchQuery = ""

    interface Callback {
        fun selectBank(bank: Bank)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BankViewHolder {
        val itemBinding =
            ListItemBankBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BankViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: BankViewHolder, position: Int) {
        holder.bind(filteredBanks[position])
    }

    override fun getItemCount(): Int = filteredBanks.size

    inner class BankViewHolder(
        private val binding: ListItemBankBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(bank: Bank) {
            with(binding) {
                val context = root.context
                Glide.with(context)
                    .load(bank.logo)
                    .placeholder(R.drawable.ic_bank)
                    .error(R.drawable.ic_bank)
                    .into(ivBankLogo)
                tvBankName.text = bank.bankName
                if (bank.isDisabled.isTrue) {
                    clLayout.setBackgroundColor(ContextCompat.getColor(context, R.color.black_5))
                    tvBankError.text = bank.message
                    tvBankError.showView()
                    root.setOnClickListener(null)
                } else {
                    root.setOnClickListener {
                        callback.selectBank(bank)
                    }
                    clLayout.setBackgroundColor(ContextCompat.getColor(context, R.color.white))
                    tvBankError.hideView()
                }

                if (bank.status.equals(BANK_STATUS_INACTIVE, true)) {
                    clLayout.setBackgroundColor(ContextCompat.getColor(context, R.color.black_5))
                    tvBankError.text = context.getString(R.string.not_available)
                    tvBankError.showView()
                    root.setOnClickListener(null)
                } else {
                    tvBankError.hideView()
                    clLayout.setBackgroundColor(ContextCompat.getColor(context, R.color.white))
                    root.setOnClickListener { callback.selectBank(bank) }
                }
            }
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val search = constraint.toString()
                searchQuery = search

                val filterResults = FilterResults()
                filterResults.values = if (search.isNotEmpty()) banks.filter {
                    it.bankName.lowercase().contains(search.lowercase()).isTrue
                } else banks
                return filterResults
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                results?.let {
                    filteredBanks = it.values as ArrayList<Bank>
                }
                notifyDataSetChanged()
            }
        }
    }
}
