package com.bukuwarung.edc.payments.ui.category

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentPaymentCategoriesInfoBinding
import com.bukuwarung.edc.payments.ui.core.PaymentViewModel
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class PaymentCategoriesInfoFragment : Fragment() {

    private lateinit var binding: FragmentPaymentCategoriesInfoBinding
    private var callback: Callback? = null
    private val paymentViewModel: PaymentViewModel by activityViewModels()

    interface Callback {
        fun dismissFragment()
    }

    companion object {
        const val TAG = "Categories_info_frag"

        fun createInstance() = PaymentCategoriesInfoFragment()
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater, viewGroup: ViewGroup?, bundle: Bundle?
    ): View {
        binding = FragmentPaymentCategoriesInfoBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { callback = it as? Callback }
        if (context is Callback) callback = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.includeToolBar.apply {
            tvTitle.text = getString(R.string.product_category_description)
            btnBack.setOnClickListener {
                callback?.dismissFragment()
            }
        }
        paymentViewModel.categories.observe(viewLifecycleOwner) {
            it.data?.let {
                binding.rvCategoryInfo.apply {
                    adapter = PaymentCategoryInfoAdapter(it)
                    layoutManager = LinearLayoutManager(context)
                }
            }

        }
    }
}