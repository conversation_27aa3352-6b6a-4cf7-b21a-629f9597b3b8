package com.bukuwarung.edc.payments.ui.category

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.activity.addCallback
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityPaymentCategoryBinding
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.payments.data.model.PaymentCategoryItem
import com.bukuwarung.edc.payments.ui.core.PaymentInputActivity.Companion.PAYMENT_CATEGORY
import com.bukuwarung.edc.payments.ui.core.PaymentViewModel
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.textHTML
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class PaymentCategoryActivity : AppCompatActivity(), PaymentCategoriesInfoFragment.Callback,
    PaymentCategoryItemAdapter.Callback {

    private lateinit var binding: ActivityPaymentCategoryBinding
    private val paymentViewModel: PaymentViewModel by viewModels()

    private var paymentCategoryAdapter: PaymentCategoryItemAdapter? = null
    private var infoFragment: PaymentCategoriesInfoFragment? = null
    private val selectedCatId by lazy { intent?.getStringExtra(SELECTED_CAT_ID) }

    companion object {
        private const val SELECTED_CAT_ID = "selected_cat_id"

        fun createIntent(
            context: Context, selectedCategoryId: String?
        ) = Intent(context, PaymentCategoryActivity::class.java).apply {
            putExtra(SELECTED_CAT_ID, selectedCategoryId)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityPaymentCategoryBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        binding.includeError.backBtn.setOnClickListener {
            finish()
        }

        paymentViewModel.categories.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    binding.pbProgress.hideView()
                    it.data?.let {
                        paymentCategoryAdapter = PaymentCategoryItemAdapter(it, selectedCatId, this)
                        binding.rvCategories.apply {
                            adapter = paymentCategoryAdapter
                            layoutManager = LinearLayoutManager(context)
                        }

                        binding.tvCategoryInfo.apply {
                            textHTML(getString(R.string.category_learn_more))
                            setOnClickListener {
                                infoFragment = PaymentCategoriesInfoFragment.createInstance()
                                supportFragmentManager.beginTransaction().add(
                                    R.id.fragment_container_view,
                                    infoFragment!!,
                                    PaymentCategoriesInfoFragment.TAG
                                ).commit()
                                binding.fragmentContainerView.showView()
                                onBackPressedDispatcher.addCallback {
                                    if (binding.fragmentContainerView.isVisible) {
                                        dismissFragment()
                                    } else finish()
                                }
                            }
                        }
                    }
                }

                Status.ERROR -> {
                    binding.pbProgress.hideView()
                    binding.includeError.root.showView()
                }

                Status.LOADING -> {
                    binding.pbProgress.showView()
                }
                Status.NO_INTERNET -> {
                    binding.pbProgress.hideView()
                }
            }
        }

        paymentViewModel.getCategories()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressedDispatcher.onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun dismissFragment() {
        infoFragment?.let { supportFragmentManager.beginTransaction().remove(it).commit() }
        binding.fragmentContainerView.hideView()
    }

    override fun onCategorySelected(category: PaymentCategoryItem) {
        val intent = Intent()
        intent.putExtra(PAYMENT_CATEGORY, category)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

}