package com.bukuwarung.edc.payments.ui.contacts

import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.payments.constant.Tag
import com.bukuwarung.edc.payments.data.model.RowHolder
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.ProfileIconHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.ui_component.utils.setSingleClickListener

class ContactAdapter(
    private val contactList: ArrayList<RowHolder>,
    private val useCase: CustomerSearchUseCase,
    val selectContact: (id: Int, source: String) -> Unit,
    val loadMoreRecommendationContacts: () -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    private var query = ""

    private fun bindContactViewHolder(
        contactViewHolder: ContactViewHolder,
        contactRowHolder: RowHolder.ContactRowHolder,
        position: Int
    ) {
        //highlighting the query string in the results
        contactViewHolder.name.text = Utils.makeSectionOfTextBold(
            contactRowHolder.contact.name ?: "", query,
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    //if click requires
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                    ds.color = contactViewHolder.mobile.context.getColorCompat(R.color.blue_60)
                }
            })
        var contactNumber: String
        // We are identifying the Favourites Recommendations based on the value from contact source and making ui changes.
        if (contactRowHolder.contactSource == PpobAnalytics.PHONEBOOK_RECOMMENDATION_CUSTOMER_SECTION) {
            val favouriteDescriptionString =
                contactRowHolder.favouriteText + " " + contactRowHolder.favouriteCount
            contactViewHolder.mobile.text = Utils.makeSectionOfTextBold(
                favouriteDescriptionString, contactRowHolder.favouriteCount,
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        //if click requires
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = false
                        ds.color = contactViewHolder.mobile.context.getColorCompat(R.color.blue_60)
                    }
                })
            contactViewHolder.ivFavourite.showView()
        } else {
            contactNumber = Utils.beautifyPhoneNumber(contactRowHolder.contact.mobile).orEmpty()
            if (contactNumber.isEmpty()) {
                contactNumber = "-"
            }
            contactViewHolder.mobile.text = contactNumber
            contactViewHolder.ivFavourite.hideView()
        }
        if (useCase == CustomerSearchUseCase.PAYMENT) {
            contactViewHolder.mobile.text = getBanksLabel(contactRowHolder)
        }
        contactViewHolder.contactLayout.setOnClickListener {
            selectContact(position, contactRowHolder.contactSource)
        }
        val contact = contactRowHolder.contact
        ProfileIconHelper.setProfilePic(
            contactViewHolder.itemView.context,
            contactViewHolder.contactPic,
            contactViewHolder.nameInitials,
            contact.name,
            contact.photo
        )
    }

    private fun getBanksLabel(contactRowHolder: RowHolder.ContactRowHolder): String {
        if (contactRowHolder.contact.banks.isNullOrEmpty()) return "-"
        return if (contactRowHolder.contact.banks.size == 1) {
            //[Bank Type] - [Bank Account Number]
            contactRowHolder.contact.banks.first()?.let {
                "${it.bankCode} - ${it.accountNumber}"
            } ?: "-"
        } else {
            //BCA - 123 dan 2 lainnya
            contactRowHolder.contact.banks.first()?.let {
                "${it.bankCode} - ${it.accountNumber} dan ${contactRowHolder.contact.banks.size - 1} lainnya"
            } ?: "-"
        }
    }

    fun setRowHolderList(newContactList: List<RowHolder>, query: String = "") {
        contactList.clear()
        contactList.addAll(newContactList)
        this.query = query
        notifyDataSetChanged()
    }

    override fun getItemViewType(i: Int): Int {
        return if (i < contactList.size) {
            contactList[i].tag
        } else {
            -1
        }
    }

    override fun getItemId(i: Int): Long {
        val rowHolder = contactList[i]
        return if (rowHolder is RowHolder.ContactRowHolder) {
            (rowHolder.getTag().toString() + ":" + rowHolder.contact.mobile).hashCode().toLong()
        } else {
            ":$i".hashCode().toLong()
        }
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): RecyclerView.ViewHolder {
        return when (i) {
            Tag.VIEW_CONTACT -> {
                val view = LayoutInflater.from(viewGroup.context)
                    .inflate(R.layout.phonebook_contact_item, viewGroup, false)
                ContactViewHolder(view)
            }

            Tag.VIEW_CONTACT_SEPARATOR -> {
                val view = LayoutInflater.from(viewGroup.context)
                    .inflate(R.layout.phonebook_contact_separator_new, viewGroup, false)
                EmptyViewHolder(view)
            }

            Tag.VIEW_ADDED_CONTACT_SEPARATOR -> {
                val view = LayoutInflater.from(viewGroup.context)
                    .inflate(R.layout.phonebook_customer_separator_new, viewGroup, false)
                EmptyViewHolder(view)
            }

            Tag.VIEW_FAVORITE_CONTACT_SEPARATOR -> {
                val view = LayoutInflater.from(viewGroup.context)
                    .inflate(R.layout.phonebook_customer_separator_new, viewGroup, false).apply {
                        findViewById<TextView>(R.id.new_contact).text =
                            viewGroup.context.getString(R.string.favorite_customer_contact)
                    }
                EmptyViewHolder(view)
            }

            Tag.VIEW_RECOMMENDATION_CONTACT_SEPARATOR -> {
                val view = LayoutInflater.from(viewGroup.context)
                    .inflate(R.layout.phonebook_favourites_separator, viewGroup, false)
                EmptyViewHolder(view)
            }

            Tag.BUTTON_LOAD_MORE_RECOMMENDATIONS -> {
                val view = LayoutInflater.from(viewGroup.context)
                    .inflate(R.layout.layout_load_more_recommendations, viewGroup, false).apply {
                        findViewById<TextView>(R.id.tv_load_more).text = context.getString(
                            R.string.show_more_recommendations,
                            "+" + PpobConst.RECOMMENDATION_CONTACTS_COUNT
                        )
                        findViewById<TextView>(R.id.tv_load_more).setSingleClickListener {
                            loadMoreRecommendationContacts()
                        }
                    }
                EmptyViewHolder(view)
            }

            else -> {
                val view = LayoutInflater.from(viewGroup.context)
                    .inflate(R.layout.empty_item_small, viewGroup, false)
                EmptyViewHolder(view)
            }
        }
    }

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, position: Int) {
        if (position >= contactList.size) {
            return
        }
        val rowHolder = contactList[position]
        val itemViewType = viewHolder.itemViewType
        if (itemViewType == Tag.VIEW_CONTACT) {
            val contactViewHolder = viewHolder as ContactViewHolder
            if (rowHolder != null) {
                bindContactViewHolder(
                    contactViewHolder,
                    rowHolder as RowHolder.ContactRowHolder,
                    position
                )
                return
            }
        }
    }

    override fun getItemCount(): Int {
        return contactList.size
    }
}
