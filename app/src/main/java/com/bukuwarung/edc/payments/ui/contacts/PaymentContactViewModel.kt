package com.bukuwarung.edc.payments.ui.contacts

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.R
import com.bukuwarung.edc.payments.data.model.FavouriteDetail
import com.bukuwarung.edc.payments.data.model.request.FavouriteRequest
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PaymentContactViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase
) : ViewModel() {

    sealed class Event {
        data class OnAddFavourite(val favouriteDetail: FavouriteDetail?, val message: String): Event()
    }

    private val event = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = event

    fun addFavourite(favouriteRequest: FavouriteRequest, entryPoint: String?, context: Context) = viewModelScope.launch(Dispatchers.IO) {
        try {
            val response = finproUseCase.addFavourite(Utils.getPaymentAccountId(), favouriteRequest)
            if (response.isSuccessful) {
                event.postValue(
                    Event.OnAddFavourite(
                        response.body()?.favouriteDetail, response.body()?.message
                            ?: context.getString(R.string.add_favourite_success_msg)
                    )
                )
            } else {
                event.postValue(Event.OnAddFavourite(null, response.errorMessage()))
            }
        } catch (e: Exception) {
            event.postValue(Event.OnAddFavourite(null, ""))
        }
    }
}