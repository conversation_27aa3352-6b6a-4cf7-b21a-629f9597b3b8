package com.bukuwarung.edc.payments.ui.contacts

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.UserContactFragmentBinding
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.showView
import com.bukuwarung.ui_component.utils.setSingleClickListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class UserContactFragment : Fragment(), ContactSearchFragment.IUserContactFragmentCommunicator {

    private var communicator: OnSaveButtonCallback? = null
    private lateinit var contactSearchFragment: ContactSearchFragment
    private var userContactFragmentBinding: UserContactFragmentBinding? = null
    private val binding get() = userContactFragmentBinding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        userContactFragmentBinding = UserContactFragmentBinding.inflate(inflater, container, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        communicator = context as? OnSaveButtonCallback
    }

    private fun setupView() {
        val useCase = CustomerSearchUseCase.FAVORITE
        arguments?.let {
            if (it.getString(TITLE) != null) {
                binding.screenTitle.text = it.getString(TITLE)
            } else {
                when (it.getInt(TRANSACTION_TYPE)) {
                    TRANSACTION_TYPE_CREDIT -> {
                        binding.screenTitle.text = getString(R.string.btn_add_credit)
                    }

                    TRANSACTION_TYPE_DEBIT -> {
                        binding.screenTitle.text = getString(R.string.btn_add_credit)
                    }

                    TYPE_DEBIT_FROM_TRANSACTION_FLOW -> {
                        binding.screenTitle.text =
                            getString(R.string.transaction_contact_debit_title)
                    }

                    TYPE_CREDIT_FROM_TRANSACTION_FLOW -> {
                        binding.screenTitle.text =
                            getString(R.string.transaction_contact_debit_title)
                    }

                    TRANSACTION_TYPE_FAVOURITE -> {
                        binding.screenTitle.text =
                            getString(R.string.favourite_title)
                    }

                    TRANSACTION_TYPE_BOOKKEEPING -> {
                        binding.screenTitle.text =
                            getString(R.string.transaction_contact_bookkeeping_title)
                    }
                }
            }
            contactSearchFragment = ContactSearchFragment.getInstance(
                arguments?.getString(QUERY) ?: "", arguments?.getInt(
                    TRANSACTION_TYPE
                ), useCase, arguments?.getString(ORDER_ID).orEmpty()
            )
        }
        binding.back.setOnClickListener {
            Utils.hideKeyboard(requireActivity())
            activity?.onBackPressed()
        }
        childFragmentManager.beginTransaction()
            .add(binding.searchContainer.id, contactSearchFragment)
            .commit()
        if (arguments?.getInt(TRANSACTION_TYPE) == TRANSACTION_TYPE_FAVOURITE) {
            binding.btnSave.showView()
            binding.btnSave.isEnabled = false
            binding.btnSave.setSingleClickListener {
                if (contactSearchFragment.getQueryText().isNotNullOrBlank()) {
                    communicator?.onSave(contactSearchFragment.getQueryText())
                    Utils.hideKeyboard(requireActivity())
                }
            }
            binding.cvSearchResultsContainer.hideView()
            childFragmentManager.beginTransaction()
                .add(binding.searchResults.id, ContactSearchResultsFragment.getInstance(useCase))
                .commit()
        } else {
            binding.btnSave.hideView()
            childFragmentManager.beginTransaction()
                .add(
                    binding.searchResultsContainer.id,
                    ContactSearchResultsFragment.getInstance(useCase)
                )
                .commit()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        userContactFragmentBinding = null
    }

    private fun subscribeState() {}

    companion object {
        @JvmStatic
        val TRANSACTION_TYPE_CREDIT = 1

        @JvmStatic
        val TRANSACTION_TYPE_DEBIT = -1

        @JvmStatic
        val TYPE_CREDIT_FROM_TRANSACTION_FLOW = 2

        @JvmStatic
        val TYPE_DEBIT_FROM_TRANSACTION_FLOW = -2

        @JvmStatic
        val TRANSACTION_TYPE_FAVOURITE = 3

        @JvmStatic
        val TRANSACTION_TYPE_BOOKKEEPING = 4

        private const val TRANSACTION_TYPE = "transaction_type"
        private const val SHOW_FAVORITE_CONTACT = "show_favorite_contact"
        private const val QUERY = ""
        private const val TITLE = "title"
        private const val USE_CASE = "use_case"
        private const val ORDER_ID = "order_id"

        @JvmStatic
        fun getInstance(
            transactionType: Int,
            query: String = "",
            showFavoriteContacts: Boolean = false
        ): UserContactFragment {
            val fragment = UserContactFragment()
            val bundle = Bundle()
            bundle.putInt(TRANSACTION_TYPE, transactionType)
            bundle.putString(QUERY, query)
            bundle.putBoolean(SHOW_FAVORITE_CONTACT, showFavoriteContacts)
            fragment.arguments = bundle
            return fragment
        }

        @JvmStatic
        fun getInstance(
            title: String = "", transactionType: Int,
            customerSearchUseCase: CustomerSearchUseCase = CustomerSearchUseCase.ACCOUNTING,
            orderId: String = ""
        ): UserContactFragment {
            val fragment = UserContactFragment()
            val bundle = Bundle()
            bundle.putString(TITLE, title)
            bundle.putInt(TRANSACTION_TYPE, transactionType)
            bundle.putString(USE_CASE, customerSearchUseCase.name)
            bundle.putString(ORDER_ID, orderId)
            fragment.arguments = bundle
            return fragment
        }
    }

    fun showSameNameError(message: String) {
        binding.btnSave.isEnabled = false
        Utils.showKeyboard(requireContext())
        contactSearchFragment.showSameNameError(message)
    }

    interface OnSaveButtonCallback {
        fun onSave(name: String)
    }

    override fun onTextChanged(text: String) {
        binding.btnSave.isEnabled = text.isNotBlank()
    }
}
