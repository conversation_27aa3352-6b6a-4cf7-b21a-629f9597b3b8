package com.bukuwarung.edc.payments.ui.core

import Resource
import android.content.Context
import android.content.SharedPreferences
import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.usecase.EdcDeviceUseCase
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel.State
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentConst.CATEGORY_ID
import com.bukuwarung.edc.payments.data.model.*
import com.bukuwarung.edc.payments.data.model.PaymentHistory.Companion.STATUS_PAID
import com.bukuwarung.edc.payments.data.model.request.BankValidationRequest
import com.bukuwarung.edc.payments.data.model.request.DisbursementOverviewRequest
import com.bukuwarung.edc.payments.data.model.response.DisbursementOverviewResponse
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.payments.usecase.PaymentUseCase
import com.bukuwarung.edc.util.*
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.network.utils.ResourceState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject


@HiltViewModel
class PaymentViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val finproUseCase: FinproUseCase,
    private val deviceUseCase: EdcDeviceUseCase
) : ViewModel() {

    private val bookId = Utils.getPaymentAccountId()
    private val TAG = "PaymentViewModel"

    companion object {
        const val HEALTH_OK = 0
        const val HEALTH_WARNING = 1
        const val HEALTH_ERROR = 2
    }

    private val sharedPreferences: SharedPreferences by lazy {
        EdcApplication.instance.getSharedPreferences("preference_key", Context.MODE_PRIVATE)
    }

    private var timer: CountDownTimer? = null

    private val _categories = MutableLiveData<Resource<List<PaymentCategoryItem>>>()
    val categories: LiveData<Resource<List<PaymentCategoryItem>>>
        get() = _categories
    var selectedPaymentCategory: PaymentCategoryItem? = null

    var limits: PaymentTransactionLimits? = null

    private val _saldo = MutableLiveData<ResourceState<SaldoResponse>>()
    val saldo: LiveData<ResourceState<SaldoResponse>>
        get() = _saldo

    private val _disbursementRequest = MutableLiveData<Resource<Disbursement>>()
    val disbursementRequest: LiveData<Resource<Disbursement>>
        get() = _disbursementRequest

    private val _paymentStatus = MutableLiveData<Resource<PaymentOutStatusPollingResponse>>()
    val paymentStatus: LiveData<Resource<PaymentOutStatusPollingResponse>>
        get() = _paymentStatus

    private val _orderDetail = MutableLiveData<Resource<OrderResponse>>()
    val orderDetail: LiveData<Resource<OrderResponse>>
        get() = _orderDetail

    data class RedirectToDetailScreen(val orderId: String, val type: String)

    private val _redirectToDetail = MutableLiveData<RedirectToDetailScreen>()
    val redirectToDetail: LiveData<RedirectToDetailScreen>
        get() = _redirectToDetail

    sealed class Event {
        data class SetSelectedCategory(val category: PaymentCategoryItem) : Event()
    }

    private val _event = MutableLiveData<Event>()
    val event: LiveData<Event> = _event

    private val _disbursementOverview = MutableLiveData<Resource<DisbursementOverviewResponse>>()
    val disbursementOverview: LiveData<Resource<DisbursementOverviewResponse>>
        get() = _disbursementOverview

    private val _addCustomerBank = MutableLiveData<Resource<AddBankAccountDetail>>()
    val addCustomerBank: LiveData<Resource<AddBankAccountDetail>>
        get() = _addCustomerBank

    private val productDetail = MutableLiveData<DetailEvent>()
    val observeDetail: LiveData<DetailEvent> = productDetail

    private val _state = MutableLiveData<State>()
    val printState: LiveData<State> = _state

    sealed class DetailEvent {
        data class UpdateServiceFee(val toastText: String) : DetailEvent()
        object ShowPendingTrxHelpOptionInRed: DetailEvent()
        object RefreshScreen: DetailEvent()

    }

    data class DisbursementData(
        var amount: Long = 0,
        var bankAccount: BankAccount? = null,
        var paymentCategoryId: String = "",
        var notes: String = ""
    )

    override fun onCleared() {
        super.onCleared()
        timer?.cancel()
    }

    private val disbursementData = DisbursementData()
    private var healthState = 0
    private var healthStateMessage = ""
    private var bankAccountDetail: BankAccountDetail? = null
    private var disbursementId = ""
    var paymentOutStatus: PaymentOutStatusPollingResponse? = null

    private val _healthStatus = MutableLiveData<HealthStatus?>()
    val healthStatus: LiveData<HealthStatus?> = _healthStatus

    fun setPaymentData(
        bankAccount: BankAccount?,
        amount: Long,
        paymentCategoryId: String
    ) {
        disbursementData.apply {
            this.amount = amount
            this.bankAccount = bankAccount
            this.paymentCategoryId = paymentCategoryId
        }
    }

    fun getCategories() = viewModelScope.launch(Dispatchers.IO) {
        val disbursableType = PaymentConst.DisbursementRequest
        _categories.postValue(Resource.loading(null))
        try {
            paymentUseCase.getPaymentCategoryList(disbursableType).let {
                if (it.isSuccessful) {
                    val categoryListWithUniqueInfo = mutableListOf<PaymentCategoryItem>()
                    it.body()?.forEach { category ->
                        category.categoryList.forEach { item ->
                            if (categoryListWithUniqueInfo.firstOrNull { it.name == item.name } == null) {
                                categoryListWithUniqueInfo += item
                            }
                        }
                    }
                    categoryListWithUniqueInfo.sortBy { it.priority }
                    _categories.postValue(Resource.success(categoryListWithUniqueInfo))
                    autoAssignCategory(categoryListWithUniqueInfo)
                } else {
                    _categories.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _categories.postValue(Resource.error(e.message.toString(), null))
        }
    }

    private fun autoAssignCategory(categories: List<PaymentCategoryItem>) {
        val prevCategoryId = sharedPreferences.get(CATEGORY_ID, "")
        val prevCategory = categories.firstOrNull { it.paymentCategoryId == prevCategoryId }
        val selectCategory = when {
            prevCategory != null -> prevCategory
            else -> categories[0]
        }
        assignPaymentCategory(selectCategory)
        _event.postValue(Event.SetSelectedCategory(selectCategory))
    }

    fun assignPaymentCategory(paymentCategory: PaymentCategoryItem) {
        this.selectedPaymentCategory = paymentCategory
        sharedPreferences.put(CATEGORY_ID, paymentCategory.paymentCategoryId)
    }

    fun getLimits() = viewModelScope.launch(Dispatchers.IO) {
        try {
            paymentUseCase.getPaymentOutLimits(bookId, "customerId").let {
                if (it.isSuccessful) {
                    limits = it.body()
                }
            }
        } catch (e: Exception) {

        }
    }

    fun getSaldo() = viewModelScope.launch(Dispatchers.IO) {
        _saldo.postValue(ResourceState.Loading())
        _saldo.postValue(finproUseCase.getSaldo())
    }

    fun isBelowThreshold(amount: Long) =
        amount.toDouble() < PaymentRemoteConfig.getMinimumPaymentOutAmount()

    fun isAboveThreshold(amount: Long): Boolean {
        if (limits?.remainingDailyTrxLimit == null || limits?.perTrxLimit == null) return false
        // Whitelist limits should be null for other users(SUPREME/non-whitelisted ADVANCED users)
        if (limits?.whitelistLimits?.remainingTrxAmountLimit != null) {
            return (amount > limits?.whitelistLimits?.remainingTrxAmountLimit.orNil)
        }
        return (amount > limits?.remainingDailyTrxLimit.orNil) || (amount > limits?.perTrxLimit.orNil)
    }

    fun isMoreThanSaldoBalance(amount: Long): Boolean {
        val balance = if (_saldo.value is ResourceState.Success) {
            val data = (_saldo.value as ResourceState.Success).data
            (data.subBalance?.saldo ?: data.amount) ?: 0.0
        } else {
            0.0
        }
        return if (balance > 0.0) (amount > balance) else false
    }

    fun checkDisableButton(amount: Long): Boolean {
        return isAboveThreshold(amount) || isBelowThreshold(amount) || isMoreThanSaldoBalance(amount)
    }

    fun onNoteChanged(notes: String) {
        disbursementData.notes = notes
    }

    /**
     * Since BE requires customer bank account id, we need to create a new bank account everytime,
     * a new payment is created.
     */
    fun validateAndAddCustomerBankAccount(bankAccount: BankAccount) =
        viewModelScope.launch(Dispatchers.IO) {
            _addCustomerBank.postValue(Resource.loading(null))
            try {
                paymentUseCase.validateAndAddCustomerBankAccount(
                    bookId,
                    bookId,
                    BankValidationRequest(
                        bankCode = bankAccount.bankCode.orEmpty(),
                        accountNumber = bankAccount.accountNumber.orEmpty()
                    )
                ).let {
                    if (it.isSuccessful) {
                        bankAccountDetail = it.body()?.account
                        _addCustomerBank.postValue(Resource.success(it.body()))
                        doHealthCheck()
                    } else {
                        _addCustomerBank.postValue(Resource.error(it.errorMessage(), null))
                    }
                }
            } catch (e: Exception) {
                _addCustomerBank.postValue(Resource.error(e.message.toString(), null))
            }
        }

    private fun getPaymentOutOverview() =
        viewModelScope.launch(Dispatchers.IO) {
            val request = DisbursementOverviewRequest(
                amount = disbursementData.amount.toBigDecimal(),
                description = disbursementData.notes,
                customerId = bookId,
                customerBankAccountId = bankAccountDetail?.accountDetail?.id,
                customerName = bankAccountDetail?.accountDetail?.holderName.orEmpty(),
                accountId = bookId,
                vaBankCode = "SALDO",
                paymentCategoryId = disbursementData.paymentCategoryId
            )
            _disbursementOverview.postValue(Resource.loading(null))
            try {
                paymentUseCase.getDisbursementOverview(
                    request.accountId,
                    request.customerId,
                    request
                ).let {
                    if (it.isSuccessful) {
                        _disbursementOverview.postValue(Resource.success(it.body()))
                    } else {
                        _disbursementOverview.postValue(Resource.error(it.errorMessage(), null))
                    }
                }
            } catch (e: Exception) {
                _disbursementOverview.postValue(Resource.error(e.message.toString(), null))
            }
        }

    private fun doHealthCheck() =
        viewModelScope.launch(Dispatchers.IO) {
            try {
                paymentUseCase.doHealthCheck(
                    PaymentHealthCheckRequest(listOf(bankAccountDetail?.bankDetail?.code.orEmpty()))
                ).let {
                    if (it.isSuccessful) {
                        healthState = HEALTH_OK
                        iterateMoneyOutHealthResponse(it.body()?.moneyOut)
                        if (healthState != HEALTH_ERROR) {
                            val calendar = Calendar.getInstance()
                            val hourNow = calendar.get(Calendar.HOUR_OF_DAY)
                            if (hourNow < 7 || hourNow >= 23)
                                healthState = HEALTH_WARNING
                        }
                        _healthStatus.postValue(
                            HealthStatus(status = healthState, message = healthStateMessage)
                        )
                        if (healthState == HEALTH_OK) {
                            getPaymentOutOverview()
                        }
                    } else {
                        _healthStatus.postValue(
                            HealthStatus(status = HEALTH_ERROR, message = it.errorMessage())
                        )
                    }
                }
            } catch (e: Exception) {
            }
        }

    private fun iterateMoneyOutHealthResponse(list: List<HealthStatus>?) {
        if (list != null) {
            for (status in list) {
                if (false == status.enabled) {
                    healthState = HEALTH_ERROR
                    healthStateMessage = status.message.orEmpty()
                }
            }
        }
    }

    fun createPaymentOut() {
        if (_disbursementRequest.value?.status == Status.LOADING) {
            return
        }
        _disbursementRequest.value = Resource.loading(null)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = DisbursementRequest(
                    amount = disbursementData.amount.toBigDecimal(),
                    description = disbursementData.notes,
                    customerBankAccountId = bankAccountDetail?.accountDetail?.id,
                    vaBankCode = "SALDO",
                    accountId = bookId,
                    customerId = bookId,
                    customerName = "EDC_CUSTOMER",
                    extras = PaymentExtras(
                        accounting = PaymentAccountingExtras(categoryId = disbursementData.paymentCategoryId),
                        recordIn = PaymentConst.CASH
                    ),
                    paymentCategoryId = disbursementData.paymentCategoryId,
                    totalTransfer = _disbursementOverview.value?.data?.totalTransfer
                )
                val uniqueIdempotencyKey =
                    "payment_out_${bookId}_${disbursementData.amount}"
                paymentUseCase.createDisbursement(
                    IdempotencyManager.getOrCreateKey(
                        uniqueIdempotencyKey
                    ), bookId, bookId, request
                ).let {
                    if (it.isSuccessful) {
                        IdempotencyManager.clearKey(uniqueIdempotencyKey)
                        sharedPreferences.put(CATEGORY_ID, disbursementData.paymentCategoryId)
                        _disbursementRequest.postValue(Resource.success(it.body()))
                    } else {
                        IdempotencyManager.clearKey(uniqueIdempotencyKey)
                        _disbursementRequest.postValue(Resource.error(it.errorMessage(), null))
                    }
                }
            } catch (e: Exception) {
                // Network error - keep the key so retry uses same key
                _disbursementRequest.postValue(Resource.error(e.message.toString(), null))
            }
        }
    }

    fun setDisbursementId(disbursementId: String) {
        this.disbursementId = disbursementId
    }

    fun getPaymentStatus() = viewModelScope.launch(Dispatchers.IO) {
        _paymentStatus.postValue(Resource.loading(null))
        try {
            finproUseCase.getPaymentOutStatus(bookId, disbursementId).let {
                if (it.isSuccessful) {
                    paymentOutStatus = it.body()
                    _paymentStatus.postValue(Resource.success(it.body()))
                    if (it.body()?.status == PaymentConst.STATUS_COMPLETED) {
                        getOrderDetail(disbursementId)
                    }
                } else {
                    _paymentStatus.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _paymentStatus.postValue(Resource.error(e.message.toString(), null))
        }
    }

    fun getOrderDetail(orderId: String, ledgerAccountId: String? = null, isLinkedOrder: Boolean = false) =
        viewModelScope.launch(Dispatchers.IO) {
            timer?.cancel()
            _orderDetail.postValue(Resource.loading(null))
            try {
                finproUseCase.getOrderDetail(bookId, orderId, ledgerAccountId).let {
                    if (it.isSuccessful) {
                        if (isLinkedOrder){
                            _redirectToDetail.postValue(RedirectToDetailScreen(it.body()?.orderId.orEmpty(),it.body()?.items?.firstOrNull()?.beneficiary?.category.orEmpty()))
                        } else {
                            _orderDetail.postValue(Resource.success(it.body()))
                            if (it.body()
                                    ?.isCompleted().isFalseOrNull && it.body()?.channelPendingEta.isNotNullOrBlank() && it.body()?.status == STATUS_PAID
                            ) {
                                initRetryTimer(
                                    it.body()?.progress?.firstOrNull { it.state == STATUS_PAID }?.timestamp.orEmpty()
                                )
                            }
                        }
                    } else {
                        _orderDetail.postValue(Resource.error(it.errorMessage(), null))
                    }
                }
            } catch (e: Exception) {
                _orderDetail.postValue(Resource.error(e.message.toString(), null))
            }
        }

    fun updateAgentFee(agentFee: Double, description: String, customerId: String, orderId: String) =
        viewModelScope.launch {
            Utils.safeLet(
                Utils.getPaymentAccountId(), customerId, orderId
            ) { accountId, customerId, orderId ->
                paymentUseCase.updatePaymentOutAgentFee(
                    accountId, customerId, orderId, UpdateFeeRequest(agentFee, description)
                ).let {
                    if (it.isSuccessful) {
                            productDetail.value = DetailEvent.UpdateServiceFee("Bukti pembayaran berhasil diubah")
                        }
                    else {
                        // Show toast for admin fee not updated
                    }

                }
            }
        }

    fun printPaymentReceipt(receipt: String) = viewModelScope.launch {
        deviceUseCase.printPaymentReceipt(receipt).collect { response ->
            when (response) {
                is EdcResponse.Success -> {
                    // do nothing
                }

                is EdcResponse.Failure -> {
                    //do nothing
                }
            }
        }
    }

    fun printPpobReceipt(orderResponse: OrderResponse?) = viewModelScope.launch {
        val printCommand = ReceiptPrintCommand(orderResponse = orderResponse)
        bwLog(TAG, "printPpobReceipt: $printCommand")
        deviceUseCase.printReceipt(printCommand, null, null,null,null).collect { response ->
            when (response) {
                is EdcResponse.Success -> {
                    bwLog(TAG, "EVENT_PRINT_RECEIPT_COMPLETED: $response")
                    val printerResult = response.data
                    if (printerResult.success) {
                        _state.postValue(State.SetPrintFinish(true))
                    } else {
                        val printingErrorMessage = printerResult.status.msg.orDefault("print_error")
                        bwLog(TAG, "finish printPpobReceipt SetPrintError: $printingErrorMessage")
                        _state.postValue(State.SetPrintError(printerResult.status))
                    }
                }

                is EdcResponse.Failure -> {
                    //do nothing
                }
            }
        }
    }

    private fun initRetryTimer(startDate: String) = viewModelScope.launch {
        //adding pending trx time from remote config to trx created time and comparing it to current time to show corresponding ui
        val startTimeInMillis = DateTimeUtils.getTimestampFromUtcDate(startDate)
        val pendingTrxTimeInMillis = DateTimeUtils.convertMinsToMillis(
            RemoteConfigUtils.getPaymentPendingTimeInMinutes().toLong()
        )
        val currentTimeInMillis = DateTimeUtils.getCurrentUTCTime()
        val diffWithCurrentTime = startTimeInMillis + pendingTrxTimeInMillis - currentTimeInMillis
        if (diffWithCurrentTime > 0) {
            timer = object : CountDownTimer(diffWithCurrentTime, Constant.ONE_SECOND) {
                override fun onTick(millisUntilFinished: Long) {
                }

                override fun onFinish() {
                    productDetail.value = DetailEvent.RefreshScreen
                    timer?.cancel()
                }
            }
            timer!!.start()
        } else {
            productDetail.value = DetailEvent.ShowPendingTrxHelpOptionInRed
        }
    }

    fun printPaymentOutReceipt(orderResponse: OrderResponse?) = viewModelScope.launch {
        val printCommand = ReceiptPrintCommand(orderResponse = orderResponse)
        bwLog(TAG, "printPaymentOutReceipt: $printCommand")
        deviceUseCase.printReceipt(printCommand, null, null, null, null).collect { response ->
            when (response) {
                is EdcResponse.Success -> {
                    bwLog(TAG, "EVENT_PRINT_RECEIPT_COMPLETED: $response")
                    val printerResult = response.data
                    if (printerResult.success) {
                        _state.postValue(State.SetPrintFinish(true))
                    } else {
                        val printingErrorMessage = printerResult.status.msg.orDefault("print_error")
                        bwLog(TAG, "finish printPaymentOutReceipt SetPrintError: $printingErrorMessage")
                        _state.postValue(State.SetPrintError(printerResult.status))
                    }
                }

                is EdcResponse.Failure -> {
                    //do nothing
                }
            }
        }
    }

}