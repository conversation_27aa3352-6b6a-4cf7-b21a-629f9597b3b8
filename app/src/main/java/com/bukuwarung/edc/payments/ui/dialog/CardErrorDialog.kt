package com.bukuwarung.edc.payments.ui.dialog

import android.content.Context
import android.content.res.ColorStateList
import android.os.Build
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_TRANSACTION_TIMEOUT
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.util.*
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.Utils.extractLinkFromHtml
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.invisibleView
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.util.textHTML
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.ui_component.databinding.ExpandedBulletItemViewBinding
import com.google.android.material.button.MaterialButton
import com.google.firebase.crashlytics.FirebaseCrashlytics

class CardErrorDialog(
    context: Context,
    private val errorType: CardErrorType,
    private val transactionType: Int? = null,
    private val errorCode: String? = null,
    private val stan: String? = "",
    private val dialogType: BaseDialogType = BaseDialogType.POPUP_ROUND_CORNERED,
    private val source: String? = null,
    private val exception: Exception? = null,
    private val dismissListener: ((dialog: CardErrorDialog) -> Unit)? = null,
) : BaseDialog(context, dialogType) {
    override val resId: Int
        get() = R.layout.layout_card_error

    private var isInternalError = false
    private lateinit var tvCardTitle: TextView
    private lateinit var ivLdErrorImage: ImageView
    private lateinit var btnReturn: MaterialButton
    private lateinit var longDialogLayout: ConstraintLayout
    private lateinit var popUpDialogGroup: Group
    private lateinit var pendingInfo: ConstraintLayout
    private lateinit var pendingInfoTitle: TextView
    private lateinit var pendingInfoDesc: TextView
    private lateinit var tvTitle: TextView
    private lateinit var llBulletPoint: LinearLayout
    private lateinit var pendingCondCheckbox: CheckBox
    private lateinit var btnLongDialog: MaterialButton
    private lateinit var pendingTnc: TextView
    private lateinit var errorImage: ImageView
    private lateinit var titleError: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val fullWidth = if(dialogType == BaseDialogType.FULL_SCREEN) true else false
        setUseFullWidth(fullWidth)
        setCancellable(false)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        if (Utils.sharedPreferences.getBoolean(Utils.ERROR_SOUND, false)) {
            EdcApplication.instance.edcService.doBeep(100)
        }
        //full screen
        tvCardTitle = findViewById(R.id.tv_card_title)
        ivLdErrorImage = findViewById(R.id.iv_ld_error_image)

        titleError = findViewById(R.id.tv_error_title)
        val errorBody = findViewById<TextView>(R.id.tv_error_description)
        errorImage = findViewById(R.id.iv_error_image)
        longDialogLayout = findViewById(R.id.long_dialog)
        popUpDialogGroup = findViewById(R.id.group_pop_up_dialog)
        pendingCondCheckbox = findViewById(R.id.cb_pending_tnc)
        btnLongDialog = findViewById(R.id.btn_ld_return)
        pendingTnc = findViewById(R.id.tv_tnc)
        btnReturn = findViewById(R.id.btn_return)
        pendingInfo = findViewById(R.id.pending_balance_info)
        pendingInfoTitle = findViewById(R.id.tv_info_title)
        pendingInfoDesc = findViewById(R.id.tv_info_text)
        tvTitle = findViewById(R.id.tv_title)
        llBulletPoint = findViewById(R.id.llBulletPoint)

        longDialogLayout.hideView()
        popUpDialogGroup.showView()
        btnReturn.showView()

        val modifiedErrorCode = when {
            isInternalError -> "[$errorCode]"
            errorCode != null && errorCode.contains("BW") -> "[$errorCode]"
            errorCode != null && errorCode.contains("NO503") -> ""
            errorCode != null && errorCode.contains("E0") -> "[$errorCode]"
            errorCode != null && errorCode.contains("E1") -> "[$errorCode]"
            errorCode != null && errorCode.contains("E2") -> "[$errorCode]"
            errorCode != null && errorCode.contains(CARD_TRANSACTION_TIMEOUT) -> null
            errorCode != null && ErrorMapping.httpErrorCode.contains(errorCode) -> "[BW $errorCode]"
            errorCode != null && ErrorMapping.inputOnlineResultFailed.contains(errorCode) -> "[$errorCode]"
            errorCode != null -> "[RC $errorCode]"
            else -> null
        }
        val errorMessage: String = when (errorType) {
            CardErrorType.UNABLE_TO_READ -> {
                titleError.setText(R.string.unable_to_read_card)
                btnReturn.setText(R.string.retry)
                context.getString(R.string.unable_to_read_card_msg)
            }

            CardErrorType.SYSTEM_ERROR -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_system_error
                    )
                )
                if (errorCode == "RC 91") {
                    titleError.setText(R.string.issuer_destination_or_switch_inoperative)
                } else {
                    titleError.setText(R.string.system_error)
                }
                context.getString(R.string.system_error_msg)
            }

            CardErrorType.BCA_NOT_SUPPORTED -> {
                applyYellowButtonStyle()
                btnReturn.setText(R.string.contact_customer_care)
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_system_error
                    )
                )
                titleError.setText(R.string.system_error)
                context.getString(R.string.bca_not_supported)
            }

            CardErrorType.UN_IDENTIFIED_ERROR -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_error
                    )
                )
                btnReturn.setText(R.string.back)
                titleError.setText(R.string.transaction_not_permitted_cardholder)
                context.getString(R.string.transaction_not_permitted_cardholder_message)
            }

            CardErrorType.INVALID_MERCHANT -> {
                applyYellowButtonStyle()
                btnReturn.setText(R.string.contact_customer_care)
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_system_error
                    )
                )
                titleError.setText(R.string.invalid_merchant_title)
                context.getString(R.string.invalid_merchant_message)
            }

            CardErrorType.SYSTEM_BUSY -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_system_error
                    )
                )
                titleError.setText(R.string.system_busy)
                context.getString(R.string.system_busy_msg)
            }

            CardErrorType.REQUEST_TIMEOUT -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_request_timeout
                    )
                )
                titleError.setText(R.string.request_timeout)
                context.getString(R.string.request_timeout_msg)
            }

            CardErrorType.TRANSACTION_TIMEOUT -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                context.getString(
                    if (transactionType == PrintConst.TRANSACTION_TYPE_CHECK_BALANCE) R.string.request_timeout_balance_check_msg
                    else R.string.request_timeout_transfer_inquiry_msg
                )
            }

            CardErrorType.NO_DESTINATION_ACCOUNT_NUMBER -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_error
                    )
                )
                titleError.setText(R.string.destination_account_number_not_found)
                context.getString(R.string.destination_account_number_not_found_message)
            }

            CardErrorType.INSUFFICIENT_BALANCE -> {
                var title = context.getString(R.string.balance_not_enough)
                var message = context.getString(R.string.less_balance_msg_check_balance)
                if (transactionType == PrintConst.TRANSACTION_TYPE_TRANSFER ||
                    transactionType == PrintConst.TRANSACTION_TYPE_CASH_WITHDRAWAL
                ) {
                    title = context.getString(R.string.less_balance)
                    message = context.getString(R.string.less_balance_msg_transfer)
                }
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(context, R.drawable.ic_insufficient_balance)
                )
                titleError.text = title
                message
            }

            CardErrorType.OVER_LIMIT_AMOUNT -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_insufficient_balance
                    )
                )
                titleError.setText(R.string.exceeding_trx_limit)
                context.getString(R.string.exceeding_trx_limit_message)
            }

            CardErrorType.UNSUPPORTED_MAGNETIC_CARD -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_error
                    )
                )
                titleError.setText(R.string.failed_to_swipe_card)
                context.getString(R.string.unsupported_magnetic_card_message)
            }

            CardErrorType.UNEXPECTED_ERROR_MAGNETIC_CARD -> {
                titleError.setText("Gagal Membaca Kartu ATM")
                btnReturn.setText("Kembali")
                "Silakan coba lagi."
            }

            CardErrorType.TERMINAL_NOT_ACTIVATED -> {
                applyYellowButtonStyle()
                btnReturn.setText(R.string.contact_customer_care)
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_system_error
                    )
                )
                titleError.setText(R.string.terminal_inactive_title)
                context.getString(R.string.terminal_inactive_message)
            }

            CardErrorType.TERMINAL_NOT_ACTIVATED_LOADING_FAILED -> {
                applyYellowButtonStyle()
                btnReturn.setText(R.string.contact_customer_care)
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_system_error
                    )
                )
                titleError.setText(R.string.terminal_inactive_title)
                context.getString(R.string.terminal_inactive_loading_failed_message)
            }

            CardErrorType.REMOVE_CARD -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        if(Utils.isCardReader()) R.drawable.ic_remove_card_from_cardreader else R.drawable.ic_remove_card
                    )
                )
                titleError.setText(R.string.remove_card)
                btnReturn.hideView()
                context.getString(R.string.remove_card_msg)
            }

            CardErrorType.CARD_NOT_SUPPORTED -> {
                titleError.setText(R.string.card_not_supported)
                context.getString(R.string.card_not_supported_msg)
            }

            CardErrorType.REQUEST_NOT_SUPPORTED -> {
                titleError.setText(R.string.request_unsupported)
                context.getString(R.string.request_unsupported_msg)
            }

            CardErrorType.NO_CHEQUING_AMOUNT -> {
                titleError.setText(R.string.no_chequing)
                context.getString(R.string.no_chequing_msg)
            }

            CardErrorType.EXPIRED_CARD -> {
                titleError.setText(R.string.card_expired)
                context.getString(R.string.card_expired_msg)
            }

            CardErrorType.INCORRECT_PIN -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.trx_failed_incorrect_pin)
                btnReturn.setText(R.string.understand)
                context.getString(R.string.trx_failed_incorrect_pin_message)
            }

            CardErrorType.INCORRECT_PIN_FIRST_ATTEMPT -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.wrong_pin_entered)
                btnReturn.setText(R.string.understand)
                context.getString(R.string.pls_re_enter_your_pin)
            }

            CardErrorType.INVALID_PIN_LENGTH -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText("PIN Salah")
                btnReturn.setText(R.string.retry)
                context.getString(R.string.pin_entered_is_less_than_six_digit)
            }


            CardErrorType.CARD_BLOCKED -> {
                val (title, message) = when (errorCode) {
                    "RC 01" -> {
                        R.string.card_blocked to context.getString(R.string.card_blocked_msg)
                    }

                    "RC 75", "RC 38" -> {
                        errorImage.setImageDrawable(
                            AppCompatResources.getDrawable(
                                context,
                                R.drawable.ic_transaction_failed
                            )
                        )
                        R.string.card_blocked to context.getString(
                            R.string.atm_card_block_contact_bank,
                            ""
                        )
                    }

                    else -> R.string.transaksi_ditolak_oleh_bank to context.getString(R.string.card_blocked_msg)
                }
                titleError.setText(title)
                message
            }

            CardErrorType.TRANSACTION_NOT_PERMITTED -> {
                titleError.setText(R.string.transaksi_ditolak_oleh_bank)
                context.getString(R.string.card_blocked_msg)
            }

            CardErrorType.CLOSED_ACCOUNT -> {
                titleError.setText(R.string.account_closed)
                context.getString(R.string.account_closed_msg)
            }

            CardErrorType.TRANSACTION_FAILED -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                btnReturn.setText(R.string.understand)

                context.getString(R.string.transaction_canceled_msg)
            }

            CardErrorType.INVALID_TRANSACTION -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                btnReturn.setText(R.string.go_back)
                context.getString(R.string.change_card_or_check_destination)
            }

            CardErrorType.FORMAT_ERROR -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_error
                    )
                )
                titleError.setText(R.string.card_format_error_title)
                btnReturn.setText(R.string.go_back)
                context.getString(R.string.card_format_error_title_message)
            }

            CardErrorType.INVALID_CARD_DAMAGED_UNSUPPORTED -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_error
                    )
                )
                titleError.setText(R.string.card_not_supported)
                btnReturn.setText(R.string.go_back)
                context.getString(R.string.invalid_card_damaged_unsupported_message)
            }

            CardErrorType.CARD_BE_PICKED_UP_AT_ATM -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.can_not_do_transaction)
                context.getString(R.string.can_not_do_transaction_message)
            }

            CardErrorType.TRANSACTION_CANCELED -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                context.getString(R.string.transaction_failed_msg)
            }

            CardErrorType.INPUT_ONLINE_RESPONSE_FAILED -> {
                isInternalError = true
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                context.getString(R.string.input_online_failed)
            }

            CardErrorType.TRANSACTION_INCOMPLETE -> {
                isInternalError = true
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                context.getString(R.string.transaction_incompleted_message)
            }

            CardErrorType.TRANSACTION_DECLINED -> {
                isInternalError = true
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                btnReturn.setText(R.string.go_back)
                context.getString(R.string.transaction_declined_message)
            }

            CardErrorType.INTERNAL_ERROR_TIME_OUT -> {
                isInternalError = true
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_transaction_failed
                    )
                )
                titleError.setText(R.string.transaction_failed)
                context.getString(R.string.transaction_failed_msg)
            }

            CardErrorType.CARD_PREFIX_BLOCKED -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_error
                    )
                )
                titleError.setText(R.string.card_prefix_blocked)
                context.getString(R.string.card_prefix_blocked_message)
            }

            CardErrorType.NO_INTERNET -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_no_internet_connection
                    )
                )
                btnReturn.setText(R.string.retry)
                titleError.setText(R.string.no_internet_connection)
                context.getString(R.string.make_sure_internet_data_is_available)
            }

            CardErrorType.NO_BLUETOOTH -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_phone_bt_not_connected
                    )
                )
                btnReturn.setText("Aktifkan Bluetooth")
                btnReturn.backgroundTintList =
                    ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
                btnReturn.strokeColor =
                    ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
                btnReturn.backgroundTintList =
                    ContextCompat.getColorStateList(context, R.color.yellow_60)
                titleError.setText("Bluetooth Belum Aktif")
                "Silakan aktifkan koneksi Bluetooth HP Anda untuk menghubungkan EDC Saku."
            }

            CardErrorType.BLUETOOTH_DEVICE_SWITCHED_OFF -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_reader_off
                    )
                )
                btnReturn.setText(R.string.retry)
                btnReturn.backgroundTintList =
                    ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
                btnReturn.strokeColor =
                    ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
                btnReturn.backgroundTintList =
                    ContextCompat.getColorStateList(context, R.color.yellow_60)
                titleError.setText("EDC Saku Belum Aktif")
                "Silakan aktifkan mesin EDC Saku dengan menekan tombol power dan coba lagi."
            }

            CardErrorType.BLUETOOTH_DEVICE_IN_SLEEPMODE -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_reader_sleep
                    )
                )
                btnReturn.setText(R.string.retry)
                titleError.setText("Tekan Tombol Power EDC Saku")
                "Silakan tekan tombol power pada EDC Saku untuk melanjutkan transaksi dan coba lagi."
            }

            CardErrorType.LOCATION_DENIED -> {
                errorImage.setImageDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.ic_card_reader_location_not_found
                    )
                )
                btnReturn.setText(context.getString(R.string.aktifkan_lokasi))
                btnReturn.backgroundTintList =
                    ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
                btnReturn.strokeColor =
                    ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
                titleError.setText(context.getString(R.string.lokasi_tidak_terdeteksi))
                context.getString(R.string.silakan_aktifkan_akses)
            }

            CardErrorType.PENDING_SETTLEMENT -> {
                if (transactionType == PrintConst.TRANSACTION_TYPE_TRANSFER) {
                    renderPendingSettlementTf()
                    ""
                } else if ((transactionType == PrintConst.TRANSACTION_TYPE_CASH_WITHDRAWAL)) {
                    renderPendingSettlementCw()
                    ""
                } else {
                    ""
                }
            }

            CardErrorType.PENDING_REFRESH-> {
                if (transactionType == PrintConst.TRANSACTION_TYPE_TRANSFER) {
                    renderPendingRefreshTf()
                    ""
                } else if ((transactionType == PrintConst.TRANSACTION_TYPE_CASH_WITHDRAWAL)) {
                    renderPendingRefreshCw()
                    ""
                } else {
                    ""
                }
            }

            CardErrorType.PENDING-> {
                if (transactionType == PrintConst.TRANSACTION_TYPE_TRANSFER) {
                    renderPendingTf()
                    ""
                } else if ((transactionType == PrintConst.TRANSACTION_TYPE_CASH_WITHDRAWAL)) {
                    renderPendingCw()
                    ""
                } else {
                    ""
                }
            }

    CardErrorType.NETWORK_OR_SERVER_ISSUE -> {
        titleError.setText(R.string.network_server_issue_title)
        errorImage.setImageDrawable(
            AppCompatResources.getDrawable(
                context,
                R.drawable.ic_no_internet_connection
            )
        )
        btnReturn.setText(R.string.try_again)
        context.getString(R.string.network_server_issue_description)
    }

    CardErrorType.DEVICE_CONNECTION_FAILED -> {
        titleError.setText(R.string.device_connection_failed_title)
        errorImage.setImageDrawable(
            AppCompatResources.getDrawable(
                context,
                R.drawable.ic_card_reader_off
            )
        )
        btnReturn.setText(R.string.try_again)
        context.getString(R.string.device_connection_failed_description)
    }

    CardErrorType.TERMINAL_NOT_CONFIGURED -> {
        titleError.setText(R.string.activation_process_failed_title)
        errorImage.setImageDrawable(
            AppCompatResources.getDrawable(
                context,
                R.drawable.ic_system_error
            )
        )
        btnReturn.setText(R.string.contact_cs)
        btnReturn.backgroundTintList =
            ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
        btnReturn.strokeColor =
            ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
        btnReturn.backgroundTintList =
            ContextCompat.getColorStateList(context, R.color.yellow_60)
        context.getString(R.string.activation_process_failed_description)
    }

    CardErrorType.GENERAL_ERROR -> {
        titleError.setText(R.string.general_error_title)
        errorImage.setImageDrawable(
            AppCompatResources.getDrawable(
                context,
                R.drawable.ic_system_error
            )
        )
        btnReturn.setText(R.string.try_again)
        context.getString(R.string.general_error_description)
    }

    CardErrorType.TERMINAL_NOT_EXIST_IN_TMS -> {
        titleError.setText(R.string.device_activation_issue_title)
        errorImage.setImageDrawable(
            AppCompatResources.getDrawable(
                context,
                R.drawable.ic_system_error
            )
        )
        btnReturn.setText(R.string.contact_customer_care)
        btnReturn.backgroundTintList =
            ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
        btnReturn.strokeColor =
            ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
        btnReturn.backgroundTintList =
            ContextCompat.getColorStateList(context, R.color.yellow_60)
        context.getString(R.string.device_activation_issue_description)
    }



    CardErrorType.FAILED_TO_LOAD_DATA -> {
        titleError.setText(R.string.failed_load_data_title)
        errorImage.setImageDrawable(
            AppCompatResources.getDrawable(
                context,
                R.drawable.ic_system_error
            )
        )
        btnReturn.setText(R.string.understand)
        context.getString(R.string.failed_load_data_description)
    }
        }

        errorBody.text = if (stan.isNullOrEmpty()) {
            if (modifiedErrorCode != null) "$errorMessage\n$modifiedErrorCode" else errorMessage
        } else {
            if (modifiedErrorCode != null) "$errorMessage\n$modifiedErrorCode [$stan]" else "$errorMessage [$stan]"
        }

        btnLongDialog.singleClick {
            dismiss()
            dismissListener?.invoke(this)
        }

        btnReturn.singleClick {
            dismiss()
            dismissListener?.invoke(this)
        }
    }

    fun setCancellable() {
        btnReturn.hideView()
    }

    private fun inflateBullet(items: List<Int>, linearLayout:LinearLayout) {
        linearLayout.removeAllViews()
        items.forEachIndexed { index, info ->
            val item = ExpandedBulletItemViewBinding.inflate(LayoutInflater.from(context))
            item.tvBullet.text = " • "
            item.tvContent.textHTML(context.getString(info))
            linearLayout.addView(item.root)
        }
    }

    override fun show() {
        super.show()
        try{
            trackCardDialog()
            bwLog(e = exception)
        } catch (e: Exception) {
            bwLog(e = e)
        }
    }

    private fun trackCardDialog() {
        try{
            // Do not track the dialog if it is for card removal, mixpanle event limit
            if("CARD_REMOVE_INTERCEPTOR" == source)
                return
            val props = HashMap<String, String>()
            props["error_code"] = errorCode.orDash
            props["stan"] = stan.orDash
            props["dialog_type"] = errorType?.name.orDash
            props["source"] = source.orDash
            props["exception"] = exception?.message.orDash
            Analytics.trackEvent("show_edc_dialog",props)
            FirebaseCrashlytics.getInstance().recordException(Throwable("ErrorCode: $errorCode, source:$source", exception))
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    private fun renderPendingSettlementTf(){
        longDialogLayout.showView()
        popUpDialogGroup.hideView()
        tvCardTitle.text = "Informasi Penting"
        ivLdErrorImage.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.ic_pending_alert))
        tvCardTitle.showView()
        ivLdErrorImage.showView()
        tvTitle.text = "Pencairan Uang Diproses"
        inflateBullet(listOf(R.string.pending_trnx_point_1, R.string.pending_trnx_point_2_68), llBulletPoint)
        pendingInfo.invisibleView()
        pendingInfoTitle.invisibleView()
        btnReturn.showView()
        val text = CheckBalancePinConfig.getEdcPendingTransactionTncUrl()
        setPendingTnc(text)
        btnReturn.text = context.getString(R.string.understand)
        applyYellowButtonStyle()
    }

    private fun renderPendingSettlementCw(){
        longDialogLayout.showView()
        popUpDialogGroup.hideView()
        tvCardTitle.text = "Informasi Penting"
        ivLdErrorImage.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.ic_pending_alert))
        tvCardTitle.showView()
        ivLdErrorImage.showView()
        tvTitle.text = "Pencairan Uang Diproses"
        inflateBullet(listOf(R.string.pending_trnx_point_1 ,R.string.pending_trnx_point_2, R.string.pending_trnx_point_3_cw), llBulletPoint)
        pendingInfo.invisibleView()
        pendingInfoTitle.invisibleView()
        btnReturn.showView()
        val text = CheckBalancePinConfig.getEdcPendingTransactionTncUrl()
        setPendingTnc(text)
        btnReturn.text = context.getString(R.string.understand)
        applyYellowButtonStyle()
    }

    private fun renderPendingRefreshTf(){
        longDialogLayout.showView()
        popUpDialogGroup.hideView()
        tvCardTitle.text = "Informasi Penting"
        ivLdErrorImage.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.ic_pending_alert))
        tvCardTitle.showView()
        ivLdErrorImage.showView()
        tvTitle.text = "Transaksi Pending"
        inflateBullet(listOf(R.string.pending_trnx_point_1, R.string.pending_trnx_point_2), llBulletPoint)
        pendingInfo.invisibleView()
        pendingInfoTitle.invisibleView()
        btnReturn.showView()
        val text = CheckBalancePinConfig.getEdcPendingTransactionTncUrl()
        setPendingTnc(text)
        btnReturn.text = context.getString(R.string.understand)
        applyYellowButtonStyle()
    }

    private fun renderPendingRefreshCw(){
        longDialogLayout.showView()
        popUpDialogGroup.hideView()
        tvCardTitle.textHTML(context.getString(R.string.transaction_warning))
        ivLdErrorImage.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.ic_pending_alert_reddot))
        tvCardTitle.showView()
        ivLdErrorImage.showView()
        tvTitle.text = "Transaksi Pending"
        inflateBullet(listOf(R.string.pending_trnx_point_2_5minute, R.string.pending_trnx_point_1), llBulletPoint)
        pendingInfo.invisibleView()
        pendingInfoTitle.invisibleView()
        btnReturn.showView()
        val text = context.getString(R.string.tnc_pending_refresh)
        setPendingTnc(text)
        btnReturn.text = context.getString(R.string.understand)
        applyYellowButtonStyle()
    }

    private fun renderPendingTf(){
        longDialogLayout.showView()
        popUpDialogGroup.hideView()
        tvCardTitle.text = "Informasi Penting"
        ivLdErrorImage.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.ic_pending_alert))
        tvCardTitle.showView()
        ivLdErrorImage.showView()
        tvTitle.text = "Transaksi Pending"
        inflateBullet(listOf(R.string.pending_trnx_point_1, R.string.pending_trnx_point_2), llBulletPoint)
        pendingInfo.invisibleView()
        pendingInfoTitle.invisibleView()
        btnReturn.showView()
        val text = CheckBalancePinConfig.getEdcPendingTransactionTncUrl()
        setPendingTnc(text)
        btnReturn.text = context.getString(R.string.understand)
        applyYellowButtonStyle()
    }

    private fun renderPendingCw(){
        longDialogLayout.showView()
        popUpDialogGroup.hideView()
        tvCardTitle.textHTML(context.getString(R.string.transaction_warning))
        ivLdErrorImage.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.ic_pending_alert_reddot))
        tvCardTitle.showView()
        ivLdErrorImage.showView()
        tvTitle.text = "Transaksi Pending"
        inflateBullet(listOf(R.string.pending_trnx_point_1_to, R.string.pending_trnx_point_2_to, R.string.pending_trnx_point_1), llBulletPoint)
        pendingInfo.invisibleView()
        pendingInfoTitle.invisibleView()
        btnReturn.showView()
        val text = context.getString(R.string.tnc_pending_refresh)
        setPendingTnc(text)
        btnReturn.text = context.getString(R.string.understand)
        applyYellowButtonStyle()
    }


    private fun setPendingTnc(text: String) {
        pendingCondCheckbox.setOnCheckedChangeListener { _, isChecked ->
            btnLongDialog.isEnabled = isChecked
        }
        pendingTnc.textHTML(text)
        pendingTnc.singleClick {
            val link: String? = extractLinkFromHtml(text)
            if (link.isNotNullOrEmpty() && (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX || Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE)) {
                context.openActivity(WebviewActivity::class.java) {
                    putString(
                        ClassConstants.WEBVIEW_URL,
                        link
                    )
                }
            } else {
                pendingTnc.movementMethod = LinkMovementMethod.getInstance()
            }
        }
    }

    private fun applyYellowButtonStyle() {
        try {
            btnReturn.backgroundTintList = ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
            btnReturn.strokeColor = ColorStateList.valueOf(context.getColorCompat(R.color.yellow_60))
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }
}