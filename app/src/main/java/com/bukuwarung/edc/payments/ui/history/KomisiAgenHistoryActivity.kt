package com.bukuwarung.edc.payments.ui.history

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.ui.transactiondetail.ui.TransactionDetailsActivity
import com.bukuwarung.edc.databinding.ActivityKomisiAgenHistoryBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.homepage.ui.home.HomePageViewModel
import com.bukuwarung.edc.payments.constant.PaymentAnalyticsConst
import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.payments.data.model.DeviceDetails
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.ui.core.OrderDetailActivity
import com.bukuwarung.edc.payments.ui.history.adapter.OrderHistoryAdapter
import com.bukuwarung.edc.payments.ui.history.adapter.OrderHistoryPagingDataAdapter
import com.bukuwarung.edc.payments.ui.history.bottomsheet.DateFilterBottomSheet
import com.bukuwarung.edc.payments.ui.history.bottomsheet.DeviceFilterBottomSheet
import com.bukuwarung.edc.payments.ui.history.bottomsheet.ErrorBottomSheet
import com.bukuwarung.edc.payments.util.HeaderItemDecoration
import com.bukuwarung.edc.payments.util.OrderListCallback
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlin.getValue

@AndroidEntryPoint
class KomisiAgenHistoryActivity: AppCompatActivity(), OrderListCallback {

    private val viewModel: KomisiAgenHistoryViewModel by viewModels()
    private val homeViewModel: HomePageViewModel by viewModels()
    private lateinit var binding: ActivityKomisiAgenHistoryBinding
    private var deviceList = mutableListOf<DeviceDetails>(DeviceDetails("Semua Perangkat", null))
    private var ordersAdapter: OrderHistoryAdapter? = null
    private var paginationAdapter: OrderHistoryPagingDataAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityKomisiAgenHistoryBinding.inflate(layoutInflater)

        ordersAdapter = OrderHistoryAdapter(this, hashMapOf())
        paginationAdapter = OrderHistoryPagingDataAdapter(this, hashMapOf())

        setContentView(binding.root)

        viewModel.selectedDevice = deviceList.first()

        initialiseDate()
        setupToolbar()
        setFilterClickListeners()
        homeViewModel.apply {
            viewModelScope.launch {
                fetchUserDevices()
            }
        }
        observeDeviceList()
        viewModel.fetchKomisiAgenHistory()
        trackEvent()
        subscribeState()

        binding.rvHistory.apply {
            adapter = paginationAdapter
            layoutManager = LinearLayoutManager(this@KomisiAgenHistoryActivity)
            addItemDecoration(
                HeaderItemDecoration(binding.rvHistory, false) { pos: Int ->
                    if (pos == -1) false
                    else paginationAdapter?.isHeaderItem(pos).isTrue
                }
            )
        }
    }

    private fun subscribeState(){

        viewModel.ordersPagedData.observe(this) {
            lifecycleScope.launch{
                if(it != null){ paginationAdapter?.submitData(it) }
            }
        }

        viewModel.pagingStatus.observe(this) { status ->
            when (status) {
                PagingStatus.Loading -> {
                    with(binding) {
                        layoutEmptyView.root.hideView()
                        rvHistory.hideView()
                        pbLoading.showView()
                    }
                }
                is PagingStatus.Loaded -> {
                    with(binding) {
                        layoutEmptyView.root.hideView()
                        rvHistory.showView()
                        binding.pbLoading.hideView()
                        filterResultCount(0)
                    }
                }
                PagingStatus.Empty -> {
                    binding.pbLoading.hideView()
                    setEmptyView()
                }
                PagingStatus.EmptyNextPage -> {
                    // Hide any loading for the next page
                    binding.pbLoading.hideView()
                    setEmptyView()
                }
                PagingStatus.LoadingNextPage -> {
                    // Show any loading for the next page
                    binding.pbLoading.showView()
                }
                is PagingStatus.Error -> {
                    handleError(status.errorMessage)
                }
                PagingStatus.NoInternet -> {
                    handleError(Constant.NO_INTERNET_ERROR_MESSAGE)
                }
                else -> {}
            }
        }

        ordersAdapter?.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                super.onChanged()
                if (viewModel.viewState.value?.loading.isTrue) return
                if (ordersAdapter?.itemCount == 0) {
                    setEmptyView()
                } else {
                    binding.layoutEmptyView.root.hideView()
                }
            }
        })

        viewModel.orderHistoryLive.observe(this) {
            updateAdapter()
        }

        viewModel.viewState.observe(this) {
            with(binding) {
                if (it.loading) {
                    rvHistory.hideView()
                    pbLoading.showView()
                } else {
                    rvHistory.showView()
                    pbLoading.hideView()
                }
                if (it.errorMessage != null) {
                    handleError(it.errorMessage)
                }
            }
        }
    }

    private fun handleError(errorMessage: String?) {
        if (errorMessage.isNotNullOrBlank()) {
            if (errorMessage != Constant.NO_INTERNET_ERROR_MESSAGE) {
                ErrorBottomSheet.createInstance(
                    ErrorBottomSheet.Companion.ApiErrorType.API_ERROR,
                    errorMessage?.ifEmpty { getString(R.string.try_again_or_wait) }
                ).show(supportFragmentManager, ErrorBottomSheet.TAG)
            } else {
                ErrorBottomSheet.createInstance(
                    ErrorBottomSheet.Companion.ApiErrorType.CONNECTION_ERROR,
                    errorMessage
                ).show(supportFragmentManager, ErrorBottomSheet.TAG)
            }
        }
    }

    private fun setEmptyView(){
        with(binding.layoutEmptyView) {
            root.showView()
            ivEmptyIcon.setImageResource(R.drawable.vector_no_search_result)
            tvEmptyTitle.text = getString(R.string.no_filter_transaction)
            tvEmptyMessage.text = getString(R.string.no_filter_transaction_description)
            btnEmptyCta.hideView()
        }
    }

    private fun updateAdapter(){
        viewModel.ordersHistory.let {
            ordersAdapter?.setData(it)
        }
    }

    private fun observeDeviceList(){
        homeViewModel.deviceList.observe(this){
            it.forEach {
                if (it.serialNumber.isNotNullOrBlank())
                    deviceList.add(DeviceDetails("Device Name - " + it.serialNumber.orEmpty(), it.serialNumber.orEmpty()))
            }
        }
    }

    private fun setFilterClickListeners(){
        binding.apply {
            tvFilterDevice.singleClick{
                DeviceFilterBottomSheet.createInstance(
                    supportFragmentManager,
                    deviceList,
                    viewModel.selectedDevice,
                    {
                        tvFilterDevice.text = it?.deviceName
                        viewModel.selectedDevice = it
                        tvFilterDevice.isSelected = viewModel.selectedDevice?.serialNumber.isNotNullOrBlank()
                        viewModel.fetchKomisiAgenHistory()
                        trackEvent()
                    }
                )
            }
            tvFilterDate.isSelected = true
            tvFilterDate.singleClick{
                DateFilterBottomSheet.createInstance(
                    supportFragmentManager,
                    true,
                    viewModel.defaultOrSelectedDateFilter
                ) { dateFilter ->
                    dateFilter?.let {
                        viewModel.defaultOrSelectedDateFilter = it
                        tvFilterDate.text = calculateLabel(it)
                        val startDate = Calendar.getInstance()
                        val (startTimeInMillis, endDateInMillis) = PaymentAuxilliary.getDates(it)
                        val endDateInYYYYMMDDFormat = DateTimeUtils.getFormattedDateTime(
                            endDateInMillis ?: startDate.timeInMillis,
                            DateTimeUtils.YYYY_MM_DD
                        )
                        val startDateInYYYYMMDDFormat = DateTimeUtils.getFormattedDateTime(
                            startTimeInMillis ?: startDate.timeInMillis,
                            DateTimeUtils.YYYY_MM_DD
                        )
                        viewModel.startDate = startDateInYYYYMMDDFormat
                        viewModel.endDate = endDateInYYYYMMDDFormat

                        viewModel.fetchKomisiAgenHistory()
                        trackEvent()
                    }
                }
            }
        }
    }

    private fun calculateLabel(dateFilter: DateFilter): String {
        return if (dateFilter.presetValue?.name?.uppercase() == "CUSTOM_RANGE") {
            val startDate =
                DateTimeUtils.getFormattedDateTime(
                    dateFilter.startDate ?: 0,
                    DateTimeUtils.DD_MMM_YY
                )
            val endDate =
                DateTimeUtils.getFormattedDateTime(dateFilter.endDate ?: 0, DateTimeUtils.DD_MMM_YY)
            return startDate.plus("-").plus(endDate)
        } else dateFilter.label
    }

    private fun setupToolbar() {
        with(binding.toolbar) {
            navigationIcon =
                <EMAIL>(R.drawable.ic_back)
            setNavigationOnClickListener {
                Utils.hideKeyboard(this@KomisiAgenHistoryActivity)
                finish()
            }
        }
    }

    override fun openOrderDetail(order: PaymentHistory) {
        if (order.detailService?.equals("EDC_ADAPTER", true).isTrue){
            openActivity(TransactionDetailsActivity::class.java) {
                putString(TransactionDetailsActivity.INTENT_KEY_TRANSACTION_ID, order.orderId)
                putString(TransactionDetailsActivity.INTENT_KEY_TRANSACTION_TYPE, order.originalTransactionType)
                putString(TransactionDetailsActivity.INTENT_KEY_ENTRY_POINT, TransactionDetailsActivity.ENTRY_POINT_TRANSACTION_HISTORY)
            }
        } else {
            val orderType = order.type?.let { if (it.equals(PaymentHistory.TYPE_CASHBACK_SETTLEMENT, true)) PaymentHistory.TYPE_PAYMENT_OUT else it }
            openActivity(OrderDetailActivity::class.java) {
                putString(OrderDetailActivity.ORDER_ID, order.orderId)
                putString(OrderDetailActivity.PAYMENT_TYPE, orderType)
                putString(OrderDetailActivity.LEDGER_ACCOUNT_ID, order.ledgerAccountId)
            }
        }
    }

    override fun fetchLinkedOrders(orderId: String, adapterPos: Int) {
        //this is not needed for this activity
    }

    private fun initialiseDate(){
        val calendar = Calendar.getInstance()
        // Define the date format
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        // Format the current date
        val initialDate = sdf.format(calendar.time)
        viewModel.startDate = initialDate
        viewModel.endDate = initialDate
    }

    private fun trackEvent(){
        var map = HashMap<String, String>()
        map.put(PaymentAnalyticsConst.START_DATE, viewModel.startDate.orEmpty())
        map.put(PaymentAnalyticsConst.END_DATE, viewModel.endDate.orEmpty())
        map.put(PaymentAnalyticsConst.SERIAL_NUMBER_SELECTED, viewModel.selectedDevice?.serialNumber.orDefault("all"))
        Analytics.trackEvent(PaymentAnalyticsConst.EVENT_KOMISI_AGEN_HISTORY_VIEW, map)
    }
}