package com.bukuwarung.edc.payments.ui.history

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.DataSource
import androidx.paging.LivePagedListBuilder
import androidx.paging.PagedList
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.ui.history.adapter.OrderHistoryAdapter
import com.bukuwarung.edc.payments.data.datasource.OrdersPagedDataSource
import com.bukuwarung.edc.payments.data.model.*
import com.bukuwarung.edc.payments.usecase.OrderUseCase
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class OrderHistoryViewModel @Inject constructor(
    private val ordersUseCase: OrderUseCase
) : ViewModel() {

    companion object {
        fun sortByDescending(orders: List<PaymentHistory>) = orders.sortedByDescending {
            val dateString = DateTimeUtils.getStringFromUtc(
                it.timestamp, DateTimeUtils.DD_MMM_YYYY
            )
            val dateLong = DateTimeUtils.getTimestampFromDateString(
                dateString, DateTimeUtils.DD_MMM_YYYY
            )
            dateLong
        }

        fun addDateHeaders(
            orders: List<PaymentHistory>,
            orderDates: ArrayList<String>,
            shouldAddDateHeaders: Boolean = true
        ): ArrayList<OrderHistoryData> {
            val ordersData = arrayListOf<OrderHistoryData>()
            orders.map {
                val dateString = DateTimeUtils.getStringFromUtc(
                    it.timestamp, DateTimeUtils.DD_MMM_YYYY
                )
                if (!orderDates.contains(dateString) && shouldAddDateHeaders) {
                    orderDates.add(dateString)
                    ordersData.add(
                        OrderHistoryData(
                            orderData = null,
                            formattedDate = dateString,
                            timestamp = it.timestamp,
                            viewType = OrderHistoryAdapter.Companion.ViewType.DATE_HEADER
                        )
                    )
                }
                ordersData.add(
                    OrderHistoryData(
                        orderData = it,
                        formattedDate = null,
                        timestamp = null,
                        viewType = OrderHistoryAdapter.Companion.ViewType.ORDER
                    )
                )
            }
            return ordersData
        }

        fun getExistingOrderDates(
            activeTab: PaymentConst.HISTORY_TABS,
            orderDatesMap: HashMap<PaymentConst.HISTORY_TABS, ArrayList<String>>
        ): ArrayList<String> {
            val existingDates = orderDatesMap[activeTab]
            return if (existingDates == null) {
                val datesArray = arrayListOf<String>()
                orderDatesMap[activeTab] = datesArray
                datesArray
            } else {
                existingDates
            }
        }
    }

    data class ViewState(
        val loading: Boolean = true,
        val errorMessage: String? = null,
        val cashbackLoading: Boolean = true,
        val cashbackError: String? = null,
    )

    sealed class Event {
        object UpdateAdapter : Event()
        object ClearSearch : Event()
        data class LinkedItemsFetched(var adapterPos: Int) : Event()
        data class ApiError(var errorMessage: String?, var adapterPos: Int) : Event()
    }

    val filtersState = hashMapOf(
        PaymentConst.HISTORY_TABS.ALL to getDefaultFilters(PaymentConst.HISTORY_TABS.ALL),
        PaymentConst.HISTORY_TABS.PPOB to getDefaultFilters(PaymentConst.HISTORY_TABS.PPOB),
        PaymentConst.HISTORY_TABS.PEMBAYARAN to getDefaultFilters(PaymentConst.HISTORY_TABS.PEMBAYARAN),
        PaymentConst.HISTORY_TABS.SALDO to getDefaultFilters(PaymentConst.HISTORY_TABS.SALDO),
        PaymentConst.HISTORY_TABS.SALDOBONUS to getDefaultFilters(PaymentConst.HISTORY_TABS.SALDOBONUS)
    )
    private val _filtersStateLive = MutableLiveData(filtersState)
    val filtersStateLive: LiveData<HashMap<PaymentConst.HISTORY_TABS, PaymentFilterDtoX>> =
        _filtersStateLive

    private val _event = MutableLiveData<Event>()
    val event: LiveData<Event> = _event

    var activeTab: PaymentConst.HISTORY_TABS = PaymentConst.HISTORY_TABS.ALL
    val ordersHistoryMap = HashMap<PaymentConst.HISTORY_TABS, ArrayList<OrderHistoryData>>()
    val orderHistoryLive: MutableLiveData<HashMap<PaymentConst.HISTORY_TABS, ArrayList<OrderHistoryData>>> =
        MutableLiveData(ordersHistoryMap)

    private val orderDatesMap = hashMapOf<PaymentConst.HISTORY_TABS, ArrayList<String>>()

    val linkedOrdersMap = hashMapOf<String, LinkedOrdersData>()

    private val _expiringCashbacks = MutableLiveData<ExpiringCashBackInfo>()
    val expiringCashbacks: LiveData<ExpiringCashBackInfo> = _expiringCashbacks

    private val _pagingStatus = MutableLiveData<PagingStatus>()
    val pagingStatus: LiveData<PagingStatus> = _pagingStatus

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    val config = PaymentRemoteConfig.getPaymentConfigs()
    private var customerId: String? = null
    private var bookId: String = Utils.getPaymentAccountId()
    private var billerCode: String? = null
    private var bukuOrigin: String? = Constant.BUKUWARUNG_EDC
    private val typeDigitalProductChildren = arrayListOf<String>().apply {
        addAll(
            config.historyFiltersNew?.ppob?.products?.flatMap { it.filters }?.map { it.key }
                .orEmpty()
        )
    }
    private var ordersDataSource: OrdersPagedDataSource? = null
    var ordersPagedData: LiveData<PagedList<OrderHistoryData>>? = null
    private var queryParams = OrdersPagedDataSource.QueryParams(
        accountId = Utils.getPaymentAccountId(),
        bukuOrigin = this.bukuOrigin
    )

    fun getApplicableFilters(): FilterValues? {
        return when (activeTab) {
            PaymentConst.HISTORY_TABS.ALL -> config.historyFiltersNew?.all
            PaymentConst.HISTORY_TABS.PPOB -> config.historyFiltersNew?.ppob
            PaymentConst.HISTORY_TABS.PEMBAYARAN -> config.historyFiltersNew?.pembayaran
            PaymentConst.HISTORY_TABS.SALDO -> config.historyFiltersNew?.saldo
            PaymentConst.HISTORY_TABS.SALDOBONUS -> config.historyFiltersNew?.cashback
        }
    }

    /**
     * This sets the incoming filters to the activity
     * Here we check if incoming array has "parent" types i.e. DIGITAL_PRODUK or PEMBAYARAN
     * Then we add all the children to the applied filters list.
     */
    fun init(
        bookId: String, preselectedTab: PaymentConst.HISTORY_TABS?,
        types: ArrayList<String>?, status: ArrayList<String>?,
        startDate: Long?, endDate: Long?,
        customerId: String? = null, billerCode: String? = null,
        datePreset: PaymentConst.DATE_PRESET?
    ) {
        preselectedTab?.let { activeTab = it }
        this.bookId = bookId
        this.customerId = customerId
        this.billerCode = billerCode
        types?.let {
            getApplicableFilters()?.products?.flatMap { it.filters }?.map {
                // Here we are changing checked status of children, if 'types' contains parent type
                if (types.contains(PaymentConst.TYPE_PEMBAYARAN)) {
                    if (!it.isChecked) {
                        it.isChecked = PaymentConst.TYPE_PEMBAYARAN_CHILDREN.contains(it.key)
                    }
                }
                types.map { type ->
                    if (!it.isChecked) {
                        it.isChecked = it.key == type
                    }
                }
            }
        }
        status?.let {
            getApplicableFilters()?.status?.flatMap { it.filters }?.map {
                status.map { type ->
                    if (!it.isChecked) {
                        it.isChecked = it.key == type
                    }
                }
            }
        }
        datePreset?.let { preset ->
            getApplicableFilters()?.date?.find { it.presetValue == preset }
                ?.let { it.isChecked = true }
        }
        Utils.safeLet(startDate, endDate) { start, end ->
            if (start == 0L || end == 0L) return@safeLet
            getApplicableFilters()?.date?.find { it.presetValue == PaymentConst.DATE_PRESET.CUSTOM_RANGE }
                ?.let {
                    it.isChecked = true
                    it.startDate = start
                    it.endDate = end
                }
        }
        applyTypeFilters(false)
        applyStatusFilters(false)
        applyDateFilters(false)
        fetchOrders(activeTab)
    }

    fun onTabSelected(tab: PaymentConst.HISTORY_TABS) {
        activeTab = tab
        queryParams.searchQuery = filtersState[activeTab]?.searchQuery
        applyDateFilters(false)
        invalidateDataSource()
    }

    fun applyTypeFilters(fetchResults: Boolean = true) {
        // Update appliedTypeFilters from filterValues
        filtersState[activeTab]?.typeFilters?.apply {
            clear()
            getApplicableFilters()?.products?.flatMap { it.filters }?.map {
                addIf(it.key, it.isChecked)
            }
            // If typeFilters are empty and we are on any specific tab, we need to add parent
            // filter types otherwise it'll not filter based on the active tab
            if (isEmpty()) {
                when (activeTab) {
                    PaymentConst.HISTORY_TABS.ALL -> {}
                    PaymentConst.HISTORY_TABS.PPOB -> add(PaymentConst.TYPE_DIGITAL_PRODUCT)
                    PaymentConst.HISTORY_TABS.PEMBAYARAN -> add(PaymentConst.TYPE_PEMBAYARAN)
                    PaymentConst.HISTORY_TABS.SALDO -> add(PaymentConst.TYPE_SALDO_ALL)
                    PaymentConst.HISTORY_TABS.SALDOBONUS -> add(PaymentConst.TYPE_CASHBACK_ALL)
                }
            }
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    fun applyStatusFilters(fetchResults: Boolean = true) {
        // Update appliedTypeFilters from filterValues
        filtersState[activeTab]?.statusFilters?.apply {
            clear()
            getApplicableFilters()?.status?.flatMap { it.filters }?.map {
                addIf(it.key, it.isChecked)
            }
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    fun applyDateFilters(fetchResults: Boolean = true) {
        setDefaultDateFilter()
        // Update appliedTypeFilters from filterValues
        filtersState[activeTab]?.dateFilters?.apply {
            getApplicableFilters()?.date?.find { it.isChecked }?.let {
                val dates = PaymentAuxilliary.getDates(it)
                presetValue = it.presetValue
                startDate = dates.first
                endDate = dates.second
            }
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    fun updateSearchQuery(searchQuery: String, fetchResults: Boolean = true) {
        filtersState[activeTab]?.searchQuery = searchQuery
        queryParams.searchQuery = searchQuery
        if (fetchResults) invalidateDataSource()
    }

    fun updateEdcFilterSelected(isEdcFilterSelected: Boolean = true){
        bukuOrigin = if (isEdcFilterSelected) Constant.BUKUWARUNG_EDC else null
        filtersState[activeTab]?.bukuOrigin = bukuOrigin
        queryParams.bukuOrigin = bukuOrigin
        invalidateDataSource()
    }

    fun applySorting(fetchResults: Boolean = true) {
        // Update appliedTypeFilters from filterValues
        getApplicableFilters()?.sort?.find { it.isChecked }?.let {
            filtersState[activeTab]?.sorting = it.key
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    private fun setDefaultDateFilter() {
        // We will select today's date filter if nothing is applied
        if (getApplicableFilters()?.date?.find { it.isChecked } == null) {
            getApplicableFilters()?.date?.find { it.presetValue == PaymentConst.DATE_PRESET.TODAY }
                ?.isChecked = true
        }
    }

    fun clearFilters() {
        getApplicableFilters()?.products?.flatMap { it.filters }?.map {
            it.isChecked = false
        }
        getApplicableFilters()?.status?.flatMap { it.filters }?.map {
            it.isChecked = false
        }
        getApplicableFilters()?.date?.map { it.isChecked = false }
        // Applying default
        getApplicableFilters()?.date?.find { it.presetValue == PaymentConst.DATE_PRESET.TODAY }
            ?.let { it.isChecked = true }
        applyTypeFilters(false)
        applyStatusFilters(false)
        applyDateFilters(false)
        _event.value = Event.ClearSearch
        fetchOrders(activeTab)
    }

    private fun getTypeFilters(): ArrayList<String> {
        // If typeFilters contains directly unsupported PARENT types,
        // we add their children here.
        val filtersForApi = arrayListOf<String>()
        filtersState[activeTab]?.typeFilters?.let {
            filtersForApi.addAll(it)
            if (it.contains(PaymentConst.TYPE_SALDO_ALL)) {
                filtersForApi.addAll(PaymentConst.TYPE_SALDO_CHILDREN)
                filtersForApi.remove(PaymentConst.TYPE_SALDO_ALL)
            }
            if (it.contains(PaymentConst.TYPE_CASHBACK_ALL)) {
                filtersForApi.addAll(PaymentConst.TYPE_CASHBACK_CHILDREN)
                filtersForApi.remove(PaymentConst.TYPE_CASHBACK_ALL)
            }
        }
        /**
         * If type=DEFAULT is passed, BE will remove Saldo and Komisi Agen orders from the list
         * We need to show Saldo and Komisi Agen orders as nested orders, so just for "ALL" tab,
         * if we pass type=DEFAULT, Saldo and Komisi Agen orders will be hidden.
         *
         * On all the other tabs, i.e. DIGITAL PRODUK, PEMBAYRAN, etc. these orders are not visible
         * already due to filtering.
         */
        if (filtersForApi.isEmpty()) {
            filtersForApi.add(PaymentConst.TYPE_DEFAULT)
        } else {
            filtersForApi.remove(PaymentConst.TYPE_DEFAULT)
        }
        return filtersForApi
    }

    private fun getStatusFilters(): ArrayList<String> {
        val statusFilters = arrayListOf<String>()
        filtersState[activeTab]?.statusFilters?.let {
            statusFilters.addAll(it)
            if (it.contains(PaymentHistory.STATUS_PAID).isTrue) {
                statusFilters.add(PaymentHistory.STATUS_HOLD)
            }
        }
        return statusFilters
    }

    private fun fetchOrders(activeTab: PaymentConst.HISTORY_TABS) = viewModelScope.launch {
        if (ordersPagedData == null) {
            providePagedDataSource(activeTab)
        } else {
            invalidateDataSource()
        }
    }

    private fun invalidateDataSource() {
        val typeFilters = getTypeFilters()
        val statusFilters = getStatusFilters()
        val sorting = filtersState[activeTab]?.sorting
        val startDate = filtersState[activeTab]?.dateFilters?.startDate
        val endDate = filtersState[activeTab]?.dateFilters?.endDate

        /*
           NOTE: orderDatesMap has to be cleared as well when invalidating data source
           otherwise it won't add date headers to the data.
           Check the code in addDateHeaders function for more details.
         */
        orderDatesMap[activeTab]?.clear()

        queryParams.apply {
            this.startDate = startDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            }
            this.endDate = endDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            }
            type = typeFilters
            status = statusFilters
            this.sorting = sorting
            this.bukuOrigin = bukuOrigin
        }

        ordersDataSource?.invalidate()
    }

    private fun providePagedDataSource(activeTab: PaymentConst.HISTORY_TABS) {
        val pagingConfig = PagedList.Config.Builder()
            .setPrefetchDistance(
                PaymentRemoteConfig.getPaymentConfigs().paginationConfig?.limitPerPage!!.or(10)
            )
            .build()
        val typeFilters = getTypeFilters()
        val statusFilters = getStatusFilters()
        val sorting = filtersState[activeTab]?.sorting
        val searchQuery = filtersState[activeTab]?.searchQuery
        val startDate = filtersState[activeTab]?.dateFilters?.startDate
        val endDate = filtersState[activeTab]?.dateFilters?.endDate

        queryParams = OrdersPagedDataSource.QueryParams(
            accountId = Utils.getPaymentAccountId(),
            customerId = customerId,
            startDate = startDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            },
            endDate = endDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            },
            type = typeFilters,
            status = statusFilters,
            billerCode = billerCode,
            sorting = sorting,
            limit = PaymentRemoteConfig.getPaymentConfigs().paginationConfig?.limitPerPage!!.or(
                10
            ),
            searchQuery = searchQuery,
            bukuOrigin = this.bukuOrigin
        )

        val dataSource = object : DataSource.Factory<Int, OrderHistoryData>() {
            override fun create(): DataSource<Int, OrderHistoryData> {
                return OrdersPagedDataSource(
                    ordersUseCase = ordersUseCase,
                    coroutineScope = viewModelScope,
                    pagingStatusLiveData = _pagingStatus,
                    activeTab = activeTab,
                    orderDates = orderDatesMap,
                    queryParams = queryParams
                ).also {
                    ordersDataSource = it
                }
            }
        }

        ordersPagedData = LivePagedListBuilder(dataSource, pagingConfig).build()
    }

    fun fetchLinkedOrders(orderId: String, adapterPos: Int) =
        viewModelScope.launch(Dispatchers.IO) {
            try {
                ordersUseCase.getLinkedOrders(orderId).let {
                    if (it.isSuccessful) {
                        val linkedOrders = arrayListOf<OrderHistoryData>()
                        it.body()?.map { paymentHistory ->
                            linkedOrders.add(
                                OrderHistoryData(
                                    orderData = paymentHistory.copy(isLinkedOrder = true),
                                    formattedDate = null,
                                    timestamp = null,
                                    viewType = OrderHistoryAdapter.Companion.ViewType.ORDER
                                )
                            )
                        }
                        linkedOrdersMap[orderId] = LinkedOrdersData(
                            linkedOrders = linkedOrders,
                            linkedOrdersVisibility = true,
                            linkedOrdersLoading = false
                        )
                        withContext(Dispatchers.Main) {
                            _event.value = Event.LinkedItemsFetched(adapterPos)
                        }
                    } else {
                        withContext(Dispatchers.Main) {
                            _event.value = Event.ApiError(it.errorMessage(), adapterPos)
                        }
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    _event.value = Event.ApiError(e.message.toString(), adapterPos)
                }
            }
        }
}