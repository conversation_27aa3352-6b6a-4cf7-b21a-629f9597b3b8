package com.bukuwarung.edc.payments.ui.history.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ItemDateFilterBinding
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentConst.DEFAULT_FILTER_CALENDAR_MAX_RANGE
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.DateTimeUtils.EEE_DD_MMM_YYYY
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import javax.inject.Inject


class DateFilterAdapter @Inject constructor(
    private val isFromCardHistory: Boolean,
    private var selectedFilter: DateFilter?,
    private val dateFilters: ArrayList<DateFilter>,
    private val callback: Callback
) : RecyclerView.Adapter<DateFilterAdapter.DateViewHolder>() {

    interface Callback {
        fun onDateFilterSelected(dateFilter: DateFilter)
        fun showDatePicker(dateFilter: DateFilter, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DateViewHolder {
        val itemBinding =
            ItemDateFilterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DateViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: DateViewHolder, position: Int) {
        holder.bind(dateFilters[position])
    }

    override fun getItemCount() = dateFilters.size

    fun updateSelectedDates(position: Int) {
        notifyItemChanged(position)
    }

    private fun checkDateFilter(dateFilter: DateFilter) {
        selectedFilter = dateFilter
        dateFilters.map { it.isChecked = (it == dateFilter) }
        notifyDataSetChanged()
    }

    inner class DateViewHolder(val binding: ItemDateFilterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(dateFilter: DateFilter) {
            with(binding) {
                tvTitle.text = dateFilter.label
                tvDateRange.hideView()
                vwPadding.hideView()
                tvSubtitle.showView()
                val dates = PaymentAuxilliary.getDates(dateFilter)
                if (isFromCardHistory) {
                    val isPreviousPickedDate = dateFilter.presetValue == selectedFilter?.presetValue
                    rbDate.isChecked = isPreviousPickedDate
                    dateFilter.isChecked = isPreviousPickedDate
                } else {
                    rbDate.isChecked = dateFilter.isChecked
                }
                when (dateFilter.presetValue) {
                    PaymentConst.DATE_PRESET.TODAY -> tvSubtitle.hideView()
                    PaymentConst.DATE_PRESET.YESTERDAY,
                    PaymentConst.DATE_PRESET.THIS_WEEK,
                    PaymentConst.DATE_PRESET.LAST_WEEK,
                    PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS,
                    PaymentConst.DATE_PRESET.THIS_MONTH,
                    PaymentConst.DATE_PRESET.LAST_MONTH,
                    PaymentConst.DATE_PRESET.THIS_YEAR,
                    PaymentConst.DATE_PRESET.LAST_YEAR,
                    PaymentConst.DATE_PRESET.LAST_TEN_DAYS,
                    null -> {
                        showDatesAsSubtitle(binding, dates)
                    }

                    PaymentConst.DATE_PRESET.CUSTOM_RANGE -> {
                        setCustomRangeView(binding, dateFilter)
                    }

                    PaymentConst.DATE_PRESET.ALL -> {}
                }
                clRadioButton.setOnClickListener {
                    if (dateFilter.isChecked) return@setOnClickListener
                    checkDateFilter(dateFilter)
                    callback.onDateFilterSelected(dateFilter)
                }
            }
        }

        private fun showDatesAsSubtitle(binding: ItemDateFilterBinding, dates: Pair<Long?, Long?>) {
            Utils.safeLet(dates.first, dates.second) { start, end ->
                binding.tvSubtitle.text = binding.root.context.getString(
                    R.string.two_dashed_strings,
                    DateTimeUtils.getFormattedDateTime(start, EEE_DD_MMM_YYYY),
                    DateTimeUtils.getFormattedDateTime(end, EEE_DD_MMM_YYYY)
                )
            }
        }

        private fun setCustomRangeView(binding: ItemDateFilterBinding, dateFilter: DateFilter) {
            with(binding) {
                tvSubtitle.text = root.context.getString(
                    R.string.max_span_of_x_days,
                    PaymentRemoteConfig.getPaymentConfigs().customCalendarMaxRange
                        ?: DEFAULT_FILTER_CALENDAR_MAX_RANGE
                )
                tvDateRange.showView()
                vwPadding.showView()
                Utils.safeLet(dateFilter.startDate, dateFilter.endDate) { start, end ->
                    tvDateRange.text = root.context.getString(
                        R.string.two_dashed_strings,
                        DateTimeUtils.getFormattedDateTime(start, EEE_DD_MMM_YYYY),
                        DateTimeUtils.getFormattedDateTime(end, EEE_DD_MMM_YYYY)
                    )
                } ?: run {
                    tvDateRange.text = root.context.getString(R.string.start_dash_end)
                }
                tvDateRange.isEnabled = dateFilter.isChecked
                tvDateRange.setOnClickListener {
                    callback.showDatePicker(dateFilter, adapterPosition)
                }
            }
        }
    }
}
