package com.bukuwarung.edc.payments.ui.history.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ItemFilterSectionBinding
import com.bukuwarung.edc.payments.data.model.FilterSection
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import javax.inject.Inject


class FilterSectionAdapter @Inject constructor(
    val sections: ArrayList<FilterSection>, val callback: FilterAdapter.Callback
) : RecyclerView.Adapter<FilterSectionAdapter.SectionViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SectionViewHolder {
        val itemBinding =
            ItemFilterSectionBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SectionViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: SectionViewHolder, position: Int) {
        holder.bind(sections[position])
    }

    override fun getItemCount() = sections.size

    inner class SectionViewHolder(val binding: ItemFilterSectionBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(section: FilterSection) {
            with(binding) {
                if (sections.size == 1) {
                    vwDivider.hideView()
                    tvTitle.hideView()
                } else {
                    tvTitle.text = section.sectionLabel
                    tvTitle.showView()
                    vwDivider.showView()
                }
                rvFilters.apply {
                    layoutManager = LinearLayoutManager(binding.root.context)
                    adapter = FilterAdapter(section.filters, callback)
                }
            }
        }
    }
}
