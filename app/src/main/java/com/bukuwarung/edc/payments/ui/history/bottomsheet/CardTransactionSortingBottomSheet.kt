package com.bukuwarung.edc.payments.ui.history.bottomsheet

import android.content.res.Resources
import android.graphics.Color
import android.os.Bundle
import android.provider.CalendarContract
import android.view.ContextThemeWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.fragment.app.FragmentManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.BottomsheetCardTransactionSortingBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.util.getClassTag
import com.bukuwarung.edc.util.singleClick
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CardTransactionSortingBottomSheet(
    private val title: String,
    private val sortOptions: List<Pair<String, String>>,
    private val previousOrDefaultSelected: Pair<String, String>,
    private val sortSelectedCallback: (Pair<String, String>) -> Unit
) :
    BaseBottomSheetDialogFragment() {

    companion object {

        fun createInstance(
            fr: FragmentManager,
            title: String,
            sortOptions: List<Pair<String, String>>,
            previousOrDefaultSelected: Pair<String, String>,
            sortSelectedCallback: (Pair<String, String>) -> Unit
        ) =
            CardTransactionSortingBottomSheet(
                title,
                sortOptions,
                previousOrDefaultSelected,
                sortSelectedCallback
            ).show(
                fr,
                getClassTag()
            )
    }

    private lateinit var binding: BottomsheetCardTransactionSortingBinding
    private var sortType: Pair<String, String> = previousOrDefaultSelected

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        binding = BottomsheetCardTransactionSortingBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupBottomSheetBehavior()
        setupSortingOptions()

        binding.tvTitle.text = title
        binding.ivClose.setOnClickListener { dismiss() }
        binding.btnConfirm.singleClick {
            sortSelectedCallback.invoke(sortType)
            dismiss()
        }
    }

    private fun setupBottomSheetBehavior() {
        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
        }
    }

    private fun setupSortingOptions() {
        binding.rgSort.removeAllViews()

        sortOptions.forEachIndexed { index, (value, text) ->
            val contextWrapper =
                ContextThemeWrapper(requireContext(), R.style.CustomRadioButtonStyle)
            val radioButton = RadioButton(contextWrapper).apply {
                id = View.generateViewId()
                this.text = text
                this.layoutParams = RadioGroup.LayoutParams(
                    RadioGroup.LayoutParams.MATCH_PARENT,
                    RadioGroup.LayoutParams.WRAP_CONTENT
                )
                setTextColor(Color.parseColor("#000000"))
                this.isChecked = previousOrDefaultSelected.first == value
            }
            binding.rgSort.addView(radioButton)

            if (radioButton.isChecked) {
                sortType = Pair(value, text)
            }
        }

        binding.rgSort.setOnCheckedChangeListener { group, checkedId ->
            val selectedButton = group.findViewById<RadioButton>(checkedId)
            val position = group.indexOfChild(selectedButton)
            val selectedValue = sortOptions[position].first
            val selectedText = sortOptions[position].second
            sortType = Pair(selectedValue, selectedText)
        }
    }

}