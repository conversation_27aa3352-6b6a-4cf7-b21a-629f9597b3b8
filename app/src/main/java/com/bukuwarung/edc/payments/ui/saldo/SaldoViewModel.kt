package com.bukuwarung.edc.payments.ui.saldo

import Resource
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.payments.data.model.*
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class SaldoViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase
) : ViewModel() {

    private val _saldo = MutableLiveData<Resource<SaldoResponse>>()
    val saldo: LiveData<Resource<SaldoResponse>>
        get() = _saldo

    private val _existingTopup = MutableLiveData<Resource<List<ExistingTopupSaldoResponse>>>()
    val existingTopup: LiveData<Resource<List<ExistingTopupSaldoResponse>>>
        get() = _existingTopup

    private val _saldoAdminFee = MutableLiveData<Resource<SaldoAdminFeeResponse>>()
    val saldoAdminFee: LiveData<Resource<SaldoAdminFeeResponse>>
        get() = _saldoAdminFee

    private val _topupRequest = MutableLiveData<Resource<OrderResponse>>()
    val topupRequest: LiveData<Resource<OrderResponse>>
        get() = _topupRequest

    var topupAmount = 0.0

    fun init(fetchAdminFee: Boolean = true) {
        checkSaldoBalance()
        if (fetchAdminFee) getAdminFee()
        checkExisting()
    }

    fun onAmountChanged(amount: Double) {
        this.topupAmount = amount
    }

    fun isBelowMinRequired(): Boolean {
        return topupAmount < PaymentRemoteConfig.getMinimumTopupSaldoAmount()
    }

    fun isAboveMaxLimits(): Boolean {
        return Utils.safeLet(
            _saldo.value?.data?.limit,
            _saldo.value?.data?.amount
        ) { maxTopUp, currentBalance ->
            (topupAmount + currentBalance > maxTopUp)
        } ?: run { false }
    }

    private fun getAdminFee() = viewModelScope.launch(Dispatchers.IO) {
        _saldoAdminFee.postValue(Resource.loading(null))
        delay(1000)
        try {
            finproUseCase.getSaldoAdminFee().let {
                if (it.isSuccessful) {
                    _saldoAdminFee.postValue(Resource.success(it.body()))
                } else {
                    _saldoAdminFee.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _saldoAdminFee.postValue(Resource.error(e.message.toString(), null))
        }
    }

    fun checkSaldoBalance() = viewModelScope.launch(Dispatchers.IO) {
        _saldo.postValue(Resource.loading(null))
        try {
            finproUseCase.getSaldo().let {
                if (it.isSuccessful) {
                    _saldo.postValue(Resource.success(it.body()))
                } else {
                    _saldo.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _saldo.postValue(Resource.error(e.message.toString(), null))
        }
    }

    private fun checkExisting() = viewModelScope.launch(Dispatchers.IO) {
        _existingTopup.postValue(Resource.loading(null))
        try {
            finproUseCase.getExistingSaldoTopup().let {
                if (it.isSuccessful) {
                    _existingTopup.postValue(Resource.success(it.body()))
                } else {
                    _existingTopup.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _existingTopup.postValue(Resource.error(e.message.toString(), null))
        }
    }

    fun createSaldoTopup() = viewModelScope.launch(Dispatchers.IO) {
        val request = TopupSaldoRequest(
            cancelUnpaidTopupId = _existingTopup.value?.data?.firstOrNull()?.id,
            amount = topupAmount,
            accountId = Utils.getPaymentAccountId(),
            paymentMethods = listOf()
        )
        _topupRequest.postValue(Resource.loading(null))
        try {
            finproUseCase.createSaldoTopup(request).let {
                if (it.isSuccessful) {
                    _topupRequest.postValue(Resource.success(it.body()))
                } else {
                    _topupRequest.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _topupRequest.postValue(Resource.error(e.message.toString(), null))
        }
    }
}