package com.bukuwarung.edc.payments.ui.widgets

import DynamicKeyboardBody
import android.content.Context
import android.content.res.Resources
import android.graphics.Typeface
import android.os.Build
import android.text.SpannableString
import android.text.style.StyleSpan
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Button
import android.widget.GridLayout
import android.widget.LinearLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.DYNAMIC_KEYS
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.IS_FROM_FIREBASE
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.KEYBOARD_TYPE
import com.bukuwarung.edc.databinding.DynamicPinKeyboardBinding
import com.bukuwarung.edc.util.*
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlin.random.Random


class DynamicNumericKeyboard :
    LinearLayout {

    constructor(context: Context) : super(context) {
        init(null)
    }

    constructor(context: Context, attr: AttributeSet) : super(context, attr) {
        init(attr)
    }

    constructor(context: Context, attr: AttributeSet, defStyle: Int) : super(
        context,
        attr,
        defStyle
    ) {
        init(attr)
    }

    private var type = 1
    private var isTypeSet = false
    private var isFirstFunctionButton = true
    private var isSecondFunctionButton = true
    private var isThirdFunctionButton = true
    private val keys = listOf(
        listOf("1", "2", "3"),
        listOf("4", "5", "6"),
        listOf("7", "8", "9"),
        listOf("", "0", "")
    )
    private var firebaseKeys = listOf<List<String>?>()
    private val digitList = mutableListOf<Int>()
    private val random = Random.Default

    private val firebaseDynamicKeys = Utils.sharedPreferences.get(DYNAMIC_KEYS, "")
    private val isFromFirebase = Utils.sharedPreferences.get(IS_FROM_FIREBASE, false)
    private val getKeyboardType = Utils.sharedPreferences.get(KEYBOARD_TYPE, 0)


    private var _binding: DynamicPinKeyboardBinding? = null
    private val binding get() = _binding!!

    private fun init(attr: AttributeSet?) {
        val typeToken = object : TypeToken<DynamicKeyboardBody>() {}.type
        val keyFromFirebase = Gson().fromJson<DynamicKeyboardBody>(firebaseDynamicKeys, typeToken)
        firebaseKeys = listOf(
            keyFromFirebase?.row1,
            keyFromFirebase?.row2,
            keyFromFirebase?.row3,
            keyFromFirebase?.row4
        )
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        _binding = DynamicPinKeyboardBinding.inflate(inflater, this, true)


        context.theme.obtainStyledAttributes(attr, R.styleable.DynamicNumericKeyboard, 0, 0)
            .apply {
                try {
                    if (isFromFirebase) {
                        type = getKeyboardType
                    } else {
                        if (hasValue(R.styleable.DynamicNumericKeyboard_Type)) {
                            type = getInt(R.styleable.DynamicNumericKeyboard_Type, 0)
                        } else {
                            type = 2
                        }
                    }

                    if (hasValue(R.styleable.DynamicNumericKeyboard_FirstExtraButton)) {
                        isFirstFunctionButton =
                            getBoolean(R.styleable.DynamicNumericKeyboard_IsFirstExtraButton, true)
                    }
                    if (hasValue(R.styleable.DynamicNumericKeyboard_FirstExtraButton)) {
                        isSecondFunctionButton =
                            getBoolean(R.styleable.DynamicNumericKeyboard_IsFirstExtraButton, true)
                    }
                    if (hasValue(R.styleable.DynamicNumericKeyboard_FirstExtraButton)) {
                        isThirdFunctionButton =
                            getBoolean(R.styleable.DynamicNumericKeyboard_IsFirstExtraButton, true)
                    }
                    setKeyboardFunctionButton(
                        isFirstFunctionButton,
                        isSecondFunctionButton,
                        isThirdFunctionButton
                    )
                    setKeyboardType(toKeyboardType(type))
                } finally {
                    recycle()
                }
            }


    }

    interface OnButtonClickedListener {
        fun onButtonClicked(digit: String)
        fun onFunctionButtonClicked(function: String)
    }

    private var buttonCLickedListener: OnButtonClickedListener? = null

    fun setOnButtonCLickedListener(listener: OnButtonClickedListener) {
        this.buttonCLickedListener = listener
    }

    private fun onButtonClicked(digit: String) {
        buttonCLickedListener?.onButtonClicked(digit)
    }

    private fun onFunctionButtonClicked(function: String) {
        buttonCLickedListener?.onFunctionButtonClicked(function)
    }

    private fun setKeyboardFunctionButton(
        isFirstButton: Boolean,
        isSecondButton: Boolean,
        isThirdButton: Boolean
    ) {
        val functionButtonView = binding.extraButton

        if (isFirstButton) {
            val button = Button(context)
            if(Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE) {
                button.background = resources.getDrawable(R.drawable.ic_keyboard_clear)
            }else{
                button.background = resources.getDrawable(R.drawable.ic_cancel_trans)
            }
            button.layoutParams = LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
            button.setOnClickListener {
                onFunctionButtonClicked(KeyboardFunctionButton.Clear.name)
            }
            functionButtonView.addView(button)
        }
        if (isSecondButton) {
            val button = Button(context)
            button.background = resources.getDrawable(R.drawable.ic_keyboard_back)
            button.layoutParams = LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
            button.setOnClickListener {
                onFunctionButtonClicked(KeyboardFunctionButton.Back.name)
            }
            functionButtonView.addView(button)
        }
        if (isThirdButton) {
            val button = Button(context)
            button.background = resources.getDrawable(R.drawable.ic_enter_keyboard)
            button.layoutParams = LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                0,
                2f
            )
            button.singleClick {
                onFunctionButtonClicked(KeyboardFunctionButton.Enter.name)
            }
            functionButtonView.addView(button)
        }

    }

    private fun setKeyboardType(type: String) {
        val buttonHeightSize = resources.getDimensionPixelSize(R.dimen.keyboard_height) / 4
        val buttonWidthSize = Resources.getSystem().displayMetrics.widthPixels / 4
        when (type) {
            KeyboardType.Dynamic.name -> {
                val dynamicParentView = binding.dynamicLayout
                dynamicParentView.showView()
                val firebaseParentView = binding.firebaseLayout
                firebaseParentView.hideView()
                val normalParentView = binding.normalLayout
                normalParentView.hideView()
                while (digitList.size < 10) {
                    val digit = random.nextInt(0, 10)
                    if (!digitList.contains(digit)) {
                        digitList.add(digit)
                    }
                }

                for (row in 0 until 4) {
                    for (col in 0 until 3) {
                        val button = Button(context)
                        button.setBackgroundColor(resources.getColor(R.color.white))
                        button.setTextColor(resources.getColor(R.color.black))
                        val index = row * 3 + col
                        if (index == 9 || index == 11) {
                            button.text = ""
                            button.setTextSize(TypedValue.COMPLEX_UNIT_PX, setTextSizeInSp(24f))
                        } else if (index == 10) {
                            // no implementation here, have added below.
                            button.text = textToBold(digitList[9].toString())
                            button.setTextSize(TypedValue.COMPLEX_UNIT_PX, setTextSizeInSp(24f))
                        } else {
                            button.text = textToBold(digitList[index].toString())
                            button.setTextSize(TypedValue.COMPLEX_UNIT_PX, setTextSizeInSp(24f))
                        }
                        val params = GridLayout.LayoutParams()
                        params.rowSpec = GridLayout.spec(row)
                        params.columnSpec = GridLayout.spec(col)
                        params.height = buttonHeightSize
                        params.width = buttonWidthSize
                        params.setMargins(
                            2, 2, 2, 2
                        )

                        button.layoutParams = params
                        button.setOnClickListener {
                            if (button.text.isNotBlank()) {
                                onButtonClicked(button.text.toString())
                            }
                        }
                        dynamicParentView.addView(button)
                    }
                }
            }

            KeyboardType.Normal.name -> {
                val firebaseParentView = binding.firebaseLayout
                firebaseParentView.hideView()
                val dynamicParentView = binding.dynamicLayout
                dynamicParentView.hideView()
                val normalParentView = binding.normalLayout
                normalParentView.showView()
                for (rows in keys) {
                    val rowLayout = LinearLayout(context)
                    rowLayout.layoutParams = LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    rowLayout.orientation = HORIZONTAL
                    for (digit in rows) {
                        val button = Button(context)
                        button.setBackgroundColor(resources.getColor(R.color.white))
                        button.setTextColor(resources.getColor(R.color.black))
                        button.text = textToBold(digit)
                        button.setTextSize(TypedValue.COMPLEX_UNIT_PX, setTextSizeInSp(24f))
                        val params =
                            LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1f)
                        params.setMargins(
                            2, 2, 2, 2
                        )
                        button.layoutParams = params
                        button.height = buttonHeightSize
                        button.setOnClickListener {
                            if (button.text.isNotBlank()) {
                                onButtonClicked(button.text.toString())
                            }
                        }
                        rowLayout.addView(button)
                    }

                    normalParentView.addView(rowLayout)
                }
            }

            KeyboardType.Firebase.name -> {
                val firebaseParentView = binding.firebaseLayout
                firebaseParentView.showView()
                val dynamicParentView = binding.dynamicLayout
                dynamicParentView.hideView()
                val normalParentView = binding.normalLayout
                normalParentView.hideView()
                for (rows in firebaseKeys) {
                    val rowLayout = LinearLayout(context)
                    rowLayout.layoutParams = LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    rowLayout.orientation = HORIZONTAL
                    if (rows != null) {
                        for (digit in rows) {
                            val button = Button(context)
                            button.setBackgroundColor(resources.getColor(R.color.white))
                            button.setTextColor(resources.getColor(R.color.black))
                            button.setTextSize(TypedValue.COMPLEX_UNIT_PX, setTextSizeInSp(24f))
                            button.text = textToBold(digit)
                            val params =
                                LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1f)
                            params.setMargins(
                                2, 2, 2, 2
                            )
                            params.height = buttonHeightSize
                            params.width = buttonWidthSize
                            button.layoutParams = params
                            button.singleClick {
                                if (button.text.isNotBlank()) {
                                    onButtonClicked(button.text.toString())
                                }
                            }
                            rowLayout.addView(button)
                        }
                    }
                    firebaseParentView.addView(rowLayout)
                }
            }
        }
    }

    private fun setTextSizeInSp(size: Float): Float {
        val scaledDensity = resources.displayMetrics.scaledDensity
        return scaledDensity * size
    }

    private fun textToBold(text: String): SpannableString {
        val spanString = SpannableString(text)
        spanString.setSpan(StyleSpan(Typeface.BOLD), 0, spanString.length, 0)
        return spanString
    }

    fun setType(keyboardType: KeyboardType) {
        isTypeSet = true
        type = keyboardType.id
        invalidate()
    }

    enum class KeyboardType(val id: Int) {
        Dynamic(0),
        Normal(1),
        Firebase(2)
    }

    enum class KeyboardFunctionButton {
        Back,
        Clear,
        Enter
    }

    companion object {
        fun toKeyboardType(type: Int): String {
            return when (type) {
                0 -> KeyboardType.Dynamic.name
                1 -> KeyboardType.Normal.name
                else -> KeyboardType.Firebase.name
            }
        }
    }


}
