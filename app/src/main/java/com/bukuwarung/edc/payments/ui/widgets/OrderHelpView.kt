package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.databinding.OrderHelpViewBinding
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.zoho.livechat.android.ZohoLiveChat

class OrderHelpView(context: Context, attrs: AttributeSet) : ConstraintLayout(context, attrs) {

    private val binding: OrderHelpViewBinding =
        OrderHelpViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setView(order: OrderResponse?, paymentType: String?) {
        if (PaymentAuxilliary.isPpob(paymentType) && order?.status?.equals(PaymentConst.STATUS_FAILED).isTrue) {
            <EMAIL>()
            binding.tvHelp.singleClick { helpButtonClicked() }
            binding.ivHelp.singleClick { helpButtonClicked() }
        } else {
            <EMAIL>()
        }
    }

    private fun helpButtonClicked() {
        ZohoChat.openZohoChat("order_help_view")
    }
}