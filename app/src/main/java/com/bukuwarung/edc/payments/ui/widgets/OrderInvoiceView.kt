package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.FragmentManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.databinding.OrderInvoiceViewBinding
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.ui.core.PaymentViewModel
import com.bukuwarung.edc.payments.util.EditPaymentReceiptDialog
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.printer.EDCPrint
import com.bukuwarung.edc.printer.util.PaxPrinterUtils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick

class OrderInvoiceView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    private val binding: OrderInvoiceViewBinding =
        OrderInvoiceViewBinding.inflate(LayoutInflater.from(context), this, true)

    private fun setReceiptView(
        order: OrderResponse,
        paymentType: String?,
        supportFragmentManager: FragmentManager,
        paymentViewModel: PaymentViewModel
    ) {
        with(binding) {
            <EMAIL>()
            ivInvoiceCollapse.setOnClickListener {
                // Handle invoice view's visibility
                if (ivInvoiceCollapse.tag == "down") {
                    ivInvoiceCollapse.setImageResource(R.drawable.ic_chevron_up)
                    ivInvoiceCollapse.tag = "up"
                    orderInvoice.showView()
                    btnPrint.showView()
                } else {
                    ivInvoiceCollapse.setImageResource(R.drawable.ic_chevron_down)
                    ivInvoiceCollapse.tag = "down"
                    orderInvoice.hideView()
                    btnPrint.hideView()
                }
            }
            tvEdit.visibility = PaymentAuxilliary.isPaymentOut(paymentType).asVisibility()
            tvEdit.setOnClickListener {
                EditPaymentReceiptDialog.newInstance(
                    order.amount?.minus(order.fee.orNil),
                    order.agentFeeInfo?.amount,
                    order.description
                )
                    .show(supportFragmentManager, "EditPaymentReceipt")
            }
            orderInvoice.apply {
                makeInvoice(order, paymentType)
                showView()
            }

            btnPrint.singleClick {
                bwLog("OrderInvoiceView", "btnPrint.singleClick")
                if (PaymentAuxilliary.isPpob(paymentType)) paymentViewModel.printPpobReceipt(order)
                else {
                    var textToPrint = ""
                    when(Build.MANUFACTURER) {
                        Constants.DEVICE_MANUFACTURER_VERIFONE -> {
                            textToPrint = binding.orderInvoice.extractTextForVerifonePrinting()
                            paymentViewModel.printPaymentReceipt(textToPrint)
                        }
                        Constants.DEVICE_MANUFACTURER_PAX -> {
                            textToPrint = binding.orderInvoice.extractTextForPaxPrinting()
                            PaxPrinterUtils.printMultiline(textToPrint)
                            PaxPrinterUtils.commitPrintJob()
                        }
                        else -> {
                            paymentViewModel.printPaymentOutReceipt(order)
                        }
                    }
                }
            }
        }
    }

    fun setView(
        order: OrderResponse,
        paymentType: String?,
        supportFragmentManager: FragmentManager,
        paymentViewModel: PaymentViewModel,
        showReceipt: Boolean = false
    ) {
        if (PaymentAuxilliary.isPpob(paymentType) && (showReceipt || order.status == PaymentConst.STATUS_COMPLETED)) setReceiptView(
            order,
            paymentType,
            supportFragmentManager,
            paymentViewModel
        )
        else if (PaymentAuxilliary.isPaymentOut(paymentType) && order.status == PaymentConst.STATUS_COMPLETED) setReceiptView(
            order,
            paymentType,
            supportFragmentManager,
            paymentViewModel
        )
        else <EMAIL>()
    }
}
