package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.OrderPpobInfoViewBinding
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotZero
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView


class OrderPpobInfoView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    private val binding: OrderPpobInfoViewBinding =
        OrderPpobInfoViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setView(order: OrderResponse, paymentType: String?) {
        if (!PaymentAuxilliary.isPpob(paymentType)) {
            this.hideView()
            return
        }
        val item = order.items?.firstOrNull()
        with(binding) {
            tvCapitalCostValue.text = Utils.formatAmount(order.cost)
            val adminFee = item?.adminFee
            val discountedFee = item?.discountedFee
            when (adminFee) {
                0.0 -> {
                    tvAdminFeeValue.hideView()
                    tvDiscountedFeeValue.text = context.getString(R.string.free_upper_case)
                }

                discountedFee -> {
                    tvAdminFeeValue.hideView()
                    tvDiscountedFeeValue.text = Utils.formatAmount(adminFee)
                    tvDiscountedFeeValue.text = Utils.formatAmount(adminFee)
                }

                else -> {
                    tvAdminFeeValue.text = Utils.formatAmount(adminFee)
                    tvDiscountedFeeValue.text = Utils.formatAmount(discountedFee)
                }
            }
            tvDiscountValue.text =
                if (order.totalDiscount.orNil != 0.0) "-${Utils.formatAmount(order.totalDiscount)}" else "-"
            if (order.totalBonus?.isNotZero().isTrue) {
                grpSaldoBonus.showView()
                tvSaldoBonusValue.text = "-" + Utils.formatAmount(order.totalBonus)
            }
            tvTotalPaymentValue.text = Utils.formatAmount(order.amount)
            val sellingPrice =
                if (order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0) {
                    order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
                } else if (item?.sellingPrice.orNil > 0.0) {
                    item?.sellingPrice
                } else {
                    order.amount
                }
            tvSellingPriceValue.text = Utils.formatAmount(sellingPrice)
            if (sellingPrice.orNil - order.amount.orNil >= 0.0) {
                tvProfit.text = context.getString(R.string.profit)
                tvProfitValue.text = Utils.formatAmount(sellingPrice.orNil - order.amount.orNil)
            } else {
                tvProfit.text = context.getString(R.string.loss)
                tvProfitValue.text = Utils.formatAmount(order.amount.orNil - sellingPrice.orNil)
            }
            tvSaldoValue.text = Utils.formatAmount(order.amount)
            ivDetailCollapse.setOnClickListener {
                if (clDetailsContainer.isVisible) {
                    clDetailsContainer.hideView()
                    ivDetailCollapse.setImageResource(R.drawable.ic_chevron_down)
                } else {
                    clDetailsContainer.showView()
                    ivDetailCollapse.setImageResource(R.drawable.ic_chevron_up)
                }
            }
            <EMAIL>()
        }
    }
}
