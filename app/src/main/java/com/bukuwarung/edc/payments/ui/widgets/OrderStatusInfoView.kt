package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.OrderStatusInfoViewBinding
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick

class OrderStatusInfoView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    val binding: OrderStatusInfoViewBinding =
        OrderStatusInfoViewBinding.inflate(LayoutInflater.from(context), this, true)
    private var showStatus = true

    fun setView(
        orderResponse: OrderResponse?,
        paymentType: String?,
        refreshScreen: () -> Unit,
        showReceipt: (Boolean) -> Unit
    ) {
        with(binding) {
            if (PaymentAuxilliary.isPpob(paymentType)) <EMAIL>()
            else return@with
            tvRefresh.singleClick { refreshScreen() }
            val adapter = PaymentHistoryTimelineAdapter { showReceipt(it) }
            adapter.setOrderStatus(
                orderResponse?.status,
                paymentType
            )
            adapter.setShowReceiptStatus(false)
            adapter.submitList(orderResponse?.progress?.reversed())
            rvTimeline.adapter = adapter
            ivArrow.singleClick {
                ivArrow.setImageResource(if (showStatus) R.drawable.ic_chevron_down else R.drawable.ic_chevron_up)
                showStatus = !showStatus
                rvTimeline.visibility = showStatus.asVisibility()
            }
        }
    }

}