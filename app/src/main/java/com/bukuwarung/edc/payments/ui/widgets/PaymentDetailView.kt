package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutPaymentDetailViewBinding
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.response.DisbursementOverviewResponse
import com.bukuwarung.edc.util.*


class PaymentDetailView : ConstraintLayout {

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr)

    private val binding =
        LayoutPaymentDetailViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setView(
        amount: Double?,
        bankAccount: BankAccount?
    ) {
        with(binding) {
            tvTotalAmountValue.text = Utils.formatAmount(amount)
            tvTotalAmountReceivedByCustomerValue.text = Utils.formatAmount(amount)
            bankAccount?.let {
                bankAccountView.setBankView(
                    bankAccount = it,
                    bankStatus = BankAccountView.BankStatus.VERIFIED
                )
            }
        }
    }

    fun onPaymentMethodSelected(disbursementOverviewResponse: DisbursementOverviewResponse) {
        with(binding) {
            val loyaltyDiscount = disbursementOverviewResponse.loyaltyDiscount
            val discountedFee =
                disbursementOverviewResponse.fee.orNil - disbursementOverviewResponse.discountFee.orNil
            tvTotalAmountValue.text =
                Utils.formatAmount(disbursementOverviewResponse.amount?.toDouble().orNil + discountedFee - loyaltyDiscount?.tierDiscount.orNil)
            tvLoyaltyTier.apply {
                visibility = (loyaltyDiscount?.tierDiscount.orNil.isNotZero()).asVisibility()
                text =
                    context.getString(R.string.discount_level, loyaltyDiscount?.tierName.orEmpty())
            }
            tvLoyaltyDiscount.apply {
                visibility = loyaltyDiscount?.tierDiscount.orNil.isNotZero().asVisibility()
                text = "-${Utils.formatAmount(loyaltyDiscount?.tierDiscount)}"
            }
            when (disbursementOverviewResponse.fee) {
                null -> {
                    tvDiscountedFee.apply {
                        text = "-"
                        setTextColor(context.getColorCompat(R.color.black_80))
                        showView()
                    }
                    tvAdminFee.hideView()
                }

                0.0 -> {
                    tvDiscountedFee.apply {
                        text = context.getString(R.string.free).uppercase()
                        setTextColor(context.getColorCompat(R.color.black_80))
                        showView()
                    }
                    tvAdminFee.hideView()
                }

                discountedFee -> {
                    tvDiscountedFee.apply {
                        text = Utils.formatAmount(discountedFee)
                        setTextColor(context.getColorCompat(R.color.black_80))
                        showView()
                    }
                    tvAdminFee.hideView()
                }

                else -> {
                    tvDiscountedFee.apply {
                        text = Utils.formatAmount(discountedFee)
                        setTextColor(context.getColorCompat(R.color.blue_60))
                        showView()
                    }
                    tvAdminFee.showView()
                    tvAdminFee.text = Utils.formatAmount(disbursementOverviewResponse.fee.orNil)
                }
            }
        }
    }

    fun setMoneyTransferUiChanges(
        optionalText: String?,
        amount: Double?,
        bankAccount: BankAccount?
    ) {
        with(binding) {
            tvDestinationBankAccount.text = context.getString(R.string.destination_bank)
            tvTotalAmount.text = context.getString(R.string.transfer_amount)
            bankAccount?.let {
                bankAccountView.setBankViewForMoneyTransfer(
                    bankAccount = it
                )
            }
            gpOnlyTotalAmount.hideView()
            tvTotalAmount.showView()
            tvTotalAmountValue.text = Utils.formatAmount(amount)
            optionalText?.let {
                tvCommentsOptional.showView()
                tvCommentsOptionalValue.showView()
                tvCommentsOptionalValue.text = optionalText
            } ?: run {
                tvCommentsOptional.hideView()
                tvCommentsOptionalValue.hideView()
            }
        }
    }

    fun setCashWithdrawalUiChanges(
        amount: Double?,
        bankAccount: BankAccount?
    ) {
        with(binding) {
            tvDestinationBankAccount.text = context.getString(R.string.destination_bank)
            tvTotalAmount.text = context.getString(R.string.cash_withdrawal_amount)
            bankAccount?.let {
                bankAccountView.setBankViewForCardTransaction(
                    bankAccount = it
                )
            }
            gpOnlyTotalAmount.hideView()
            tvTotalAmount.showView()
            tvTotalAmountValue.text = Utils.formatAmount(amount)
            tvCommentsOptional.hideView()
            tvCommentsOptionalValue.hideView()
        }
    }
}