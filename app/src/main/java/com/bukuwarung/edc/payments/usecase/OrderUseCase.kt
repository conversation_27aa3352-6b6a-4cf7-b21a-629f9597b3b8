package com.bukuwarung.edc.payments.usecase

import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.data.repository.OrderRepository
import retrofit2.Response
import javax.inject.Inject

class OrderUseCase @Inject constructor(private val orderRepository: OrderRepository){

    suspend fun getOrders(
        accountId: String,
        customerId: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: List<String>? = null,
        status: List<String>? = null,
        page: Int? = null,
        limit: Int? = null,
        billerCode: String? = null,
        sorting: String? = null,
        searchQuery: String? = null,
        bukuOrigin: String? = null,
        serialNumberSelected: String? = null
    ): Response<List<PaymentHistory>> {

        return orderRepository.getOrders(
            accountId, customerId, startDate, endDate,
            type, status, page, limit, billerCode, sorting, searchQuery, bukuOrigin, serialNumberSelected
        )
    }

    suspend fun getFeatureFlag() = orderRepository.getFeatureFlag()

    suspend fun getLinkedOrders(orderId: String) = orderRepository.getLinkedOrders(orderId)
}