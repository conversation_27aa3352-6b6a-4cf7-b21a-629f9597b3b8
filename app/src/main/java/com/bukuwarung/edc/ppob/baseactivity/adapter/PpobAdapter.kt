package com.bukuwarung.edc.ppob.baseactivity.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.LayoutPpobIconItemBinding
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.payments.data.model.PpobListItem
import com.bukuwarung.payments.data.model.State
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable

class PpobAdapter(
    private val list: List<PpobListItem>,
    private val clickAction: (PpobListItem, Int) -> Unit,
    defaultSelectedItem: Int
) : RecyclerView.Adapter<PpobAdapter.PpobItemViewHolder>() {

    private var currentSelectedItem = defaultSelectedItem

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PpobItemViewHolder {
        val itemBinding =
            LayoutPpobIconItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PpobItemViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: PpobItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    override fun getItemCount(): Int = list.size

    inner class PpobItemViewHolder(
        private val binding: LayoutPpobIconItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()

        private val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }

        fun bind(ppobProduct: PpobListItem, position: Int) {
            val context = binding.ivPpob.context
            val image = when (ppobProduct.state) {
                State.NOT_AVAILABLE -> {
                    ppobProduct.icon
                }
                State.COMING_SOON -> {
                    ppobProduct.comingSoonIcon
                }
                State.AVAILABLE -> {
                    if (ppobProduct.isSelected) {
                        ppobProduct.selectedIcon
                    } else {
                        ppobProduct.icon
                    }
                }
                else -> ppobProduct.icon
            }
            Glide.with(context).load(image).placeholder(shimmerDrawable).into(binding.ivPpob)
            binding.tvTitle.text = ppobProduct.displayName
            binding.ivPromo.visibility = ppobProduct.isPromo.isTrue.asVisibility()
            binding.root.singleClick {
                if (ppobProduct.state == State.AVAILABLE && (ppobProduct.category != PpobConst.CATEGORY_VOUCHER_GAME || ppobProduct.category != PpobConst.CATEGORY_TRAIN_TICKET)) {
                    list[currentSelectedItem].isSelected = false
                    notifyItemChanged(currentSelectedItem)
                    currentSelectedItem = position
                    ppobProduct.isSelected = true
                    notifyItemChanged(currentSelectedItem)
                } else {

                }
                clickAction(ppobProduct, position)
            }
        }
    }
}