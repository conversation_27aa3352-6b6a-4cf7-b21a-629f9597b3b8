package com.bukuwarung.edc.ppob.baseactivity.view

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityPpobBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.payments.data.model.PpobListItem
import com.bukuwarung.payments.data.model.State
import com.bukuwarung.edc.ppob.constants.PpobUtils
import com.bukuwarung.edc.ppob.baseactivity.adapter.PpobAdapter
import com.bukuwarung.edc.ppob.baseactivity.adapter.PpobCategoryAdapter
import com.bukuwarung.edc.ppob.common.model.PpobToolbarData
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.constants.PpobRemoteConfig
import com.bukuwarung.edc.ppob.train.view.TrainTicketWebviewActivity
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PpobActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPpobBinding
    private val category: String by lazy { intent.getStringExtra(PPOB_CATEGORY) ?: PpobConst.CATEGORY_PULSA }
    private val accountNumber: String by lazy { intent.getStringExtra(ACCOUNT_NUMBER).orEmpty() }
    private val phoneNumber: String by lazy { intent.getStringExtra(PHONE_NUMBER).orEmpty() }
    private val code: String by lazy { intent.getStringExtra(CODE).orEmpty() }
    private val layoutType: String by lazy { intent.getStringExtra(LAYOUT_TYPE).orEmpty() }
    private val machineNumber: String by lazy { intent.getStringExtra(MACHINE_NUMBER).orEmpty() }
    private val frameNumber: String by lazy { intent.getStringExtra(FRAME_NUMBER).orEmpty() }
    private val from: String by lazy { intent.getStringExtra(FROM).orEmpty() }

    private var adapter: PpobAdapter? = null

    private val viewModel: PpobViewModel by viewModels()
    private var versionFilteredList = emptyList<PpobListItem>()

    companion object {
        private const val PPOB_CATEGORY = "category"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        private const val CODE = "CODE"
        private const val FROM = "FROM"
        private const val LAYOUT_TYPE = "layout_type"
        private const val MACHINE_NUMBER = "machine_number"
        private const val FRAME_NUMBER = "frame_number"

        fun createIntent(
            context: Context,
            category: String,
            from: String = "",
            accountNumber: String = "",
            phoneNumber: String = "",
            code: String = "",
            layoutType: String = "",
            machineNumber: String = "",
            frameNumber: String = ""
        ): Intent {
            return Intent(context, PpobActivity::class.java).apply {
                putExtra(FROM, from)
                putExtra(PPOB_CATEGORY, category)
                putExtra(ACCOUNT_NUMBER, accountNumber)
                putExtra(PHONE_NUMBER, phoneNumber)
                putExtra(CODE, code)
                putExtra(LAYOUT_TYPE, layoutType)
                putExtra(MACHINE_NUMBER, machineNumber)
                putExtra(FRAME_NUMBER, frameNumber)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPpobBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    private fun setupView() {
        with(binding) {
            val showPpobCategoriesTopSection = PpobRemoteConfig.showPpobTopSection()
            includeSemua.root.visibility = showPpobCategoriesTopSection.asVisibility()
            rvPpob.visibility = showPpobCategoriesTopSection.asVisibility()
            rvPpob.layoutManager = LinearLayoutManager(this@PpobActivity, RecyclerView.HORIZONTAL, false)
            versionFilteredList = viewModel.getVersionList().orEmpty()
            val defaultSelectedItemIndex =
                versionFilteredList.indexOfFirst { it.category == category }.orNil
            if (versionFilteredList.isNotEmpty())
                versionFilteredList[defaultSelectedItemIndex].isSelected = true
            val ppobFragmentList = viewModel.setFragmentList(
                versionFilteredList,
                from,
                accountNumber,
                phoneNumber,
                code,
                layoutType,
                machineNumber,
                frameNumber,
                category
            )
            adapter = PpobAdapter(
                versionFilteredList,
                ::onCategorySelected,
                defaultSelectedItemIndex
            )
            rvPpob.adapter = adapter
            vpPpob.adapter = PpobCategoryAdapter(ppobFragmentList, supportFragmentManager)
            vpPpob.disableSwipe()
            vpPpob.offscreenPageLimit = 1
            vpPpob.currentItem = defaultSelectedItemIndex
            rvPpob.scrollToPosition(defaultSelectedItemIndex)
            if (versionFilteredList.isNotEmpty())
                versionFilteredList[defaultSelectedItemIndex].category?.let { setCategoryData(it) }
//            includeSemua.root.setSingleClickListener {
//                val bukuTileBottomSheet = BukuTileViewBottomSheet.createInstance(
//                    "ppob", isPaymentsScreen = true, showToolsSection = false
//                )
//                bukuTileBottomSheet.setPpobProductsListener(object : PpobProductsListener {
//                    override fun onPpobSelected(fragmentBodyBlock: BodyBlock?) {
//                        if (fragmentBodyBlock == null) return
//                        if (fragmentBodyBlock.coming_soon) {
//                            PpobUtils.showPpobComingSoonDialog(this@PpobActivity)
//                        } else if (fragmentBodyBlock.is_available.not()) {
//                            PpobUtils.showPpobUnAvailable(
//                                this@PpobActivity,
//                                fragmentBodyBlock.ppobCategoryName.orEmpty()
//                            )
//                        } else {
//                            if (viewModel.isVoucherGame(fragmentBodyBlock.ppobCategoryName.orEmpty())) {
//                                openActivity(WebviewActivity::class.java) {
//                                    putString(ClassConstants.WEBVIEW_URL, BuildConfig.VOUCHER_GAME_URL + Utils.getPaymentAccountId())
//                                }
//                            } else if (viewModel.isTrainTicket(fragmentBodyBlock.ppobCategoryName.orEmpty())) {
//                                startActivity(
//                                    TrainTicketWebviewActivity.createIntent(this@PpobActivity)
//                                )
//                            } else {
//                                versionFilteredList.forEach { it.isSelected = false }
//                                val pos = versionFilteredList.indexOfFirst { it.category == fragmentBodyBlock.ppobCategoryName }
//                                versionFilteredList[pos].isSelected = true
//                                adapter?.notifyDataSetChanged()
//                                binding.rvPpob.scrollToPosition(pos)
//                                setCategoryData(fragmentBodyBlock.ppobCategoryName.orEmpty())
//                                binding.vpPpob.currentItem = pos
//                            }
//                        }
//                    }
//
//                })
//                bukuTileBottomSheet.show(supportFragmentManager, BukuTileViewBottomSheet.TAG)
//            }
        }
    }

    private fun setCategoryData(category: String) {
        setToolBarView(
            PpobToolbarData(
                title = getString(
                    PpobConst.CATEGORY_NAME_MAP[category]
                        ?: R.string.ppob
                ),
                helpUrl = PpobConst.CATEGORY_HELP_URL[category].orEmpty(),
                showReminderIcon = PpobConst.CATEGORY_SHOW_REMINDER_ICON[category].isTrue
            )
        )
    }

    private fun setToolBarView(ppobToolbarData: PpobToolbarData) {
        with(binding.includeToolBar) {
            toolBarLabel.text = ppobToolbarData.title
            tbPpob.navigationIcon =
                ContextCompat.getDrawable(this@PpobActivity, R.drawable.ic_arrow_back)
            tbPpob.setNavigationOnClickListener {
                Utils.hideKeyboard(this@PpobActivity)
                onBackPressed()
            }
            ivHelp.singleClick {
                openActivity(WebviewActivity::class.java) {
                    putString(ClassConstants.WEBVIEW_TITLE, getString(R.string.help))
                    putString(ClassConstants.WEBVIEW_URL, ppobToolbarData.helpUrl)
                }
            }
        }
    }

    private fun onCategorySelected(ppobListItem: PpobListItem, position: Int) {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.ENTRY_POINT] = PpobAnalytics.PPOB_BUY_PAGE
        map["ppob_type"] = PpobConst.CATEGORY_ANALYTICS_MAP[ppobListItem.category].orEmpty()
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_BUY_BUTTON_CLICKED, map)
        when (ppobListItem.state) {
            State.AVAILABLE -> {
                binding.rvPpob.scrollToPosition(position)
                setCategoryData(ppobListItem.category.orEmpty())
                binding.vpPpob.currentItem = position
                when {
                    viewModel.isVoucherGame(ppobListItem.category.orEmpty()) -> {
                        openActivity(WebviewActivity::class.java) {
                            putString(ClassConstants.WEBVIEW_URL, BuildConfig.VOUCHER_GAME_URL + Utils.getPaymentAccountId())
                            putString(ClassConstants.WEBVIEW_TITLE, getString(R.string.voucher_game1))
                        }
                    }
                    viewModel.isTrainTicket(ppobListItem.category.orEmpty()) -> {
                        startActivity(
                            TrainTicketWebviewActivity.createIntent(this)
                        )
                    }
                    else -> {  }
                }
            }
            State.NOT_AVAILABLE -> {
                PpobUtils.showPpobUnAvailable(this@PpobActivity, ppobListItem.category.orEmpty())
            }
            State.COMING_SOON -> {
                PpobUtils.showPpobComingSoonDialog(this@PpobActivity)
            }
            else -> {}
        }
    }


    override fun onDestroy() {
        binding.rvPpob.adapter = null
        super.onDestroy()
    }
}