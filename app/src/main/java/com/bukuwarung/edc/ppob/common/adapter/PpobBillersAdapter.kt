package com.bukuwarung.edc.ppob.common.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutPpobBillerItemBinding
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.payments.data.model.PpobProduct
import com.bumptech.glide.Glide

class PpobBillersAdapter(
    private var list: List<PpobProduct>,
    private var billerDetails: Map<String, Biller>,
    private val clickAction: (PpobProduct, Biller) -> Unit,
    private val category: String
) : RecyclerView.Adapter<PpobBillersAdapter.PpobBillerViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PpobBillerViewHolder {
        val itemBinding =
            LayoutPpobBillerItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return PpobBillerViewHolder(itemBinding, billerDetails, clickAction, category)
    }

    override fun onBindViewHolder(holder: PpobBillerViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<PpobProduct>) {
        this.list = list
        notifyDataSetChanged()
    }

    class PpobBillerViewHolder(
        private val binding: LayoutPpobBillerItemBinding,
        private val billerDetails: Map<String, Biller>,
        private val clickAction: (PpobProduct, Biller) -> Unit,
        private val category: String
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(ppobProduct: PpobProduct) {
            with(binding) {
                tvName.text = ppobProduct.name
                val logo =
                    billerDetails[ppobProduct.productInfo?.biller.orEmpty()]?.billerMetaData?.icon.orEmpty()
                Glide.with(ivBiller.context)
                    .load(logo)
                    .error(PpobConst.CATEGORY_DEFAULT_ICON[category] ?: R.mipmap.ic_biller_default)
                    .into(ivBiller)
                if (ppobProduct.active == true) {
                    tvError.hideView()
                    root.singleClick {
                        clickAction(
                            ppobProduct,
                            billerDetails[ppobProduct.productInfo?.biller] ?: Biller("", "", "")
                        )
                    }
                } else {
                    tvError.showView()
                    clItem.setBackgroundColor(binding.clItem.context.getColorCompat(R.color.black_5))
                    root.singleClick {
                    }
                }
                if (ppobProduct.isSelected) {
                    tvName.setTextColor(binding.clItem.context.getColorCompat(R.color.blue_60))
                    clItem.setBackgroundColor(binding.clItem.context.getColorCompat(R.color.blue_5))
                    ivSelected.showView()
                } else {
                    tvName.setTextColor(binding.clItem.context.getColorCompat(if (ppobProduct.active.isTrue) R.color.black_80 else R.color.black_20))
                    clItem.setBackgroundColor(binding.clItem.context.getColorCompat(R.color.white))
                    ivSelected.hideView()
                }
            }
        }
    }
}