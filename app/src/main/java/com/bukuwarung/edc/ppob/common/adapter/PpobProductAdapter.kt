package com.bukuwarung.edc.ppob.common.adapter

import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ItemPpobProductBinding
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.makeSectionOfTextBold
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isFalseOrNull
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.payments.data.model.PpobProduct


class PpobProductAdapter(
    private val clickAction: (PpobProduct) -> Unit,
    private val productType: String
) : RecyclerView.Adapter<PpobProductAdapter.PpobProductViewHolder>() {

    private var list = emptyList<PpobProduct>()
    private var searchTerm = ""
    private var enableButton = false
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PpobProductViewHolder {
        val itemBinding =
            ItemPpobProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PpobProductViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: PpobProductViewHolder, position: Int) {
        holder.bind(list[position], enableButton)
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<PpobProduct>, searchTerm: String = "") {
        this.list = list.filter { it.productInfo?.isFreeForm.isFalseOrNull }
        this.searchTerm = searchTerm
        notifyDataSetChanged()
    }

    fun enableButtonToggle(enable: Boolean) {
        enableButton = enable
        notifyDataSetChanged()
    }

    inner class PpobProductViewHolder(
        private val binding: ItemPpobProductBinding,
        private val clickAction: (PpobProduct) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(product: PpobProduct, enableButton: Boolean) {
            with(binding) {
                val context = root.context
                nameTxt.text = if (productType == PpobConst.CATEGORY_PAKET_DATA) {
                    makeSectionOfTextBold(
                        product.name.orEmpty(),
                        searchTerm,
                        object : ClickableSpan() {
                            override fun onClick(widget: View) {
                                //if click requires
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                super.updateDrawState(ds)
                                ds.isUnderlineText = false
                                ds.color = context.getColorCompat(R.color.blue_60)
                            }
                        })
                } else {
                    product.name
                }
                priceTxt.text = Utils.formatAmount(product.amount)
                chooseBtn.isEnabled =
                    if (productType != PpobConst.CATEGORY_EWALLET) enableButton else true
                val isPopular = product.productInfo?.isPopular?.toBoolean() ?: false
                popularTxt.visibility = isPopular.asVisibility()
                if (product.active == true) {
                    disturbanceTxt.visibility = View.GONE
                    chooseBtn.visibility = View.VISIBLE
                    chooseBtn.singleClick {
                        clickAction(product)
                    }
                } else {
                    disturbanceTxt.visibility = View.VISIBLE
                    chooseBtn.visibility = View.INVISIBLE
                }
                product.discount?.let {
                    ivPromo.showView()
                    tvOriginalPrice.showView()
                    tvOriginalPrice.text = Utils.formatAmount(it.original)
                } ?: kotlin.run {
                    ivPromo.hideView()
                    tvOriginalPrice.hideView()
                }
            }
        }
    }
}
