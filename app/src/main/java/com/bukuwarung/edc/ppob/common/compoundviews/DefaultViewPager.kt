package com.bukuwarung.edc.ppob.common.compoundviews

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager


class DefaultViewPager(context: Context, attrs: AttributeSet) : ViewPager(context, attrs) {

    private var swipeEnabled: Boolean = true

    override fun onTouchEvent(event: MotionEvent): Boolean {
        return if (swipeEnabled) {
            super.onTouchEvent(event)
        } else false
    }

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        return if (swipeEnabled) {
            super.onInterceptTouchEvent(event)
        } else false
    }

    fun enableSwipe() {
        this.swipeEnabled = true
    }

    fun disableSwipe() {
        this.swipeEnabled = false
    }

}