package com.bukuwarung.edc.ppob.common.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class FinproBeneficiary(
    @SerializedName("category")
    val category: String,
    @SerializedName("code")
    val code: String,
    @SerializedName("account_number")
    val accountNumber: String? = null,
    @SerializedName("phone_number")
    val phoneNumber: String? = null,
    @SerializedName("number")
    val number: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("flags")
    val flag: Flags? = null,
    @SerializedName("id")
    val id: String? = null
) : Parcelable

@Parcelize
data class Flags(
    @SerializedName("is_favourite")
    var favourite: Boolean? = null
): Parcelable