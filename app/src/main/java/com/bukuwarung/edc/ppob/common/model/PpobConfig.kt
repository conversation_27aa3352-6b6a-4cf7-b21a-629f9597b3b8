package com.bukuwarung.edc.ppob.common.model

import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.payments.data.model.PpobListItem
import com.google.gson.annotations.SerializedName


data class PpobConfig(
        @SerializedName("ppob_list")
        val ppobList: List<PpobListItem>? = null,
        val trainPollingConfig: TrainPollingConfig? = null,
        val supportUrls: PpobSupportUrls = PpobSupportUrls(),
        val freeFormEwalletCap: Double = 5000.0,
        val ppobPollingConfig: PpobPollingConfig? = null
)

data class PpobPollingConfig(
        val ppobStatusPollingIntervalSeconds: Long = 5,
        val ppobStatusPollingTotalTimeSeconds: Long = 30
)

data class TrainPollingConfig(
        val pollingTimeInSeconds: Int? = PpobConst.DEFAULT_POLLING_TIME,
        val pollingInternalSeconds: Int = PpobConst.DEFAULT_POLLING_INTERVAL,
        val redirectionUrlContains: String = PpobConst.REDIRECTION_URL_CONTAINS
)

data class PpobSupportUrls(
        val pulsa: String = PpobConst.PULSA_BANTUAN,
        val tokenListrik: String = PpobConst.TOKEN_LISTRIK_BANTUAN,
        val eWallet: String = PpobConst.EWALLET_BANTUAN,
        val packetData: String = PpobConst.PAKET_DATA_BANTUAN,
        val postpaidListrik: String = PpobConst.TAGIHAN_LISTRIK_BANTUAN,
        val voucherGame: String = PpobConst.VOUCHER_GAME_BANTUAN,
        val bpjs: String = PpobConst.BPJS_BANTUAN,
        val pdam: String = PpobConst.PDAM_BANTUAN,
        val multiFinance: String = PpobConst.MULTIFINANCE_BANTUAN,
        val internetTvCable: String = PpobConst.INTERNET_DAN_TV_CABLE_BANTUAN,
        val vehicleTax: String = PpobConst.VEHICLE_TAX_BANTUAN,
        val trainTickets: String = PpobConst.TRAIN_TICKET_BANTUAN
)