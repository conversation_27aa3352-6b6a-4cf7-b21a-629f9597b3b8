package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class PpobListItem(

        @SerializedName("selected_icon")
        val selectedIcon: String? = null,

        @SerializedName("start_version")
        val startVersion: Int? = null,

        @SerializedName("icon")
        val icon: String? = null,

        @SerializedName("coming_soon_icon")
        val comingSoonIcon: String? = null,

        @SerializedName("rank")
        val rank: Int? = null,

        @SerializedName("state")
        val state: State? = null,

        @SerializedName("display_name")
        val displayName: String? = null,

        @SerializedName("category")
        val category: String? = null,

        @SerializedName("analytics_name")
        val analyticsName: String? = null,

        @SerializedName("end_version")
        val endVersion: Int? = null,

        @SerializedName("is_promo")
        val isPromo: Boolean? = null,

        var isSelected: Boolean = false
) : Parcelable

enum class State {
    @SerializedName("COMING_SOON")
    COMING_SOON,

    @SerializedName("NOT_AVAILABLE")
    NOT_AVAILABLE,

    @SerializedName("AVAILABLE")
    AVAILABLE
}