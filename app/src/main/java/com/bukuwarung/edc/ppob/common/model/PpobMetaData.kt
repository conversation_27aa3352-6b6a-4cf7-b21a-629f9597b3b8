package com.bukuwarung.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class PpobMetaData(
    @SerializedName("biller_type")
    val billerType: String? = null,
    @SerializedName("biller_name")
    val billerName: String? = null,
    @SerializedName("infobox_text")
    val infoboxText: String? = null,
    @SerializedName("id_field_name")
    val idFieldName: String? = null,
    @SerializedName("warning_message")
    val warningMessage: String? = null,
    @SerializedName("id_field_value")
    val idFieldValue: String? = null,
    @SerializedName("redeem_faq_link")
    val redeemFaqLink: String? = null,
    @SerializedName("faq_link")
    val faq_link: String? = null,
    @SerializedName("faq_cta")
    val faq_cta: String? = null,
    @SerializedName("fine_amount")
    val fineAmount: Double? = null,
    @SerializedName("biller_icon")
    val logo: String? = null,
    @SerializedName("category_display_name")
    val categoryDisplayName: String? = null,
    @SerializedName("expiry_date")
    val expiryDate: String? = null,
    @SerializedName("expected_date")
    val expectedDate: String? = null,
    @SerializedName("transaction_code")
    val transactionCode: String? = null,
    @SerializedName("aggregation_data")
    val aggregationData: AggregationData? = null,
    @SerializedName("buku_origin")
    val bukuOrigin: String? = null
) : Parcelable

@Keep
@Parcelize
data class AggregationData(
    @SerializedName("metadata")
    val metadata: List<Metadata>? = null
) : Parcelable

@Keep
@Parcelize
data class Metadata(
    @SerializedName("ledgerable")
    val ledgerable: Ledgerable? = null
) : Parcelable

@Keep
@Parcelize
data class Ledgerable(
    @SerializedName("type")
    val type: String? = null
) : Parcelable