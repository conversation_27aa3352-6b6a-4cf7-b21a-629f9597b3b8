package com.bukuwarung.edc.ppob.common.model

import android.os.Parcelable
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.AutoMatchingInfo
import com.bukuwarung.edc.payments.data.model.AutoMatchingStatus
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.ManualMatchingInfo
import com.bukuwarung.edc.payments.data.model.ManualMatchingStatus
import com.bukuwarung.edc.util.isTrue
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.io.Serializable

@Parcelize
data class RefundBankAccount(
    @SerializedName("account_id")
    val accountId: String? = null,

    @SerializedName("account_number")
    val accountNumber: String? = null,

    @SerializedName("account_holder_name", alternate = ["name"])
    val accountHolderName: String? = null,

    @SerializedName("customer_id")
    val customerId: String? = "",

    @SerializedName("bank_code", alternate = ["code"])
    val bankCode: String? = null,

    @SerializedName("balance")
    val balance: Double? = null,

    @SerializedName("bank_name")
    val bankName: String? = null,

    @SerializedName("bank_account_id")
    val bankAccountId: String = "",

    @SerializedName("is_selected")
    var isSelected: Boolean = false,

    @SerializedName("id")
    val id: String? = null,

    @SerializedName("bank_logo")
    val bankLogo: String? = null,
    @SerializedName("minimum_limit")
    val minimumLimit: Double? = null,
    @SerializedName("maximum_limit")
    val maximumLimit: Double? = null,

    @SerializedName("message")
    var message: String? = null,

    @SerializedName("is_disabled")
    val isDisabled: Boolean? = null,

    @SerializedName("auto_matching_info")
    var autoMatchingInfo: AutoMatchingInfo? = null,

    @SerializedName("manual_matching_info")
    var manualMatchingInfo: ManualMatchingInfo? = null,

    @SerializedName("account_owner")
    var accountOwner: PaymentConst.BankAccountOwner? = PaymentConst.BankAccountOwner.SELF
):Parcelable, Serializable {
    fun getBankLogoIfAvailable(): String? {
        val currentBank = Bank(bankCode ?: "", "")
        if (bankCode == PaymentConst.SALDO)
            return PaymentConst.SALDO_LOGO
        return if (Bank.BANKS.contains(currentBank)) {
            Bank.BANKS.first { it.bankCode == currentBank.bankCode }.logo
        } else {
            null
        }
    }

    fun isMatchingFailure(): Boolean {
        return when {
            autoMatchingInfo?.isAutoNameMatching.isTrue -> {
                autoMatchingInfo?.autoMatchingStatus == AutoMatchingStatus.FAILED
            }
            manualMatchingInfo?.isManualVerification.isTrue -> {
                manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.REJECTED
            }
            else -> false
        }
    }

    fun isManualMatchingRequired(): Boolean =
        autoMatchingInfo?.autoMatchingStatus == AutoMatchingStatus.FAILED
                && manualMatchingInfo == null

    fun isManualMatchingInProgress() =
        manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.UNVERIFIED

    fun isManualMatchingRejected() =
        manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.REJECTED
}