package com.bukuwarung.edc.ppob.common.view

import android.content.Context
import android.os.Bundle
import com.bukuwarung.edc.ppob.train.dialog.BasePromptDialog
import com.bukuwarung.edc.util.isTrue

class GenericConfirmationDialog(
        context: Context,
        private val titleRes: Int,
        private val bodyRes: Int,
        private val btnRightRes: Int,
        private val btnLeftRes: Int,
        private val rightBtnCallback: (() -> Unit)? = null,
        private val leftBtnCallback: (() -> Unit)? = null
) : BasePromptDialog(
    context,
    {
        if (it.isTrue) leftBtnCallback?.invoke()
        else rightBtnCallback?.invoke()
    }
) {
    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        super.onCreate(savedInstanceState)

        setTitle(context.getString(titleRes))
        setContent(context.getString(bodyRes))
        setNegativeText(context.getString(btnRightRes))
        setPositiveText(context.getString(btnLeftRes))
    }
}