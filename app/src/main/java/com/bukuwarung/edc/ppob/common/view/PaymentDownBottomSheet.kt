package com.bukuwarung.edc.ppob.common.view

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.PaymentDownBottomSheetBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.isTrue
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentDownBottomSheet : BaseBottomSheetDialogFragment() {

    private var _binding : PaymentDownBottomSheetBinding? = null
    private val binding get() = _binding!!

    companion object {
        private const val IS_SERVICE_DOWN = "isServiceDown"
        private const val MESSAGE = "message"
        private const val HIDE_CLOSE_BUTTON = "hideCloseButton"
        const val TAG = "PaymentDownBottomSheet"
        fun createInstance(isServiceDown: Boolean, message: String?, hideCloseButton: Boolean = false): PaymentDownBottomSheet {
            val bottomSheet = PaymentDownBottomSheet()
            val bundle = Bundle()
            bundle.putBoolean(IS_SERVICE_DOWN, isServiceDown)
            bundle.putString(MESSAGE, message)
            bundle.putBoolean(HIDE_CLOSE_BUTTON, hideCloseButton)
            bottomSheet.arguments = bundle
            return bottomSheet
        }
    }

    interface PaymentDownBsListener {
        fun onButtonClicked()
    }

    private val isServiceDown by lazy {
        arguments?.getBoolean(IS_SERVICE_DOWN) ?: false
    }
    private val message by lazy {
        arguments?.getString(MESSAGE)
    }

    private val hideCloseButton by lazy { arguments?.getBoolean(HIDE_CLOSE_BUTTON).isTrue }

    private var listener: PaymentDownBsListener? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        _binding = PaymentDownBottomSheetBinding.inflate(layoutInflater, container, false)



        with(binding){
            ivClose.setOnClickListener {
                dismiss()
            }
            ivClose.visibility = hideCloseButton.not().asVisibility()
            if (isServiceDown) {
                ivIcPaymentDown.setImageResource(R.mipmap.ic_server_busy)
                tvPaymentDownTitle.setText(R.string.disturbance_message)
                tvPaymentDownBody.text =
                    if (message.isNullOrBlank()) getString(R.string.try_later) else message
                btnPaymentDown.setText(R.string.back)
                btnPaymentDown.setOnClickListener {
                    dismiss()
                    listener?.onButtonClicked()
                }
            } else {
                btnPaymentDown.setOnClickListener {
                    dismiss()
                    listener?.onButtonClicked()
                }
            }
        }
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? PaymentDownBsListener }
        if (context is PaymentDownBsListener) listener = context
    }
}
