package com.bukuwarung.edc.ppob.common.view

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.PpobBillDetailsBottomSheetBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.ui.contacts.PaymentContactActivity
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.viewmodel.PpobBillDetailsBsViewModel
import com.bukuwarung.edc.ppob.confirmation.view.PpobOrderFormActivity
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.constants.PpobUtils
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.showView
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PpobBillDetailsBottomSheet : BaseBottomSheetDialogFragment() {
    companion object {
        private const val ORDER_DETAIL = "orderDetail"
        private const val CATEGORY = "category"
        const val TAG = "ppob_bill_detail_bottom_sheet"
        private const val MESSAGE = "message"
        private const val FAVOURITE_DETAIL = "favourite_detail"

        fun createInstance(orderDetail: OrderResponse? = null, category: String) =
            PpobBillDetailsBottomSheet().apply {
                val bundle = Bundle()
                bundle.putParcelable(ORDER_DETAIL, orderDetail as? Parcelable)
                bundle.putString(CATEGORY, category)
                arguments = bundle
            }
    }

    private var _binding: PpobBillDetailsBottomSheetBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PpobBillDetailsBsViewModel by viewModels()

    interface PpobBillDetailsBsListener {
        fun refreshFavouritesTab()
    }

    private var listener: PpobBillDetailsBsListener? = null

    private var orderDetail: OrderResponse? = null
    private val category by lazy { arguments?.getString(CATEGORY) }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? PpobBillDetailsBsListener }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = PpobBillDetailsBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        isCancelable = false
        orderDetail = arguments?.getParcelable(ORDER_DETAIL)
        with(binding) {
            ivClose.setSingleClickListener {
                listener?.refreshFavouritesTab()
                dismiss()
            }
            Glide.with(requireContext())
                .load(orderDetail?.metadata?.logo)
                .error(
                    PpobConst.CATEGORY_DEFAULT_ICON[category] ?: R.mipmap.ic_biller_default
                )
                .into(includeBillerInfo.ivPpobUsecase)
            includeBillerInfo.tvCategoryName.setText(
                PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob
            )
            includeBillerInfo.tvNumber.text = orderDetail?.items?.getOrNull(0)?.beneficiary?.accountNumber.orEmpty()
            if (orderDetail?.customerProfile?.isFavorite.isTrue) {
                setFavouriteView()
            } else {
                setNotFavouriteView()
            }
            billDetailView.setView(orderDetail, false, null, null)
            btnNext.setSingleClickListener {
                dismiss()
                startActivity(
                    PpobOrderFormActivity.createIntent(
                        requireActivity(),
                        orderDetail,
                        null,
                        category
                    )
                )
            }
            val isBillAlreadyPaidError = orderDetail?.finproError?.isBillAlreadyPaid().isTrue
            if (orderDetail?.metadata?.warningMessage.isNotNullOrBlank() || isBillAlreadyPaidError) {
                grpErrorMessage.showView()
                if (isBillAlreadyPaidError) {
                    tvErrorMessage.text = orderDetail?.finproError?.message
                    btnNext.isEnabled = false
                } else {
                    tvErrorMessage.text = orderDetail?.metadata?.warningMessage
                }
            }
        }
        setObservers()
    }

    private fun setObservers() {
        viewModel.eventStatus.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.RefreshFavourite -> {
                    Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_LONG
                    ).show()
                    if (it.refreshFavourite) {
                        listener?.refreshFavouritesTab()
                        orderDetail?.customerProfile?.favouriteDetails = null
                        setNotFavouriteView()
                    }
                }
                else -> {}
            }
        }
    }

    private fun setFavouriteView() = with(binding) {
        includeBillerInfo.ivFavourite.setImageResource(R.drawable.ic_favourite_fill)
        includeBillerInfo.ivFavourite.setSingleClickListener {
            PpobUtils.showRemoveFavouriteDialog(requireContext()) {
                viewModel.removeFavourite(
                    orderDetail?.customerProfile?.favouriteDetails?.id.orEmpty(), requireContext()
                )
            }
        }
        binding.billDetailView.setFavouriteData(
            favName = orderDetail?.customerProfile?.favouriteDetails?.alias.orEmpty(),
            isFavourite = true
        )
    }

    private fun setNotFavouriteView() = with(binding) {
        includeBillerInfo.ivFavourite.setImageResource(R.drawable.ic_favourite_grey)
        includeBillerInfo.ivFavourite.setSingleClickListener {
            loadUserContactActivity()
        }
        binding.billDetailView.setFavouriteData(
            favName = orderDetail?.customerProfile?.favouriteDetails?.alias.orEmpty(),
            isFavourite = false
        )
    }

    private fun loadUserContactActivity() {
        startContactActivityForResult.launch(
            PaymentContactActivity.createIntent(
                requireContext(),
                orderDetail?.orderId,
                PpobAnalytics.PPOB_BUY_PAGE
            )
        )
    }

    private val startContactActivityForResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            Toast.makeText(
                context,
                result.data?.getStringExtra(MESSAGE),
                Toast.LENGTH_LONG
            ).show()
            orderDetail?.customerProfile?.favouriteDetails =
                result.data?.getParcelableExtra(FAVOURITE_DETAIL)
            orderDetail?.customerProfile?.isFavorite = true
            setFavouriteView()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
        _binding = null
    }
}