package com.bukuwarung.edc.ppob.common.view

import android.content.Context
import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.DialogBillersBinding
import com.bukuwarung.edc.global.base.BaseDialogFragment
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.common.adapter.PpobBillersAdapter
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.model.PpobViewState
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.edc.ppob.common.viewmodel.PpobBillersViewModel
import com.bukuwarung.ui_component.base.BaseErrorView
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class PpobBillersDialog() : BaseDialogFragment() {

    private var _binding: DialogBillersBinding? = null
    private val binding get() = _binding!!
    private var iCommunicator: ICommunicator? = null
    private var ppobProductList: List<PpobProduct>? = null
    private lateinit var adapter: PpobBillersAdapter
    private val selectedProduct by lazy { arguments?.getParcelable(SELECTED_PRODUCT) as? PpobProduct }
    private val category by lazy { arguments?.getString(CATEGORY).orEmpty() }

    private val viewModel: PpobBillersViewModel by viewModels()

    companion object {
        const val TAG = "PpobBillersDialog"
        private const val SELECTED_PRODUCT = "selected_product"
        private const val CATEGORY = "category"
        fun getInstance(selectedProduct: PpobProduct? = null, category: String) =
            PpobBillersDialog().apply {
                arguments = Bundle().apply {
                    putParcelable(SELECTED_PRODUCT, selectedProduct)
                    putString(CATEGORY, category)
                }
            }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { iCommunicator = it as? ICommunicator }
        if (context is ICommunicator) iCommunicator = context
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding =
            DialogBillersBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            viewModel.getPpobProductsWithBillerDetails(
                category,
                emptyMap()
            )
            bukuErrorView.addCallback(errorViewCallBack)
            etSearch.apply {
                backgroundTintList =
                    context?.getColorCompat(R.color.black_10)?.let { ColorStateList.valueOf(it) }
                afterTextChanged { searchText ->
                    if (!ppobProductList.isNullOrEmpty()) {
                        viewModel.onSearchTextChanged(searchText, ppobProductList!!)
                    }
                }
            }
            includeToolBar.apply {
                ivHelp.setOnClickListener {
                    context?.openActivity(WebviewActivity::class.java) {
                        putString(ClassConstants.WEBVIEW_URL, PpobConst.CATEGORY_HELP_URL[category].orEmpty())
                    }
                }
                tbPpob.apply {
                    navigationIcon = context.getDrawableCompat(R.drawable.ic_arrow_back)
                    setNavigationOnClickListener {
                        dismiss()
                    }
                }
                toolBarLabel.text = PpobConst.CATEGORY_NAME[category]?.let { getString(it) } ?: getString(R.string.ppob)
                if(category == PpobConst.CATEGORY_VEHICLE_TAX) tvChoosePolicy.showView()
            }
        }
        subscribeState()
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            with(binding) {
                when (it) {
                    is PpobEvent.ShowProductsList -> {
                        rvItem.showView()
                        tvEmpty.hideView()
                        grpNoProducts.hideView()
                        it.list.forEach { product -> product.isSelected = (product.sku == selectedProduct?.sku) }
                        if (!it.isFilteredCall) {
                            ppobProductList = it.list
                            adapter = PpobBillersAdapter(
                                it.list,
                                it.billerDetails ?: emptyMap(),
                                ::clickAction,
                                category
                            )
                            rvItem.layoutManager = LinearLayoutManager(context)
                            rvItem.adapter = adapter
                        } else {
                            if (it.list.isNotEmpty()) {
                                adapter.setData(it.list)
                            } else {
                                rvItem.hideView()
                                if (category == PpobConst.CATEGORY_VEHICLE_TAX) grpNoProducts.showView()
                                else tvEmpty.showView()
                            }
                        }
                    }
                    is PpobEvent.SearchInternetError -> {
                        includeShimmer.sflLayout.hideShimmer()
                        includeShimmer.sflLayout.hideView()
                        bukuErrorView.apply {
                            showView()
                            setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.no_connection_title),
                                it.message,
                                getString(R.string.reload), R.mipmap.ic_no_inet
                            )
                        }
                    }
                    is PpobEvent.SearchServerError -> {
                        includeShimmer.sflLayout.hideShimmer()
                        includeShimmer.sflLayout.hideView()
                        bukuErrorView.apply {
                            showView()
                            setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.server_error_title),
                                it.message,
                                getString(R.string.reload), R.mipmap.ic_server_down
                            )
                        }
                    }
                    else -> {}
                }
            }
        }
        viewModel.viewState.observe(this) {
            setViewState(it)
        }
    }

    private fun setViewState(ppobViewState: PpobViewState) {
        with(binding) {
            when {
                ppobViewState.showLoading -> {
                    includeShimmer.sflLayout.showShimmer(true)
                    includeShimmer.sflLayout.showView()
                    bukuErrorView.hideView()
                }
                !ppobViewState.showLoading -> {
                    includeShimmer.sflLayout.hideShimmer()
                    includeShimmer.sflLayout.hideView()
                    bukuErrorView.hideView()
                }
                else -> {}
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            viewModel.getPpobProductsWithBillerDetails(category, emptyMap())
        }

        override fun messageClicked() {}

    }

    private fun clickAction(selectedProduct: PpobProduct, biller: Biller) {
        iCommunicator?.setSelectedProduct(selectedProduct, biller)
        dismiss()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface ICommunicator {
        fun setSelectedProduct(selectedProduct: PpobProduct, biller: Biller)
    }

}
