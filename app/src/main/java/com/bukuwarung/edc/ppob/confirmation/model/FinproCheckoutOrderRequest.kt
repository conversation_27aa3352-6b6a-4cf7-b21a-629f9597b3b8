package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.edc.payments.data.model.FinproCheckoutPayment
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class FinproCheckoutOrderRequest(
        @SerializedName("payment_methods")
        val paymentMethod: List<FinproCheckoutPayment>,
        @SerializedName("items")
        val items: List<FinproCheckoutItem>? = null,
        @SerializedName("purchase_type")
        val purchaseType: String? = null,
        @SerializedName("use_saldo_bonus")
        val useSaldoBonus: Boolean = false
) : Parcelable

@Parcelize
data class FinproCheckoutItem(
        @SerializedName("sku")
        val sku: String,
        @SerializedName("selling_price")
        val sellingPrice: Double
) : Parcelable
