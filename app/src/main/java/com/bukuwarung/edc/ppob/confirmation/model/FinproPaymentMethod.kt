package com.bukuwarung.edc.ppob.confirmation.model

import android.os.Parcelable
import com.bukuwarung.edc.payments.data.model.FeeDetails
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class FinproPaymentMethod(
        @SerializedName("name")
        val name: String? = null,
        @SerializedName("code")
        val code: String? = null,
        @SerializedName("family")
        val family: String? = null,
        @SerializedName("logo")
        val logo: String? = null,
        @SerializedName("details")
        val details: FinProPaymentMethodDetails? = null,
        @SerializedName("tag")
        val tag: FinproPaymentMethodTags? = null,
        @SerializedName("group_name")
        val groupName: String? = null,
        @SerializedName("group_code")
        val groupCode: String? = null,
        @SerializedName("group_logo")
        val groupLogo: String? = null,
        @SerializedName("balance")
        val balance: Double? = null,
        @SerializedName("payment_destination")
        val virtualAccountNumber: String? = null,
        @SerializedName("detail")
        val detail: FinProPaymentMethodDetail? = null,
        @SerializedName("campaign_text")
        val campaignText: String? = null,
        @SerializedName("fee_details")
        val feeDetails: FeeDetails? = null
) : Parcelable

@Parcelize
data class FinproPaymentMethodTags(
        @SerializedName("tag_type")
        val tagType: String?,
        @SerializedName("display_text")
        val displayText: String?,
        @SerializedName("cta_text")
        val ctaText: String? = null,
        @SerializedName("deeplink")
        val deeplink: String? = null
): Parcelable

@Parcelize
data class FinProPaymentMethodDetails(
        @SerializedName("saldo_balance")
        val saldoBalance: Double?,
        @SerializedName("saldo_bonus")
        val saldoBonus: SaldoBonus? = null,
        @SerializedName("is_selected")
        var isSelected: Boolean?,
        @SerializedName("limit")
        val limit: Double? = null,
        @SerializedName("remaining_daily_limit")
        val remainingDailyLimit: Double? = null,
        @SerializedName("remaining_monthly_limit")
        val remainingMonthlyLimit: Double? = null,
        @SerializedName("saldo_freeze")
        val isSaldoFreezed: Boolean? = null
): Parcelable

@Parcelize
data class SaldoBonus(
        @SerializedName("total")
        val total: Double?,
        @SerializedName("available")
        val available: Double?
): Parcelable

@Parcelize
data class FinProPaymentMethodDetail(
        @SerializedName("mdr_type")
        val mdrType: String? = null,
        @SerializedName("current_timestamp")
        val currentTimestamp: String? = null,
        @SerializedName("mdr_value")
        val mdr: Double? = null,
        @SerializedName("qris_fee")
        val qrisFee: Double? = null,
        @SerializedName("disbursement_retry_number")
        val retryAttempts: Int? = null,
        // rrn(Retrieval Reference Number) is a unique id assigned to each QRIS transaction by the bank
        @SerializedName("rrn")
        val rrn: String? = null,
        @SerializedName("qrisScheduledDisbursementInfo")
        val qrisScheduledDisbursementInfo: String? = null,
        @SerializedName("cta_text")
        val ctaText: String? = null,
        @SerializedName("deeplink")
        val deeplink: String? = null,
        @SerializedName("display_text")
        val displayText: String? = null,
        @SerializedName("ledger_transaction_id")
        val ledgerTransactionId: String? = null,
        @SerializedName("saldo_bonus_transaction")
        val saldoBonusTrx: LedgerTrxDetail? = null,
        @SerializedName("saldo_transaction")
        val saldoTrx: LedgerTrxDetail? = null
) : Parcelable

@Parcelize
data class LedgerTrxDetail(
        @SerializedName("ledger_account_id")
        val ledgerAccountId: String? = null,
        @SerializedName("amount")
        val amount: Double? = null
): Parcelable