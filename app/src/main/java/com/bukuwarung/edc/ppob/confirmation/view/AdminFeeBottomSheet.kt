package com.bukuwarung.edc.ppob.confirmation.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.AdminFeeBottomSheetBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.ui_component.utils.setSingleClickListener

class AdminFeeBottomSheet : BaseBottomSheetDialogFragment() {
    private var _binding: AdminFeeBottomSheetBinding? = null
    private val binding get() = _binding!!

    companion object {
        const val TAG = "AdminFeeBottomSheet"
        private const val IS_TRAIN_TICKET = "is_train_ticket"
        fun createInstance(
            isTrainTicket: Boolean = false,
        ) = AdminFeeBottomSheet().apply {
            arguments = Bundle().apply {
                putBoolean(IS_TRAIN_TICKET, isTrainTicket)
            }
        }
    }

    private val isTrainTicket by lazy { arguments?.getBoolean(IS_TRAIN_TICKET).isTrue }

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        _binding = AdminFeeBottomSheetBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.btnPrimaryAction.setSingleClickListener {
            dismiss()
        }
        binding.tvHeading.text = if(isTrainTicket) getString(R.string.convenience_fee_heading) else getString(R.string.admin_fee_heading)
        binding.tvSubHeading.text = if(isTrainTicket) getString(R.string.convenience_fee_subheading) else getString(R.string.admin_fee_subheading)
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}