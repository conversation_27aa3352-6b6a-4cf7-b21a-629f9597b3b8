package com.bukuwarung.edc.ppob.confirmation.view

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.PpobBankAccountsBottomSheetBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.payments.constant.PaymentConst.TYPE_PAY_OUT
import com.bukuwarung.edc.payments.ui.saldo.SaldoTopupActivity
import com.bukuwarung.edc.ppob.confirmation.model.FinproPaymentMethod
import com.bukuwarung.edc.ppob.confirmation.adapter.OtherPaymentMethodsAdapter
import com.bukuwarung.edc.ppob.confirmation.adapter.PaymentMethodsAdapter
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.ppob.confirmation.viewmodel.PaymentMethodViewModel
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.google.android.material.bottomsheet.BottomSheetBehavior
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentMethodBottomSheet : BaseBottomSheetDialogFragment() {

    private var viewModel: PaymentMethodViewModel? = null

    companion object {
        const val TAG = "PaymentMethodBottomSheet"
        private const val CATEGORY = "category"
        private const val TYPE = "type"
        private const val BANK_CODE = "bank_code"
        private const val SELECTED_PAYMENT_METHOD_CODE = "selected_payment_method_code"
        private const val AMOUNT = "amount"
        private const val USE_SALDO_REWARD = "use_saldo_reward"
        fun createInstance(
            category: String? = null,
            type: String? = null,
            bankCode: String? = null,
            amount: Double? = null,
            selectedPaymentMethodCode: String? = null,
            useSaldoReward: Boolean = false
        ) = PaymentMethodBottomSheet().apply {
            arguments = Bundle().apply {
                putString(CATEGORY, category)
                putString(TYPE, type)
                putString(BANK_CODE, bankCode)
                putString(SELECTED_PAYMENT_METHOD_CODE, selectedPaymentMethodCode)
                putDouble(AMOUNT, amount.orNil)
                putBoolean(USE_SALDO_REWARD, useSaldoReward)
            }
        }
    }

    private val category by lazy { arguments?.getString(CATEGORY) }
    private val selectedPaymentMethodCode by lazy {
        arguments?.getString(
            SELECTED_PAYMENT_METHOD_CODE
        )
    }
    private val type by lazy { arguments?.getString(TYPE) }
    private val bankCode by lazy { arguments?.getString(BANK_CODE) }
    private val amount by lazy { arguments?.getDouble(AMOUNT) }
    private val useSaldoReward by lazy { arguments?.getBoolean(USE_SALDO_REWARD).isTrue }
    private var _binding: PpobBankAccountsBottomSheetBinding? = null
    private val binding get() = _binding!!


    interface PpobBankAccountsBsListener {
        fun changePaymentMethod(
            finproPaymentMethod: FinproPaymentMethod,
            dailySaldoLimit: Double? = null,
            monthlySaldoLimit: Double? = null,
            eventProperty: HashMap<String,String>? = null
        )
        fun changeUseSaldoReward(useSaldoReward: Boolean = false)
    }

    private var listener: PpobBankAccountsBsListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        listener = context as? PpobBankAccountsBsListener
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = PpobBankAccountsBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.setOnShowListener {
            Utils.showBottomSheet(it, BottomSheetBehavior.STATE_EXPANDED)
        }
        viewModel = activity?.let { ViewModelProvider(it).get(PaymentMethodViewModel::class.java) }
        if (type == TYPE_PAY_OUT) {
            binding.poweredByFooter.showView()
            if (viewModel?.finproGetPaymentMethodsV2Response != null) {
                viewModel?.getFinalPaymentMethod(
                    viewModel?.finproGetPaymentMethodsV2Response,
                    viewModel?.dailySaldoLimit,
                    viewModel?.monthlySaldoLimit
                )
            } else {
                viewModel?.getSaldoBalance(type.orEmpty(), bankCode.orEmpty(), amount.orNil)
            }
        } else {
            viewModel?.getFinalPaymentMethod(viewModel?.finproGetPaymentMethodsV2Response)
        }
        viewModel?.viewState?.observe(viewLifecycleOwner) {
            binding.pbProgress.visibility = it.showLoading.asVisibility()
        }

        viewModel?.observeEvent?.observe(viewLifecycleOwner) {
            when (it) {
                is PaymentMethodViewModel.Event.PaymentMethodsData -> {
                    with(binding) {
                        bevError.hideView()
                        binding.poweredByFooter.showView()
                        tvTitle.text = it.paymentMethods?.title.orEmpty()
                        tvTitle.visibility =
                            it.paymentMethods?.title.isNotNullOrBlank().asVisibility()
                        rvHighlightedMethods.adapter =
                            PaymentMethodsAdapter(
                                it.paymentMethods?.highlightedPaymentChannels?.channels.orEmpty(),
                                { selectedPaymentMethod ->
                                    dismiss()
                                    listener?.changePaymentMethod(
                                        finproPaymentMethod = selectedPaymentMethod,
                                        dailySaldoLimit = it.dailySaldoLimit,
                                        monthlySaldoLimit = it.monthlySaldoLimit,
                                        eventProperty = setPaymentSelectedEvent(selectedPaymentMethod)
                                    )
                                },
                                { saldoRechargeScreen() },
                                { url -> redirectToWebview(url) },
                                useSaldoReward,
                                {useSaldoReward -> listener?.changeUseSaldoReward(useSaldoReward)},
                                false,
                                {
                                    dismiss()
                                    SaldoFreezeBottomSheet.createInstance()
                                        .show(requireFragmentManager(), SaldoFreezeBottomSheet.TAG)
                                }
                            )
                        tvOtherPaymentsTitle.text =
                            it.paymentMethods?.otherPaymentChannels?.title.orEmpty()
                        tvOtherPaymentsTitle.visibility =
                            it.paymentMethods?.otherPaymentChannels?.methods?.isNotEmpty().isTrue.asVisibility()
                        var isExpanded = true
                        tvOtherPaymentsTitle.setDrawable(right = R.drawable.ic_chevron_up)
                        rvOtherMethods.showView()
                        tvOtherPaymentsTitle.setSingleClickListener {
                            if (isExpanded) {
                                tvOtherPaymentsTitle.setDrawable(right = R.drawable.ic_chevron_down)
                                rvOtherMethods.hideView()
                            } else {
                                tvOtherPaymentsTitle.setDrawable(right = R.drawable.ic_chevron_up)
                                rvOtherMethods.showView()
                            }
                            isExpanded = !isExpanded
                        }
                        rvOtherMethods.adapter =
                            OtherPaymentMethodsAdapter(
                                it.paymentMethods?.otherPaymentChannels?.methods.orEmpty(),
                                { selectedPaymentMethod ->
                                    dismiss()
                                    listener?.changePaymentMethod(
                                        finproPaymentMethod = selectedPaymentMethod,
                                        eventProperty = setPaymentSelectedEvent(selectedPaymentMethod)
                                    )
                                },
                                { saldoRechargeScreen() },
                                { url -> redirectToWebview(url) },
                                useSaldoReward,
                                {useSaldoReward -> listener?.changeUseSaldoReward(useSaldoReward)},
                                true,
                                {
                                    dismiss()
                                    SaldoFreezeBottomSheet.createInstance()
                                        .show(requireFragmentManager(), SaldoFreezeBottomSheet.TAG)
                                }
                            )
                    }
                }

                is PaymentMethodViewModel.Event.PaymentMethodMapper -> {
                    context?.let { context ->
//                        viewModel?.getFinalPaymentMethod(
//                            it.response.toFinproPaymentMethod(
//                                context = context,
//                                amount = amount.orNil,
//                                saldoBalance = it.saldoBalance,
//                                selectedPaymentCode = selectedPaymentMethodCode,
//                                isBmsRegistered = it.isBmsRegistered,
//                                dailySaldoLimit = it.dailySaldoLimit,
//                                monthlySaldoLimit = it.monthlySaldoLimit,
//                                isSaldoFreezed = it.isSaldoFreezed
//                            ),
//                            it.dailySaldoLimit,
//                            it.monthlySaldoLimit
//                        )
                    }
                }
                is PaymentMethodViewModel.Event.ApiError -> {
                    binding.clPaymentMethod.hideView()
                    binding.poweredByFooter.hideView()
                    with(binding.bevError) {
                        showView()
                        setErrorType(
                            type = BaseErrorView.Companion.ErrorType.SERVER_ERROR
                        )
                    }
                }
                is PaymentMethodViewModel.Event.ConnectionError -> {
                    binding.clPaymentMethod.hideView()
                    binding.poweredByFooter.hideView()
                    with(binding.bevError) {
                        showView()
                        setErrorType(
                            type = BaseErrorView.Companion.ErrorType.CONNECTION_ERROR
                        )
                    }
                }
                else -> {}
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun saldoRechargeScreen() {
        dismiss()
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.ENTRY_POINT] = PpobAnalytics.INSUFFICIENT_BALANCE
        map[PpobAnalytics.WALLET] = PpobAnalytics.SALDO
        if (type != TYPE_PAY_OUT) map[PpobAnalytics.TYPE] = category.toString()
        map[PpobAnalytics.CURRENT_WALLET_BALANCE] = viewModel?.saldoBalance.toString()
        Analytics.trackEvent(PpobAnalytics.EVENT_WALLET_TOP_UP_CLICK, map)
        context?.openActivity(SaldoTopupActivity::class.java)
    }

    private fun redirectToWebview(url: String) {
        dismiss()
        context?.openActivity(WebviewActivity::class.java) {
            putString(ClassConstants.WEBVIEW_URL, url)
        }
    }

    private fun setPaymentSelectedEvent(selectedPaymentMethod: FinproPaymentMethod): HashMap<String,String> {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.AVAILABLE_PAYMENT_METHOD] = viewModel?.getAvailableMethods().toString()
        map[PpobAnalytics.NON_AVAILABLE_PAYMENT_METHOD] = viewModel?.getUnAvailableMethods().toString()
        map[PpobAnalytics.REASON] = viewModel?.getUnAvailableReasons().toString()
        map[PpobAnalytics.BANK] = selectedPaymentMethod.code.toString()
        return map
    }
}