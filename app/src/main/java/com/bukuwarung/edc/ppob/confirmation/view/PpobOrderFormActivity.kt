package com.bukuwarung.edc.ppob.confirmation.view

import com.bukuwarung.edc.ppob.common.compoundviews.BillDetailExpandableView
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Parcelable
import android.text.SpannableStringBuilder
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityPpobOrderFormBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.ppob.common.view.GenericConfirmationDialog
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PinType
import com.bukuwarung.edc.payments.data.model.CustomerProfile
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.ui.contacts.PaymentContactActivity
import com.bukuwarung.edc.payments.ui.core.NewPaymentPinActivity
import com.bukuwarung.edc.payments.ui.saldo.SaldoTopupActivity
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.confirmation.model.FinproPaymentMethod
import com.bukuwarung.edc.ppob.confirmation.model.SaldoBonus
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.edc.ppob.constants.PpobUtils
import com.bukuwarung.edc.ppob.confirmation.viewmodel.PaymentMethodViewModel
import com.bukuwarung.edc.ppob.confirmation.viewmodel.PpobOrderFormViewModel
import com.bukuwarung.edc.ppob.confirmation.viewmodel.PpobOrderFormViewModel.Companion.HEALTH_ERROR
import com.bukuwarung.edc.ppob.confirmation.viewmodel.PpobOrderFormViewModel.Companion.HEALTH_OK
import com.bukuwarung.edc.ppob.confirmation.viewmodel.PpobOrderFormViewModel.Companion.HEALTH_WARNING
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.train.view.PaymentExpiredDialog
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.boldText
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.invisibleView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.isNotZero
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.isZero
import com.bukuwarung.edc.util.makeButtonInactive
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.setStyleButtonColorFill
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.Locale

@AndroidEntryPoint
class PpobOrderFormActivity : AppCompatActivity(),
    PaymentDownBottomSheet.PaymentDownBsListener,
    BillDetailExpandableView.ICommunicator, PaymentMethodBottomSheet.PpobBankAccountsBsListener {

    private val viewModel: PpobOrderFormViewModel by viewModels()

    private val paymentMethodViewModel: PaymentMethodViewModel by viewModels()
    private lateinit var binding: ActivityPpobOrderFormBinding

    private var from: String? = null
    private var updatingSaldoBalance = false
    private val saldoCampaign by lazy { orderDetail?.campaigns?.filter { it.type == PpobConst.CASHBACK_TYPE && it.paymentMethodChannel == PpobConst.SALDO_METHOD } }
    private val saldoUseCaseCampaign by lazy { orderDetail?.campaigns?.filter { it.type == PpobConst.CASHBACK_TYPE && it.paymentMethodChannel == PpobConst.ALL_METHOD } }
    private var sellingPrice = 0L
    private var bnplFee = 0.0
    private val orderDetail by lazy { intent?.getParcelableExtra(ORDER_DETAIL) as? OrderResponse }
    private val biller by lazy { intent?.getParcelableExtra(BILLER) as? Biller }
    private var timer: CountDownTimer? = null

    companion object {
        private const val ORDER_DETAIL = "orderDetail"
        private const val BILLER = "biller"
        private const val TYPE = "type"
        private const val FROM = "from"
        private const val IS_RECENT = "is_recent"
        private const val IS_REMINDER = "is_reminder"
        private const val SALES = "SALES"
        private const val EXPENSE = "EXPENSE"

        fun createIntent(
            context: Context,
            orderDetail: OrderResponse? = null,
            biller: Biller? = null,
            type: String? = null,
            from: String? = null, isRecent: Boolean = false, isReminder: Boolean = false
        ): Intent {
            val i = Intent(context, PpobOrderFormActivity::class.java)
            i.putExtra(ORDER_DETAIL, orderDetail as? Parcelable)
            i.putExtra(BILLER, biller as? Parcelable)
            i.putExtra(TYPE, type)
            i.putExtra(FROM, from)
            i.putExtra(IS_RECENT, isRecent)
            i.putExtra(IS_REMINDER, isReminder)
            return i
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPpobOrderFormBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        subscribeState()
    }


    private fun setupView() {
        orderDetail ?: finish()
        val productDetail =
            if (orderDetail?.items.isNullOrEmpty()) null else orderDetail?.items?.first()
        if (productDetail == null) {
            finish()
            return
        }
        with(binding) {
            orderDetail?.run {
                viewModel.init(this)
                tbPpob.navigationIcon =
                    ContextCompat.getDrawable(this@PpobOrderFormActivity, R.drawable.ic_arrow_back)
                tbPpob.setNavigationOnClickListener {
                    Utils.hideKeyboard(this@PpobOrderFormActivity)
                    onBackPressed()
                }
                sellingPrice = this.items?.firstOrNull()?.sellingPrice?.toLong().orNil
                if (sellingPrice != 0L) {
                    includePpobAmount
                    includePpobAmount.etAmount.setText(
                        Utils.formatAmountWithoutRp(
                            sellingPrice.toDouble()
                        )
                    )
                    viewModel.onSellingPriceChanged(sellingPrice)
                }
                includePpobAmount.tvBillValue.text = Utils.formatAmount(this.cost)
                includePpobAmount.tvTotalAmount.text = Utils.formatAmount(orderDetail?.amount.orNil)

                includePaymentMethod.btnCompletePayment.text = getString(
                    R.string.payment_button_text,
                    Utils.formatAmount(this.amount)
                )
                includePaymentMethod.btnCompletePayment.singleClick {
                    viewModel.submit(
                        intent.getBooleanExtra(IS_RECENT, false),
                        intent.getBooleanExtra(IS_REMINDER, false)
                    )
                }
                includePaymentMethod.tvChange.singleClick {
                    val map =  HashMap<String,String>()
                    map["ppob_type"] = viewModel.getCategoryName()
                    map[PpobAnalytics.CURRENT_SALDO_BONUS_BALANCE] = viewModel.getTotalSaldoBonus().toString()
                    Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_SELECT_PAYMENT_METHOD, map)
                    viewModel.showPpobBankAccountsBs()
                }
                includePpobAmount.etAmount.afterTextChanged { text ->
                    val numbers = text.filter { it.isDigit() }.toLongOrNull()
                    sellingPrice = numbers.orNil
                    viewModel.onSellingPriceChanged(sellingPrice)
                }
                includePaymentMethod.includeLayout.btnCheck.singleClick {
                    topupSaldoInitiated()
                }
                onAmountChanged(this.amount.orNil, false)
                billDetailView.showView()
                billDetailView.setView(
                    orderDetail,
                    true,
                    biller,
                    this@PpobOrderFormActivity
                )
                initViewBasedOnCategory()
                recentsAndFavouritesView()
                setAdminFee()
            }
            with(includePpobAmount) {
                btSell.singleClick {
                    btSell.isEnabled = false
                    btPersonal.isEnabled = true
                    clickAction(true)
                }
                btPersonal.singleClick {
                    btPersonal.isEnabled = false
                    btSell.isEnabled = true
                    clickAction(false)
                }
            }
        }
        orderDetail?.items?.firstOrNull()?.details?.paymentExpirationTimestamp?.let {
            setExpiryTimer(
                it
            )
        }
    }

    private fun setExpiryTimer(expiredAt: String) {
        binding.includeTimer.root.showView()
        timer = object :
            CountDownTimer(DateTimeUtils.getInvoiceExpiryTime(expiredAt), Constant.ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                binding.includeTimer.tvExpiredTime.text =
                    DateTimeUtils.millisecondsToTime(millisUntilFinished)
            }

            override fun onFinish() {
                binding.includePaymentMethod.btnCompletePayment.isEnabled = false
                PaymentExpiredDialog(
                    context = this@PpobOrderFormActivity,
                    action = {
                        goToDestination(HomePageActivity::class.java)
                    }
                ).show()
                timer?.cancel()
            }
        }
        timer?.start()
    }

    private fun setAdminFee() {
        val adminFee = orderDetail?.items?.firstOrNull()?.adminFee
        val discountedFee = orderDetail?.items?.firstOrNull()?.discountedFee
        val discount = orderDetail?.campaigns?.filter { it.type == PpobConst.ADMIN_FEE_TYPE }
            ?.getOrNull(0)?.amount.orNil
        val totalDiscount = discount + orderDetail?.totalDiscount.orNil
        with(binding.includePpobAmount) {
            if (totalDiscount.isZero()) {
                tvDiscountValue.hideView()
                tvDiscount.hideView()
            } else {
                tvDiscountValue.showView()
                tvDiscount.showView()
                tvDiscountValue.text = "-${Utils.formatAmount(totalDiscount)}"
            }
            tvTransactionFeesMessage.text =
                if (viewModel.isTrainCategory()) getString(R.string.convenience_fee) else getString(
                    R.string.transaction_fees
                )
            ivInfo.singleClick {
                AdminFeeBottomSheet.createInstance(viewModel.isTrainCategory()).show(
                    supportFragmentManager,
                    AdminFeeBottomSheet.TAG
                )
            }
            tvTotalAmount.text = Utils.formatAmount(orderDetail?.amount.orNil)
            when (adminFee) {
                0.0 -> {
                    adminFeeTxt.hideView()
                    tvDiscountedFee.text = getString(R.string.free_upper_case)
                }

                discountedFee -> {
                    adminFeeTxt.hideView()
                    tvDiscountedFee.text = Utils.formatAmount(adminFee)
                }

                else -> {
                    adminFeeTxt.text = Utils.formatAmount(adminFee)
                    tvDiscountedFee.text = Utils.formatAmount(discountedFee)
                }
            }
        }
    }

    private fun openPpobStatusScreen() {
        val category = orderDetail?.items?.firstOrNull()?.beneficiary?.category.orEmpty()
        val pdtInfo = getPdtInfo(category)
        startActivity(
            PpobStatusActivity.createIntent(
                context = this@PpobOrderFormActivity,
                orderId = orderDetail?.orderId.orEmpty(),
                category = category,
                imageUrl = orderDetail?.metadata?.logo.orEmpty(),
                pdtNumber = pdtInfo.pdtNumber,
                pdtName = pdtInfo.pdtName
            )
        )
    }

    private fun initViewBasedOnCategory() {
        with(binding.includeBillerInfo) {
            val category = orderDetail?.items?.firstOrNull()?.beneficiary?.category
            Glide.with(this@PpobOrderFormActivity)
                .load(orderDetail?.metadata?.logo)
                .error(
                    PpobConst.CATEGORY_DEFAULT_ICON[category]
                        ?: R.mipmap.ic_biller_default
                )
                .into(ivPpobUsecase)
            tvCategoryName.setText(
                PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob
            )
            if (category == PpobConst.CATEGORY_TRAIN_TICKET) {
                tvNumber.hideView()
            } else {
                tvNumber.text =
                    orderDetail?.items?.getOrNull(0)?.beneficiary?.accountNumber.orEmpty()
            }
        }
    }

    private val startPaymentPinActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.checkout(
                    intent.getBooleanExtra(IS_RECENT, false), intent.getBooleanExtra(
                        IS_REMINDER, false
                    ),
                    result.data?.getStringExtra(PpobConst.CHECKOUT_TOKEN).orEmpty()
                )
            }
        }

    private val startPaymentContactActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                Toast.makeText(
                    this@PpobOrderFormActivity,
                    result.data?.getStringExtra("message"),
                    Toast.LENGTH_LONG
                ).show()
                orderDetail?.customerProfile?.favouriteDetails =
                    result.data?.getParcelableExtra("favourite_detail")
                orderDetail?.customerProfile?.isFavorite = true
                recentsAndFavouritesView()
            }
        }

    private fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is PpobOrderFormViewModel.Event.UpdateSaldoRewardView -> setSaldoRewardCheckedView(
                    it.isChecked,
                    it.saldoBonus
                )

                is PpobOrderFormViewModel.Event.ShowPaymentMethod -> showPaymentMethod(
                    it.paymentMethod,
                    it.enoughSaldo,
                    it.saldoBonus,
                    it.isDisabled,
                    it.isSaldoFreezed
                )

                is PpobOrderFormViewModel.Event.ShowPpobPaymentMethodsBs -> {
                    paymentMethodViewModel.finproGetPaymentMethodsV2Response =
                        it.finproGetPaymentMethodsResponse
                    PaymentMethodBottomSheet.createInstance(
                        category = viewModel.getCategoryName(),
                        useSaldoReward = viewModel.getUseSaldoReward()
                    ).show(supportFragmentManager, PaymentMethodBottomSheet.TAG)
                }

                is PpobOrderFormViewModel.Event.ShowServerError -> showPaymentDownBottomSheet(
                    true,
                    it.message
                )

                is PpobOrderFormViewModel.Event.UpdateScreenWithFavouritesInfo -> {
                    if (it.isRemove) {
                        orderDetail?.run {
                            this.customerProfile?.favouriteDetails?.alias = null
                            this.customerProfile?.favouriteDetails?.id = null
                            this.customerProfile?.isFavorite = false
                            recentsAndFavouritesView()
                            viewModel.updateOrderDetail(this)
                        }
                    }
                }

                is PpobOrderFormViewModel.Event.ShowInternetError -> showPaymentDownBottomSheet(
                    false
                )

                is PpobOrderFormViewModel.Event.ShowPinForSaldo -> {
                    openActivityForResult(NewPaymentPinActivity::class.java, startPaymentPinActivityForResult) {
                        putString(NewPaymentPinActivity.USECASE, PinType.PIN_CONFIRM.toString())
                    }
                }

                is PpobOrderFormViewModel.Event.ProfitChange -> onAmountChanged(
                    it.amount,
                    it.hasSellingPrice
                )

                is PpobOrderFormViewModel.Event.OnCheckoutSuccess -> checkoutSuccess(
                    it.url,
                    it.prop,
                    it.paymentMethodCode
                )

                else -> {}
            }
        }
        viewModel.viewState.observe(this) {
            with(binding.includePaymentMethod) {
                if (it.showLoading) {
                    progressBar.showView()
                    btnCompletePayment.invisibleView()
                    return@observe
                }
                if (it.showToastMessage.isNotNullOrBlank()) {
                    Toast.makeText(
                        this@PpobOrderFormActivity,
                        it.showToastMessage,
                        Toast.LENGTH_LONG
                    ).show()
                }
                progressBar.hideView()
                btnCompletePayment.showView()
                when (it.healthState) {
                    HEALTH_OK -> {
                        tvWarning.hideView()
                    }

                    HEALTH_WARNING -> {
                        tvWarning.showView()
                        tvWarning.setDrawable(
                            left = R.drawable.ic_warning_yellow
                        )
                        tvWarning.text =
                            getString(R.string.ppob_outside_operational_msg)
                        includeLayout.root.hideView()
                    }

                    HEALTH_ERROR -> {
                        tvWarning.showView()
                        tvWarning.setDrawable(left = R.drawable.bg_warning_red)
                        tvWarning.text =
                            getString(R.string.ppob_outside_operational_msg)
                        btnCompletePayment.makeButtonInactive(this@PpobOrderFormActivity)
                        btnCompletePayment.singleClick { }
                        includeLayout.root.hideView()
                    }
                }
                if (it.isSaldoFreezed) setSaldoFreezeWarningUi()
                btnCompletePayment.isEnabled =
                    it.healthState != HEALTH_ERROR && it.enoughSaldo && !it.isDisabled && !it.isSaldoFreezed
            }
        }
    }

    private fun onAmountChanged(amount: Double, hasSellingPrice: Boolean) {
        with(binding.includePpobAmount) {
            if (amount >= 0) {
                tvProfit.text = getString(R.string.profit_text)
                tvProfit.setTextColor(getColorCompat(R.color.green_80))
                tvProfitAmount.setTextColor(getColorCompat(R.color.green_80))
            } else {
                tvProfit.text = getString(R.string.loss_text)
                tvProfit.setTextColor(getColorCompat(R.color.red_80))
                tvProfitAmount.setTextColor(getColorCompat(R.color.red_80))
            }
            if (!hasSellingPrice) {
                tvProfit.hideView()
                tvProfitAmount.hideView()
                vwDottedLine.hideView()
                return
            }
            tvProfit.showView()
            tvProfitAmount.showView()
            vwDottedLine.showView()
            tvProfitAmount.text = Utils.formatAmount(amount)
        }
    }

    private fun recentsAndFavouritesView() {
        if (viewModel.isVoucherGame() || viewModel.isTrainCategory()) {
            binding.includeBillerInfo.ivFavourite.hideView()
            return
        }
        with(binding.includeBillerInfo.ivFavourite) {
            showView()
            if (orderDetail?.customerProfile?.isFavorite == true) {
                setImageResource(R.drawable.ic_favourite_fill)
                singleClick {
                    PpobUtils.showRemoveFavouriteDialog(this@PpobOrderFormActivity) {
                        viewModel.removeFavourite(
                            orderDetail?.customerProfile
                        )
                    }
                }
            } else {
                setImageResource(R.drawable.ic_favourite_grey)
                singleClick { loadUserContactFragment() }
            }
        }
        setFavouritesView(orderDetail?.customerProfile?.isFavorite == true)
    }

    private fun loadUserContactFragment() {
        startPaymentContactActivityForResult.launch(
            PaymentContactActivity.createIntent(
                this,
                orderDetail?.orderId,
                PpobAnalytics.ORDER_FORM
            )
        )
    }

    private fun setFavouritesView(isFavourite: Boolean) = binding.billDetailView.setFavouriteData(
        orderDetail?.customerProfile?.favouriteDetails?.alias.orEmpty(),
        isFavourite = isFavourite
    )

    override fun favButtonClick() {
        showDialog(orderDetail?.customerProfile)
    }

    private fun showDialog(customerProfile: CustomerProfile?) {
        GenericConfirmationDialog(
            context = this,
            titleRes = R.string.remove_favourite,
            bodyRes = R.string.remove_favourite_subtitle,
            btnLeftRes = R.string.delete,
            btnRightRes = R.string.batal,
            rightBtnCallback = { },
            leftBtnCallback = {
                viewModel.removeFavourite(customerProfile)
            }
        ).show()
    }

    private fun showPaymentMethod(
        paymentMethod: FinproPaymentMethod?,
        enoughSaldo: Boolean,
        saldoBonus: SaldoBonus?,
        isDisabled: Boolean,
        isSaldoFreezed: Boolean
    ) {
        paymentMethod ?: return
        val type = viewModel.getAnalyticsType()
        val map = HashMap<String, String>()
        map[PpobAnalytics.PAYMENT_METHOD] = paymentMethod.code.orEmpty().lowercase()
        map["ppob_type"] = type
        map[PpobAnalytics.CURRENT_SALDO_BONUS_BALANCE] = viewModel.getTotalSaldoBonus().toString()
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_SELECT_PAYMENT_METHOD, map)
        setSaldoRewardView(saldoBonus)
        with(binding.includePaymentMethod) {
            Glide.with(this@PpobOrderFormActivity)
                .load(paymentMethod.logo)
                .placeholder(R.drawable.ic_bank)
                .error(R.drawable.ic_bank)
                .into(ivPayMethodIcon)
            tvPaymentMethodName.text = paymentMethod.name
            if (paymentMethod.code == PpobConst.SALDO_METHOD) {
                when {
                    isSaldoFreezed -> {
                        tvPaymentAmount.text =
                            " - ${Utils.formatAmount(paymentMethod.details?.saldoBalance)}"
                        tvPaymentAmount.showView()
                        includeLayout.root.hideView()
                        setSaldoFreezeWarningUi()
                    }

                    enoughSaldo -> {
                        tvPaymentAmount.text =
                            " - ${Utils.formatAmount(paymentMethod.details?.saldoBalance)}"
                        tvPaymentAmount.showView()
                        includeLayout.root.hideView()
                    }

                    else -> {
                        includeLayout.root.showView()
                        includeLayout.clLayout.background =
                            getDrawableCompat(R.drawable.bg_rounded_rectangle_red_5)
                        tvPaymentAmount.showView()
                        tvPaymentAmount.text =
                            " - ${Utils.formatAmount(paymentMethod.details?.saldoBalance)}"
                        tvPaymentAmount.setTextColor(getColorCompat(R.color.red_60))
                        includeLayout.tvMessage.text = getString(R.string.not_enough_balance)
                        includeLayout.tvMessage.setDrawable(left = 0)
                        includeLayout.btnCheck.text = getString(R.string.topup_saldo)
                        includeLayout.tvMessage.setTextColor(getColorCompat(R.color.red_60))
                        includeLayout.btnCheck.setStyleButtonColorFill(
                            this@PpobOrderFormActivity,
                            R.color.red_80,
                            R.color.white
                        )
                        includeLayout.btnCheck.singleClick { topupSaldoInitiated() }
                    }
                }
                setCashbackText()
                enableSaldoReward(true, saldoBonus)
                binding.includePaymentMethod.btnCompletePayment.isEnabled =
                    enoughSaldo && !isDisabled && !isSaldoFreezed
            } else {
                enableSaldoReward(false, saldoBonus)
                clCashBack.hideView()
                tvPaymentAmount.hideView()
                setUseSaldoMethod()
            }
        }
    }

    private fun setSaldoRewardView(saldoBonus: SaldoBonus?) {
        with(binding.includeSaldoReward) {
            val showSaldoReward = saldoBonus != null && saldoBonus.available?.isNotZero().isTrue
            this.root.visibility = showSaldoReward.asVisibility()
            tvTitle.text =
                getString(R.string.use_saldo_reward, Utils.formatAmount(saldoBonus?.available))
            tvSubtitle.text =
                getString(R.string.total_saldo_bonus, Utils.formatAmount(saldoBonus?.total))
            this.root.background = getDrawableCompat(R.drawable.bg_rounded_rectangle_blue_5_8dp)
            swReward.setOnCheckedChangeListener { _, isChecked ->
                setSaldoRewardCheckedView(isChecked, saldoBonus)
            }
        }
    }

    private fun setSaldoRewardCheckedView(isChecked: Boolean, saldoBonus: SaldoBonus?) {
        viewModel.setUseSaldoReward(isChecked)
        viewModel.onSellingPriceChanged(sellingPrice)
        viewModel.changePaymentMethod(viewModel.getSelectedPaymentMethod())
        val totalAmount =
            if (isChecked) orderDetail?.amount.orNil - saldoBonus?.available.orNil else orderDetail?.amount.orNil
        with(binding) {
            with(includeSaldoReward) {
                root.background = getDrawableCompat(if (isChecked) R.drawable.bg_rounded_rectangle_blue_5_8dp else R.drawable.bg_rounded_rectangle_white_8dp)
                swReward.isChecked = isChecked
            }
            with(includePpobAmount) {
                tvSaldoReward.visibility = isChecked.asVisibility()
                tvSaldoRewardValue.visibility = isChecked.asVisibility()
                tvSaldoRewardValue.text =
                    "-" + Utils.formatAmount(saldoBonus?.available)
                tvTotalAmount.text = Utils.formatAmount(totalAmount)
            }
            with(includePaymentMethod) {
                tvSaldoReward.visibility = isChecked.asVisibility()
                btnCompletePayment.text = getString(
                    R.string.payment_button_text,
                    Utils.formatAmount(totalAmount)
                )
            }
        }
    }

    private fun enableSaldoReward(enable: Boolean, saldoBonus: SaldoBonus?) {
        with(binding.includeSaldoReward) {
            if (enable) {
                swReward.isEnabled = true
                this.root.background = getDrawableCompat(R.drawable.bg_rounded_rectangle_white_8dp)
                tvTitle.showView()
            } else {
                swReward.isChecked = false
                swReward.isEnabled = false
                tvTitle.hideView()
                tvSubtitle.text = getString(
                    R.string.saldo_reward_disabled,
                    Utils.formatAmount(saldoBonus?.total)
                )
                this.root.background = getDrawableCompat(R.drawable.bg_rounded_rectangle_blue_5_8dp)
            }
        }
    }

    private fun topupSaldoInitiated() {
        updatingSaldoBalance = true
        openActivity(SaldoTopupActivity::class.java)
    }

    private fun setCashbackText() {
        with(binding.includePaymentMethod) {
            if (saldoUseCaseCampaign.isNullOrEmpty()) {
                clCashBack.hideView()
            } else {
                clCashBack.showView()
                tvCashbackAmount.text = getString(
                    R.string.show_saldo_cashback,
                    Utils.formatAmount(saldoUseCaseCampaign?.getOrNull(0)?.amount)
                )
            }
        }
    }

    private fun setUseSaldoMethod() {
        with(binding.includePaymentMethod.includeLayout) {
            val paymentChannelsList = viewModel.getPaymentChannelsList()
            val saldoPaymentChannel =
                paymentChannelsList.find { it.code == PpobConst.SALDO_METHOD }
            saldoPaymentChannel?.let {
                if (!saldoCampaign.isNullOrEmpty()) {
                    root.showView()
                    tvMessage.text = saldoCampaign?.getOrNull(0)?.displayText.orEmpty()
                    tvMessage.setDrawable(left = R.drawable.ic_gift)
                    tvMessage.setTextColor(getColorCompat(R.color.blue_80))
                } else {
                    root.hideView()
                }
                btnCheck.showView()
                clLayout.background =
                    getDrawableCompat(R.drawable.bg_rounded_rectangle_blue_5)
                btnCheck.singleClick {
                    val paymentMethodChannel: String
                    val paymentMethodFamily: String
                    if (saldoCampaign.isNullOrEmpty()) { // changing the payment method to saldo
                        paymentMethodChannel =
                            it.code.orEmpty()
                        paymentMethodFamily =
                            PpobConst.SALDO_FAMILY_CODE
                        trackChangeToSuggestedPaymentMethod(
                            false,
                            PpobAnalytics.SALDO
                        )
                    } else { // we can change the method to any payment method based on the response from the backend
                        paymentMethodChannel =
                            saldoCampaign?.getOrNull(0)?.paymentMethodChannel.orEmpty()
                        paymentMethodFamily =
                            saldoCampaign?.getOrNull(0)?.paymentMethodfamily.orEmpty()
                        binding.includePaymentMethod.clCashBack.showView()
                        binding.includePaymentMethod.tvCashbackAmount.text = getString(
                            R.string.show_saldo_cashback,
                            Utils.formatAmount(
                                kotlin.math.min(
                                    saldoUseCaseCampaign?.getOrNull(0)?.amount.orNil + saldoCampaign?.getOrNull(
                                        0
                                    )?.amount.orNil, orderDetail?.amount.orNil
                                )
                            )
                        )
                        trackChangeToSuggestedPaymentMethod(
                            true,
                            PpobAnalytics.SALDO
                        )
                    }
                    viewModel.setCashbackPaymentMethod(
                        paymentMethodChannel,
                        paymentMethodFamily
                    )
                }
            } ?: run {
                root.hideView()
            }
        }
    }

    private fun trackChangeToSuggestedPaymentMethod(
        isCashback: Boolean,
        paymentMethodName: String
    ) {
        val action =
            if (isCashback) PpobAnalytics.CASHBACK else PpobAnalytics.CHANGE_PAYMENT_METHOD
        val actionPaymentMethod = action + "_" + paymentMethodName
        val map = HashMap<String, String>()
        map[PpobAnalytics.ACTION_TYPE] = actionPaymentMethod
        map[PpobAnalytics.PPOB_TYPE] = PpobConst.CATEGORY_ANALYTICS_MAP[viewModel.getCategoryName()].toString()
        map[PpobAnalytics.BUYING_PRICE] = Utils.formatAmount(orderDetail?.amount)
        Analytics.trackEvent(PpobAnalytics.EVENT_PAYMENT_METHOD_NUDGE, map)
    }

    private fun checkoutSuccess(
        url: String?,
        map: HashMap<String, String>,
        paymentMethodCode: String?
    ) {
        val type = viewModel.getAnalyticsType()
        map[PpobAnalytics.TYPE] = type
        map[PpobAnalytics.PAYMENT_STATUS] = PpobAnalytics.INCOMPLETE
        map[PpobAnalytics.PPOB_STATUS] = PpobAnalytics.INCOMPLETE
        map[PpobAnalytics.CUSTOMER_ID] =
            orderDetail?.items?.firstOrNull()?.beneficiary?.phoneNumber.toString()
        map[PpobAnalytics.PAYMENT_METHOD] = paymentMethodCode.orEmpty().lowercase()
        if (from.isNotNullOrEmpty()) {
            map[PpobAnalytics.ENTRY_POINT] = PpobAnalytics.HOMEPAGE
        }
        map[PpobAnalytics.TRANSACTION_TYPE] = viewModel.getPurchaseTypeAnalytics()
        map[PpobAnalytics.ENABLE_SALDO_BONUS] = viewModel.getUseSaldoReward().toString()
        map[PpobAnalytics.NOMINAL_SALDO_BONUS_USED] =
            if (viewModel.getUseSaldoReward()) viewModel.getAvailableSaldoBonus().toString()
            else "Rp0"
        map[PpobAnalytics.CURRENT_SALDO_BONUS_BALANCE] = viewModel.getTotalSaldoBonus().toString()
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_CREATED, map)


        if (url.isNotNullOrBlank()) {
            goToDestination(HomePageActivity::class.java)
            openActivity(WebviewActivity::class.java) {
                putString(ClassConstants.WEBVIEW_URL, url)
            }
        } else {
            openPpobStatusScreen()
        }
        finish()
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, "PaymentDownBottomSheet")
    }

    override fun onBackPressed() {
        if (viewModel.isVoucherGame()) {
            val webUrl =
                if (orderDetail?.items?.firstOrNull()?.beneficiary?.code.isNotNullOrBlank())
                    BuildConfig.VOUCHER_GAME_PRODUCTS_URL + (orderDetail?.items?.firstOrNull()?.beneficiary?.code.orEmpty()) + "/" + Utils.getPaymentAccountId()
                else
                    BuildConfig.VOUCHER_GAME_URL + Utils.getPaymentAccountId()
            openActivity(WebviewActivity::class.java) {
                putString(ClassConstants.WEBVIEW_URL, webUrl)
                putString("title", getString(R.string.voucher_game1))
            }
            finish()
        } else
            super.onBackPressed()
    }


    fun clickAction(isSales: Boolean) {
        binding.includePpobAmount.etAmount.visibility = isSales.asVisibility()
        binding.includePpobAmount.tvTitle.visibility = isSales.asVisibility()
        viewModel.onSellingPriceChanged(if (isSales) getNumberValue() else 0L)
        viewModel.setPurchaseType(if (isSales) SALES else EXPENSE)
    }

    private fun getNumberValue(): Long {
        val fractionDecimalFormat =
            (NumberFormat.getNumberInstance(Locale("ID", "id")) as DecimalFormat)
        val numbersOnly = binding.includePpobAmount.etAmount.text.toString().filter { it.isDigit() }
        return if (numbersOnly.isEmpty()) {
            0
        } else {
            fractionDecimalFormat.parse(numbersOnly)!!.toLong()

        }
    }

    override fun onButtonClicked() {
        viewModel.reloadData()
    }

    override fun onResume() {
        super.onResume()
        if (updatingSaldoBalance) {
            viewModel.reloadData()
            updatingSaldoBalance = false
        }
    }

    override fun changePaymentMethod(
        finproPaymentMethod: FinproPaymentMethod,
        dailySaldoLimit: Double?,
        monthlySaldoLimit: Double?,
        eventProperty: HashMap<String, String>?
    ) {
        viewModel.changePaymentMethod(finproPaymentMethod)
    }

    override fun onDestroy() {
        timer?.cancel()
        super.onDestroy()
    }

    override fun changeUseSaldoReward(useSaldoReward: Boolean) {
        viewModel.updateSaldoRewardView(useSaldoReward)
    }

    private fun getPdtInfo(category: String): PdtInfo {
        val item = orderDetail?.items?.firstOrNull()
        return when (category) {
            PpobConst.CATEGORY_PULSA -> {
                PdtInfo(item?.beneficiary?.phoneNumber.orEmpty(), item?.name.orEmpty())
            }

            PpobConst.CATEGORY_LISTRIK -> {
                PdtInfo(
                    item?.beneficiary?.accountNumber.orEmpty() + "-" + item?.details?.customerName.orEmpty(),
                    item?.name.orEmpty()
                )
            }

            PpobConst.CATEGORY_EWALLET -> {
                PdtInfo(
                    item?.beneficiary?.accountNumber.orEmpty() + "-" + item?.details?.customerName.orEmpty(),
                    if (item?.details?.adminFee?.isNotZero().isTrue) {
                        Utils.formatAmount(item?.details?.amount ?: orderDetail?.amount)
                    } else {
                        item?.name.orEmpty()
                    }
                )
            }

            PpobConst.CATEGORY_PAKET_DATA -> {
                PdtInfo(item?.beneficiary?.phoneNumber.orEmpty(), item?.name.orEmpty())
            }

            PpobConst.CATEGORY_PLN_POSTPAID -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.beneficiary?.accountNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_LISTRIK_POSTPAID -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.beneficiary?.accountNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_PULSA_POSTPAID -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.beneficiary?.phoneNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_VOUCHER_GAME -> {
                PdtInfo(
                    if (orderDetail?.metadata?.billerType.isNotNullOrBlank() && orderDetail?.metadata?.billerType == PpobConst.VOUCHER_TYPE_TOPUP) orderDetail?.metadata?.idFieldValue.orEmpty() else item?.beneficiary?.phoneNumber.orEmpty(),
                    item?.name.orEmpty()
                )
            }

            PpobConst.CATEGORY_BPJS -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.details?.customerNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_PDAM -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.details?.customerNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_MULTIFINANCE -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.details?.customerNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.details?.customerNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_VEHICLE_TAX -> {
                PdtInfo(
                    item?.details?.customerName.orEmpty(),
                    item?.details?.policyNumber.orEmpty()
                )
            }

            PpobConst.CATEGORY_TRAIN_TICKET -> {
                val numberOfAdult =
                    item?.details?.trainPassenger?.filter { passenger -> passenger.type == PpobConst.ADULT_TYPE }?.size.orNil
                val numberOfChild =
                    item?.details?.trainPassenger?.filter { passenger -> passenger.type == PpobConst.INFANT_TYPE }?.size.orNil
                PdtInfo(
                    "${item?.details?.trainOriginStationCode} - ${item?.details?.trainDestinationStationCode}",
                    if (numberOfChild > 0) {
                        "$numberOfAdult ${getString(R.string.adult)}, $numberOfChild ${getString(R.string.baby)}"
                    } else {
                        "$numberOfAdult ${getString(R.string.adult)}"
                    }
                )
            }

            else -> PdtInfo("", "")
        }
    }

    private data class PdtInfo(
        val pdtNumber: String,
        val pdtName: String
    )

    private fun setSaldoFreezeWarningUi() = with(binding.includePaymentMethod) {
        tvWarning.apply {
            showView()
            setDrawable(left = R.drawable.ic_alert_red_bg)
            text = SpannableStringBuilder(getString(R.string.saldo_freeze_message)).boldText(
                getString(R.string.saldo_freeze_message_highlight_text), ignoreCase = true
            )
            singleClick {
                SaldoFreezeBottomSheet.createInstance()
                    .show(supportFragmentManager, SaldoFreezeBottomSheet.TAG)
            }
        }
        includeLayout.root.hideView()
    }
}