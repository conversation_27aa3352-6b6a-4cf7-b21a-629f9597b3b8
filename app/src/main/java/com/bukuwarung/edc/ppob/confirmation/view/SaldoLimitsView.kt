package com.bukuwarung.payments.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutSaldoLimitsBinding
import com.bukuwarung.edc.payments.data.model.SaldoResponse
import com.bukuwarung.edc.util.TextUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.singleClick


class SaldoLimitsView(context: Context, attrs: AttributeSet) : ConstraintLayout(context, attrs) {
    private val binding: LayoutSaldoLimitsBinding =
        LayoutSaldoLimitsBinding.inflate(LayoutInflater.from(context), this, true)

    interface Callback {
        fun showSaldoLimits(saldoResponse: SaldoResponse)
    }

    /**
     * This method will not show info icon to show more details about saldo limits for each tier
     */
    fun setViewWithHiddenInfoIcon(
        remainingDailyLimit: Double?, remainingMonthlyLimit: Double?
    ) {
        setLimitsText(
            remainingDailyLimit.orNil,
            remainingMonthlyLimit.orNil,
            dailyLimitText = R.string.remaining_daily_saldo_limit_is,
            monthlyLimitText = R.string.remaining_monthly_saldo_limit_is
        )
        binding.flSaldoLimitContainer.background = null
        binding.tvSaldoLimit.setDrawable()
    }

    /**
     * This method will show info icon and open up SaldoLimitsBottomSheet
     */
    fun setView(
        saldoResponse: SaldoResponse,
        showLimitsInfo: Boolean,
        callback: Callback?
    ) {
        val remainingDailyLimit =
            saldoResponse.debitDailyLimit.orNil - saldoResponse.debitDaily.orNil
        val remainingMonthlyLimit =
            saldoResponse.debitMonthlyLimit.orNil - saldoResponse.debitMonthly.orNil
        setLimitsText(
            remainingDailyLimit,
            remainingMonthlyLimit,
            dailyLimitText = R.string.remaining_daily_saldo_limit_is,
            monthlyLimitText = R.string.remaining_monthly_saldo_limit_is
        )
        binding.tvSaldoLimit.apply {
            if (showLimitsInfo) {
                setDrawable(right = R.drawable.ic_outline_info)
                singleClick { callback?.showSaldoLimits(saldoResponse) }
            } else {
                setDrawable()
            }
        }
    }

    private fun setLimitsText(
        remainingDailyLimit: Double, remainingMonthlyLimit: Double,
        dailyLimitText: Int, monthlyLimitText: Int,
    ) {
        if (remainingMonthlyLimit >= remainingDailyLimit) {
            val dailyLimitAmount = Utils.formatAmount(remainingDailyLimit)
            binding.tvSaldoLimit.text = TextUtils.decorateTextString(
                context.getString(
                    dailyLimitText,
                    dailyLimitAmount
                ),
                hashMapOf(dailyLimitAmount to TextUtils.TextDecorations(bold = true))
            )
        } else {
            val monthlyLimitAmount = Utils.formatAmount(remainingMonthlyLimit)
            binding.tvSaldoLimit.text = TextUtils.decorateTextString(
                context.getString(
                    monthlyLimitText,
                    monthlyLimitAmount
                ),
                hashMapOf(monthlyLimitAmount to TextUtils.TextDecorations(bold = true))
            )
        }
    }
}
