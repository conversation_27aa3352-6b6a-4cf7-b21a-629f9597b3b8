package com.bukuwarung.edc.ppob.confirmation.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.payments.usecase.PaymentUseCase
import com.bukuwarung.edc.ppob.constants.PpobUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.ifNull
import com.bukuwarung.edc.util.isFalseOrNull
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.ppob.common.model.FinproGetPaymentMethodsV2Response
import com.bukuwarung.network.utils.ResourceState
import com.bukuwarung.payments.data.model.PaymentMethod
import com.bukuwarung.payments.data.model.PaymentMethodsResponse
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class PaymentMethodViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val finproUseCase: FinproUseCase
) : ViewModel() {

    private fun currentViewState(): ViewState = viewState.value!!
    private val _viewState: MutableLiveData<ViewState> = MutableLiveData<ViewState>(
        ViewState()
    )
    val viewState: LiveData<ViewState> = _viewState
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    var saldoBalance: Double? = null
    private var paymentMethodRes: PaymentMethodsResponse? = null
    var finproGetPaymentMethodsV2Response: FinproGetPaymentMethodsV2Response? = null
    var dailySaldoLimit: Double? = null
    var monthlySaldoLimit: Double? = null

    sealed class Event {
        data class PaymentMethodsData(
            val paymentMethods: FinproGetPaymentMethodsV2Response?,
            val dailySaldoLimit: Double? = null,
            val monthlySaldoLimit: Double? = null
        ) : Event()

        data class PaymentMethodMapper(
            val response: PaymentMethodsResponse?,
            val saldoBalance: Double,
            val isBmsRegistered: Boolean,
            val dailySaldoLimit: Double,
            val monthlySaldoLimit: Double,
            val isSaldoFreezed: Boolean
        ) : Event()

        // ApiErrors are 3xx, 4xx and 5xx responses
        data class ApiError(val code: String, val message: String?) : Event()

        // ServerError is when server is unreachable, it comes as ApiErrorResponse
        // Can be due to service being down or no internet
        data class ConnectionError(val message: String?) : Event()
    }

    data class ViewState(
        val showLoading: Boolean = false,
    )

    private fun getPaymentMethods(
        paymentType: String,
        bankCode: String,
        amount: Double,
        balance: Double,
        isBmsRegistered: Boolean,
        dailySaldoLimit: Double,
        monthlySaldoLimit: Double,
        isSaldoFreezed: Boolean
    ) = viewModelScope.launch(Dispatchers.IO) {
        _viewState.value = currentViewState().copy(showLoading = true)
        try {
            paymentUseCase.getPaymentMethods(
                Utils.getPaymentAccountId(), paymentType, bankCode, amount
            ).let {
                if (it.isSuccessful) {
//                        AppAnalytics.trackEvent(
//                            AnalyticsConst.EVENT_VISIT_PAYMENT_METHOD_SELECTION, AppAnalytics.PropBuilder()
//                                .put("payment_type", AnalyticsConst.OUT)
//                                .put(AnalyticsConst.AVAILABLE_METHODS, getAvailableMethods())
//                        )
                    withContext(Dispatchers.Main) {
                        paymentMethodRes = it.body()
                        eventStatus.value = Event.PaymentMethodMapper(
                            it.body(),
                            balance,
                            isBmsRegistered,
                            dailySaldoLimit,
                            monthlySaldoLimit,
                            isSaldoFreezed
                        )
                        _viewState.value = currentViewState().copy(showLoading = false)
                    }
                } else {
                    handleErrorResponse(
                        it.code().toString(),
                        it.errorMessage()
                    )
                }
            }
        } catch (e: Exception) {
        }
    }

    private suspend fun handleErrorResponse(code: String, message: String = "") {
        withContext(Dispatchers.Main) {
            val isApiError =
                message.isNotBlank() && message != Constant.NO_INTERNET_ERROR_MESSAGE
            if (isApiError) {
                eventStatus.value = Event.ApiError(code, message)
            } else {
                eventStatus.value = Event.ConnectionError(message)
            }
            _viewState.value = currentViewState().copy(showLoading = false)
        }
    }

    fun getFinalPaymentMethod(
        finproGetPaymentMethodsV2Response: FinproGetPaymentMethodsV2Response?,
        dailySaldoLimit: Double? = null,
        monthlySaldoLimit: Double? = null
    ) {
        eventStatus.value = Event.PaymentMethodsData(
            finproGetPaymentMethodsV2Response,
            dailySaldoLimit,
            monthlySaldoLimit
        )
    }

    fun getSaldoBalance(paymentType: String, bankCode: String, amount: Double) = viewModelScope.launch(Dispatchers.IO) {
        try {
            when(val res = finproUseCase.getSaldo()){
                is ResourceState.Success ->{
                    saldoBalance = res.data.subBalance?.saldo.ifNull { res.data.amount }
                    getPaymentMethods(
                        paymentType,
                        bankCode,
                        amount,
                        saldoBalance.orNil,
                        res.data.bmsRegistered.isTrue,
                        res.data.debitDailyLimit.orNil - res.data.debitDaily.orNil,
                        res.data.debitMonthlyLimit.orNil - res.data.debitMonthly.orNil,
                        res.data.isSaldoFreezed.isTrue
                    )
                }
                is ResourceState.Failure ->{
                    handleErrorResponse(res.getErrorCode()?:"", res.message?:"")
                }
                else -> {
                    //loading
                }
            }
        } catch (e: Exception) {
            handleErrorResponse("")
        }
    }

    private fun appendItemToSB(sb: StringBuilder, string: String?) {
        if (sb.isEmpty()) {
            sb.append(string)
        } else {
            sb.append(", ").append(string)
        }
    }

    private fun appendAvailableMethod(sb: StringBuilder, method: PaymentMethod) {
        if (method.enabled?.status.isTrue)
            appendItemToSB(sb, method.code)
    }

    fun getAvailableMethods(): String {
        val sb = StringBuilder()
        paymentMethodRes?.paymentMethods?.let { payMethods ->
            payMethods.bankTransfers?.forEach { appendAvailableMethod(sb, it) }
            payMethods.eWallets?.forEach { appendAvailableMethod(sb, it) }
            payMethods.retailOutlets?.forEach { appendAvailableMethod(sb, it) }
            payMethods.buku?.forEach { appendAvailableMethod(sb, it) }
        }
        return sb.toString()
    }

    private fun appendUnAvailableMethod(sb: StringBuilder, method: PaymentMethod) {
        if (method.enabled?.status.isFalseOrNull)
            appendItemToSB(sb, "${method.code}(${PpobUtils.getReason(method.enabled?.reasonCode)})")
    }

    fun getUnAvailableMethods(): String {
        val sb = StringBuilder()
        paymentMethodRes?.paymentMethods?.let { payMethods ->
            payMethods.bankTransfers?.forEach { appendUnAvailableMethod(sb, it) }
            payMethods.eWallets?.forEach { appendUnAvailableMethod(sb, it) }
            payMethods.retailOutlets?.forEach { appendUnAvailableMethod(sb, it) }
            payMethods.buku?.forEach { appendUnAvailableMethod(sb, it) }
        }
        return sb.toString()
    }

    private fun appendUnAvailableReason(sb: StringBuilder, method: PaymentMethod) {
        if (method.enabled?.status.isFalseOrNull)
            appendItemToSB(sb, PpobUtils.getReason(method.enabled?.reasonCode))
    }

    fun getUnAvailableReasons(): String {
        val sb = StringBuilder()
        paymentMethodRes?.paymentMethods?.let { payMethods ->
            payMethods.bankTransfers?.forEach { appendUnAvailableReason(sb, it) }
            payMethods.eWallets?.forEach { appendUnAvailableReason(sb, it) }
            payMethods.retailOutlets?.forEach { appendUnAvailableReason(sb, it) }
            payMethods.buku?.forEach { appendUnAvailableReason(sb, it) }
        }
        return sb.toString()
    }
}