package com.bukuwarung.edc.ppob.confirmation.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.CustomerProfile
import com.bukuwarung.edc.payments.data.model.FinproCheckoutPayment
import com.bukuwarung.edc.payments.data.model.HealthStatus
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckRequest
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.payments.usecase.PaymentUseCase
import com.bukuwarung.edc.ppob.common.model.FinproGetPaymentMethodsV2Response
import com.bukuwarung.edc.ppob.confirmation.model.FinproGetPaymentMethodsResponse
import com.bukuwarung.edc.ppob.confirmation.model.FinproPaymentMethod
import com.bukuwarung.edc.ppob.confirmation.model.SaldoBonus
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.payments.data.model.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject
import kotlin.collections.HashMap

@HiltViewModel
class PpobOrderFormViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase,
    private val paymentUseCase: PaymentUseCase,
) : ViewModel()  {
    companion object {
        const val HEALTH_OK = 0
        const val HEALTH_WARNING = 1
        const val HEALTH_ERROR = 2
        const val SALES = "SALES"
        const val sales = "sales"
        const val purchase = "purchase"
        const val DISABLE = "DISABLE"

    }

    sealed class Event {
        data class UpdateSaldoRewardView(val isChecked: Boolean, val saldoBonus: SaldoBonus?): Event()
        data class ShowPaymentMethod(val paymentMethod: FinproPaymentMethod?, val enoughSaldo: Boolean, val saldoBonus: SaldoBonus? = null, val isDisabled: Boolean = false, val isSaldoFreezed: Boolean) : Event()
        data class ShowPpobPaymentMethodsBs(val finproGetPaymentMethodsResponse: FinproGetPaymentMethodsV2Response?) : Event()
        data class ProfitChange(val amount: Double, val hasSellingPrice: Boolean) : Event()
        data class OnCheckoutSuccess(val url: String?, val prop: HashMap<String,String>, val paymentMethodCode: String? = null) : Event()
        data class ShowServerError(val message: String?) : Event()
        object ShowInternetError : Event()
        object ShowPinForSaldo : Event()
        data class UpdateScreenWithFavouritesInfo(val isRemove: Boolean): Event()
    }

    data class ViewState(
        val showLoading: Boolean = false,
        val healthState: Int = HEALTH_OK,
        val enoughSaldo: Boolean = true,
        val showToastMessage: String? = null,
        val isDisabled: Boolean = false,
        val isSaldoFreezed: Boolean = false
    )

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private var paymentChannels = emptyList<FinproPaymentMethod>()
    private var paymentMethodsV2Response: FinproGetPaymentMethodsV2Response? = null
    private var sellingPrice = 0.0
    private lateinit var orderDetail: OrderResponse
    private var adminFee = 0.0
    private var method: FinproGetPaymentMethodsResponse? = null
    private var channelCode = ""
    var category = ""
    private var code = ""
    private var purchaseType = "SALES"
    private var useSaldoReward = false
    private var saldoBonus: SaldoBonus? = null
    private var selectedPaymentMethod: FinproPaymentMethod? = null

    fun init(orderDetail: OrderResponse) {
        category = orderDetail.items?.firstOrNull()?.beneficiary?.category.orEmpty()
        code = orderDetail.items?.firstOrNull()?.beneficiary?.code.orEmpty()
        this.orderDetail = orderDetail
        adminFee = orderDetail.items?.firstOrNull()?.fee.orNil
        getPaymentMethods()
    }

    fun updateOrderDetail(orderDetail: OrderResponse) {
        this.orderDetail = orderDetail
    }

    fun setCashbackPaymentMethod(paymentMethod: String?, paymentMethodFamily: String?) {
        val paymentMethodSelected = paymentChannels.filter { it.code == paymentMethod}.getOrNull(0)
        changePaymentMethod(paymentMethodSelected)
    }

    fun onSellingPriceChanged(amount: Long) {
        sellingPrice = amount.toDouble()
        var costOfProduct = orderDetail.amount.orNil
        if (useSaldoReward) costOfProduct -= saldoBonus?.available.orNil
        eventStatus.value = Event.ProfitChange(sellingPrice - costOfProduct, sellingPrice != 0.0)
    }

    fun getSelectedPaymentMethod() = selectedPaymentMethod

    fun changePaymentMethod(paymentMethod: FinproPaymentMethod?) = viewModelScope.launch {
        //update is_default in the paymentMethodsV2response
        selectedPaymentMethod = paymentMethod
        paymentMethodsV2Response?.highlightedPaymentChannels?.channels?.forEach {
            it.details?.isSelected = it.code?.equals(paymentMethod?.code).isTrue
        }
        paymentMethodsV2Response?.otherPaymentChannels?.methods?.forEach { finproPaymentMethod ->
            finproPaymentMethod.channels.forEach {
                it.details?.isSelected = it.code?.equals(paymentMethod?.code).isTrue
            }
        }
        for(channel in paymentChannels){
            if(paymentMethod?.code.equals(channel.code)){
                method = FinproGetPaymentMethodsResponse(code = channel.family.orEmpty())
                break
            }
        }
        channelCode = paymentMethod?.code.orEmpty()
        val enoughSaldo = checkEnoughSaldoBalance(paymentMethod)
        val isSaldoFreezed = paymentMethod?.details?.isSaldoFreezed.isTrue
        setViewState(currentViewState().copy(enoughSaldo = enoughSaldo, isSaldoFreezed = isSaldoFreezed, isDisabled = isPaymentMethodDisabled(paymentMethod)))
        eventStatus.value = Event.ShowPaymentMethod(
            paymentMethod,
            enoughSaldo,
            saldoBonus,
            isPaymentMethodDisabled(paymentMethod),
            isSaldoFreezed
        )
    }

    private fun isPaymentMethodDisabled(paymentMethod: FinproPaymentMethod?) =
        paymentMethod?.tag?.tagType?.equals(DISABLE, true).isTrue ||
                paymentMethod?.tag?.tagType?.equals(PaymentConst.DAILY_LIMIT_REACHED, true).isTrue ||
                paymentMethod?.tag?.tagType?.equals(PaymentConst.MONTHLY_LIMIT_REACHED, true).isTrue ||
                paymentMethod?.code?.isBlank().isTrue ||
                paymentMethod?.family?.isBlank().isTrue

    private fun checkEnoughSaldoBalance(paymentMethod: FinproPaymentMethod?): Boolean {
        if (paymentMethod?.details?.saldoBalance != null) {
            val balance =
                if (useSaldoReward) paymentMethod.details.saldoBalance.toDouble() + saldoBonus?.available.orNil else paymentMethod.details.saldoBalance.toDouble()
            return balance >= adminFee + orderDetail.amount.orNil
        }
        return true // default value is true here because above if condition is used to check if the payment method is saldo and if other payment method is select then we should still let user continue to do the payment.
    }

    fun setUseSaldoReward(flag: Boolean) {
        useSaldoReward = flag
    }

    fun getUseSaldoReward() = useSaldoReward

    fun getAvailableSaldoBonus() = saldoBonus?.available.orNil
    fun getTotalSaldoBonus() = saldoBonus?.total.orNil

    fun updateSaldoRewardView(isChecked: Boolean) = viewModelScope.launch {
        setEventStatus(Event.UpdateSaldoRewardView(isChecked, saldoBonus))
    }

    private fun getPaymentMethods() = viewModelScope.launch {
        setViewState(currentViewState().copy(showLoading = true))
        withContext(Dispatchers.IO) {
            try {
                finproUseCase.getPaymentMethodsV2(orderDetail.amount.orNil, category, getCashbackProductCode()).let {
                    if (it.isSuccessful) {
                        paymentMethodsV2Response = it.body()
                        paymentChannels = it.body()?.highlightedPaymentChannels?.channels.orEmpty()
                        it.body()?.otherPaymentChannels?.methods?.forEach { methods ->
                            paymentChannels = paymentChannels + methods.channels
                        }
                        for (channel in paymentChannels) {
                            if(channel.details?.isSelected.isTrue){
                                selectedPaymentMethod = channel
                                break
                            }
                        }
                        getSaldoBonus()
                        if (selectedPaymentMethod == null && paymentChannels.isNotEmpty()) {
                            selectedPaymentMethod = paymentChannels.first()
                        }
                        method = FinproGetPaymentMethodsResponse(code = selectedPaymentMethod?.family.orEmpty())
                        channelCode = selectedPaymentMethod?.code.orEmpty()
                        val enoughSaldo = checkEnoughSaldoBalance(selectedPaymentMethod)
                        val isSaldoFreezed = selectedPaymentMethod?.details?.isSaldoFreezed.isTrue
                        setViewState(
                            currentViewState().copy(
                                showLoading = false,
                                enoughSaldo = enoughSaldo,
                                isSaldoFreezed = isSaldoFreezed,
                                isDisabled = isPaymentMethodDisabled(selectedPaymentMethod)
                            )
                        )
                        setEventStatus(
                            Event.ShowPaymentMethod(
                                selectedPaymentMethod,
                                enoughSaldo,
                                saldoBonus,
                                isPaymentMethodDisabled(selectedPaymentMethod),
                                isSaldoFreezed
                            )
                        )
                        doHealthCheck()
                    } else {
                        if (it.errorMessage().isNotBlank() && it.errorMessage() != Constant.NO_INTERNET_ERROR_MESSAGE) {
                            setViewState(currentViewState().copy(showLoading = false))
                            setEventStatus(Event.ShowServerError(it.errorMessage()))
                        } else {
                            setViewState(currentViewState().copy(showLoading = false))
                            setEventStatus(Event.ShowInternetError)
                        }
                    }
                }
            } catch (e: Exception) {
                setViewState(currentViewState().copy(showLoading = false))
                setEventStatus(Event.ShowServerError(""))
            }
        }
    }

    private fun getSaldoBonus(){
        for(channel in paymentChannels){
            if (channel.code.equals((PaymentConst.SALDO))) {
                saldoBonus = channel.details?.saldoBonus
            }
        }
    }

    private fun getCashbackProductCode() =
        orderDetail.campaigns?.find {
            it.type == PpobConst.CASHBACK_TYPE && it.paymentMethodChannel == PpobConst.SALDO_METHOD
        }?.productCode ?: orderDetail.campaigns?.find {
            it.type == PpobConst.CASHBACK_TYPE && it.paymentMethodChannel == PpobConst.ALL_METHOD
        }?.productCode.orEmpty()

    private suspend fun doHealthCheck() {
        val request = PaymentHealthCheckRequest()
        try {
            paymentUseCase.doHealthCheck(request).let {
                if (it.isSuccessful) {
                    setViewState(currentViewState().copy(healthState = HEALTH_OK))
                    iterateProviderHealthResponse(it.body()?.providers)
                    if (isHealthNotError()) {
                        iterateMoneyOutHealthResponse(it.body()?.moneyOut)
                        if (isHealthNotError()) {
                            iterateMoneyInHealthResponse(it.body()?.moneyIn?.paymentOut)
                        }
                    }
                }
            }
        } catch (_: Exception) {}
        //TODO: currently need to call 2 endpoint. later should be only 1 when backend combine this
        if (isHealthNotError()) {
            try {
                finproUseCase.doHealthCheck(request).let {
                    if (it.isSuccessful) {
                        iterateProviderHealthResponse(it.body()?.providers)
                        if (isHealthNotError()) {
                            iterateMoneyOutHealthResponse(it.body()?.moneyOut)
                            if (isHealthNotError()) {
                                iterateMoneyInHealthResponse(it.body()?.moneyIn?.paymentOut)
                            }
                        }
                    }
                }
            } catch (_: Exception) {}
        }
        if (isHealthNotError()) {
            val calendar = Calendar.getInstance()
            val hourNow = calendar.get(Calendar.HOUR_OF_DAY)
            if (hourNow < 7 || hourNow >= 23)
                setViewState(currentViewState().copy(healthState = HEALTH_WARNING))
        }
    }

    private fun isHealthNotError() = currentViewState().healthState != HEALTH_ERROR

    private suspend fun iterateProviderHealthResponse(list: List<HealthStatus>?) {
        setViewState(currentViewState().copy(healthState = HEALTH_OK))
        if (list.isNullOrEmpty()) return
        var currentMessage = ""
        for (status in list) {
            if (true == status.enabled) return
            else currentMessage = status.message.orEmpty()
        }
        setViewState(currentViewState().copy(healthState = HEALTH_ERROR))
    }

    private suspend fun iterateMoneyOutHealthResponse(list: List<HealthStatus>?) {
        if (list.isNullOrEmpty()) return
        for (status in list) {
            if (false == status.enabled) {
                setViewState(currentViewState().copy(healthState = HEALTH_ERROR))
            }
        }
    }

    private suspend fun iterateMoneyInHealthResponse(list: List<HealthStatus>?) {
        if (list.isNullOrEmpty()) return
        for (status in list) {
            if (channelCode == status.name && false == status.enabled) {
                setViewState(currentViewState().copy(healthState = HEALTH_ERROR))
            }
        }
    }

    fun reloadData() {
        getPaymentMethods()
    }

    fun submit(isRecent: Boolean, isReminder: Boolean) {
        if (method?.code.isNullOrBlank() && channelCode.isBlank()) return
        when(channelCode){
            PaymentConst.SALDO -> {
                eventStatus.value = Event.ShowPinForSaldo
                return
            }
            else -> {
                checkout(isRecent, isReminder)
            }
        }
    }

    fun checkout(isRecent: Boolean, isReminder: Boolean, checkoutToken: String = "") = viewModelScope.launch {
        setViewState(currentViewState().copy(showLoading = true))
        withContext(Dispatchers.IO) {
            val request = FinproCheckoutOrderRequest(
                listOf(FinproCheckoutPayment(method?.code.orEmpty(), channelCode)),
                listOf(
                    FinproCheckoutItem(
                        orderDetail.items?.firstOrNull()?.sku.orEmpty(),
                        sellingPrice
                    )
                ),
                purchaseType,
                useSaldoReward
            )
            try {
                finproUseCase.checkoutOrder(
                    Utils.getPaymentAccountId(),
                    orderDetail.orderId.orEmpty(),
                    request,
                    checkoutToken
                ).let {
                    if (it.isSuccessful) {
                        if (!it.body()?.payments.isNullOrEmpty()) {
                            val paymentMethodCode =
                                it.body()?.payments?.firstOrNull()?.paymentMethod?.code
                            val map = HashMap<String, String>()
                            map[PpobAnalytics.ENTRY_POINT] = PpobAnalytics.PPOB_FORM
                            map[PpobAnalytics.ORDER_ID] = it.body()?.orderId.toString()
                            map[PpobAnalytics.PROVIDER] =
                                if (category == PpobConst.CATEGORY_PDAM || category == PpobConst.CATEGORY_MULTIFINANCE || category == PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE) {
                                    it.body()?.items?.firstOrNull()?.name.toString()
                                } else {
                                    it.body()?.items?.firstOrNull()?.beneficiary?.code.toString()
                                }
                            map[PpobAnalytics.PACKAGE_PRICE] = (it.body()?.amount.orNil.toString())
                            map[PpobAnalytics.BUYING_PRICE] =
                                (it.body()?.amount.orNil + it.body()?.fee.orNil).toString()
                            map[PpobAnalytics.SELLING_PRICE] = sellingPrice.toString()
                            map[PpobAnalytics.PAYMENT_METHOD] =
                                paymentMethodCode.orEmpty().lowercase()
                            map[PpobAnalytics.PAYMENT_STATUS] = PpobAnalytics.INCOMPLETE
                            map[PpobAnalytics.PPOB_STATUS] = PpobAnalytics.INCOMPLETE
                            map[PpobAnalytics.FAVOURITE_STATUS] =
                                orderDetail.customerProfile?.isFavorite.toString()
                            map[PpobAnalytics.FAVOURITE_CONTACT_NAME] =
                                orderDetail.customerProfile?.favouriteDetails?.alias.toString()
                            // for saldo, url is null
                            setEventStatus(
                                Event.OnCheckoutSuccess(
                                    it.body()?.payments?.firstOrNull()?.paymentUrl,
                                    map,
                                    paymentMethodCode
                                )
                            )
                        }
                        setViewState(currentViewState().copy(showLoading = false))
                    } else {
                        if (it.errorMessage() != Constant.NO_INTERNET_ERROR_MESSAGE) {
                            setViewState(currentViewState().copy(showLoading = false))
                            setEventStatus(Event.ShowServerError(it.errorMessage()))
                        } else {
                            setViewState(currentViewState().copy(showLoading = false))
                            setEventStatus(Event.ShowInternetError)
                        }
                    }
                }
            } catch (e: NoConnectivityException){
                setViewState(currentViewState().copy(showLoading = false))
                setEventStatus(Event.ShowInternetError)
            } catch (e: Exception) {
                setViewState(currentViewState().copy(showLoading = false))
                setEventStatus(Event.ShowServerError(""))
            }
        }
    }

    fun removeFavourite(customerProfile: CustomerProfile?) = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.deleteFavourite(Utils.getPaymentAccountId(), customerProfile?.favouriteDetails?.id.orEmpty()).let {
                if (it.isSuccessful) {
                    setViewState(currentViewState().copy(showToastMessage = it.body()?.message?:"Berhasil menghapus pelanggan favorit"))
                    setEventStatus(Event.UpdateScreenWithFavouritesInfo(true))
                } else {
                    setViewState(currentViewState().copy(showToastMessage = it.errorMessage()))
                }
            }
        } catch (e: Exception) {
            setViewState(currentViewState().copy(showToastMessage = "Failed to do API Call"))
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    private suspend fun setViewState(state: ViewState) = withContext(Dispatchers.Main) {
        viewState.value = state
    }

    fun getAnalyticsType(): String {
        return PpobConst.CATEGORY_ANALYTICS_MAP[if (category == PpobConst.CATEGORY_LISTRIK && code == PpobConst.CATEGORY_PLN_POSTPAID) {
            PpobConst.CATEGORY_PLN_POSTPAID
        } else {
            category
        }].orEmpty()
    }

    fun isVoucherGame() = category == PpobConst.CATEGORY_VOUCHER_GAME

    fun isTrainCategory() = category == PpobConst.CATEGORY_TRAIN_TICKET

    fun getCategoryName() = category

    fun setPurchaseType(purchaseType: String) {
        this.purchaseType = purchaseType
    }

    fun getPurchaseTypeAnalytics(): String {
        return if(this.purchaseType == SALES) sales else purchase
    }

    fun getPaymentChannelsList(): List<FinproPaymentMethod> {
        return paymentChannels
    }

    fun showPpobBankAccountsBs(){
        eventStatus.value = Event.ShowPpobPaymentMethodsBs(paymentMethodsV2Response)
    }
}
