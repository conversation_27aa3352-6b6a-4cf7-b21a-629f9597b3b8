package com.bukuwarung.edc.ppob.confirmation.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.orNil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class PpobStatusViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase
) : ViewModel() {

    sealed class Event {
        object ShowPpobPendingUi : Event()
        object ShowPpobCompletedUi : Event()
        object ShowPpobFailedUi : Event()
        object HandleApiError : Event()
        data class ShowPpobReceipt(val orderResponse: OrderResponse?) : Event()
    }

    val eventStatus = MutableLiveData<Event>()
    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    private var orderId = ""
    private var amount = 0.0
    private val accountId = Utils.getPaymentAccountId()
    private var fetchPendingStatusInfo = true

    fun init(disbursementId: String) {
        this.orderId = disbursementId
    }

    fun getPpobStatus() = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.getPpobStatus(accountId, orderId).let {
                if (it.isSuccessful) {
                    amount = it.body()?.amount.orNil
                    when (it.body()?.status) {
                        PaymentHistory.STATUS_PENDING, PaymentHistory.STATUS_PAID -> {
                            if (fetchPendingStatusInfo) {
                                setEventStatus(Event.ShowPpobPendingUi)
                                fetchPendingStatusInfo = false
                            } else {}
                        }
                        PaymentHistory.STATUS_COMPLETED -> {
                            setEventStatus(Event.ShowPpobCompletedUi)
                            getOrderDetail()
                        }
                        else -> {
                            setEventStatus(Event.ShowPpobFailedUi)
                        }
                    }
                } else { setEventStatus(Event.HandleApiError) }
            }
        } catch (e: Exception) { setEventStatus(Event.HandleApiError) }
    }

    fun getPaymentAmount() = amount

    private fun getOrderDetail() = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.getOrderDetail(accountId, orderId, null).let {
                if (it.isSuccessful){
                    setEventStatus(Event.ShowPpobReceipt(it.body()))
                } else {
                    setEventStatus(Event.HandleApiError)
                }
            }
        } catch (e: Exception) {
            setEventStatus(Event.HandleApiError)
        }
    }

}