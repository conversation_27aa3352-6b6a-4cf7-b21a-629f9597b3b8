package com.bukuwarung.edc.ppob.constants

import com.bukuwarung.edc.ppob.common.model.PpobConfig
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.Utils
import com.google.gson.reflect.TypeToken

object PpobRemoteConfig {

    const val PPOB_CONFIG = "edc_ppob_config"
    const val PPOB_ERROR_POPUP_CONTENT = "edc_ppob_error_popup_content"
    const val EDC_HOMEPAGE_PPOB = "edc_homepage_ppob"
    const val EDC_SHOW_PPOB_TOP_SECTION = "edc_show_ppob_top_section"

    const val EDC_HOMEPAGE_PPOB_VALUE = """
        {
  "body_name": "edc_homepage_ppob",
  "body_analytics_name": "ppob",
  "body_title": "Pembayaran Produk Digital",
  "body_subtitle": "",
  "body_rank": 4,
  "body_type": 2,
  "no_of_columns": 4,
  "body_content": [
    {
      "display_name": "Pulsa",
      "analytics_name": "pulsa",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
      "rank": 1,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=PULSA",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Token\nListrik",
      "analytics_name": "token_listrik",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
      "rank": 2,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=LISTRIK",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Ewallet",
      "analytics_name": "ewallet",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
      "rank": 3,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=EMONEY",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Paket Data",
      "analytics_name": "packet_data",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
      "rank": 4,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=PAKET_DATA",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Pulsa\nPascabayar",
      "analytics_name": "pascabayar",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
      "rank": 5,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=PASCABAYAR",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "BPJS",
      "analytics_name": "bpjs",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
      "rank": 6,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=BPJS",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "PDAM",
      "analytics_name": "pdam",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
      "rank": 7,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=PDAM",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Tagihan\nListrik",
      "analytics_name": "tagihan_listrik",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
      "rank": 8,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=LISTRIK_POSTPAID",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Internet & TV Kabel",
      "analytics_name": "ppob_cable",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/internet-tv-cable.webp",
      "rank": 9,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=INTERNET_DAN_TV_KABEL",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Angsuran Kredit",
      "analytics_name": "multi_finance",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/multifinance.webp",
      "rank": 10,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=ANGSURAN",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "E-Samsat",
      "analytics_name": "e_samsat",
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/vehicle-tax.webp",
      "rank": 11,
      "is_visible": true,
      "start_version": 7,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity?category=VEHICLE_TAX",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    }
  ]
}
    """

    const val PPOB_ERROR_POPUP_CONTENT_VALUE = """
        [
  {
    "ic_ppob_error": "",
    "tv_ppob_error_heading": "Yah, sedang ada gangguan",
    "tv_ppob_error_body": "Saat ini fitur beli {category} sedang dalam perbaikan, tunggu kira-kira {timer} jam lagi!",
    "tv_ppob_error_button": "Kembali",
    "ppob_category": [
      "PULSA",
      "LISTRIK",
      "PLN_PREPAID",
      "PLN_POSTPAID",
      "LISTRIK_POSTPAID",
      "EMONEY",
      "PAKET_DATA",
      "PASCABAYAR",
      "VOUCHER_GAME",
      "BPJS",
      "PDAM",
      "REMINDER",
      "ANGSURAN",
      "INTERNET_DAN_TV_KABEL",
      "VEHICLE_TAX",
      "SET_SELLING_PRICE",
      "PROMOTIONS"
    ],
    "text_after_timer_complete": "Tunggu ya, sedang diperbaiki. Aplikasi akan normal dan kamu bisa coba logi.",
    "start_time_millis": 1655177522236,
    "error_wait_time_hrs": 2
  },
  {
    "ic_ppob_error": "",
    "tv_ppob_error_heading": "Yah, sedang ada gangguan1bhargav",
    "tv_ppob_error_body": "Saat ini fitur beli {category} sedang dalam perbaikan, tunggu kira-kira {timer} jam lagi!12kikel",
    "tv_ppob_error_button": "Kembali123kesa",
    "ppob_category": [
      "BPJS"
    ],
    "text_after_timer_complete": "Tunggu ya, sedang diperbaiki. Aplikasi akan normal dan kamu bisa coba logi.1234hfdsjfds",
    "start_time_millis": 1656484087985,
    "error_wait_time_hrs": 2
  }
]
    """


    const val PPOB_CONFIG_VALUE = """
        {
  "ppob_list": [
    {
      "display_name": "Pulsa",
      "analytics_name": "pulsa",
      "rank": 1,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/pulsa.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/pulsa.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "is_promo": true,
      "category": "PULSA"
    },
    {
      "display_name": "Token Listrik",
      "analytics_name": "token_listrik",
      "rank": 2,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/listrik.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/listrik.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "LISTRIK"
    },
    {
      "display_name": "E-Wallet",
      "analytics_name": "ewallet",
      "rank": 3,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/ewallet.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/ewallet.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "EMONEY"
    },
    {
      "display_name": "Paket Data",
      "analytics_name": "packet_data",
      "rank": 4,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/paket-data.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/paket-data.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "is_promo": true,
      "category": "PAKET_DATA"
    },
    {
      "display_name": "Tagihan Listrik",
      "analytics_name": "tagihan_listrik",
      "rank": 5,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/postpaid-listrik.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/postpaid-listrik.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "LISTRIK_POSTPAID"
    },
    {
      "display_name": "BPJS",
      "analytics_name": "bpjs",
      "rank": 6,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/bpjs.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/bpjs.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "BPJS"
    },
    {
      "display_name": "PDAM",
      "analytics_name": "pdam",
      "rank": 7,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/pdam.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/pdam.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "PDAM"
    },
    {
      "display_name": "Voucher Game",
      "analytics_name": "gaming_voucher",
      "rank": 8,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/voucher-game.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/voucher-game.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/voucher-game.webp",
      "state": "NOT_AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "VOUCHER_GAME"
    },
    {
      "display_name": "Pulsa\nPascabayar",
      "analytics_name": "pascabayar",
      "rank": 9,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/postpaid-pulsa.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/postpaid-pulsa.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "PASCABAYAR"
    },
    {
      "display_name": "Angsuran Kredit",
      "analytics_name": "multi_finance",
      "rank": 10,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/multifinance.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/multifinance.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/multifinance.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "ANGSURAN"
    },
    {
      "display_name": "Internet & TV",
      "analytics_name": "ppob_cable",
      "rank": 11,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/internet-tv-cable.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/internet-tv-cable.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/internet-tv-cable.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "INTERNET_DAN_TV_KABEL"
    },
    {
      "display_name": "E-Samsat",
      "analytics_name": "e_samsat",
      "rank": 12,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/vehicle-tax.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/vehicle-tax.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/vehicle-tax.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "VEHICLE_TAX"
    },
    {
      "display_name": "Tiket Kereta Api",
      "analytics_name": "ppob_train_tickets",
      "rank": 13,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/train.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/train.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/train.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "TRAIN_TICKET"
    }
  ],
  "trainPollingConfig": {
    "pollingTimeInSeconds": 300,
    "pollingInternalSeconds": 10,
    "redirectionUrlContains": "/redirect"
  },
  "supportUrls": {
    "pulsa": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/pulsa/",
    "tokenListrik": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/token-listrik/",
    "eWallet": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/e-wallet",
    "packetData": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/paket-data/",
    "postpaidListrik": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/tagihan-listrik",
    "voucherGame": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/voucher-game/",
    "bpjs": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/bpjs",
    "pdam": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/pdam",
    "multiFinance": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/angsuran-kredit/",
    "internetTvCable": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/internet-tv-kabel",
    "vehicleTax": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/e-samsat",
    "trainTickets": "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/tiket-kereta-api"
  },
  "freeFormEwalletCap": 5000,
  "ppobPollingConfig": {
    "ppobStatusPollingIntervalSeconds": 5,
    "ppobStatusPollingTotalTimeSeconds": 50
  }
}
    """

    fun getPpobConfigs(): PpobConfig {
        val json = getRemoteConfigString(PPOB_CONFIG)
        val type = object : TypeToken<PpobConfig>() {}.type
        return Utils.jsonToObject(json, type) ?: PpobConfig()
    }

    fun getPpobErrorPopupContent(): String {
        return getRemoteConfigString(PPOB_ERROR_POPUP_CONTENT)
    }


    private fun getRemoteConfigString(configKey: String): String {
        val originalValue = RemoteConfigUtils.remoteConfig.getString(configKey)
        val budRemoteConfig =
            originalValue.replace("api-v3.bukuwarung.com", "api-v4.bukuwarung.com")
                .replace("api-v2.bukuwarung.com", "api-v4.bukuwarung.com");
        return budRemoteConfig;
    }

    fun showPpobTopSection() = RemoteConfigUtils.remoteConfig.getBoolean(EDC_SHOW_PPOB_TOP_SECTION)
}