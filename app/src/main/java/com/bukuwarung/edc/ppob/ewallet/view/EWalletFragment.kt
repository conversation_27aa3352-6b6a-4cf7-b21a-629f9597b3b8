package com.bukuwarung.edc.ppob.ewallet.view

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentEwalletBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.adapter.PpobProductAdapter
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PpobBillDetailsBottomSheet
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobRemoteConfig
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.isNotZero
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.ewallet.viewmodel.EWalletViewModel
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EWalletFragment : Fragment(), RecentAndFavouriteFragment.IRecentAndFavCommunicator {

    private var fragmentEwalletBinding: FragmentEwalletBinding? = null
    private val binding get() = fragmentEwalletBinding!!
    private lateinit var adapter: PpobProductAdapter
    private val from by lazy { arguments?.getString(FROM).orEmpty() }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER).orEmpty() }
    private val code by lazy { arguments?.getString(CODE).orEmpty() }
    private var selectedBiller: Biller? = null
    private val category = PpobConst.CATEGORY_EWALLET
    private var ppobProductsList: List<PpobProduct>? = null
    private var freeformAmountCap: Double = 5000.0
    private var freeFormMinimumAmount = 10000.0
    private var freeFormMaximumAmount = ********.0
    private var isCardInputBillerSelected = false
    private var isFreeFormProductSelected = false

    private val viewModel: EWalletViewModel by viewModels()

    companion object {
        private const val FROM = "from"
        private const val ACCOUNT_NUMBER = "account_number"
        private const val CODE = "code"
        private const val PHONE_INPUT = "PHONE_INPUT"
        private const val CARD_INPUT = "CARD_INPUT"
        private const val FREEFORM = "FREEFORM"
        fun createIntent(from: String, accountNumber: String, code: String): EWalletFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(CODE, code)
            }
            return EWalletFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        fragmentEwalletBinding = FragmentEwalletBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        addRecentAndFavourites()
    }

    private fun addRecentAndFavourites() {
        childFragmentManager.beginTransaction().add(
            binding.flRecentAndFav.id,
            RecentAndFavouriteFragment.createIntent(category)
        ).commit()
    }

    private fun setupView() {
        with(binding) {
            adapter = PpobProductAdapter({
                isFreeFormProductSelected = false
                if (isCardInputBillerSelected) {
                    if (bivCustomerNumber.getLength() > 16) {
                        bivCustomerNumber.setErrorMessage(getString(R.string.ewallet_card_number_length_exceed))
                    } else {
                        Utils.hideKeyboard(requireActivity())
                        addProductToCart(it.sku.orEmpty())
                    }
                } else {
                    if (!viewModel.checkPhoneNumberValidity(bivCustomerNumber.getText())) {
                        Utils.showKeyboard(requireActivity())
                        bivCustomerNumber.setFocus()
                    } else {
                        Utils.hideKeyboard(requireActivity())
                        addProductToCart(it.sku.orEmpty())
                    }
                }
            }, category)
            rvProducts.adapter = adapter
            bivBiller.setDropDownDrawable { showEwalletBillers() }
            bivAmount.onTextChangeActionListeners(
                {},
                {
                    bivAmount.setSuccessState("")
                    val productAmount = getFreeFormAmount()
                    bivAmount.setText(Utils.formatAmount(productAmount))
                    filterProductsList(productAmount)
                },
                {}
            )
            btnNext.singleClick {
                val productAmount = getFreeFormAmount()
                when {
                    productAmount < freeFormMinimumAmount -> bivAmount.setErrorMessage(getString(R.string.ewallet_minimum_topup))
                    productAmount > freeFormMaximumAmount -> bivAmount.setErrorMessage(getString(R.string.ewallet_maximum_topup))
                    else -> {
                        var freeFormProductSku = ""
                        ppobProductsList?.forEach {
                            if (it.productInfo?.isFreeForm.isTrue)
                                freeFormProductSku = it.sku.orEmpty()
                        }
                        isFreeFormProductSelected = true
                        addProductToCart(freeFormProductSku, productAmount)
                    }
                }
            }
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                bivCustomerNumber.setSuccessState("")
                if (!isCardInputBillerSelected) viewModel.checkPhoneNumberValidity(it)
            }
            if (accountNumber.isNotNullOrEmpty()) {
                setPreviousData(accountNumber, code)
            }
            freeformAmountCap = PpobRemoteConfig.getPpobConfigs().freeFormEwalletCap
        }
    }

    private fun addProductToCart(sku: String, freeFormAmount: Double? = null) {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = sku,
                beneficiary = FinproBeneficiary(
                    category = category,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = selectedBiller?.code.orEmpty()
                ),
                userType = PpobConst.USERTYPE_TREATMENT,
                amount = freeFormAmount
            )
        )
    }

    private fun filterProductsList(productAmount: Double) {
        if (productAmount < freeFormMinimumAmount || productAmount > freeFormMaximumAmount) {
            adapter.setData(ppobProductsList.orEmpty())
        } else {
            binding.bivAmount.setSuccessState(getString(R.string.freeform_ewallet_admin_fees))
            adapter.setData(ppobProductsList?.filter {
                it.amount?.isNotZero().isTrue && it.amount.orNil >= productAmount - freeformAmountCap && it.amount.orNil <= productAmount + freeformAmountCap
            }.orEmpty())
        }
    }

    private fun showEwalletBillers() {
        startEwalletBillersActivityForResult.launch(
            EwalletBillersActivity.createIntent(context, selectedBiller)
        )
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ShowProductsList -> {
                    binding.flRecentAndFav.hideView()
                    binding.rvProducts.showView()
                    binding.tvListTitle.showView()
                    adapter.setData(it.list)
                    ppobProductsList = it.list
                }
                is PpobEvent.ServerError -> {
                    showPaymentDownBottomSheet(true, it.message)
                }
                is PpobEvent.InternetError -> {
                    showPaymentDownBottomSheet(false, it.message)
                }
                is PpobEvent.ToDetail -> {
                    trackBillFetchEvent(it.orderDetail)
                    PpobBillDetailsBottomSheet.createInstance(
                        it.orderDetail,
                        category
                    ).show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
                }
                is PpobEvent.OtherError -> {
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }
                else -> {
                }
            }
        }

        viewModel.viewState.observe(viewLifecycleOwner) {
            with(binding) {
                if (it.numberLengthInvalid) {
                    bivCustomerNumber.setErrorMessage(getString(R.string.please_enter_mobile_number))
                } else {
                    bivCustomerNumber.setSuccessState("")
                }
                if (it.showShimmer) {
                    includeShimmer.sflLayout.showShimmer(true)
                    includeShimmer.root.showView()
                    rvProducts.hideView()
                    tvListTitle.hideView()
                } else {
                    includeShimmer.sflLayout.hideShimmer()
                    includeShimmer.root.hideView()
                }
                pbProgress.visibility = it.showLoading.asVisibility()
            }
        }
    }

    private fun trackBillFetchEvent(orderDetail: OrderResponse?) {
        val nominalAmount =
            orderDetail?.items?.firstOrNull()?.details?.amount ?: orderDetail?.amount
        val map = HashMap<String, String>()
        map[PpobAnalytics.NOMINAL] = nominalAmount.toString()
        map[PpobAnalytics.PROVIDER] = orderDetail?.items?.firstOrNull()?.details?.billerName.orEmpty()
        map["ppob_ewallet_is_freeform"] = isFreeFormProductSelected.toString()
        map[PpobAnalytics.TYPE] = PpobAnalytics.PPOB_EWALLET
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_SELECT_PACK, map)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentEwalletBinding?.bivCustomerNumber?.removeTextChanged()
        fragmentEwalletBinding?.bivAmount?.removeTextChanged()
        fragmentEwalletBinding?.rvProducts?.adapter = null
        fragmentEwalletBinding = null
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private val startEwalletBillersActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                val data = it.data
                selectedBiller =
                    data?.getParcelableExtra(EwalletBillersActivity.SELECTED_BILLER) as? Biller?
                setBillerView(selectedBiller)
                getEwalletProducts(selectedBiller?.code)
            }
        }

    private fun getEwalletProducts(code: String?) {
        val map = HashMap<String, String>()
        code?.let { map[PpobConst.BILLER] = it }
        viewModel.getPpobProductsWithBillerDetails(category, map)
    }

    private fun setBillerView(selectedBiller: Biller?) {
        binding.bivBiller.setText(selectedBiller?.name)
        binding.bivAmount.setText(null)
        binding.bivCustomerNumber.setText(null)
        isCardInputBillerSelected = false
        when (selectedBiller?.billerMetaData?.type) {
            FREEFORM -> setFreeFormView()
            PHONE_INPUT -> setPhoneInputView()
            CARD_INPUT -> setCardInputView()
        }
    }

    private fun setFreeFormView() = with(binding) {
        bivCustomerNumber.apply {
            showView()
            setTitle(getString(R.string.enter_phone_number))
            setHint(getString(R.string.enter_phone_number_hint))
        }
        bivAmount.showView()
        btnNext.showView()
    }

    private fun setPhoneInputView() = with(binding) {
        bivCustomerNumber.apply {
            showView()
            setTitle(getString(R.string.enter_phone_number))
            setHint(getString(R.string.enter_phone_number_hint))
        }
        bivAmount.hideView()
        btnNext.hideView()
    }

    private fun setCardInputView() = with(binding) {
        isCardInputBillerSelected = true
        bivCustomerNumber.apply {
            showView()
            setTitle(getString(R.string.enter_ewallet_card_number))
            setHint(getString(R.string.enter_ewallet_card_number_hint))
        }
        bivAmount.hideView()
        btnNext.hideView()
    }

    override fun setData(profilesItem: ProfilesItem) {
        setPreviousData(profilesItem.details?.accountNumber, profilesItem.biller?.code)
    }

    private fun setPreviousData(accountNumber: String?, billerCode: String?) {
        binding.bivCustomerNumber.setText(accountNumber)
        binding.bivCustomerNumber.setSelection()
        setPhoneInputView()
        binding.bivBiller.setText(billerCode)
        getEwalletProducts(billerCode)
    }

    private fun getFreeFormAmount(): Double {
        val numbers = binding.bivAmount.getText().filter { it.isDigit() }.toDoubleOrNull()
        return numbers.orNil
    }

}
