package com.bukuwarung.edc.ppob.ewallet.view

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityEwalletBillersBinding
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.ewallet.adapter.EwalletBillersAdapter
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.ewallet.viewmodel.EwalletBillersViewModel
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EwalletBillersActivity : AppCompatActivity(),
    PaymentDownBottomSheet.PaymentDownBsListener {

    private val viewModel: EwalletBillersViewModel by viewModels()
    private lateinit var binding: ActivityEwalletBillersBinding
    private val selectedBiller by lazy { intent?.getParcelableExtra(SELECTED_BILLER) as? Biller }

    companion object {
        const val SELECTED_BILLER = "selected_biller"
        fun createIntent(context: Context?, selectedBiller: Biller?): Intent {
            val i = Intent(context, EwalletBillersActivity::class.java)
            i.putExtra(SELECTED_BILLER, selectedBiller)
            return i
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEwalletBillersBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setToolBar()
        viewModel.getBillerList()
        subscribeState()
    }

    private fun setToolBar() = with(binding.includeToolBar){
        toolBarLabel.text = getString(R.string.choose_ewallet)
        tbPpob.navigationIcon =
            ContextCompat.getDrawable(this@EwalletBillersActivity, R.drawable.ic_arrow_back)
        tbPpob.setNavigationOnClickListener { onBackPressed() }
        ivHelp.singleClick {
            openActivity(WebviewActivity::class.java) {
                putString(ClassConstants.WEBVIEW_URL, PpobConst.CATEGORY_HELP_URL[PpobConst.CATEGORY_EWALLET].orEmpty())
                putString(ClassConstants.WEBVIEW_TITLE, getString(R.string.help))
            }
        }
    }

    private fun subscribeState() {
        viewModel.eventStatus.observe(this) {
            when (it) {
                is PpobEvent.ShowBillerList -> {
                    binding.pbProgress.hideView()
                    binding.rvBillers.adapter =
                        EwalletBillersAdapter(
                            it.list,
                            selectedBiller
                        ) {
                            val intent = Intent().apply {
                                putExtra(SELECTED_BILLER, it)
                            }
                            setResult(RESULT_OK, intent)
                            finish()
                        }
                }
                is PpobEvent.ServerError -> {
                    showPaymentDownBottomSheet(true, it.message)
                }
                is PpobEvent.InternetError -> {
                    showPaymentDownBottomSheet(false, it.message)
                }
                else -> { showPaymentDownBottomSheet(false, "") }
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, PaymentDownBottomSheet.TAG)
    }

    override fun onButtonClicked() {
        finish()
    }
}