package com.bukuwarung.edc.ppob.internetandtvcable.viewmodel

import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.common.model.Biller
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class InternetAndTvCableViewModel @Inject constructor(
    finproUseCase: FinproUseCase
) : PpobViewModel(finproUseCase) {

    fun checkCustomerNumberValidation(customerNumber: String = "", selectedBiller: Biller?): Boolean{
        val validationRegex = selectedBiller?.billerMetaData?.requriedParametersList?.firstOrNull()?.validationRegex.orEmpty()
        val errorMessage = selectedBiller?.billerMetaData?.requriedParametersList?.firstOrNull()?.errorMessage.orEmpty()
        val numberLengthInvalid: Boolean = validationRegex.isNotEmpty() && !Regex(validationRegex).matches(customerNumber)
        viewState.value = currentViewState()?.copy(numberLengthInvalid =  numberLengthInvalid, errorMessage = errorMessage)
        return numberLengthInvalid
    }
}