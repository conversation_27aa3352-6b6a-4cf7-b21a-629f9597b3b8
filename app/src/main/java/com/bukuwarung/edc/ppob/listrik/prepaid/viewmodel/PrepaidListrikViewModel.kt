package com.bukuwarung.edc.ppob.listrik.prepaid.viewmodel

import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.isFalseOrNull
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PrepaidListrikViewModel @Inject constructor(
    finproUseCase: FinproUseCase
) : PpobViewModel(finproUseCase) {

    fun checkCustomerIdValidation(customerID: String) {
        val customerIDLengthInvalid =
            customerID.isEmpty() || (customerID.length > 16 || customerID.length < 8)
        viewState.value =
            currentViewState()?.copy(customerIdLengthInvalid = customerIDLengthInvalid)
        if (currentViewState()?.numberLengthInvalid.isFalseOrNull && !customerIDLengthInvalid)
            getPpobProductsWithBillerDetails(PpobConst.CATEGORY_LISTRIK, emptyMap())
    }

    fun checkCustomerNumberValidation(customerNumber: String) {
        val phoneNumberInvalid = !checkPhoneNumberValidity(customerNumber)
        if (currentViewState()?.customerIdLengthInvalid.isFalseOrNull && !phoneNumberInvalid)
            getPpobProductsWithBillerDetails(PpobConst.CATEGORY_LISTRIK, emptyMap())
    }

}