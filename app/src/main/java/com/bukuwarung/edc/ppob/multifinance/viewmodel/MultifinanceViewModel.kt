package com.bukuwarung.edc.ppob.multifinance.viewmodel

import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.common.model.Biller
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class MultifinanceViewModel @Inject constructor(
    finproUseCase: FinproUseCase
) : PpobViewModel(finproUseCase) {

    fun checkCustomerNumberValidation(
        customerNumber: String = "",
        selectedBiller: Biller?
    ): Boolean {
        val validationRegex =
            selectedBiller?.billerMetaData?.requriedParametersList?.getOrNull(1)?.validationRegex.orEmpty()
        val errorMessage =
            selectedBiller?.billerMetaData?.requriedParametersList?.getOrNull(1)?.errorMessage.orEmpty()
        val numberLengthInvalid: Boolean =
            validationRegex.isNotEmpty() && !Regex(validationRegex).matches(customerNumber)
        viewState.value = currentViewState()?.copy(
            numberLengthInvalid = numberLengthInvalid,
            errorMessage = errorMessage
        )
        return numberLengthInvalid
    }
}