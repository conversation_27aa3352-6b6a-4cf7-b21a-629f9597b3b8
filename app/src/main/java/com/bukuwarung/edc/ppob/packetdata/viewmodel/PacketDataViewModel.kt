package com.bukuwarung.edc.ppob.packetdata.viewmodel

import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.constants.PpobConst
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PacketDataViewModel @Inject constructor(
    finproUseCase: FinproUseCase
) : PpobViewModel(finproUseCase) {

    val map = HashMap<String, String>()
    fun checkCustomerNumberValidation(customerNumber: String = ""): Boolean {
        if (customerNumber.length < 4) {
            viewState.value = currentViewState()?.copy(providerLogo = 0)
            return false
        }
        if (!checkPhoneNumberValidity(customerNumber)) return false
        val lastPhonePrefix = customerNumber.substring(0, 4)
        val providerName = PpobConst.PROVIDER_NUMBER_MAP[lastPhonePrefix]
        viewState.value = currentViewState()?.copy(
            numberInvalid = providerName == null,
            providerLogo = PpobConst.PROVIDER_ICON_MAP[providerName] ?: 0
        )
        if (providerName == null) return false
        map[PpobConst.BILLER] = providerName
        getPpobProductsWithBillerDetails(PpobConst.CATEGORY_PAKET_DATA, map, false)
        return true
    }

    fun changeProductsBasedOnFilterSelected(isSpecialProduct: Boolean) {
        getPpobProductsWithBillerDetails(PpobConst.CATEGORY_PAKET_DATA, map, isSpecialProduct)
    }

}