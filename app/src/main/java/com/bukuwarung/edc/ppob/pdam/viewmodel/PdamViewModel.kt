package com.bukuwarung.edc.ppob.pdam.viewmodel

import android.content.Context
import com.bukuwarung.edc.R
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.payments.data.model.PpobProduct
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PdamViewModel @Inject constructor(
    finproUseCase: FinproUseCase
) : PpobViewModel(finproUseCase) {

    fun checkCustomerNumberValidation(
        customerNumber: String = "",
        context: Context,
        selectedProduct: PpobProduct?
    ): Boolean {
        val validationRegex = selectedProduct?.validationInfo?.validationRegex.orEmpty()
        val numberLengthInvalid: Boolean =
            validationRegex.isNotEmpty() && !Regex(validationRegex).matches(customerNumber)
        viewState.value = currentViewState()?.copy(
            numberLengthInvalid = numberLengthInvalid,
            errorMessage = context.getString(R.string.customer_number_validation_error)
        )
        return numberLengthInvalid
    }
}