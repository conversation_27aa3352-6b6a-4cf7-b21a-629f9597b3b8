package com.bukuwarung.edc.ppob.pulsa.prepaid.viewmodel

import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PrepaidPulsaViewModel @Inject constructor(
    finproUseCase: FinproUseCase
) : PpobViewModel(finproUseCase) {

    fun checkCustomerNumberValidation(customerNumber: String = ""): Boolean {
        if (customerNumber.length < 4) {
            viewState.value = currentViewState()?.copy(providerLogo = 0)
            return false
        }
        if (!checkPhoneNumberValidity(customerNumber)) return false
        val lastPhonePrefix = customerNumber.substring(0, 4)
        val providerName = PpobConst.PROVIDER_NUMBER_MAP[lastPhonePrefix]
        viewState.value = currentViewState()?.copy(
            numberInvalid = providerName == null,
            providerLogo = PpobConst.PROVIDER_ICON_MAP[providerName] ?: 0
        )
        if (providerName == null) return false
        val map = HashMap<String, String>()
        map[PpobConst.BILLER] = providerName
        getPpobProductsWithBillerDetails(PpobConst.CATEGORY_PULSA, map)
        return true
    }

}