package com.bukuwarung.edc.ppob.recentsandfavourites.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagedListAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutRecentFavItemNewBinding
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bumptech.glide.Glide

class FavouriteItemAdapter(
    private val removeFavourite: (ProfilesItem?) -> Unit,
    private val clickAction: (ProfilesItem?) -> Unit
) :
    PagedListAdapter<ProfilesItem, FavouriteItemAdapter.FavouriteProductViewHolder>(diffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FavouriteProductViewHolder {
        val inflater = LayoutInflater.from(parent.context)

        return FavouriteProductViewHolder(
            LayoutRecentFavItemNewBinding.inflate(
                inflater,
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: FavouriteProductViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    companion object {

        private val diffCallback =
            object : DiffUtil.ItemCallback<ProfilesItem>() {
                override fun areItemsTheSame(
                    oldItem: ProfilesItem,
                    newItem: ProfilesItem
                ): Boolean =
                    oldItem.id == newItem.id

                override fun areContentsTheSame(
                    oldItem: ProfilesItem,
                    newItem: ProfilesItem
                ): Boolean =
                    oldItem == newItem
            }
    }

    inner class FavouriteProductViewHolder(private val binding: LayoutRecentFavItemNewBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(product: ProfilesItem?) {
            with(binding) {
                tvTitle.text = product?.alias
                tvDate.text = product?.details?.accountNumber
                ivFavourite.setImageResource(R.drawable.ic_favourite_fill)
                tvAmount.hideView()
                ivFavourite.singleClick {
                    removeFavourite(product)
                }
                btCheck.singleClick {
                    clickAction(product)
                }
                Glide.with(ivCategory.context)
                    .load(product?.biller?.icon)
                    .placeholder(PpobConst.CATEGORY_ICON[product?.category].orNil)
                    .error(PpobConst.CATEGORY_ICON[product?.category].orNil)
                    .into(ivCategory)
            }
        }
    }
}