package com.bukuwarung.edc.ppob.recentsandfavourites.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bukuwarung.edc.ppob.recentsandfavourites.view.FavouriteFragment
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentsFragment

class RecentsAndFavouriteAdapter(
    fragment: Fragment, private val category: String = "", private val billerCode: String = ""
) : FragmentStateAdapter(fragment) {
    override fun getItemCount() = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> FavouriteFragment.createIntent(category)

            else -> RecentsFragment.createIntent(category, billerCode)
        }
    }
}