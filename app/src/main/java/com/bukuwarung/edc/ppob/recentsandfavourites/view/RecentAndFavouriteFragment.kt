package com.bukuwarung.edc.ppob.recentsandfavourites.view

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentRecentAndFavouritesBinding
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.adapter.RecentsAndFavouriteAdapter
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.ppob.recentsandfavourites.viewmodel.RecentAndFavouriteViewModel
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RecentAndFavouriteFragment: Fragment() {

    private val viewModel: RecentAndFavouriteViewModel by activityViewModels()
    private var _binding: FragmentRecentAndFavouritesBinding? = null
    private val binding get() = _binding!!
    private var recentFavAdapter: RecentsAndFavouriteAdapter? = null
    private val category by lazy { arguments?.getString(ARG_CATEGORY).orEmpty() }
    private var iCommunicator: IRecentAndFavCommunicator? = null

    companion object {
        const val TAG = "recent_and_fav_fragment"
        private const val ARG_CATEGORY = "arg_category"
        private const val ARG_BILLER_CODE = "arg_biller_code"
        private const val FAVOURITE_POSITION = 0
        private const val RECENT_POSITION = 1

        fun createIntent(category: String, billerCode: String? = null): RecentAndFavouriteFragment {
            val fragment = RecentAndFavouriteFragment()
            fragment.arguments = Bundle().apply {
                putString(ARG_CATEGORY, category)
                putString(ARG_BILLER_CODE, billerCode)
            }
            return fragment
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding =
            FragmentRecentAndFavouritesBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    private fun setupView() {
        with(binding) {
            viewModel.setCategory(arguments?.getString(ARG_CATEGORY).orEmpty())
            recentFavAdapter = RecentsAndFavouriteAdapter(
                this@RecentAndFavouriteFragment,
                category = category,
                billerCode = arguments?.getString(ARG_BILLER_CODE).orEmpty()
            )
            vpPager.adapter = recentFavAdapter
            TabLayoutMediator(tbLayout, vpPager) { tab, position ->
                tab.text = getTabTitle(position)
            }.attach()
            vpPager.offscreenPageLimit = 2
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        iCommunicator = (parentFragment ?: context) as? IRecentAndFavCommunicator
    }

    private fun getTabTitle(position: Int): String {
        return when (position) {
            FAVOURITE_POSITION -> getString(R.string.favorite_customer_contact)
            else -> getString(R.string.recent_transaction)
        }
    }

    private fun subscribeState() {
        viewModel.observeEventState.observe(viewLifecycleOwner) {
            when(it) {
                is RecentAndFavouriteViewModel.Event.SetData -> {
                    iCommunicator?.setData(it.profilesItem)
                }
                is RecentAndFavouriteViewModel.Event.SwitchToRecent -> {
                    binding.vpPager.currentItem = RECENT_POSITION
                    if (it.hideKeyboard) {
                        Utils.hideKeyboard(requireActivity())
                    }
                }
                else -> {}
            }
        }
    }

    fun refreshFavAndRecentTab() {
        viewModel.getPaymentList(category)
        viewModel.invalidateDataSource()
    }

    interface IRecentAndFavCommunicator {
        fun setData(profilesItem: ProfilesItem)
    }


}