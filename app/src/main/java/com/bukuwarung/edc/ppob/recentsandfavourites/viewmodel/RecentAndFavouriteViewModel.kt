package com.bukuwarung.edc.ppob.recentsandfavourites.viewmodel


import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.DataSource
import androidx.paging.LivePagedListBuilder
import androidx.paging.PagedList
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.payments.data.repository.FavouriteDataSource
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.util.Utils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class RecentAndFavouriteViewModel @Inject constructor(
    val finproUseCase: FinproUseCase
) : PpobViewModel(finproUseCase) {

    companion object {
        const val VIEW_TYPE_SEE_ALL = 1
    }

    private var categoryName: String = ""
    private val _pagingStatus = MutableLiveData<PagingStatus>()
    val pagingStatus: LiveData<PagingStatus> = _pagingStatus
    var productData: LiveData<PagedList<ProfilesItem>>? = null
    private var favouriteDataSource: FavouriteDataSource? = null
    private val eventState = MutableLiveData<Event>()
    val observeEventState: LiveData<Event> = eventState
    sealed class Event {
        data class ShowPaymentList(
            val list: List<ProfilesItem>,
            val showOnBoarding: Boolean = false,
            val category: String = ""
        ) : Event()
        data class ShowRecentError(val showServerError: Boolean) : Event()
        object ShowRecentEmpty : Event()
        data class SetData(val profilesItem: ProfilesItem) : Event()
        data class SwitchToRecent(val hideKeyboard: Boolean) : Event()
    }

    fun setCategory(name: String) {
        categoryName = name
        provideDataSource()
    }

    fun invalidateDataSource() {
        favouriteDataSource?.invalidate()
    }

    private fun provideDataSource() {
        val pagingConfig = PagedList.Config.Builder()
            .setPrefetchDistance(10)
            .build()

        val dataSource = object : DataSource.Factory<Int, ProfilesItem>() {
            override fun create(): DataSource<Int, ProfilesItem> {
                return FavouriteDataSource(
                    finproUseCase = finproUseCase,
                    coroutineScope = viewModelScope,
                    category = categoryName,
                    pagingStatusLiveData = _pagingStatus
                ).also {
                    favouriteDataSource = it
                }
            }
        }

        productData = LivePagedListBuilder(dataSource, pagingConfig).build()
    }

    fun getPaymentList(category: String, showOnBoarding: Boolean = false) = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.getRecentTransactions(
                bookId = Utils.getPaymentAccountId(),
                category = category,
                page = 0,
                limit = 10
            ).let {
                if (it.isSuccessful) {
                    val data = it.body()?.profiles.orEmpty()
                    if (data.isNotEmpty()) {
                        it.body()?.profiles?.let {
                            val list = data.toMutableList()
                            if (list.size == 10) {
                                list.add(ProfilesItem(viewType = VIEW_TYPE_SEE_ALL))
                            }
                            setEventState(
                                Event.ShowPaymentList(
                                    list,
                                    showOnBoarding,
                                    category
                                )
                            )
                        }
                    } else {
                        setEventState(Event.ShowRecentEmpty)
                    }
                } else {
                    setEventState(Event.ShowRecentError(true))
                }
            }
        } catch (e: Exception) {
            setEventState(Event.ShowRecentError(true))
        }
    }

    fun setData(profilesItem: ProfilesItem?)  = viewModelScope.launch{
        profilesItem?.let {
            setEventState(Event.SetData(it))
        }
    }

    fun showRecent(hideKeyboard: Boolean) = viewModelScope.launch {
        setEventState(Event.SwitchToRecent(hideKeyboard))
    }

    private suspend fun setEventState(event: Event) =
        withContext(Dispatchers.Main) {
            eventState.value = event
        }
}