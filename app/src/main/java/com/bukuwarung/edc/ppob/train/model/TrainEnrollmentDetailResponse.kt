package com.bukuwarung.edc.ppob.train.model

import com.google.gson.annotations.SerializedName


data class TrainEnrollmentDetailResponse(
    @SerializedName("enrollment_id")
    val enrollmentId: String? = "",
    @SerializedName("status")
    val status: EnrollmentStatus? = null,
    @SerializedName("inquiry_number")
    val inquiryNumber: String? = null
)

enum class EnrollmentStatus {
    SUCCESS, FAILURE, CREATED
}