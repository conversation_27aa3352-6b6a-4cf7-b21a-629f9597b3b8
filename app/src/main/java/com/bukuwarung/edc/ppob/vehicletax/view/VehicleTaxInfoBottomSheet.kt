package com.bukuwarung.edc.ppob.vehicletax.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.edc.databinding.BottomsheetVehicleTaxInfoBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.ppob.common.model.BillerExtraData
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VehicleTaxInfoBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "VehicleTaxInfoBottomSheet"
        const val BILLER_EXTRA_DATA = "biller_extra_data"
        fun createInstance(billerExtraData: BillerExtraData) = VehicleTaxInfoBottomSheet().apply {
            this.arguments = Bundle().apply {
                putParcelable(BILLER_EXTRA_DATA, billerExtraData)
            }
        }
    }

    private var _binding: BottomsheetVehicleTaxInfoBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomsheetVehicleTaxInfoBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val billerExtraData = arguments?.getParcelable<BillerExtraData>(BILLER_EXTRA_DATA)

        binding.apply {
            btnClose.setSingleClickListener {
                dialog?.dismiss()
            }
            context?.let {
                if (billerExtraData?.bannerImage.isNotNullOrBlank()) {
                    ivImage.showView()
                    Glide.with(it)
                        .load(billerExtraData?.bannerImage)
                        .into(ivImage)
                }
            }
            tvBody.text = billerExtraData?.bannerText ?: ""
            tvHeading.text = billerExtraData?.bannerTitle ?: ""
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
