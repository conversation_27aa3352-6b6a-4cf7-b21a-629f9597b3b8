package com.bukuwarung.edc.printer.util

import android.util.Log

open class BasePrint {

    private var childName = ""

    fun logTrue(method: String) {
        childName = javaClass.simpleName + "."
        val trueLog = childName + method
        Log.i("IPPITest", trueLog)
        //clear();
    }

    fun logErr(method: String, errString: String) {
        childName = javaClass.simpleName + "."
        val errorLog = "$childName$method   errorMessage：$errString"
        Log.e("IPPITest", errorLog)
        //clear();
    }

    fun clear() {

    }

}