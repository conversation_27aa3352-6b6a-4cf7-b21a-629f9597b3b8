package com.bukuwarung.edc.printer.util

import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.appcompat.content.res.AppCompatResources
import com.bukuwarung.edc.R
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.util.getColorCompat
import com.pax.dal.entity.EFontTypeAscii
import com.pax.dal.entity.EFontTypeExtCode
import com.pax.dal.exceptions.PrinterDevException

class PrintUtil : BasePrint() {

    companion object {
        val printer = EdcApplication.dal?.printer
        val printUtil: PrintUtil
            get() {
                return PrintUtil()
            }
    }

    fun init() {
        try {
            printer!!.init()
            logTrue("init")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("init", e.toString())
        }
    }

    fun getStatus(): String? {
        return try {
            val status = printer!!.status
            logTrue("getStatus")
            statusCode2Str(status)
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("getStatus", e.toString())
            ""
        }
    }

    fun getStatusCode(): Int? {
        return try {
            logTrue("getStatus")
            printer!!.status
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("getStatus", e.toString())
            -1
        }
    }

    fun fontSet(asciiFontType: EFontTypeAscii?, cFontType: EFontTypeExtCode?) {
        try {
            printer!!.fontSet(asciiFontType, cFontType)
            logTrue("fontSet")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("fontSet", e.toString())
        }
    }

    fun spaceSet(wordSpace: Byte, lineSpace: Byte) {
        try {
            printer!!.spaceSet(wordSpace, lineSpace)
            logTrue("spaceSet")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("spaceSet", e.toString())
        }
    }

    fun printStr(str: String?, charset: String?) {
        try {
            printer!!.printStr(str, charset)
            logTrue("printStr")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("printStr", e.toString())
        }
    }

    fun step(b: Int) {
        try {
            printer!!.step(b)
            logTrue("setStep")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("setStep", e.toString())
        }
    }

    fun printBitmap(bitmap: Bitmap?) {
        try {
            printer!!.printBitmap(bitmap)
            logTrue("printBitmap")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("printBitmap", e.toString())
        }
    }

    fun start(): String? {
        return try {
            val res = printer!!.start()
            logTrue("start")
            statusCode2Str(res)
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("start", e.toString())
            ""
        }
    }

    fun leftIndents(indent: Short) {
        try {
            printer!!.leftIndent(indent.toInt())
            logTrue("leftIndent")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("leftIndent", e.toString())
        }
    }

    fun getDotLine(): Int {
        return try {
            val dotLine = printer!!.dotLine
            logTrue("getDotLine")
            dotLine
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("getDotLine", e.toString())
            -2
        }
    }

    fun setGray(level: Int) {
        try {
            printer!!.setGray(level)
            logTrue("setGray")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("setGray", e.toString())
        }
    }

    fun setDoubleWidth(isAscDouble: Boolean, isLocalDouble: Boolean) {
        try {
            printer!!.doubleWidth(isAscDouble, isLocalDouble)
            logTrue("doubleWidth")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("doubleWidth", e.toString())
        }
    }

    fun setDoubleHeight(isAscDouble: Boolean, isLocalDouble: Boolean) {
        try {
            printer!!.doubleHeight(isAscDouble, isLocalDouble)
            logTrue("doubleHeight")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("doubleHeight", e.toString())
        }
    }

    fun setInvert(isInvert: Boolean) {
        try {
            printer!!.invert(isInvert)
            logTrue("setInvert")
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("setInvert", e.toString())
        }
    }

    fun cutPaper(mode: Int): String? {
        return try {
            printer!!.cutPaper(mode)
            logTrue("cutPaper")
            "cut paper successful"
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("cutPaper", e.toString())
            e.toString()
        }
    }

    fun getCutMode(): String? {
        var resultStr = ""
        return try {
            val mode = printer!!.cutMode
            logTrue("getCutMode")
            when (mode) {
                0 -> resultStr = "Only support full paper cut"
                1 -> resultStr = "Only support partial paper cutting "
                2 -> resultStr = "support partial paper and full paper cutting "
                -1 -> resultStr = "No cutting knife,not support"
                else -> {}
            }
            resultStr
        } catch (e: PrinterDevException) {
            e.printStackTrace()
            logErr("getCutMode", e.toString())
            e.toString()
        }
    }

    private fun statusCode2Str(status: Int): String? {
        var res = ""
        when (status) {
            0 -> res = "Success "
            1 -> res = "Printer is busy "
            2 -> res = "Out of paper "
            3 -> res = "The format of print data packet error "
            4 -> res = "Printer malfunctions "
            8 -> res = "Printer over heats "
            9 -> res = "Printer voltage is too low"
            240 -> res = "Printing is unfinished "
            252 -> res = "The printer has not installed font library "
            254 -> res = "Data package is too long "
            else -> {}
        }

        return res
    }

    fun findTintColor(code: Int?): ColorStateList {
        EdcApplication.instance.apply {
            return when (code) {
                0 -> ColorStateList.valueOf(getColorCompat(R.color.light_green))
                else -> {
                    ColorStateList.valueOf(getColorCompat(R.color.snackbar_error))
                }
            }
        }
    }

    fun findToastImage(code: Int?): Drawable? {
        EdcApplication.instance.apply {
            return when (code) {
                0 -> AppCompatResources.getDrawable(this, R.drawable.vector_tick_green)
                2 -> AppCompatResources.getDrawable(this, R.drawable.ic_warning_red)
                else -> AppCompatResources.getDrawable(this, R.drawable.ic_alert_warning)
            }
        }
    }

    fun findTextColor(code: Int?): ColorStateList {
        EdcApplication.instance.apply {
            return when (code) {
                0 -> ColorStateList.valueOf(getColorCompat (R.color.dark_green)
                    )
                    2
                -> ColorStateList.valueOf(getColorCompat(R.color.snackbar_error_color))
                else -> ColorStateList.valueOf(getColorCompat(R.color.snackbar_error_color))
            }
        }
    }

    fun findStatusText(code: Int?): String {
        return when(code) {
            0 -> "Tes cetak struk pembayaran berhasil!"
            2 -> "Kertas thermal tidak tersedia."
            else -> "Ada gangguan. Coba beberapa saat lagi"
        }
    }

    fun getDottedLine(n: Int): String {
        var str = ""
        repeat(n) { str += "-" }
        return str
    }

}