package com.bukuwarung.edc.replacement

// Import for MoreFun ReadCardParam if needed, or create a simplified version/use Bundle keys directly
// import com.mf.mpos.pub.param.ReadCardParam // This might not be directly accessible here

import android.app.Application
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.cardreader.CardReaderType
import com.bukuwarung.cardreader.contant.CardConstants
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_AUTH_AMOUNT_String
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_FORCE_ONLINE_boolean
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_TRANSACTION_TIMEOUT_byte
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_transProcessCode_byte
import com.bukuwarung.cardreader.dto.CardReaderResult
import com.bukuwarung.edc.card.domain.model.consts.Constants.DEVICE_MANUFACTURER_MOREFUN
import com.bukuwarung.edc.card.domain.usecase.ConfigureAidUseCase
import com.bukuwarung.edc.card.domain.usecase.ConfigureRidUseCase
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.replacement.data.model.BluetoothStatus
import com.bukuwarung.edc.replacement.data.model.CardDetails
import com.bukuwarung.edc.replacement.data.model.CardReadingStatus
import com.bukuwarung.edc.replacement.data.model.ChipCardReadings
import com.bukuwarung.edc.replacement.data.model.MagneticCardReadings
import com.bukuwarung.edc.replacement.data.model.QualityCheckDetails
import com.bukuwarung.edc.replacement.data.model.ReplacementFormSubmitRequest
import com.bukuwarung.edc.replacement.domain.usecase.UploadReplacementFormUseCase
import com.bukuwarung.edc.replacement.presentation.check.ReplacementCheckState
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject


@HiltViewModel
class ReplacementViewModel @Inject constructor(
    private val application: Application, // Hilt will provide this
    // We might need to inject a Provider<CardReaderHelper> if it's not a singleton
    // or ensure CardReaderHelper.getInstance(application) is correctly managed.
    // For now, direct injection is assumed to be set up or we'll use getInstance.
    // private val cardReaderHelper: CardReaderHelper
    private val useCase: UploadReplacementFormUseCase,
    private val configureAidUseCase: ConfigureAidUseCase,
    private val configureRidUseCase: ConfigureRidUseCase,
) : ViewModel() {

    private val _uiState = MutableStateFlow<ReplacementCheckState>(ReplacementCheckState.Initial)
    val uiState: StateFlow<ReplacementCheckState> = _uiState

    private val _snackbarMessage = MutableStateFlow<Pair<String?, String?>>(Pair(null,null))
    val snackbarMessage: StateFlow<Pair<String?, String?>> = _snackbarMessage

    private val _snackbarAction = MutableStateFlow<(() -> Unit)?>(null)
    val snackbarAction: StateFlow<(() -> Unit)?> = _snackbarAction

    private var cardReaderHelper: CardReaderHelper
    private var isCardReaderInitialized = false

    private val chipCardDetails = mutableListOf<CardDetails?>()
    private val magStripeCardDetails = mutableListOf<CardDetails?>()

    private val _bluetoothScanAction = MutableStateFlow<BluetoothScanData?>(null)
    val bluetoothScanAction: StateFlow<BluetoothScanData?> = _bluetoothScanAction

    init {
        configureEmv()
        cardReaderHelper = CardReaderHelper.getInstance()
    }

    private fun initializeCardReaderHelper() {
        viewModelScope.launch {
            try {
                val pairedDevices = BluetoothDevices.getPairedCardReaderList()?:emptyList()
                if (pairedDevices.isNotEmpty()) {
                    val deviceName = pairedDevices[0].name ?: ""
                    val type = if (deviceName.uppercase().contains("MP-")) CardReaderType.MOREFUN else CardReaderType.TIANYU
                    // initCardReaderHelper is synchronous and involves SDK initialization which might be blocking
                    withContext(Dispatchers.IO) {
                         cardReaderHelper.initCardReaderHelper(type)
                    }
                    isCardReaderInitialized = true
                } else {
                    _uiState.value = ReplacementCheckState.Error("Tidak ada EDC Saku yang terpasang. Silakan pasang EDC terlebih dahulu.")
                    _snackbarMessage.value = Pair("Tidak ada EDC Saku yang terpasang.",null)
                    isCardReaderInitialized = false
                }
            } catch (e: Exception) {
                Log.e("ViewModelInit", "Error initializing CardReaderHelper: ${e.message}", e)
                _uiState.value = ReplacementCheckState.Error("Gagal menginisialisasi card reader: ${e.message}")
                isCardReaderInitialized = false
            }
        }
    }

    fun connectDeviceIfNeeded() {
        viewModelScope.launch {
            if (!isCardReaderInitialized) {
                _snackbarMessage.value = Pair("Card reader belum siap.",null)
                initializeCardReaderHelper() // Try to re-initialize
                return@launch
            }

            if (cardReaderHelper.isDeviceConnected()) {
                val deviceInfo = cardReaderHelper.getCardReaderInfo()
                _snackbarMessage.value = Pair("EDC ${deviceInfo.manufacturer} ${deviceInfo.deviceSn} sudah terhubung",null)
                return@launch
            }
            val pairedDevices = BluetoothDevices.getPairedCardReaderList()?:emptyList()
            if (pairedDevices.isNotEmpty()) {
                val deviceToConnect = BluetoothDevices.getPairedCardReader() // Connect to the first one by default
                val deviceName = pairedDevices[0].name ?: "EDC Saku"

                // Re-initialize helper for the specific device being connected if type logic is complex
                // For now, assuming init in initializeCardReaderHelper or generic connect works.

                val connected = withContext(Dispatchers.IO) {
                    try {
                        cardReaderHelper.connectToDevice(deviceToConnect.orEmpty())
                    } catch (e: Exception) {
                        Log.e("ViewModelConnect", "Error connecting to device: ${e.message}", e)
                        false
                    }
                }

                if (connected) {
                    _snackbarMessage.value = Pair("EDC ${deviceName} terhubung",null)
                } else {
                    _snackbarMessage.value = Pair("Gagal menghubungkan ke EDC ${deviceName}. Pastikan EDC menyala dan dalam jangkauan.",null
                    )
                }
            } else {
                _snackbarMessage.value = Pair("Tidak ada EDC Saku yang terpasang.",null)
            }
        }
    }

    fun checkBluetoothStatus() {
        viewModelScope.launch {
             if (!isCardReaderInitialized) {
                // initializeCardReaderHelper() // Wait for init to complete or user to trigger connect
                _uiState.value = ReplacementCheckState.Initial // Or an error state if init failed previously
                return@launch
            }
            if (cardReaderHelper.isDeviceConnected()) {
                 // If already in a card check state, don't regress to BluetoothConnected
                if (_uiState.value !is ReplacementCheckState.ChipCardCheck && _uiState.value !is ReplacementCheckState.MagStripeCardCheck && _uiState.value !is ReplacementCheckState.AllChecksCompleted) {
                    _uiState.value = ReplacementCheckState.BluetoothConnected
                }
            } else {
                _uiState.value = ReplacementCheckState.Initial // Indicate Bluetooth is not connected
                _snackbarMessage.value = Pair("Koneksi Bluetooth ke EDC terputus.",null)
            }
        }
    }

    private fun createEmvBundle(cardModeByte: Byte): Bundle {
        val bundle = Bundle()
        bundle.putString("transAmount", "0")
        bundle.putString("otherAmount", "0")
        bundle.putByte("transType", 0x20.toByte()) // Typically 0x20 for Inquiry/Check Card
        bundle.putByte("emvTransactionType", 0x20.toByte()) // Balance Inquiry for EMV
        // Add date and time for some readers
        // val sdfDate = SimpleDateFormat("yyMMdd", Locale.getDefault())
        // val sdfTime = SimpleDateFormat("HHmmss", Locale.getDefault())
        // bundle.putString("transDate", sdfDate.format(Date()))
        // bundle.putString("transTime", sdfTime.format(Date()))
        bundle.putByte("cardMode", cardModeByte) // 0x01 MAG, 0x02 ICC, 0x03 MAG_FALLBACK_ICC, 0x04 NFC, etc.
        bundle.putInt("timeout", 60) // Timeout in seconds
        return bundle
    }

    fun extractCardDetails(bundle: Bundle?): CardDetails? {
        if (bundle == null) return null
        val cardNumber = bundle.getString(CardConstants.ResultBundleConst.KEY_PAN_String)
        val cardType = bundle.getString("cardType") // Map this if needed
        val issuer = bundle.getString("issuer") // Map this if available
        return CardDetails(
            cardNumberMasked = com.bukuwarung.edc.util.Utils.maskCardNo(cardNumber),
            cardType = cardType,
            issuer = issuer
        )
    }

    fun performChipCardRead(attempt: Int) {
        //TODO kesa has to add chip cardRead
        // check startCheckCard implementation of EdcDeviceUseCase
        if(isCardReaderInitialized.not()) {
            initializeCardReaderHelper()
        }
        viewModelScope.launch {
            // Check if device is connected, if not try to reconnect (device might be in standby)
            if (!cardReaderHelper.isDeviceConnected()) {
                val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                // Show snackbar with action button to connect Bluetooth
                _snackbarMessage.value = Pair("EDC tidak terhubung.", "Turn On device")
                _snackbarAction.value = {
                    connectDeviceIfNeeded()
                }
                _uiState.value = ReplacementCheckState.ChipCardCheck(
                    step = attempt,
                    error = "EDC tidak terhubung",
                    isLoading = false,
                    stepResults = prevState?.stepResults ?: listOf(Pair(null, null), Pair(null, null), Pair(null, null))
                )
                return@launch
            }

            // Handle initial state (step 0)
            if (attempt == 0) {
                val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                _uiState.value = ReplacementCheckState.ChipCardCheck(
                    step = 1,
                    isLoading = false,
                    stepResults = prevState?.stepResults ?: listOf(Pair(null, null), Pair(null, null), Pair(null, null))
                )
                return@launch
            }

            // Set loading state, preserve previous stepResults
            val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
            _uiState.value = ReplacementCheckState.ChipCardCheck(
                step = attempt,
                isLoading = true,
                stepResults = prevState?.stepResults ?: listOf(Pair(null, null), Pair(null, null), Pair(null, null))
            )

            // Create EMV bundle specifically for chip card reading test
            val emvBundle = Bundle().apply {
                putString(KEY_AUTH_AMOUNT_String, "0")
                putByte(KEY_transProcessCode_byte, 0x30.toByte())
                putByte(KEY_TRANSACTION_TIMEOUT_byte, 0x14.toByte()) // 20 seconds timeout
                putBoolean(KEY_FORCE_ONLINE_boolean, true)
            }

            // Track start of card reading
            Log.d("ChipCardTest", "Starting chip card read attempt #$attempt")

            val result: CardReaderResult? = withContext(Dispatchers.IO) {
                try {
                    // Read the card
                    cardReaderHelper.readCard(emvBundle)
                } catch (e: Exception) {
                    Log.e("ChipRead", "Error reading chip card: ${e.message}", e)
                    null
                }
            }

            // Process the result
            when {
                // Success case
                result != null && result.response == com.bukuwarung.cardreader.tianyu.contant.ResponseCode.SUCCESS -> {
                    // Get card entry mode to confirm it was read as ICC
                    val cardEntryMode = result.data.getInt(CardConstants.ResultBundleConst.KEY_CARD_ENTRY_MODE_Int, -1)
                    val cardNumber = result.data.getString(CardConstants.ResultBundleConst.KEY_PAN_String)
                    val successDetails = if (cardNumber != null) " (${cardNumber.take(6)}...)" else ""

                    // Store card details for this attempt
                    while (chipCardDetails.size < attempt) chipCardDetails.add(null)
                    chipCardDetails.add(extractCardDetails(result.data))

                    // If first attempt succeeds, skip remaining attempts and go directly to magnetic stripe
                    if (attempt == 1) {
                        // Mark first attempt as success, remaining as skipped
                        val chipState = ReplacementCheckState.ChipCardCheck(
                            step = 4, // Mark as completed
                            isLoading = false,
                            stepResults = listOf(
                                Pair(true, null), // First attempt success
                                Pair(null, "Dilewati"), // Second attempt skipped
                                Pair(null, "Dilewati")  // Third attempt skipped
                            )
                        )
                        val magStripeState = ReplacementCheckState.MagStripeCardCheck(
                            step = 1,
                            isLoading = false
                        )
                        _uiState.value = ReplacementCheckState.Composite(
                            chipCardCheck = chipState,
                            magStripeCardCheck = magStripeState
                        )
                        _snackbarMessage.value = Pair("Pembacaan kartu chip berhasil pada percobaan pertama. Lanjut ke pembacaan kartu magnetik.", null)
                    } else if (attempt < 3) {
                        // Continue with normal flow for subsequent attempts
                        val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                        _uiState.value = ReplacementCheckState.ChipCardCheck(
                            step = attempt + 1,
                            isLoading = false,
                            stepResults = updateStepResults(prevState, attempt - 1, true)
                        )
                        _snackbarMessage.value = Pair("Pembacaan kartu chip #$attempt berhasil$successDetails.", null)
                    } else {
                        // All chip checks done (attempt 3), move to Composite state
                        val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                        val chipState = ReplacementCheckState.ChipCardCheck(
                            step = 4,
                            isLoading = false,
                            stepResults = (prevState?.stepResults?.let { updateStepResults(prevState, attempt - 1, true) }
                                ?: listOf(Pair(true, null), Pair(true, null), Pair(true, null)))
                        )
                        val magStripeState = ReplacementCheckState.MagStripeCardCheck(
                            step = 1,
                            isLoading = false
                        )
                        _uiState.value = ReplacementCheckState.Composite(
                            chipCardCheck = chipState,
                            magStripeCardCheck = magStripeState
                        )
                        _snackbarMessage.value = Pair("Semua pembacaan kartu chip berhasil.", null)
                    }
                }

                // Handle specific error cases
                result != null && result.response == com.bukuwarung.cardreader.tianyu.contant.ResponseCode.ERROR_CANCEL -> {
                    val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                    _uiState.value = ReplacementCheckState.ChipCardCheck(
                        step = attempt + 1,
                        isLoading = false,
                        error = "Pembacaan dibatalkan",
                        stepResults = updateStepResults(
                            prevState,
                            attempt - 1,
                            false,
                            "Pembacaan dibatalkan"
                        )
                    )
                    _snackbarMessage.value =
                        Pair("Pembacaan kartu chip #$attempt dibatalkan.", null)

                    // Store card details for this attempt
                    while (chipCardDetails.size < attempt) chipCardDetails.add(null)
                    chipCardDetails.add(null)
                }

                result != null && result.response == com.bukuwarung.cardreader.tianyu.contant.ResponseCode.ERROR_READING_CARD -> {
                    val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                    _uiState.value = ReplacementCheckState.ChipCardCheck(
                        step = attempt + 1,
                        isLoading = false,
                        error = "Kartu tidak bisa dibaca",
                        stepResults = updateStepResults(
                            prevState,
                            attempt - 1,
                            false,
                            "Kartu tidak bisa dibaca"
                        )
                    )
                    _snackbarMessage.value =
                        Pair("Gagal membaca kartu chip #$attempt. Silakan coba lagi.", null)

                    // Store card details for this attempt
                    while (chipCardDetails.size < attempt) chipCardDetails.add(null)
                    chipCardDetails.add(null)
                }

                result != null && result.response == com.bukuwarung.cardreader.tianyu.contant.ResponseCode.ERROR_TIMEOUT -> {
                    val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                    _uiState.value = ReplacementCheckState.ChipCardCheck(
                        step = attempt + 1,
                        isLoading = false,
                        error = "Waktu habis",
                        stepResults = updateStepResults(
                            prevState,
                            attempt - 1,
                            false,
                            "Waktu habis"
                        )
                    )
                    _snackbarMessage.value =
                        Pair("Waktu pembacaan kartu chip #$attempt habis.", null)

                    // Store card details for this attempt
                    while (chipCardDetails.size < attempt) chipCardDetails.add(null)
                    chipCardDetails.add(null)
                }

                // Generic error or null result
                else -> {
                    val prevState = _uiState.value as? ReplacementCheckState.ChipCardCheck
                    val errorCode = result?.data?.getString("error") ?: "unknown"
                    val errorMessage = "Gagal membaca kartu chip (kode: $errorCode)"
                    _uiState.value = ReplacementCheckState.ChipCardCheck(
                        step = attempt,
                        isLoading = false,
                        error = errorMessage,
                        stepResults = updateStepResults(prevState, attempt - 1, false, errorMessage)
                    )
                    _snackbarMessage.value = Pair("Pembacaan kartu chip #$attempt gagal: $errorMessage", null)

                    // Store card details for this attempt
                    while (chipCardDetails.size < attempt) chipCardDetails.add(null)
                    chipCardDetails.add(null)
                }
            }
        }
    }


    fun performMagStripeCardRead(attempt: Int) {
        //TODO kesa has to add magnetic cardRead
        // check startCheckCard implementation
        viewModelScope.launch {
            if (!cardReaderHelper.isDeviceConnected()) {
                _snackbarMessage.value = Pair("EDC tidak terhubung.",null)
                val currentState = _uiState.value
                val chipState = when (currentState) {
                    is ReplacementCheckState.Composite -> currentState.chipCardCheck
                    is ReplacementCheckState.ChipCardCheck -> currentState
                    else -> ReplacementCheckState.ChipCardCheck(step = 4)
                }
                val prevMagStripeState = when (currentState) {
                    is ReplacementCheckState.Composite -> currentState.magStripeCardCheck
                    is ReplacementCheckState.MagStripeCardCheck -> currentState
                    else -> ReplacementCheckState.MagStripeCardCheck(step = attempt)
                }
                val magStripeState = ReplacementCheckState.MagStripeCardCheck(step = attempt, error = "EDC tidak terhubung", isLoading = false, stepResults = prevMagStripeState.stepResults)
                _uiState.value = ReplacementCheckState.Composite(
                    chipCardCheck = chipState,
                    magStripeCardCheck = magStripeState
                )
                return@launch
            }
            if (attempt == 0) { // Came from chip done, now starting magstripe checks
                val currentState = _uiState.value
                val chipState = when (currentState) {
                    is ReplacementCheckState.Composite -> currentState.chipCardCheck
                    is ReplacementCheckState.ChipCardCheck -> currentState
                    else -> ReplacementCheckState.ChipCardCheck(step = 4)
                }
                val prevMagStripeState = when (currentState) {
                    is ReplacementCheckState.Composite -> currentState.magStripeCardCheck
                    is ReplacementCheckState.MagStripeCardCheck -> currentState
                    else -> ReplacementCheckState.MagStripeCardCheck(step = 1)
                }
                val magStripeState = ReplacementCheckState.MagStripeCardCheck(step = 1, isLoading = false, stepResults = prevMagStripeState.stepResults)
                _uiState.value = ReplacementCheckState.Composite(
                    chipCardCheck = chipState,
                    magStripeCardCheck = magStripeState
                )
                return@launch
            }

            val currentState = _uiState.value
            val chipState = when (currentState) {
                is ReplacementCheckState.Composite -> currentState.chipCardCheck
                is ReplacementCheckState.ChipCardCheck -> currentState
                else -> ReplacementCheckState.ChipCardCheck(step = 4)
            }
            val prevMagStripeState = when (currentState) {
                is ReplacementCheckState.Composite -> currentState.magStripeCardCheck
                is ReplacementCheckState.MagStripeCardCheck -> currentState
                else -> ReplacementCheckState.MagStripeCardCheck(step = attempt)
            }
            val magStripeLoadingState = ReplacementCheckState.MagStripeCardCheck(
                step = attempt,
                isLoading = true,
                stepResults = prevMagStripeState.stepResults
            )
            _uiState.value = ReplacementCheckState.Composite(
                chipCardCheck = chipState,
                magStripeCardCheck = magStripeLoadingState
            )
            val emvBundle = Bundle().apply {
                putString(KEY_AUTH_AMOUNT_String, "0")
                putByte(KEY_transProcessCode_byte, 0x30.toByte())
                putByte(KEY_TRANSACTION_TIMEOUT_byte, 0x14.toByte()) // 20 seconds timeout
                putBoolean(KEY_FORCE_ONLINE_boolean, true)
            }
            val result: CardReaderResult? = withContext(Dispatchers.IO) {
                try {
                    cardReaderHelper.readCard(emvBundle)
                } catch (e: Exception) {
                    Log.e("MagRead", "Error reading magstripe card: ${e.message}", e)
                    null
                }
            }

            // Process the result
            when {
                // Success case
                result != null && result.response == com.bukuwarung.cardreader.tianyu.contant.ResponseCode.SUCCESS -> {
                    // Store card details for this attempt
                    while (magStripeCardDetails.size < attempt) magStripeCardDetails.add(null)
                    magStripeCardDetails.add(extractCardDetails(result.data))

                    // If first attempt succeeds, skip remaining attempts and complete all checks
                    if (attempt == 1) {
                        // Mark first attempt as success, remaining as skipped
                        val magStripeStepResults = listOf(
                            Pair(true, null), // First attempt success
                            Pair(null, "Dilewati"), // Second attempt skipped
                            Pair(null, "Dilewati")  // Third attempt skipped
                        )
                        _uiState.value = ReplacementCheckState.AllChecksCompleted(
                            chipCardStepResults = chipState.stepResults,
                            magStripeStepResults = magStripeStepResults
                        )
                        _snackbarMessage.value = Pair("Pembacaan kartu magnetik berhasil pada percobaan pertama. Semua pengecekan selesai.", null)
                    } else if (attempt < 3) {
                        // Continue with normal flow for subsequent attempts
                        val magStripeState = ReplacementCheckState.MagStripeCardCheck(
                            step = attempt + 1,
                            isLoading = false,
                            stepResults = updateMagStripeStepResults(prevMagStripeState, attempt - 1, true)
                        )
                        _uiState.value = ReplacementCheckState.Composite(
                            chipCardCheck = chipState,
                            magStripeCardCheck = magStripeState
                        )
                        _snackbarMessage.value = Pair("Pembacaan kartu gesek #${attempt} berhasil.", null)
                    } else {
                        // All magstripe checks done (attempt 3), complete all checks
                        _uiState.value = ReplacementCheckState.AllChecksCompleted(
                            chipCardStepResults = chipState.stepResults,
                            magStripeStepResults = updateMagStripeStepResults(prevMagStripeState, attempt - 1, true)
                        )
                        _snackbarMessage.value = Pair("Semua pembacaan kartu gesek selesai.", null)
                    }
                }

                // Handle error cases
                else -> {
                    val errorMessage = "Gagal membaca kartu gesek"

                    // Store card details for this attempt
                    while (magStripeCardDetails.size < attempt) magStripeCardDetails.add(null)
                    magStripeCardDetails.add(null)

                    if (attempt < 3) {
                        val magStripeState = ReplacementCheckState.MagStripeCardCheck(
                            step = attempt + 1,
                            error = errorMessage,
                            isLoading = false,
                            stepResults = updateMagStripeStepResults(prevMagStripeState, attempt - 1, false, errorMessage)
                        )
                        _uiState.value = ReplacementCheckState.Composite(
                            chipCardCheck = chipState,
                            magStripeCardCheck = magStripeState
                        )
                        _snackbarMessage.value = Pair("Pembacaan kartu gesek #${attempt} gagal: $errorMessage", null)
                    } else {
                        // All attempts done, complete all checks
                        _uiState.value = ReplacementCheckState.AllChecksCompleted(
                            chipCardStepResults = chipState.stepResults,
                            magStripeStepResults = updateMagStripeStepResults(prevMagStripeState, attempt - 1, false, errorMessage)
                        )
                        _snackbarMessage.value = Pair("Semua pembacaan kartu gesek selesai.", null)
                    }
                }
            }
        }
    }

    // Helper function to update the step results list for chip card
    private fun updateStepResults(currentState: ReplacementCheckState.ChipCardCheck?, stepIndex: Int, success: Boolean, errorMessage: String? = null): List<Pair<Boolean?, String?>> {
        val currentResults = currentState?.stepResults ?: listOf(Pair(null, null), Pair(null, null), Pair(null, null))
        return currentResults.toMutableList().apply {
            if (stepIndex in 0..2) {
                this[stepIndex] = Pair(success, errorMessage)
            }
        }
    }

    // Helper function to update the step results list for magstripe card
    private fun updateMagStripeStepResults(currentState: ReplacementCheckState.MagStripeCardCheck?, stepIndex: Int, success: Boolean, errorMessage: String? = null): List<Pair<Boolean?, String?>> {
        val currentResults = currentState?.stepResults ?: listOf(Pair(null, null), Pair(null, null), Pair(null, null))
        return currentResults.toMutableList().apply {
            if (stepIndex in 0..2) {
                this[stepIndex] = Pair(success, errorMessage)
            }
        }
    }
    
    fun dismissSnackbar() {
        _snackbarMessage.value = Pair(null,null)
        _snackbarAction.value = null
    }

    fun resetChecks() {
        chipCardDetails.clear()
        magStripeCardDetails.clear()
        // Re-initialize or just set to initial after ensuring BT status?
        checkBluetoothStatus() // This will set to Initial if BT is disconnected, or BluetoothConnected if connected.
        // If already connected, user might want to restart checks, not re-pair BT.
        if (cardReaderHelper.isDeviceConnected()) {
            _uiState.value = ReplacementCheckState.BluetoothConnected // Reset to start checks again from chip
        } else {
            _uiState.value = ReplacementCheckState.Initial
        }
    }

    fun setBluetoothConnectSuccess(message: String) {
        _uiState.value = ReplacementCheckState.BluetoothConnectSuccess(message)
        _snackbarMessage.value = Pair(message,"Success")
//        viewModelScope.launch {
//            kotlinx.coroutines.delay(1500)
//            _uiState.value = ReplacementCheckState.ChipCardCheck(step = 1)
//        }
    }
    fun setBluetoothConnectFailure(message: String) {
        _uiState.value = ReplacementCheckState.BluetoothConnectFailure(message)
        _snackbarMessage.value = Pair(message,"Error")
    }

    // Consider adding methods to handle specific errors or to navigate the user.
    // e.g., onDeviceNotFound, onPairingFailed, etc.

    fun buildQualityCheckDetailsDataClass(
        chipResults: List<Pair<Boolean?, String?>>,
        magResults: List<Pair<Boolean?, String?>>, 
        chipCardDetails: List<CardDetails?>, 
        magCardDetails: List<CardDetails?>
    ): QualityCheckDetails {
        fun toFirstCard(status: Boolean?, details: CardDetails?): CardReadingStatus? =
            when (status) {
                true -> CardReadingStatus(cardDetails = details, status = "SUCCESS")
                false -> CardReadingStatus(cardDetails = details, status = "FAILURE")
                null -> null
            }
        fun toSecondCard(status: Boolean?, details: CardDetails?): CardReadingStatus? =
            when (status) {
                true -> CardReadingStatus(cardDetails = details, status = "SUCCESS")
                false -> CardReadingStatus(cardDetails = details, status = "FAILURE")
                null -> null
            }
        fun toThirdCard(status: Boolean?, details: CardDetails?): CardReadingStatus? =
            when (status) {
                true -> CardReadingStatus(cardDetails = details, status = "SUCCESS")
                false -> CardReadingStatus(cardDetails = details, status = "FAILURE")
                null -> null
            }
        return QualityCheckDetails(
            bluetoothStatus = BluetoothStatus(status = "SUCCESS"),
            chipCardReadings = ChipCardReadings(
                firstCard = toFirstCard(chipResults.getOrNull(0)?.first, chipCardDetails.getOrNull(0)),
                secondCard = toSecondCard(chipResults.getOrNull(1)?.first, chipCardDetails.getOrNull(1)),
                thirdCard = toThirdCard(chipResults.getOrNull(2)?.first, chipCardDetails.getOrNull(2))
            ),
            magneticCardReadings = MagneticCardReadings(
                firstCard = toFirstCard(magResults.getOrNull(0)?.first, magCardDetails.getOrNull(0)),
                secondCard = toSecondCard(magResults.getOrNull(1)?.first, magCardDetails.getOrNull(1)),
                thirdCard = toThirdCard(magResults.getOrNull(2)?.first, magCardDetails.getOrNull(2))
            )
        )
    }

    private fun configureEmv() = viewModelScope.launch {
        //configuration should be loaded only once, don't calling this flow on normal android devices to avoid crash
        if (Build.MANUFACTURER == DEVICE_MANUFACTURER_MOREFUN) {
            try {
                val bundle = Bundle()
                val businessId = "00000000"
                val ret: Int = DeviceHelper.getDeviceService().login(bundle, businessId)
                bwLog("DeviceHelper.getDeviceService().login(bundle, $businessId): $ret")
            } catch (e: Exception) {
                bwLog("ReplacementViewModel", "error on login device service")
                bwLog(e = e)
            }
        }
        if(!Utils.hasLoadedConfig()) {
            configureAidUseCase()
            configureRidUseCase()
        }
    }

    fun uploadReplacementForm(
        userId: String,
        terminalId: String,
        serialNumber: String,
        deviceType: String,
        vendor: String,
        issue: List<String>,
        description: String?,
        evidencePhotoS3Path: String?,
        evidenceVideoS3Path: String?
    ) {
        val state = _uiState.value as? ReplacementCheckState.AllChecksCompleted
        val qualityCheckDetails = buildQualityCheckDetailsDataClass(
            chipResults = state?.chipCardStepResults ?: emptyList(),
            magResults = state?.magStripeStepResults ?: emptyList(),
            chipCardDetails = chipCardDetails,
            magCardDetails = magStripeCardDetails
        )
        val requestBody = ReplacementFormSubmitRequest(
            userId = userId,
            terminalId = terminalId,
            serialNumber = serialNumber,
            deviceType = deviceType,
            vendor = vendor,
            issue = issue,
            description = description,
            evidencePhotoS3Path = evidencePhotoS3Path,
            evidenceVideoS3Path = evidenceVideoS3Path,
            qualityCheckDetails = qualityCheckDetails
        )
        Log.d("ReplacementViewModel", "Uploading replacement form: $requestBody")
        viewModelScope.launch {
            try {
                val response = useCase.invoke(requestBody)
                if (response.isSuccess) {
                    _uiState.value = ReplacementCheckState.UploadSuccess
                    _snackbarMessage.value = Pair("Formulir penggantian berhasil dikirim.", "Success")
                } else {
                    _snackbarMessage.value = Pair("Gagal mengirim formulir penggantian: ${response.exceptionOrNull()?.message}", "Error")
                }
            } catch (e: Exception) {
                _snackbarMessage.value = Pair("Terjadi kesalahan saat mengirim formulir penggantian: ${e.message}", "Error")
            }
        }
    }

    fun startBluetoothScan() {
        viewModelScope.launch {
            val device = Utils.getUserRegisteredDevices()
            val deviceSerialNumberList = device.map { it.serialNumber.orEmpty() }

            // Check if needs pairing analytics
            val needsPairingAnalytics = !BluetoothDevices.hasPairedCardReader()

            _bluetoothScanAction.value = BluetoothScanData(
                deviceSerialNumberList = ArrayList(deviceSerialNumberList),
                trackPairingAnalytics = needsPairingAnalytics
            )
        }
    }

    fun connectChipForFixedTerminal(){
       //TODO Kesa to add connect chp function
    }

    fun clearBluetoothScanAction() {
        _bluetoothScanAction.value = null
    }

    data class BluetoothScanData(
        val deviceSerialNumberList: ArrayList<String>,
        val trackPairingAnalytics: Boolean
    )
}
