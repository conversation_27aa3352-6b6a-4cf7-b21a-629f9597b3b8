package com.bukuwarung.edc.replacement.data

import com.bukuwarung.edc.replacement.data.api.ReplacementApi
import com.bukuwarung.edc.replacement.data.model.ReplacementDetailsResponse
import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceResponse
import com.bukuwarung.edc.replacement.data.model.ReplacementFormSubmitRequest
import com.bukuwarung.edc.replacement.data.model.SignedImageResponse
import com.bukuwarung.edc.replacement.domain.ReplacementRepository
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import javax.inject.Inject

class ReplacementRepositoryImpl @Inject constructor(
    private val replacementApi: ReplacementApi
) : ReplacementRepository {

    override suspend fun getReplacementReasons(deviceType: String) = replacementApi.getReplacementReasons(deviceType)
    
    override suspend fun uploadReplacementEvidence(
        category: RequestBody,
        fileName: RequestBody,
        file: MultipartBody.Part
    ): Response<ReplacementEvidenceResponse> = replacementApi.uploadReplacementEvidence(category, fileName, file)

    override suspend fun getReplacementDetails(
        userId: String,
        terminalId: String,
        serialNumber: String
    ): Response<ReplacementDetailsResponse> {
        return replacementApi.getReplacementDetails(userId, terminalId, serialNumber)
    }

    override suspend fun submitReplacementRequest(requestBody: ReplacementFormSubmitRequest): Response<ReplacementDetailsResponse> {
        return replacementApi.submitReplacementRequest(requestBody)
    }

    override suspend fun fetchAwsS3Image(s3FilePath: String): Response<SignedImageResponse> {
        return replacementApi.fetchAwsS3Image(s3FilePath)
    }
}
