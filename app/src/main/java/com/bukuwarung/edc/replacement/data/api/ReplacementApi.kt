package com.bukuwarung.edc.replacement.data.api

import com.bukuwarung.edc.replacement.data.model.ReplacementDetailsResponse
import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceResponse
import com.bukuwarung.edc.replacement.data.model.ReplacementFormSubmitRequest
import com.bukuwarung.edc.replacement.data.model.ReplacementReasonResponse
import com.bukuwarung.edc.replacement.data.model.SignedImageResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Query

interface ReplacementApi {
    
    @GET("ac/api/v2/edc/replacement/reasons")
    suspend fun getReplacementReasons(@Query("deviceType") deviceType: String): Response<ReplacementReasonResponse>
    
    @Multipart
    @POST("ac/api/v2/upload/replacement-evidence")
    suspend fun uploadReplacementEvidence(
        @Part("category") category: RequestBody,
        @Part("fileName") fileName: RequestBody,
        @Part file: MultipartBody.Part
    ): Response<ReplacementEvidenceResponse>

    @GET("ac/api/v2/edc/replacement/details")
    suspend fun getReplacementDetails(
        @Query("userId") userId: String,
        @Query("terminalId") terminalId: String,
        @Query("serialNumber") serialNumber: String
    ): Response<ReplacementDetailsResponse>

    @POST("ac/api/v2/edc/replacement/submit")
    suspend fun submitReplacementRequest(
        @Body requestBody: ReplacementFormSubmitRequest
    ): Response<ReplacementDetailsResponse>

    @GET("ac/api/v2/fetch/signed-url")
    suspend fun fetchAwsS3Image(
        @Query("path") s3FilePath: String,
    ): Response<SignedImageResponse>
}
