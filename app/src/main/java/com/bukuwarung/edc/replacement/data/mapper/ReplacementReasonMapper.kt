package com.bukuwarung.edc.replacement.data.mapper

import com.bukuwarung.edc.replacement.data.model.ReplacementReasonDto
import com.bukuwarung.edc.replacement.domain.model.ReplacementFormReason

fun ReplacementReasonDto.toDomain(id: String): ReplacementFormReason {
    return ReplacementFormReason(
        id = id,
        label = label,
        imageMandatory = imageMandatory,
        videoMandatory = videoMandatory
    )
}

fun List<ReplacementReasonDto>.toDomain(): List<ReplacementFormReason> {
    return mapIndexed { index, dto ->
        dto.toDomain(id = (index + 1).toString().padStart(2, '0'))
    }
}
