package com.bukuwarung.edc.replacement.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class ReplacementDetailsResponse(

    @SerializedName("result")
    val result: Boolean? = null,

    @SerializedName("data")
    val data: DeviceReplacementDetailsData? = null
)

@Keep
data class MerchantDetails(

    @SerializedName("phone")
    val phone: String? = null,

    @SerializedName("newSerialNumber")
    val newSerialNumber: String? = null,

    @SerializedName("awbNumberOps")
    val awbNumberOps: String? = null,
    @SerializedName("awbPhotoFromMerchant")
    val awbPhotoFromMerchant: String? = null,
    @SerializedName("awbPhotoFromOps")
    val awbPhotoFromOps: String? = null,
)

data class DeviceInformation(

    @SerializedName("serialNumber")
    val serialNumber: String? = null,

    @SerializedName("tid")
    val tid: String? = null
)

data class DeviceReplacementDetailsData(

    @SerializedName("createdAt")
    val createdAt: String? = null,

    @SerializedName("deviceInformation")
    val deviceInformation: DeviceInformation? = null,

    @SerializedName("orderProgress")
    val orderProgress: OrderProgress? = null,

    @SerializedName("merchantDetails")
    val merchantDetails: MerchantDetails? = null,

    @SerializedName("issues")
    val issues: List<String?>? = null,

    @SerializedName("status")
    val status: String? = null,
    @SerializedName("returnAddress")
    val returnAddress: ReturnAddress? = null,
)

@Keep
data class OrderProgress(

    @SerializedName("COMPLETED")
    val completed: String? = null,

    @SerializedName("NEW_DEVICE_IS_ON_SHIPPING")
    val newDeviceIsOnShipping: String? = null,

    @SerializedName("WAITING_FOR_SENDING_NEW_DEVICE")
    val waitingForSendingNewDevice: String? = null,

    @SerializedName("WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB")
    val waitingMerchantSendDeviceAndUploadAwb: String? = null,
    @SerializedName("AWAITING_APPROVAL")
    val awaitingApproval: String? = null,
    @SerializedName("WAITING_AWB_VERIFICATION")
    val waitingAwbVerification: String? = null,
    @SerializedName("REJECTED")
    val rejected: String? = null,
    @SerializedName("AWB_REJECTED")
    val awbRejected: String? = null,

    )
@Keep
data class ReturnAddress(

    @SerializedName("recipient")
    val recipient: String? = null,

    @SerializedName("phoneNumber")
    val phoneNumber: String? = null,

    @SerializedName("address")
    val address: String? = null,

    @SerializedName("notes")
    val notes: String? = null,
)