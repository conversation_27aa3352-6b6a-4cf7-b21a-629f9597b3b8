package com.bukuwarung.edc.replacement.domain.usecase

import com.bukuwarung.edc.replacement.data.model.ReplacementDetailsResponse
import com.bukuwarung.edc.replacement.domain.ReplacementRepository
import javax.inject.Inject

class GetReplacementDetailsUseCase @Inject constructor(
    private val repository: ReplacementRepository
) {
    suspend operator fun invoke(
        userId: String,
        terminalId: String,
        serialNumber: String
    ): Result<ReplacementDetailsResponse> {
        return try {
            val response = repository.getReplacementDetails(userId, terminalId, serialNumber)
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.result == true) {
                    Result.success(body)
                } else {
                    Result.failure(Exception("API returned result: false"))
                }
            } else {
                Result.failure(Exception("HTTP ${response.code()}: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}