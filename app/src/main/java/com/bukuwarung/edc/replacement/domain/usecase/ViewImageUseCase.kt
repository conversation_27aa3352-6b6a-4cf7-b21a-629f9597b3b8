package com.bukuwarung.edc.replacement.domain.usecase

import com.bukuwarung.edc.replacement.data.model.SignedImageResponse
import com.bukuwarung.edc.replacement.domain.ReplacementRepository
import com.bukuwarung.edc.util.isNotNullOrBlank
import javax.inject.Inject

class ViewImageUseCase @Inject constructor(
    private val repository: ReplacementRepository
) {
    suspend operator fun invoke(s3FilePath: String): Result<SignedImageResponse> {
        return try {
            val response = repository.fetchAwsS3Image(s3FilePath)
            if (response.isSuccessful) {
                val body = response.body()
                Result.success(
                    body ?: SignedImageResponse()
                )
            } else {
                Result.failure(Exception("HTTP ${response.code()}: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}