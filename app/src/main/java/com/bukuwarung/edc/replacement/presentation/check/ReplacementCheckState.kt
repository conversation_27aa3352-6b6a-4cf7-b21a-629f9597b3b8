package com.bukuwarung.edc.replacement.presentation.check

sealed class ReplacementCheckState {
    object Initial : ReplacementCheckState()
    data class Error(val message: String) : ReplacementCheckState() // General error state for connection etc.
    object BluetoothConnecting : ReplacementCheckState()
    object BluetoothConnected : ReplacementCheckState()
    data class BluetoothConnectSuccess(val message: String) : ReplacementCheckState()
    data class BluetoothConnectFailure(val message: String) : ReplacementCheckState()
    // step: 0 means ready to start checks (after BT is connected)
    // step: 1, 2, 3 are for the actual card read attempts
    data class ChipCardCheck(
        val step: Int,
        val isLoading: Boolean = false,
        val error: String? = null,
        val stepResults: List<Pair<Boolean?, String?>> = listOf(
            Pair(null, null),
            Pair(null, null),
            Pair(null, null)
        )
    ) : ReplacementCheckState()

    data class MagStripeCardCheck(
        val step: Int,
        val isLoading: Boolean = false,
        val error: String? = null,
        val stepResults: List<Pair<Boolean?, String?>> = listOf(
            Pair(null, null),
            Pair(null, null),
            Pair(null, null)
        )
    ) : ReplacementCheckState()

    data class AllChecksCompleted(
        val chipCardStepResults: List<Pair<Boolean?, String?>>,
        val magStripeStepResults: List<Pair<Boolean?, String?>>
    ) : ReplacementCheckState()

    data class Composite(
        val chipCardCheck: ChipCardCheck,
        val magStripeCardCheck: MagStripeCardCheck
    ) : ReplacementCheckState()

    object UploadSuccess: ReplacementCheckState()
}