package com.bukuwarung.edc.replacement.presentation.details

import android.Manifest
import android.os.Bundle
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.bukuwarung.edc.R
import com.bukuwarung.edc.components.BukuComposeDialog
import com.bukuwarung.edc.replacement.presentation.check.ReplacementScreenActivity.IntentExtras.DEVICE_SERIAL_NUMBER
import com.bukuwarung.edc.replacement.presentation.check.ReplacementScreenActivity.IntentExtras.DEVICE_TERMINAL_NUMBER
import com.bukuwarung.edc.replacement.presentation.check.ReplacementScreenActivity.IntentExtras.USER_ID
import com.bukuwarung.edc.replacement.components.CameraOverlayActivity
import com.bukuwarung.edc.replacement.components.InfoCard
import com.bukuwarung.edc.replacement.components.InfoRow
import com.bukuwarung.edc.replacement.components.PrimaryButton
import com.bukuwarung.edc.replacement.components.RejectionWarningCard
import com.bukuwarung.edc.replacement.components.ReturnAddressCard
import com.bukuwarung.edc.replacement.components.StatusBadge
import com.bukuwarung.edc.replacement.components.StatusCard
import com.bukuwarung.edc.replacement.data.model.DeviceInformation
import com.bukuwarung.edc.replacement.data.model.DeviceReplacementDetailsData
import com.bukuwarung.edc.replacement.data.model.MerchantDetails
import com.bukuwarung.edc.replacement.data.model.OrderProgress
import com.bukuwarung.edc.replacement.data.model.ReplacementDetailsResponse
import com.bukuwarung.edc.replacement.data.model.ReturnAddress
import com.bukuwarung.edc.replacement.presentation.check.ReplacementScreenActivity.IntentExtras.DEVICE_NAME
import com.bukuwarung.edc.replacement.presentation.check.ReplacementScreenActivity.IntentExtras.DEVICE_TYPE
import com.bukuwarung.edc.replacement.presentation.form.ReplacementFormActivity
import com.bukuwarung.edc.replacement.presentation.form.ReplacementFormActivity.IntentExtras.WARRANTY_EXPIRED_AT
import com.bukuwarung.edc.replacement.presentation.form.ReplacementFormIntentExtras
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.DateTimeUtils.YYYY_MM_DD_T_HH_MM_SS
import com.bukuwarung.edc.util.DateTimeUtils.getUTCTimeToLocalDateTime
import com.bukuwarung.edc.util.DateTimeUtils.isWarrantyExpired
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orDash
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint


// Helper data class for returning four values
data class Quadruple<A, B, C, D>(
    val first: A,
    val second: B,
    val third: C,
    val fourth: D
)

@AndroidEntryPoint
class ReplacementMonitoringActivity : ComponentActivity() {

    private val viewModel: ReplacementMonitoringViewModel by viewModels()
    private val cameraLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.getStringExtra(CameraOverlayActivity.EXTRA_IMAGE_PATH)?.let { imagePath ->
                viewModel.uploadAwbPhoto(imagePath)
            }
        }
    }
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            launchCamera()
        } else {
            Toast.makeText(this, "Camera permission required", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val userId = intent.getStringExtra(USER_ID) ?: ""
        val terminalId = intent.getStringExtra(DEVICE_TERMINAL_NUMBER) ?: ""
        val serialNumber = intent.getStringExtra(DEVICE_SERIAL_NUMBER) ?: ""
        val warrantyExpiryDate = intent.getStringExtra(WARRANTY_EXPIRED_AT) ?: ""
        val deviceType = intent.getStringExtra(DEVICE_TYPE) ?: ""
        val deviceVendor = intent.getStringExtra(DEVICE_NAME) ?: ""

        val isWarrantyExpired = isWarrantyExpired(warrantyExpiryDate,YYYY_MM_DD_T_HH_MM_SS)

        viewModel.fetchDetails(userId, terminalId, serialNumber)
        setContent {
            val detailState by viewModel.detailState.collectAsState()
            val awbUploadState by viewModel.awbUploadState.collectAsState()
            var showAwbSuccessDialog by remember { mutableStateOf(false) }
            var showAwbErrorDialog by remember { mutableStateOf(false) }

            // Handle different upload states
            LaunchedEffect(awbUploadState) {
                when (awbUploadState) {
                    is ImageUploadState.Success -> {
                        showAwbSuccessDialog = true
                        // Show success message and refresh details
                        Toast.makeText(this@ReplacementMonitoringActivity, "AWB photo uploaded successfully!", Toast.LENGTH_SHORT).show()
                        // Refresh the details to get updated status
                    }
                    is ImageUploadState.Error -> {
                        // Show error message
                        Toast.makeText(this@ReplacementMonitoringActivity,
                            "Upload failed: ${(awbUploadState as ImageUploadState.Error).errorMessage}",
                            Toast.LENGTH_LONG).show()
                        showAwbErrorDialog = true
                    }
                    is ImageUploadState.Loading, is ImageUploadState.Idle -> {
                        // No action needed for these states
                    }
                }
            }

            // Handle detail state errors
            LaunchedEffect(detailState) {
                when (detailState) {
                    is DetailState.Error -> {
                        Toast.makeText(this@ReplacementMonitoringActivity,
                            "Failed to load details: ${(detailState as DetailState.Error).errorMessage}",
                            Toast.LENGTH_LONG).show()
                    }
                    else -> {
                        // No action needed for other states
                    }
                }
            }

            ReplacementMonitoringScreen(
                detailState = detailState,
                uploadAwb = { launchCamera() },
                requestPermission = { requestPermissionLauncher.launch(Manifest.permission.CAMERA) },
                awbUploadState = awbUploadState,
                isWarrantyExpired = isWarrantyExpired,
                submitReplacementRequest = {
                    openActivity(ReplacementFormActivity::class.java) {
                        putString(ReplacementFormIntentExtras.DEVICE_TYPE, deviceType)
                        putString(ReplacementFormIntentExtras.DEVICE_NAME, deviceVendor)
                        putString(
                            ReplacementFormIntentExtras.DEVICE_TERMINAL_NUMBER,
                            terminalId
                        )
                        putString(
                            ReplacementFormIntentExtras.DEVICE_SERIAL_NUMBER,
                            serialNumber
                        )
                        putString(ReplacementFormIntentExtras.USER_ID, Utils.getUserId())
                        putString(
                            ReplacementFormIntentExtras.WARRANTY_EXPIRED_AT,
                            warrantyExpiryDate
                        )
                    }
                }
            )

            // Show success dialog when AWB upload is successful
            BukuComposeDialog(
                showDialog = showAwbSuccessDialog,
                title = "Berhasil Upload Bukti Pengiriman",
                subTitle = "Bukti pengiriman Anda telah berhasil kami terima, silakan cek secara berkala untuk mengetahui pembaruan status.",
                btnRightText = "Kembali",
                imageRes = com.bukuwarung.bluetooth_printer.R.drawable.ic_success_ok,
                onLeftButtonClick = {
                    // no Implementation
                },
                onRightButtonClick = {
                    showAwbSuccessDialog = false
                },
                onDismiss = {
                    showAwbSuccessDialog = false
                }
            )

            BukuComposeDialog(
                showDialog = showAwbErrorDialog,
                title = "Gagal Ambil Gambar",
                subTitle = "Lakukan kembali pengambilan gambar ulang dengan hasil yang lebih baik.",
                btnRightText = "Ambil Gambar Ulang",
                imageRes = com.bukuwarung.bluetooth_printer.R.drawable.ic_system_error,
                onLeftButtonClick = {
                    // no Implementation
                },
                onRightButtonClick = {
                    showAwbErrorDialog = false
                },
                onDismiss = {
                    showAwbErrorDialog = false
                }
            )
        }
    }


    private fun launchCamera() {
        val intent = CameraOverlayActivity.newIntent(this)
        cameraLauncher.launch(intent)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReplacementMonitoringScreen(
    detailState: DetailState = DetailState.Idle,
    uploadAwb: () -> Unit = {},
    requestPermission: () -> Unit = {},
    awbUploadState: ImageUploadState = ImageUploadState.Idle,
    isWarrantyExpired: Boolean = false,
    submitReplacementRequest:()->Unit = {},
) {
    var showImageDialog by remember { mutableStateOf(false) }
    var imageUrl by remember { mutableStateOf("") }

    // Extract data from DetailState when it's Success
    val details = when (detailState) {
        is DetailState.Success -> detailState.data
        else -> null
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF1F1F1))
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = stringResource(id = R.string.billing_details),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = { /* Handle back navigation */ }) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_back),
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
            },
            actions = {
                IconButton(onClick = { /* Handle help */ }) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_baseline_help_outline),
                        contentDescription = "Help",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF0091FF)
            )
        )

        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
            ) {
                // Blue background section
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color(0xFF0091FF))
                        .padding(16.dp)
                ) {
                    Column {
                        // Status Badge and Date
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            //Status Badge
                            StatusBadge(status = details?.data?.status.orEmpty())

                            Text(
                                text = getUTCTimeToLocalDateTime(
                                    details?.data?.createdAt,
                                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                                ).orDash,
                                color = Color.White,
                                fontSize = 12.sp
                            )
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        // Order Info Card
                        InfoCard(title = "") {
                            InfoRow(
                                label = stringResource(R.string.order_id),
                                value = "INV1234567890"
                            )
                            InfoRow(
                                label = stringResource(R.string.no_terminal),
                                value = details?.data?.deviceInformation?.tid.orDash
                            )
                            InfoRow(
                                label = stringResource(R.string.no_seri),
                                value = details?.data?.deviceInformation?.serialNumber.orDash
                            )
                            InfoRow(
                                label = stringResource(R.string.no_id_pengguna),
                                value = "BukuWarung"
                            )
                        }
                    }
                }

                // Rest of the content with 16.dp padding
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {

                    when (details?.data?.status) {
                        ReplacementMonitoringViewModel.DeviceReplacementStatus.AWB_REJECTED.name -> {
                            // Rejection Card
                            RejectionWarningCard(
                                status = "",
                                title = "",
                                message = stringResource(id = R.string.delivery_proof_does_not_match)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                        }

                        ReplacementMonitoringViewModel.DeviceReplacementStatus.REJECTED.name -> {
                            RejectionWarningCard(
                                status = "REJECTED",
                                title = stringResource(id = R.string.rejection_title),
                                message = stringResource(id = R.string.rejection_message)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                        }
                    }



                    if (details?.data?.status == ReplacementMonitoringViewModel.DeviceReplacementStatus.WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB.name) {
                        // Return Address Card
                        ReturnAddressCard()
                        Spacer(modifier = Modifier.height(16.dp))
                    }


                    // Status Steps Card
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Column(modifier = Modifier.padding(bottom = 16.dp)) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .padding(bottom = 16.dp)
                                    .background(Color(0xFFF2F9FF), shape = CardDefaults.shape)
                                    .padding(16.dp, 8.dp, 16.dp, 8.dp)
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_sparkles),
                                    contentDescription = "Status",
                                    modifier = Modifier.size(24.dp),
                                    tint = Color(0xFF0091FF)
                                )
                                Column(modifier = Modifier.padding(start = 8.dp)) {
                                    Text(
                                        text = stringResource(R.string.status_pengajuan_penggantian_perangkat_edc),
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = Color(0xFF222222)
                                    )
                                    Text(
                                        text = stringResource(R.string.cek_secara_berkala_pergantian_status),
                                        fontSize = 12.sp,
                                        color = Color(0xFF919191)
                                    )
                                }
                            }

                            val progress = details?.data?.orderProgress
                            val status = details?.data?.status

                            val steps = listOf(
                                Triple(R.string.pengajuan_terkirim, "AWAITING_APPROVAL", false),
                                Triple(
                                    R.string.kirim_perangkat_edc_lama_lalu_upload_bukti_pengiriman,
                                    "WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB",
                                    false
                                ),
                                Triple(
                                    R.string.pengajuan_dalam_proses,
                                    "WAITING_AWB_VERIFICATION",
                                    false
                                ),

//                                Triple(
//                                    R.string.kirim_perangkat_edc_lama_lalu_upload_bukti_pengiriman,
//                                    "WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB",
//                                    true
//                                ),
                                Triple(
                                    R.string.perangkat_lama_telah_diterima,
                                    "WAITING_FOR_SENDING_NEW_DEVICE",
                                    false
                                ),
                                Triple(
                                    R.string.perangkat_edc_baru_telah_dikirim,
                                    "NEW_DEVICE_IS_ON_SHIPPING",
                                    false
                                ),
                                Triple(R.string.penukaran_perangkat_selesai, "COMPLETED", false)
                            )

                            fun isStepCompleted(stepKey: String): Boolean {
                                return when (stepKey) {
                                    "AWAITING_APPROVAL" -> !progress?.awaitingApproval.isNullOrEmpty()
                                    "WAITING_AWB_VERIFICATION" -> !progress?.waitingAwbVerification.isNullOrEmpty()
                                    "WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB" -> !progress?.waitingMerchantSendDeviceAndUploadAwb.isNullOrEmpty()
                                    "WAITING_FOR_SENDING_NEW_DEVICE" -> !progress?.waitingForSendingNewDevice.isNullOrEmpty()
                                    "NEW_DEVICE_IS_ON_SHIPPING" -> !progress?.newDeviceIsOnShipping.isNullOrEmpty()
                                    "COMPLETED" -> !progress?.completed.isNullOrEmpty()
                                    else -> false
                                }
                            }

                            var activeStepIndex = -1
                            val isRejected = !progress?.rejected.isNullOrEmpty()

                            if (!isRejected) {
                                for (i in steps.indices) {
                                    val (_, stepKey, _) = steps[i]
                                    if (!isStepCompleted(stepKey)) {
                                        activeStepIndex = i
                                        break
                                    }
                                }
                            }

                            steps.forEachIndexed { index, (titleRes, stepKey, showRightArrow) ->
                                val isCompleted = if (isRejected) {
                                    index < 2
                                } else {
                                    isStepCompleted(stepKey)
                                }

                                val isActive = if (isRejected) {
                                    false
                                } else {
                                    index == activeStepIndex
                                }

                                val isStepRejected = isRejected && index == 2

                                // Special handling for WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB step
                                val (finalTitle, finalShowRightArrow, finalSubtitle, finalClickableText) = if (stepKey == "WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB") {
                                    val stepCompleted = isStepCompleted(stepKey)
                                    if (stepCompleted) {
                                        // When completed: show "Silakan kirim..." title, no right arrow, and "Lihat Resi" as clickable text part of title
                                        Quadruple("Silakan kirim perangkat EDC lama, lalu upload bukti pengiriman.", false, null, "Lihat Resi")
                                    } else {
                                        // When not completed: show "Kirim perangkat..." title, show right arrow, and no clickable text
                                        Quadruple("Kirim perangkat EDC lama, lalu upload bukti pengiriman", true, null, null)
                                    }
                                } else {
                                    Quadruple(stringResource(titleRes), showRightArrow && index == 3, null, null)
                                }

                                StatusCard(
                                    modifier = Modifier.padding(horizontal = 16.dp),
                                    number = index + 1,
                                    title = finalTitle,
                                    isCompleted = isCompleted,
                                    isActive = isActive,
                                    isRejected = isStepRejected,
                                    showRightArrow = finalShowRightArrow,
                                    subtitle = finalSubtitle,
                                    clickableText = finalClickableText,
                                    onClickableTextClick = {
                                        // Handle "Lihat Resi" click - show the uploaded AWB image
                                        imageUrl = details?.data?.merchantDetails?.awbPhotoFromMerchant ?: ""
                                        showImageDialog = true
                                    },
                                    uploadAwb = { uploadAwb() }
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Reason Card
                    InfoCard(title = stringResource(R.string.alasan_pengajuan_penggantian)) {
                        details?.data?.issues?.forEach { issue ->
                            Text(
                                text = issue.orDash,
                                fontSize = 14.sp,
                                color = Color(0xFF5C5C5C),
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Shipping Info Card
                    InfoCard(title = stringResource(R.string.shipping_info)) {
                        InfoRow(
                            label = stringResource(id = R.string.receipt_number),
                            value = details?.data?.merchantDetails?.awbNumberOps.orDash
                        )
                        InfoRow(
                            label = stringResource(R.string.no_seri_perangkat_baru),
                            value = details?.data?.merchantDetails?.newSerialNumber.orDash
                        )
                    }

                    // Add padding at the bottom for the button
                    Spacer(modifier = Modifier.height(80.dp))
                }
            }

                    if (details?.data?.status == ReplacementMonitoringViewModel.DeviceReplacementStatus.REJECTED.name || (details?.data?.status == ReplacementMonitoringViewModel.DeviceReplacementStatus.COMPLETED.name && !isWarrantyExpired)) {
                        // Bottom Button
                        PrimaryButton(
                            text = stringResource(R.string.ajukan_penggantian_edc),
                            onClick = { 
                                submitReplacementRequest.invoke()
                            },
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .padding(16.dp),
                            enabled = awbUploadState !is ImageUploadState.Loading
                        )
                    }

                }

                if (showImageDialog) {
                    Dialog(
                        onDismissRequest = { showImageDialog = false },
                        properties = DialogProperties(
                            dismissOnBackPress = true,
                            dismissOnClickOutside = true
                        )
                    ) {
                        Box {
                            Card(
                                modifier = Modifier
                                    .size(300.dp)
                                    .clip(RoundedCornerShape(16.dp)),
                                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                            ) {
                                Column {

                                    Row(modifier = Modifier.fillMaxWidth()
                                        .background(Color.White)) {
                                        Spacer(modifier = Modifier.weight(1f))
                                        // Close button
                                        IconButton(
                                            onClick = { showImageDialog = false },
                                            modifier = Modifier
                                                .padding(8.dp)
                                        ) {
                                            Icon(
                                                painter = painterResource(id = R.drawable.ic_close),
                                                contentDescription = "Close",
                                                tint = Color.Black,
                                                modifier = Modifier.size(20.dp)
                                            )
                                        }
                                    }


                                    AndroidView(
                                        factory = { context ->
                                            ImageView(context).apply {
                                                scaleType = ImageView.ScaleType.FIT_CENTER
                                                Glide.with(context)
                                                    .load(imageUrl)
                                                    .into(this)
                                            }
                                        },
                                        modifier = Modifier.fillMaxSize()
                                    )
                                }

                            }


                        }
                    }
                }
            }
        }

@Preview
@Composable
fun ReplacementMonitoringScreenAwaitingApprovalPreview() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "AWAITING_APPROVAL",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "",
                "",
                "",
                "",
                awaitingApproval = "2025-06-29T16:00:12.068",
                ""
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )
    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}



@Preview
@Composable
fun ReplacementMonitoringScreenUploadWaitingMerchantSendDeviceAndUploadAwbPreview() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "WAITING_MERCHANT_SEND_DEVICE_AND_UPLOAD_AWB",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "",
                "",
                "",
                "2025-06-29T16:21:07.474",
                awaitingApproval = "2025-06-29T16:21:07.474",
                "",
                rejected = ""
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )
    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}

@Preview
@Composable
fun ReplacementMonitoringScreenWaitingAwbVerificationPreview() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "WAITING_AWB_VERIFICATION",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "",
                "",
                "",
                "2025-06-29T16:21:07.474",
                awaitingApproval = "2025-06-29T16:21:07.474",
                "2025-06-29T16:21:07.474",
                rejected = "",
                ""
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )
    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}

@Preview
@Composable
fun ReplacementMonitoringScreenAwbRejectedPreview() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "AWB_REJECTED",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "",
                "",
                "",
                "2025-06-29T16:21:07.474",
                awaitingApproval = "2025-06-29T16:21:07.474",
                "2025-06-29T16:21:07.474",
                rejected = "",
                awbRejected = "2025-06-29T16:21:07.474"
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )
    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}

@Preview
@Composable
fun ReplacementMonitoringAwaitingForSendingNewDevicePreview() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "WAITING_FOR_SENDING_NEW_DEVICE",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "",
                "",
                "2025-06-29T16:21:07.474",
                "2025-06-29T16:21:07.474",
                awaitingApproval = "2025-06-29T16:21:07.474",
                "2025-06-29T16:21:07.474",
                rejected = "",
                awbRejected = ""
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )
    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}

@Preview
@Composable
fun ReplacementMonitoringScreenNewDeviceShippedPreview() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "NEW_DEVICE_IS_ON_SHIPPING",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "",
                "2025-06-29T16:21:07.474",
                "2025-06-29T16:21:07.474",
                "2025-06-29T16:21:07.474",
                awaitingApproval = "2025-06-29T16:21:07.474",
                "2025-06-29T16:21:07.474",
                rejected = "",
                awbRejected = ""
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )
    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}

@Preview
@Composable
fun ReplacementMonitoringScreenPreviewCompleted() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "COMPLETED",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "2025-06-29T16:23:04.282",
                "2025-06-29T16:21:55.077",
                "2025-06-29T16:21:19.628",
                "2025-06-29T16:19:14.170",
                "2025-06-29T16:00:12.068",
                "2025-06-29T16:21:07.474"
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )

    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}
@Preview
@Composable
fun ReplacementMonitoringScreenRejectedPreview() {
    val sampleDetails = ReplacementDetailsResponse(
        result = true,
        data = DeviceReplacementDetailsData(
            createdAt = "2025-06-29T16:00:12.48",
            status = "REJECTED",
            deviceInformation = DeviceInformation(
                tid = "71800007",
                serialNumber = "63240805401404"
            ),
            merchantDetails = MerchantDetails(
                phone = "114643543711",
                awbNumberOps = "awb123",
                newSerialNumber = "63240805403172"
            ),
            orderProgress = OrderProgress(
                "",
                "",
                "",
                "",
                awaitingApproval = "",
                "",
                rejected = "2025-06-29T16:21:07.474"
            ),
            issues = listOf("LCD Rusak", "EDC Mati Total"),
            returnAddress = ReturnAddress(
                recipient = "BukuWarung (Satrio - Shipper)",
                phoneNumber = "0896 8296 0798",
                address = "Gudang Shipper Pulogadung JIEP - WH-CGK60 Kw. Industri Pulogadung, Jl. Pulo Lentut Kav. II E/4, RW.1, Rw. Terate, Kec. Cakung, Kota Jakarta Timur, Daerah Khusus Ibukota Jakarta 13920",
                notes = "Pengiriman hanya bisa dilakukan secara reguler (biaya pengiriman ditanggung pengirim). Pengiriman secara COD otomatis ditolak."

            )
        )
    )
    ReplacementMonitoringScreen(
        detailState = DetailState.Success(sampleDetails),
        uploadAwb = {},
        requestPermission = {},
        awbUploadState = ImageUploadState.Idle,
        isWarrantyExpired = false
    )
}
