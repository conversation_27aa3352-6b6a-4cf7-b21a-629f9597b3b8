package com.bukuwarung.edc.replacement.presentation.form

import android.Manifest
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Toast
import androidx.activity.addCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.bluetooth_printer.utils.clickWithDebounce
import com.bukuwarung.edc.R
import com.bukuwarung.edc.core.upload.util.NotificationPermissionHelper
import com.bukuwarung.edc.databinding.ActivityReplacementFormBinding
import com.bukuwarung.edc.replacement.presentation.check.ReplacementScreenActivity
import com.bukuwarung.edc.replacement.presentation.check.ReplacementScreenIntentExtras
import com.bukuwarung.edc.replacement.presentation.dialog.ReplacementPhotoViewDialog
import com.bukuwarung.edc.replacement.presentation.dialog.ReplacementVideoPlaybackDialog
import com.bukuwarung.edc.replacement.presentation.form.ReplacementFormActivity.IntentExtras.WARRANTY_EXPIRED_AT
import com.bukuwarung.edc.util.generateVideoThumbnail
import com.bukuwarung.edc.util.openActivity
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ReplacementFormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityReplacementFormBinding

    private val viewModel: ReplacementFormViewModel by viewModels()

    private val reasonAdapter: ReplacementFormReasonAdapter by lazy {
        ReplacementFormReasonAdapter(callback = provideCallback())
    }

    private val videoFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri -> handleMediaSelection(uri) { path -> viewModel.setUploadedVideo(path) } }

    private val photoFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri -> handleMediaSelection(uri) { path -> viewModel.setUploadedPhoto(path) } }

    // Notification permission launcher for Android 13+
    private val notificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        handleNotificationPermissionResult(isGranted)
    }

    // Pending upload action - stores what upload to continue after permission is granted
    private var pendingUploadAction: (() -> Unit)? = null

    private fun handleMediaSelection(uri: Uri?, onSuccess: (String) -> Unit) {
        uri?.let { mediaUri ->
            val mediaPath = getFilePathFromUri(mediaUri) ?: mediaUri.toString()
            onSuccess(mediaPath)
        }
    }

    private val colorDefault by lazy {
        ContextCompat.getColor(this, R.color.black_800)
    }
    private val colorRed by lazy {
        ContextCompat.getColor(this, android.R.color.holo_red_dark)
    }

    private val minChars by lazy {
        ReplacementFormViewModel.OTHER_REASON_MIN_CHARS
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReplacementFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupBackPressedHandler()
        setupView()

        observeUiState()
        loadReplacementReasons()
    }

    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this) { finish() }
    }

    private fun setupView() {
        setupClickListeners()

        binding.rvReplacementReasons.apply {
            adapter = reasonAdapter
            setHasFixedSize(true)
            isNestedScrollingEnabled = false
        }
        setupOtherReasonEditText()
    }

    private fun setupClickListeners() = with(binding) {
        ivBack.clickWithDebounce { onBackPressedDispatcher.onBackPressed() }
        btnNext.clickWithDebounce { viewModel.submitForm() }

        tvUploadVideoDevice.clickWithDebounce {
            checkNotificationPermissionAndUpload { handleVideoUpload() }
        }
        ivUploadedVideoDelete.clickWithDebounce {
            viewModel.removeUploadedVideo()
        }

        tvUploadPhotoDevice.clickWithDebounce {
            checkNotificationPermissionAndUpload { handlePhotoUpload() }
        }
        ivUploadedPhotoDelete.clickWithDebounce {
            viewModel.removeUploadedPhoto()
        }
    }

    private fun setupOtherReasonEditText() {
        binding.etOtherReason.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                viewModel.updateOtherReasonText(s?.toString() ?: "")
            }
        })
    }

    private fun handleVideoUpload() {
        videoFilePickerLauncher.launch("video/*")
    }

    private fun handlePhotoUpload() {
        photoFilePickerLauncher.launch("image/*")
    }

    private fun loadReplacementReasons() {
        viewModel.loadReplacementReasons()
    }

    private fun observeUiState() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUi(state)

                if (state.isFormSubmitted && state.formData != null) {
                    navigateToReplacementCheck(state.formData)
                }
            }
        }
    }

    private fun updateUi(state: ReplacementFormUiState) {
        binding.apply {
            tvDeviceTitle.text = state.deviceInfo.name
            tvDeviceTerminalNumber.text = state.deviceInfo.terminalNumber
            tvDeviceSerialNumber.text = state.deviceInfo.serialNumber
            tvUserIdNumber.text = state.deviceInfo.userId
        }

        val reasonsWithSelection = state.replacementReasons.map { reason ->
            reason.copy(isSelected = reason.id in state.selectedReasons)
        }
        reasonAdapter.submitList(reasonsWithSelection)

        updateSubmitButton(state.isSubmitButtonEnabled)
        updateUploadViews(state)

        updateMediaRequirementIndicator(state)
        updateOtherReasonViews(state)

        if (state.otherReasonText.isNotEmpty() && binding.etOtherReason.text.toString() != state.otherReasonText) {
            binding.etOtherReason.setText(state.otherReasonText)
        }

        state.errorMessage?.let { error ->
            Toast.makeText(this@ReplacementFormActivity, error, Toast.LENGTH_LONG).show()
        }
    }

    private fun updateOtherReasonViews(state: ReplacementFormUiState) {
        binding.apply {
            tilOtherReason.visibility = if (state.isOtherReasonVisible) View.VISIBLE else View.GONE

            tvOtherReasonLabel.visibility =
                if (state.isOtherReasonVisible) View.VISIBLE else View.GONE

            if (state.isOtherReasonVisible) {
                val textLength = state.otherReasonText.length

                tilOtherReason.helperText = if (textLength < minChars) {
                    getString(
                        R.string.replacement_other_reason_min_chars_helper,
                        minChars,
                        textLength,
                        minChars
                    )
                } else null

                tilOtherReason.error = if (textLength in 1 until minChars) {
                    getString(R.string.replacement_other_reason_min_chars_error, minChars)
                } else null

                tvOtherReasonLabel.setTextColor(
                    if (state.otherReasonText.length >= minChars) colorDefault else colorRed
                )
            } else {
                tvOtherReasonLabel.setTextColor(colorDefault)
                etOtherReason.text = null
            }
        }
    }

    private fun updateMediaRequirementIndicator(state: ReplacementFormUiState) {
        binding.apply {
            val videoLabel = if (state.isMediaRequired) {
                "${getString(R.string.replacement_upload_video_label)}$ASTERISK"
            } else {
                getString(R.string.replacement_upload_video_label)
            }
            tvUploadVideoDeviceLabel.text = videoLabel

            val photoLabel = if (state.isMediaRequired) {
                "${getString(R.string.replacement_upload_photo_label)}$ASTERISK"
            } else {
                getString(R.string.replacement_upload_photo_label)
            }
            tvUploadPhotoDeviceLabel.text = photoLabel

            if (state.isMediaRequired) {
                tvUploadVideoDeviceLabel.setTextColor(
                    if (state.uploadedVideoPath == null) colorRed else colorDefault
                )
                tvUploadPhotoDeviceLabel.setTextColor(
                    if (state.uploadedPhotoPath == null) colorRed else colorDefault
                )
            } else {
                tvUploadVideoDeviceLabel.setTextColor(colorDefault)
                tvUploadPhotoDeviceLabel.setTextColor(colorDefault)
            }
        }
    }

    private fun updateSubmitButton(isEnabled: Boolean) {
        binding.btnNext.apply {
            this.isEnabled = isEnabled
            backgroundTintList = ContextCompat.getColorStateList(
                context,
                if (isEnabled) R.color.bar_dashboard_ppob_3
                else R.color.switch_thumb_disabled_material_light
            )
            setTextColor(
                ContextCompat.getColor(
                    context,
                    if (isEnabled) R.color.black_800
                    else R.color.white
                )
            )
        }
    }

    private fun updateUploadViews(state: ReplacementFormUiState) {
        binding.apply {
            vfUploadVideoDevice.displayedChild = when {
                state.isUploadingVideo -> CHILD_MEDIA_UPLOADING
                state.uploadedVideoPath != null -> CHILD_MEDIA_UPLOADED
                else -> CHILD_UPLOAD_MEDIA
            }
            if (state.uploadedVideoPath != null) {
                setupVideoPreview(state.uploadedVideoPath)
            }

            vfUploadPhotoDevice.displayedChild = when {
                state.isUploadingPhoto -> CHILD_MEDIA_UPLOADING
                state.uploadedPhotoPath != null -> CHILD_MEDIA_UPLOADED
                else -> CHILD_UPLOAD_MEDIA
            }
            if (state.uploadedPhotoPath != null) {
                setupPhotoPreview(state.uploadedPhotoPath)
            }
            tvUploadVideoDevice.isEnabled = !state.isUploadingVideo && !state.isUploadingPhoto
            tvUploadPhotoDevice.isEnabled = !state.isUploadingVideo && !state.isUploadingPhoto
        }
    }

    private fun provideCallback(): ReplacementFormReasonAdapter.Callback {
        return object : ReplacementFormReasonAdapter.Callback {
            override fun onItemToggle(reasonId: String) {
                viewModel.toggleReason(reasonId)
            }
        }
    }

    private fun setupVideoPreview(videoPath: String) {
        binding.apply {
            lifecycleScope.launch {
                val thumbnail = videoPath.generateVideoThumbnail(this@ReplacementFormActivity)
                displayVideoThumbnail(thumbnail)
                setupVideoClickListeners(videoPath)
            }
        }
    }

    private fun displayVideoThumbnail(thumbnail: Bitmap?) {
        binding.apply {
            if (thumbnail != null) {
                ivUploadedVideoContent.setImageBitmap(thumbnail)
                ivUploadedVideoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
            } else {
                displayVideoPlaceholder()
            }
        }
    }

    private fun displayVideoPlaceholder() {
        binding.apply {
            ivUploadedVideoContent.setImageResource(R.drawable.replacement_ic_video_file)
            ivUploadedVideoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_INSIDE
        }
    }

    private fun setupVideoClickListeners(videoPath: String) {
        binding.apply {
            ivUploadedVideoDelete.setOnClickListener {
                viewModel.removeUploadedVideo()
            }
            cvVideoThumbnail.setOnClickListener {
                showVideoPlaybackDialog(videoPath)
            }
        }
    }

    private fun showVideoPlaybackDialog(videoPath: String) {
        val dialog = ReplacementVideoPlaybackDialog.newInstance(videoPath)
        dialog.show(supportFragmentManager, "VideoPlaybackDialog")
    }

    private fun setupPhotoPreview(photoPath: String) {
        binding.apply {
            try {
                Glide.with(this@ReplacementFormActivity)
                    .load(photoPath)
                    .placeholder(R.drawable.replacement_ic_image_placeholder)
                    .error(R.drawable.replacement_ic_image_placeholder)
                    .centerCrop()
                    .into(ivUploadedPhotoContent)

                setupPhotoClickListeners(photoPath)
            } catch (e: Exception) {
                displayPhotoPlaceholder()
                setupPhotoClickListeners(photoPath)
            }
        }
    }

    private fun displayPhotoPlaceholder() {
        binding.apply {
            ivUploadedPhotoContent.setImageResource(R.drawable.replacement_ic_image_placeholder)
            ivUploadedPhotoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
        }
    }

    private fun setupPhotoClickListeners(photoPath: String) {
        binding.apply {
            ivUploadedPhotoDelete.setOnClickListener {
                viewModel.removeUploadedPhoto()
            }
            cvPhotoThumbnail.setOnClickListener {
                showPhotoViewDialog(photoPath)
            }
        }
    }

    private fun showPhotoViewDialog(photoPath: String) {
        val dialog = ReplacementPhotoViewDialog.newInstance(photoPath)
        dialog.show(supportFragmentManager, "PhotoViewDialog")
    }

    private fun getFilePathFromUri(uri: Uri): String? {
        return try {
            uri.toString()
        } catch (e: Exception) {
            null
        }
    }

    private fun navigateToReplacementCheck(formData: ReplacementFormData) {
        openActivity(ReplacementScreenActivity::class.java) {
            putString(ReplacementScreenIntentExtras.DEVICE_NAME, formData.deviceInfo.name)
            putString(ReplacementScreenIntentExtras.DEVICE_TYPE, formData.deviceInfo.type.name)
            putString(
                ReplacementScreenIntentExtras.DEVICE_TERMINAL_NUMBER,
                formData.deviceInfo.terminalNumber
            )
            putString(
                ReplacementScreenIntentExtras.DEVICE_SERIAL_NUMBER,
                formData.deviceInfo.serialNumber
            )
            putString(ReplacementScreenIntentExtras.USER_ID, formData.deviceInfo.userId)

            putStringArrayList(
                ReplacementScreenIntentExtras.SELECTED_REASONS,
                ArrayList(formData.selectedReasons)
            )

            putStringArrayList(
                ReplacementScreenIntentExtras.SELECTED_REASON_IDS,
                ArrayList(formData.selectedReasonIds)
            )
            putString(ReplacementScreenIntentExtras.OTHER_REASON_TEXT, formData.otherReasonText)

            putString(ReplacementScreenIntentExtras.VIDEO_PATH, formData.videoPath)
            putString(ReplacementScreenIntentExtras.PHOTO_PATH, formData.photoPath)
            putString(WARRANTY_EXPIRED_AT, formData.deviceInfo.warrantyExpiredAt)
        }
        viewModel.resetFormSubmission()
        finish()
    }

    /**
     * Check notification permissions before upload and continue with callback
     * @param uploadAction The upload action to execute after permission is granted or skipped
     */
    private fun checkNotificationPermissionAndUpload(uploadAction: () -> Unit) {
        // Get permission status from the core upload module
        val permissionStatus = viewModel.getNotificationPermissionStatus()

        if (!permissionStatus.canShowNotifications) {
            // Store the upload action to continue after permission handling
            pendingUploadAction = uploadAction

            if (permissionStatus.requiresPermissionRequest) {
                // Android 13+ - need to request permission
                showNotificationPermissionDialogForUpload()
            } else {
                // Pre-Android 13 or notifications disabled in settings
                showNotificationDisabledInfoForUpload()
            }
        } else {
            // Permissions are already granted, proceed with upload
            uploadAction()
        }
    }

    /**
     * Show dialog to request notification permission for upload (Android 13+)
     */
    private fun showNotificationPermissionDialogForUpload() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.notification_permission_title))
            .setMessage(getString(R.string.notification_permission_request_message))
            .setPositiveButton(getString(R.string.notification_permission_allow)) { _, _ ->
                requestNotificationPermission()
            }
            .setNegativeButton(getString(R.string.notification_permission_upload_without)) { dialog, _ ->
                dialog.dismiss()
                // Continue with upload without notifications
                executePendingUpload()
            }
            .setCancelable(true)
            .setOnCancelListener {
                // User cancelled - continue with upload anyway
                executePendingUpload()
            }
            .show()
    }

    /**
     * Show info about disabled notifications for upload
     */
    private fun showNotificationDisabledInfoForUpload() {
        val permissionStatus = viewModel.getNotificationPermissionStatus()
        val message = permissionStatus.getSuggestedAction(this) ?: getString(R.string.notification_permission_disabled_message)

        AlertDialog.Builder(this)
            .setTitle(getString(R.string.notification_permission_title))
            .setMessage("$message\n\n${getString(R.string.notification_permission_disabled_message)}")
            .setPositiveButton(getString(R.string.notification_permission_open_settings)) { _, _ ->
                NotificationPermissionHelper.openNotificationSettings(this)
                // Continue with upload after user returns from settings
                executePendingUpload()
            }
            .setNegativeButton(getString(R.string.notification_permission_upload_anyway)) { dialog, _ ->
                dialog.dismiss()
                // Continue with upload without notifications
                executePendingUpload()
            }
            .setCancelable(true)
            .setOnCancelListener {
                // User cancelled - continue with upload anyway
                executePendingUpload()
            }
            .show()
    }

    /**
     * Request notification permission
     */
    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            notificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }
    }

    /**
     * Handle notification permission result and continue with upload
     */
    private fun handleNotificationPermissionResult(isGranted: Boolean) {
        if (isGranted) {
            Toast.makeText(this, getString(R.string.notification_permission_enabled), Toast.LENGTH_SHORT).show()
            // Permission granted, continue with upload
            executePendingUpload()
        } else {
            // Check if we should show rationale
            if (NotificationPermissionHelper.shouldShowPermissionRationale(this)) {
                showNotificationPermissionRationaleForUpload()
            } else {
                // Permission permanently denied, continue with upload anyway
                Toast.makeText(this, getString(R.string.notification_permission_uploading_without), Toast.LENGTH_SHORT).show()
                executePendingUpload()
            }
        }
    }

    /**
     * Show rationale for notification permission during upload
     */
    private fun showNotificationPermissionRationaleForUpload() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.notification_permission_title))
            .setMessage(NotificationPermissionHelper.getPermissionRationaleMessage(this))
            .setPositiveButton(getString(R.string.notification_permission_try_again)) { _, _ ->
                requestNotificationPermission()
            }
            .setNegativeButton(getString(R.string.notification_permission_upload_without)) { dialog, _ ->
                dialog.dismiss()
                // Continue with upload without notifications
                executePendingUpload()
            }
            .setCancelable(true)
            .setOnCancelListener {
                // User cancelled - continue with upload anyway
                executePendingUpload()
            }
            .show()
    }

    /**
     * Execute the pending upload action and clear it
     */
    private fun executePendingUpload() {
        pendingUploadAction?.let { action ->
            pendingUploadAction = null // Clear the pending action
            action() // Execute the upload
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clear pending upload action to prevent memory leaks
        pendingUploadAction = null
    }

    override fun onResume() {
        super.onResume()
        // Check if user returned from settings and has pending upload
        if (pendingUploadAction != null) {
            // Re-check permission status in case user enabled notifications in settings
            val permissionStatus = viewModel.getNotificationPermissionStatus()
            if (permissionStatus.canShowNotifications) {
                Toast.makeText(this, getString(R.string.notification_permission_enabled_continuing), Toast.LENGTH_SHORT).show()
            }
            // Execute pending upload regardless of permission status
            executePendingUpload()
        }
    }

    companion object {
        private const val CHILD_UPLOAD_MEDIA = 0
        private const val CHILD_MEDIA_UPLOADED = 1
        private const val CHILD_MEDIA_UPLOADING = 2
        private const val ASTERISK = "*"
    }

    object IntentExtras {
        const val DEVICE_NAME = "extra_device_name"
        const val DEVICE_TYPE = "extra_device_type"
        const val DEVICE_TERMINAL_NUMBER = "extra_device_terminal_number"
        const val DEVICE_SERIAL_NUMBER = "extra_device_serial_number"
        const val USER_ID = "extra_user_id"

        const val SELECTED_REASON_IDS = "extra_selected_reason_ids"
        const val OTHER_REASON_TEXT = "extra_other_reason_text"
        const val UPLOADED_VIDEO_PATH = "extra_uploaded_video_path"
        const val UPLOADED_PHOTO_PATH = "extra_uploaded_photo_path"

        const val WARRANTY_EXPIRED_AT ="extra_warranty_expired_at"
    }
}

typealias ReplacementFormIntentExtras = ReplacementFormActivity.IntentExtras