package com.bukuwarung.edc.replacement.presentation.form

import androidx.lifecycle.SavedStateHandle

/**
 * Extension functions for SavedStateHandle to extract ReplacementForm-specific data
 * These functions help convert intent extras stored in SavedStateHandle to domain objects
 */

/**
 * Converts SavedStateHandle data to ReplacementFormDeviceInfo
 * Extracts device information from intent extras
 */
fun SavedStateHandle.toDeviceInfo(): ReplacementFormDeviceInfo {
    return ReplacementFormDeviceInfo(
        name = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_NAME).orEmpty(),
        type = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_TYPE).toReplacementDeviceType(),
        terminalNumber = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_TERMINAL_NUMBER).orEmpty(),
        serialNumber = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_SERIAL_NUMBER).orEmpty(),
        userId = get<String>(ReplacementFormActivity.IntentExtras.USER_ID).orEmpty(),
        warrantyExpiredAt = get<String>(ReplacementFormActivity.IntentExtras.WARRANTY_EXPIRED_AT).orEmpty()
    )
}

/**
 * Extracts selected reason IDs from SavedStateHandle
 * @return Set of selected reason IDs, empty set if none found
 */
fun SavedStateHandle.getSelectedReasonIds(): Set<String> {
    val selectedIds = get<ArrayList<String>>(ReplacementFormActivity.IntentExtras.SELECTED_REASON_IDS)
    return selectedIds?.toSet() ?: emptySet()
}

/**
 * Extracts other reason text from SavedStateHandle
 * @return Other reason text, empty string if none found
 */
fun SavedStateHandle.getOtherReasonText(): String {
    return get<String>(ReplacementFormActivity.IntentExtras.OTHER_REASON_TEXT).orEmpty()
}

/**
 * Extracts uploaded video path from SavedStateHandle
 * @return Video path if exists and not empty, null otherwise
 */
fun SavedStateHandle.getUploadedVideoPath(): String? {
    val path = get<String>(ReplacementFormActivity.IntentExtras.UPLOADED_VIDEO_PATH)
    return if (path.isNullOrEmpty()) null else path
}

/**
 * Extracts uploaded photo path from SavedStateHandle
 * @return Photo path if exists and not empty, null otherwise
 */
fun SavedStateHandle.getUploadedPhotoPath(): String? {
    val path = get<String>(ReplacementFormActivity.IntentExtras.UPLOADED_PHOTO_PATH)
    return if (path.isNullOrEmpty()) null else path
}
