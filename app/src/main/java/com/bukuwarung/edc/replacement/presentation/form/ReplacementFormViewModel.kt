package com.bukuwarung.edc.replacement.presentation.form

import android.content.Context
import android.util.Log
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.core.upload.domain.model.NotificationPermissionStatus
import com.bukuwarung.edc.core.upload.domain.model.UploadStatus
import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceCategory
import com.bukuwarung.edc.replacement.domain.model.ReplacementFormReason
import com.bukuwarung.edc.replacement.domain.usecase.GetReplacementReasonsUseCase
import com.bukuwarung.edc.replacement.domain.usecase.UploadReplacementEvidenceWithCoreUseCase
import com.bukuwarung.edc.util.CompressionUtils
import com.bukuwarung.edc.util.video.VideoCompressionCallback
import com.bukuwarung.edc.util.video.VideoCompressionResult
import com.bukuwarung.edc.util.video.VideoCompressionError
import com.bukuwarung.edc.util.video.VideoCompressionProgress
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Updated ViewModel using the core upload module
 * This maintains the same interface as the original but uses the new upload system
 */
@HiltViewModel
class ReplacementFormViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    savedStateHandle: SavedStateHandle,
    private val getReplacementReasonsUseCase: GetReplacementReasonsUseCase,
    private val uploadReplacementEvidenceUseCase: UploadReplacementEvidenceWithCoreUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(
        ReplacementFormUiState(
            deviceInfo = savedStateHandle.toDeviceInfo(),
            selectedReasons = savedStateHandle.getSelectedReasonIds(),
            otherReasonText = savedStateHandle.getOtherReasonText(),
            uploadedVideoPath = savedStateHandle.getUploadedVideoPath(),
            uploadedPhotoPath = savedStateHandle.getUploadedPhotoPath()
        )
    )
    val uiState: StateFlow<ReplacementFormUiState> = _uiState.asStateFlow()

    // Track upload IDs for progress monitoring
    private var currentVideoUploadId: String? = null
    private var currentPhotoUploadId: String? = null

    init {
        loadReplacementReasons()
    }

    fun loadReplacementReasons() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }

            getReplacementReasonsUseCase(_uiState.value.deviceInfo.type)
                .onSuccess { reasons ->
                    val updatedReasons = updateReasonsWithSelection(reasons, _uiState.value.selectedReasons)
                    _uiState.update { currentState ->
                        currentState.copy(
                            isLoading = false,
                            replacementReasons = updatedReasons,
                            isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                                currentState.selectedReasons,
                                updatedReasons,
                                currentState.uploadedVideoPath,
                                currentState.uploadedPhotoPath,
                                currentState.otherReasonText
                            )
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false, 
                            errorMessage = error.message ?: "Failed to load reasons"
                        ) 
                    }
                }
        }
    }

    fun setUploadedVideo(videoPath: String) {
        Log.d(TAG, "=== MODERN VIDEO UPLOAD STARTED ===")
        Log.d(TAG, "Input video path: $videoPath")

        viewModelScope.launch {
            _uiState.update { it.copy(isUploadingVideo = true) }

            try {
                val startTime = System.currentTimeMillis()

                // Process video with modern compression and progress tracking
                val processedFile = CompressionUtils.compressVideoFromUri(
                    context = context,
                    videoUri = videoPath,
                    maxSizeMB = REPLACEMENT_MAX_VIDEO_SIZE_MB, // 25MB limit for replacement videos
                    callback = object : VideoCompressionCallback {
                        override fun onProgressUpdate(progress: VideoCompressionProgress) {
                            Log.d(TAG, "Video compression progress: ${progress.progressPercent}%")
                            // Update UI with compression progress if needed
                            // Could add compression progress to UI state
                        }

                        override fun onCompleted(result: VideoCompressionResult) {
                            when (result) {
                                is VideoCompressionResult.Success -> {
                                    Log.d(TAG, "Modern video compression completed successfully")
                                    Log.d(TAG, "  Original size: ${result.originalSizeBytes / 1024 / 1024}MB")
                                    Log.d(TAG, "  Compressed size: ${result.compressedSizeBytes / 1024 / 1024}MB")
                                    Log.d(TAG, "  Compression ratio: ${"%.1f".format(result.compressionRatio * 100)}%")
                                    Log.d(TAG, "  Hardware acceleration: ${result.usedHardwareAcceleration}")
                                    Log.d(TAG, "  Processing time: ${result.processingTimeMs}ms")
                                }
                                is VideoCompressionResult.Error -> {
                                    Log.e(TAG, "Video compression failed: ${result.message}")
                                }
                            }
                        }

                        override fun onError(error: VideoCompressionError, message: String, exception: Exception?) {
                            Log.e(TAG, "Video compression error: $error - $message", exception)
                        }
                    }
                )

                val processingTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "Video processing completed in ${processingTime}ms")

                if (processedFile == null) {
                    Log.e(TAG, "Video processing failed - processedFile is null")
                    _uiState.update { currentState ->
                        currentState.copy(
                            isUploadingVideo = false,
                            errorMessage = "Failed to process video"
                        )
                    }
                    return@launch
                }

                // Start upload using core upload module
                uploadReplacementEvidenceUseCase.startUpload(
                    processedFile.absolutePath,
                    ReplacementEvidenceCategory.VIDEO
                ).onSuccess { uploadId ->
                    currentVideoUploadId = uploadId
                    Log.d(TAG, "Video upload started with ID: $uploadId")

                    // Monitor upload progress
                    monitorVideoUpload(uploadId, processedFile.absolutePath)
                }.onFailure { error ->
                    Log.e(TAG, "Failed to start video upload: ${error.message}")
                    _uiState.update { currentState ->
                        currentState.copy(
                            isUploadingVideo = false,
                            errorMessage = error.message ?: "Failed to start upload"
                        )
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Video upload error: ${e.message}", e)
                _uiState.update { currentState ->
                    currentState.copy(
                        isUploadingVideo = false,
                        errorMessage = e.message ?: "Upload failed"
                    )
                }
            }
        }
    }

    fun setUploadedPhoto(photoPath: String) {
        Log.d(TAG, "=== PHOTO UPLOAD STARTED ===")
        Log.d(TAG, "Input photo path: $photoPath")

        viewModelScope.launch {
            _uiState.update { it.copy(isUploadingPhoto = true) }

            try {
                val startTime = System.currentTimeMillis()

                // Process photo (compression if needed)
                val processedFile = CompressionUtils.compressImageFromUri(context, photoPath, maxSizeKB = 1024)

                val processingTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "Photo processing completed in ${processingTime}ms")

                if (processedFile == null) {
                    Log.e(TAG, "Photo processing failed - processedFile is null")
                    _uiState.update { currentState ->
                        currentState.copy(
                            isUploadingPhoto = false,
                            errorMessage = "Failed to process photo"
                        )
                    }
                    return@launch
                }

                // Start upload using core upload module
                uploadReplacementEvidenceUseCase.startUpload(
                    processedFile.absolutePath,
                    ReplacementEvidenceCategory.PHOTO
                ).onSuccess { uploadId ->
                    currentPhotoUploadId = uploadId
                    Log.d(TAG, "Photo upload started with ID: $uploadId")
                    
                    // Monitor upload progress
                    monitorPhotoUpload(uploadId, processedFile.absolutePath)
                }.onFailure { error ->
                    Log.e(TAG, "Failed to start photo upload: ${error.message}")
                    _uiState.update { currentState ->
                        currentState.copy(
                            isUploadingPhoto = false,
                            errorMessage = error.message ?: "Failed to start upload"
                        )
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Photo upload error: ${e.message}", e)
                _uiState.update { currentState ->
                    currentState.copy(
                        isUploadingPhoto = false,
                        errorMessage = e.message ?: "Upload failed"
                    )
                }
            }
        }
    }

    private fun monitorVideoUpload(uploadId: String, filePath: String) {
        viewModelScope.launch {
            uploadReplacementEvidenceUseCase.getUploadProgress(uploadId).collect { progress ->
                when (progress.status) {
                    UploadStatus.COMPLETED -> {
                        Log.d(TAG, "Video upload completed successfully")
                        _uiState.update { currentState ->
                            currentState.copy(
                                uploadedVideoPath = filePath,
                                uploadedVideoUrl = "completed", // This would be the actual signed URL
                                uploadedVideoServerPath = "completed", // This would be the actual server path
                                isUploadingVideo = false,
                                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                                    currentState.selectedReasons,
                                    currentState.replacementReasons,
                                    filePath,
                                    currentState.uploadedPhotoPath,
                                    currentState.otherReasonText
                                )
                            )
                        }
                        Log.d(TAG, "=== VIDEO UPLOAD COMPLETED SUCCESSFULLY ===")
                    }
                    UploadStatus.FAILED -> {
                        Log.e(TAG, "Video upload failed: ${progress.errorMessage}")
                        _uiState.update { currentState ->
                            currentState.copy(
                                isUploadingVideo = false,
                                errorMessage = progress.errorMessage ?: "Upload failed"
                            )
                        }
                        Log.d(TAG, "=== VIDEO UPLOAD FAILED ===")
                    }
                    else -> {
                        // Update progress if needed
                        Log.d(TAG, "Video upload progress: ${progress.progressPercent}%")
                    }
                }
            }
        }
    }

    private fun monitorPhotoUpload(uploadId: String, filePath: String) {
        viewModelScope.launch {
            uploadReplacementEvidenceUseCase.getUploadProgress(uploadId).collect { progress ->
                when (progress.status) {
                    UploadStatus.COMPLETED -> {
                        Log.d(TAG, "Photo upload completed successfully")
                        _uiState.update { currentState ->
                            currentState.copy(
                                uploadedPhotoPath = filePath,
                                uploadedPhotoUrl = "completed", // This would be the actual signed URL
                                uploadedPhotoServerPath = "completed", // This would be the actual server path
                                isUploadingPhoto = false,
                                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                                    currentState.selectedReasons,
                                    currentState.replacementReasons,
                                    currentState.uploadedVideoPath,
                                    filePath,
                                    currentState.otherReasonText
                                )
                            )
                        }
                        Log.d(TAG, "=== PHOTO UPLOAD COMPLETED SUCCESSFULLY ===")
                    }
                    UploadStatus.FAILED -> {
                        Log.e(TAG, "Photo upload failed: ${progress.errorMessage}")
                        _uiState.update { currentState ->
                            currentState.copy(
                                isUploadingPhoto = false,
                                errorMessage = progress.errorMessage ?: "Upload failed"
                            )
                        }
                        Log.d(TAG, "=== PHOTO UPLOAD FAILED ===")
                    }
                    else -> {
                        // Update progress if needed
                        Log.d(TAG, "Photo upload progress: ${progress.progressPercent}%")
                    }
                }
            }
        }
    }

    // Rest of the methods remain the same as the original ViewModel
    fun removeUploadedVideo() {
        currentVideoUploadId?.let { uploadId ->
            viewModelScope.launch {
                uploadReplacementEvidenceUseCase.cancelUpload(uploadId)
            }
        }
        currentVideoUploadId = null
        
        _uiState.update { currentState ->
            currentState.copy(
                uploadedVideoPath = null,
                uploadedVideoUrl = null,
                uploadedVideoServerPath = null,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    currentState.selectedReasons,
                    currentState.replacementReasons,
                    null,
                    currentState.uploadedPhotoPath,
                    currentState.otherReasonText
                )
            )
        }
    }

    fun removeUploadedPhoto() {
        currentPhotoUploadId?.let { uploadId ->
            viewModelScope.launch {
                uploadReplacementEvidenceUseCase.cancelUpload(uploadId)
            }
        }
        currentPhotoUploadId = null
        
        _uiState.update { currentState ->
            currentState.copy(
                uploadedPhotoPath = null,
                uploadedPhotoUrl = null,
                uploadedPhotoServerPath = null,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    currentState.selectedReasons,
                    currentState.replacementReasons,
                    currentState.uploadedVideoPath,
                    null,
                    currentState.otherReasonText
                )
            )
        }
    }

    // Helper methods remain the same...
    private fun updateReasonsWithSelection(
        reasons: List<ReplacementFormReason>,
        selectedIds: Set<String>
    ): List<ReplacementFormReason> {
        return reasons.map { reason ->
            reason.copy(isSelected = reason.id in selectedIds)
        }
    }

    private fun calculateSubmitButtonEnabled(
        selectedReasons: Set<String>,
        allReasons: List<ReplacementFormReason>,
        videoPath: String?,
        photoPath: String?,
        otherReasonText: String
    ): Boolean {
        // Same logic as original implementation
        if (selectedReasons.isEmpty()) return false

        val hasOtherReason = selectedReasons.any { reasonId ->
            allReasons.find { it.id == reasonId }?.label == OTHER_REASON_LABEL
        }

        if (hasOtherReason && otherReasonText.isBlank()) return false

        val selectedReasonObjects = allReasons.filter { it.id in selectedReasons }
        val requiresVideo = selectedReasonObjects.any { it.videoMandatory }
        val requiresPhoto = selectedReasonObjects.any { it.imageMandatory }

        if (requiresVideo && videoPath.isNullOrBlank()) return false
        if (requiresPhoto && photoPath.isNullOrBlank()) return false

        return true
    }

    private fun calculateMediaRequired(
        selectedReasons: Set<String>,
        allReasons: List<ReplacementFormReason>
    ): Boolean {
        val selectedReasonObjects = allReasons.filter { it.id in selectedReasons }
        return selectedReasonObjects.any { it.videoMandatory || it.imageMandatory }
    }

    // Additional methods required by the Activity
    fun toggleReason(reasonId: String) {
        val currentSelected = _uiState.value.selectedReasons.toMutableSet()
        if (reasonId in currentSelected) {
            currentSelected.remove(reasonId)
        } else {
            currentSelected.add(reasonId)
        }

        val updatedReasons = updateReasonsWithSelection(_uiState.value.replacementReasons, currentSelected)
        val hasOtherReason = currentSelected.any { id ->
            updatedReasons.find { it.id == id }?.label == OTHER_REASON_LABEL
        }
        val isMediaRequired = calculateMediaRequired(currentSelected, updatedReasons)

        _uiState.update { currentState ->
            currentState.copy(
                selectedReasons = currentSelected,
                replacementReasons = updatedReasons,
                isOtherReasonVisible = hasOtherReason,
                isMediaRequired = isMediaRequired,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    currentSelected,
                    updatedReasons,
                    currentState.uploadedVideoPath,
                    currentState.uploadedPhotoPath,
                    currentState.otherReasonText
                )
            )
        }
    }

    fun updateOtherReasonText(text: String) {
        _uiState.update { currentState ->
            currentState.copy(
                otherReasonText = text,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    currentState.selectedReasons,
                    currentState.replacementReasons,
                    currentState.uploadedVideoPath,
                    currentState.uploadedPhotoPath,
                    text
                )
            )
        }
    }

    fun submitForm() {
        val currentState = _uiState.value

        // Collect selected reasons as list of strings
        val selectedReasonTexts = mutableListOf<String>()

        currentState.selectedReasons.forEach { reasonId ->
            val reason = currentState.replacementReasons.find { it.id == reasonId }
            reason?.let {
                if (it.label == OTHER_REASON_LABEL) {
                    // If "Lainnya" is selected, use the text from edittext instead
                    if (currentState.otherReasonText.isNotEmpty()) {
                        selectedReasonTexts.add(currentState.otherReasonText)
                    }
                } else {
                    // Use the reason label
                    selectedReasonTexts.add(it.label)
                }
            }
        }

        // Prepare data for next activity
        val formData = ReplacementFormData(
            deviceInfo = currentState.deviceInfo,
            selectedReasons = selectedReasonTexts,
            selectedReasonIds = currentState.selectedReasons,
            otherReasonText = currentState.otherReasonText,
            videoPath = currentState.uploadedVideoPath ?: "",
            photoPath = currentState.uploadedPhotoPath ?: ""
        )

        _uiState.update { it.copy(isFormSubmitted = true, formData = formData) }
    }

    fun resetFormSubmission() {
        _uiState.update { it.copy(isFormSubmitted = false, formData = null) }
    }

    /**
     * Get notification permission status
     */
    fun getNotificationPermissionStatus(): NotificationPermissionStatus {
        return uploadReplacementEvidenceUseCase.checkNotificationPermission()
    }

    /**
     * Check if notifications are enabled for uploads
     */
    fun areNotificationsEnabled(): Boolean {
        return uploadReplacementEvidenceUseCase.areNotificationsEnabled()
    }

    companion object {
        private const val TAG = "ReplacementFormViewModel"
        private const val OTHER_REASON_LABEL = "Lainnya"
        const val OTHER_REASON_MIN_CHARS = 10
        private const val REPLACEMENT_MAX_VIDEO_SIZE_MB = 25L
    }
}
