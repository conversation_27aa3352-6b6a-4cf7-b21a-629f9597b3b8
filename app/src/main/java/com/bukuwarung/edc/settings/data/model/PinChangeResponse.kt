package com.bukuwarung.edc.settings.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class PinChangeResponse(
    @SerializedName("content")
    val content: List<PinChangeResponseContent>? = null
)

@Keep
data class PinChangeResponseContent(
    @SerializedName("requestId")
    val requestId: String? = null,
    @SerializedName("userId")
    val userId: String? = null,
    @SerializedName("requestDate")
    val requestDate: String? = null,
    @SerializedName("livelinessExpiresIn")
    val livelinessExpiresIn: String? = null,
    @SerializedName("status")
    val status: String? = null,
    @SerializedName("actionDate")
    val actionDate: String? = null,
    @SerializedName("reason")
    val reason: String? = null
)
