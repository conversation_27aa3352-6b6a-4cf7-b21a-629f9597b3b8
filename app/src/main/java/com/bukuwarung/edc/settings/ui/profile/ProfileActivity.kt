package com.bukuwarung.edc.settings.ui.profile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.bluetooth_printer.utils.BluetoothConnection
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.isSystemBluetoothEnabled
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.cardreader.CardReaderType
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityProfileBinding
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.PermissionUtil.hasBluetoothPermission
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.getBusinessAddress
import com.bukuwarung.edc.util.Utils.getBusinessName
import com.bukuwarung.edc.util.Utils.getDeviceSerialNumber
import com.bukuwarung.edc.util.Utils.getTerminalId
import com.bukuwarung.edc.util.Utils.getUserId
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.showView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ProfileActivity: AppCompatActivity() {

    private lateinit var binding: ActivityProfileBinding
    private val fragmentManager = supportFragmentManager
    private lateinit var bTCardReader: CardReaderHelper
    private var bluetoothConnection: BluetoothConnection? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityProfileBinding.inflate(layoutInflater)

        setContentView(binding.root)
        bluetoothConnection = BluetoothConnection(this)

        setUpToolbar()
        setValues()
    }

    private fun setUpToolbar() {
        with(binding.tbProfile) {
            navigationIcon =
                <EMAIL>(R.drawable.ic_back)
            setNavigationOnClickListener {
                Utils.hideKeyboard(this@ProfileActivity)
                finish()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        binding.tvUbahAccountName.showView()
        binding.pbLoading.hideView()
    }

    private fun setValues() {
        binding.apply {
                tvDevice.text = getDeviceSerialNumber()
                tvPhone.text = getUserId()
                tvTerminal.text = getTerminalId()
                tvAccountName.text = getBusinessName()
                tvAddress.text = getBusinessAddress()

            tvUbahAccountName.setOnClickListener {
                tvUbahAccountName.hideView()
                pbLoading.showView()
                if (Utils.isCardReader()) {
                    bTCardReader = CardReaderHelper.getInstance()
                    checkIfCardReaderConnected()
                } else {
                    openChangeBusinessNameFragment()
                }
            }

            fragmentManager.registerFragmentLifecycleCallbacks(object :
                FragmentManager.FragmentLifecycleCallbacks() {
                override fun onFragmentViewDestroyed(fm: FragmentManager, f: Fragment) {
                    super.onFragmentViewDestroyed(fm, f)
                    binding.clProfile.showView()
                    binding.changeBusinessNameContainer.hideView()
                    tvAccountName.text = getBusinessName()
                }

                override fun onFragmentAttached(
                    fm: FragmentManager,
                    f: Fragment,
                    context: Context
                ) {
                    super.onFragmentAttached(fm, f, context)
                    if (f is ChangeBusinessNameFragment) {
                        binding.clProfile.hideView()
                        binding.changeBusinessNameContainer.showView()
                    }
                }
            }, false)


        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            openChangeBusinessNameFragment()
        }
    }

    private fun checkIfCardReaderConnected() {
        if (!BluetoothDevices.hasPairedCardReader() || !hasBluetoothPermission() || !isSystemBluetoothEnabled(
                this
            )
        ) {
            openSetUpBluetoothActivity()
        } else if (!bTCardReader.isDeviceConnected()) {
            connectDevice()
        } else {
            setTerminalIdAndPaymentAccountId()
            openChangeBusinessNameFragment()
        }
    }

    private fun openChangeBusinessNameFragment() {
        binding.tvUbahAccountName.showView()
        binding.pbLoading.hideView()
        val fragment = ChangeBusinessNameFragment.newInstance(
            getBusinessName(),
            getBusinessAddress()
        )
        val transaction = fragmentManager.beginTransaction()
        transaction.replace(
            R.id.change_business_name_container,
            fragment
        )
            .addToBackStack(null)
            .commit()
    }

    private fun setTerminalIdAndPaymentAccountId() {
        val connectedDeviceItem =
            BluetoothDevices.getPairedCardReaderList()?.get(0)?.name?.let { it1 ->
                Utils.findConnectedDeviceItem(
                    it1, Utils.getUserRegisteredDevices()
                )
            }
        Utils.sharedPreferences.apply {
            if (connectedDeviceItem != null) {
                put("t_id", connectedDeviceItem.terminalId)
                put("serial_number", connectedDeviceItem.serialNumber)
                EncryptedPreferencesHelper.put("payment_account_id", connectedDeviceItem.paymentAccountId)
            }
        }
    }


    private fun openSetUpBluetoothActivity() {
        val device = Utils.getUserRegisteredDevices()
        val bundle = Bundle()
        val deviceSerialNumberList = device.map { it.serialNumber }
        bundle.putString(
            SetupBluetoothDeviceActivity.DEVICE_TYPE,
            SetupBluetoothDeviceActivity.CARD_READER
        )
        bundle.putStringArrayList(
            SetupBluetoothDeviceActivity.DEVICE_SN_LIST,
            ArrayList(deviceSerialNumberList)
        )
        openActivity(SetupBluetoothDeviceActivity::class.java) {
            putAll(bundle)
        }
    }
    private fun connectDevice() {
        Log.d("connect", "starting connection")
        if (!Utils.getBooleanConfig("card_reader_iniatiated") &&
            BluetoothDevices.getPairedCardReaderList()?.isNotEmpty() == true) {
            Log.d("connect", "starting init")
            val name = BluetoothDevices.getPairedCardReaderList()?.get(0)?.name
            if (name?.startsWith("MP-") == true) {
                Utils.sharedPreferences.put("bt_upgrade", true)
                bTCardReader?.initCardReaderHelper(CardReaderType.MOREFUN)
            } else {
                bTCardReader?.initCardReaderHelper(CardReaderType.TIANYU)
            }
            Utils.sharedPreferences.put("card_reader_iniatiated", true)
        }
        Thread {
            var isConnected: Boolean = false
            if (bTCardReader?.isDeviceConnected() == true) {
                Log.d("connect", "already connect")
                isConnected = true
            } else {
                Log.d("connect", "connecting")
                isConnected = BluetoothDevices.getPairedCardReader()
                    ?.let { bTCardReader?.connectToDevice(it) } == true
            }
            Utils.sharedPreferences.put("edcConnected", isConnected)

            runOnUiThread {
                if (isConnected) {
                    setTerminalIdAndPaymentAccountId()
                    openChangeBusinessNameFragment()
                }else{
                    binding.tvUbahAccountName.showView()
                    binding.pbLoading.hideView()
                    openSetUpBluetoothActivity()
                }
            }
        }.start()
    }

}