package com.bukuwarung.edc.settings.ui.setting

import android.content.Context
import android.os.Bundle
import androidx.appcompat.widget.AppCompatImageView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.singleClick
import com.google.android.material.button.MaterialButton


class LogoutDialog(context: Context): BaseDialog(context, BaseDialogType.POPUP) {

    override val resId: Int
        get() = R.layout.dialog_logout




    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        setUpView()
    }

    private fun setUpView() {
        findViewById<AppCompatImageView>(R.id.iv_close).singleClick {
            dismiss()
        }

        findViewById<MaterialButton>(R.id.btn_accept).singleClick {
            dismiss()
        }

        findViewById<MaterialButton>(R.id.btn_deny).singleClick {
            dismiss()
            Utils.clearDataAndLogout(false)
        }
    }

}