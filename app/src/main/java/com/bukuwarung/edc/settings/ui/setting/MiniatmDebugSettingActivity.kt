package com.bukuwarung.edc.settings.ui.setting

import android.os.Bundle
import android.widget.CompoundButton
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.edc.card.domain.usecase.ConfigureAidUseCase
import com.bukuwarung.edc.databinding.LayoutMiniatmDebugSettingBinding
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.put
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.refreshtoken.TokenManager
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MiniatmDebugSettingActivity: AppCompatActivity() {

    private lateinit var binding: LayoutMiniatmDebugSettingBinding
    companion object {
        const val TEST_DEVICE = "TEST_DEVICE"
        const val TEST_MOCK_API = "TEST_MOCK_API"
        const val TEST_WITH_TMS = "TEST_WITH_TMS"
        const val TEST_MOCK_PRINT = "TEST_MOCK_PRINT"
        const val TEST_REVERSAL = "TEST_REVERSAL"
        const val TEST_CORRUPT_ICC = "TEST_CORRUPT_ICC"
        const val TEST_FIXED_BANK = "TEST_FIXED_BANK"
        const val TEST_FORCE_INCOMPLETE = "TEST_FORCE_INCOMPLETE"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Utils.setLoadedConfig(false)
        binding = LayoutMiniatmDebugSettingBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.tbTestConfig.apply {
            tvTitle.text = "Miniatm Debug Setting"
            btnBack.setOnClickListener {
                super.onBackPressed()
            }
        }
        Utils.sharedPreferences.put("bt_upgrade", false)

        Utils.sharedPreferences.put("pair"+ BluetoothDevices.getPairedCardReader(),false)
        Utils.sharedPreferences.put("active"+ BluetoothDevices.getPairedCardReader(),false)

        with(binding) {
            layoutTestDevice.label.text = "ENABLE TESTING ON PHONE"
            layoutTestDevice.switchBtn.isChecked = Utils.getBooleanConfig(TEST_DEVICE)
            layoutTestDevice.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_DEVICE))

            layoutTestTms.label.text = "ENABLE TMS SERIAL NUMBER CHECK"
            layoutTestTms.switchBtn.isChecked = Utils.getBooleanConfig(TEST_WITH_TMS)
            layoutTestTms.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_WITH_TMS))

            layoutMockApi.label.text = "ENABLE MOCK API TESTING"
            layoutMockApi.switchBtn.isChecked = Utils.getBooleanConfig(TEST_MOCK_API)
            layoutMockApi.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_MOCK_API))

            layoutForceReversal.label.text = "FORCE TRANSACTION REVERSAL"
            layoutForceReversal.switchBtn.isChecked = Utils.getBooleanConfig(TEST_REVERSAL)
            layoutForceReversal.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_REVERSAL))

            layoutCorruptIcc.label.text = "ENABLE CORRUPT ICC"
            layoutCorruptIcc.switchBtn.isChecked = Utils.getBooleanConfig(TEST_CORRUPT_ICC)
            layoutCorruptIcc.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_CORRUPT_ICC))

            layoutFixedBank.label.text = "ENABLE SIMPLE BANK INFO"
            layoutFixedBank.switchBtn.isChecked = Utils.getBooleanConfig(TEST_FIXED_BANK)
            layoutFixedBank.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_FIXED_BANK))

            layoutForceIncomplete.label.text = "FORCE INCOMPLETE TRANSACTION"
            layoutForceIncomplete.switchBtn.isChecked = Utils.getBooleanConfig(TEST_FORCE_INCOMPLETE)
            layoutForceIncomplete.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_FORCE_INCOMPLETE))

            layoutMockPrint.label.text = "ENABLE MOCK PRINT TESTING"
            layoutMockPrint.switchBtn.isChecked = Utils.getBooleanConfig(TEST_MOCK_PRINT)
            layoutMockPrint.switchBtn.setOnCheckedChangeListener(SwitchChangeListener(TEST_MOCK_PRINT))

            layoutDummyCardInfo.tvCategoryName.text = "Dummy Card Information"
            var cardBundle = Utils.getDummyCardBundle()


            cardBundle.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String,"201")
            cardBundle.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String,"4410")
            var cardInfo = "Card Number: "+cardBundle.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String,"****************")+"\n"+
                            "Track2: "+cardBundle.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String,"****************D44102010000035300000")+"\n"+
                            "Expiry: "+cardBundle.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String,"4410")+"\n\n"

            layoutDummyCardInfo.tvCategoryDescription.text = cardInfo


            layoutDeviceInfo.tvCategoryName.text = "Device Information"
            var deviceInfo = "Terminal Id: "+Utils.getTerminalId()+"\n"+
                    "Merchant Id: "+Utils.getMerchantId()+"\n"+
                    "TMK: "+Utils.getMasterKey()+"\n"+
                    "TWK: "+Utils.getWorkKey()
            layoutDeviceInfo.tvCategoryDescription.text = deviceInfo

            layoutJwtToken.etJwtTokenName.setText(EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, ""))
            layoutJwtToken.btSaveText.setOnClickListener {
                val newToken = layoutJwtToken.etJwtTokenName.text.toString()
                TokenManager.updateTokens(newToken, TokenManager.getRefreshToken() ?: "")
            }

            btnSubmit.setOnClickListener{
                onBackPressed()
            }
            btnReset.setOnClickListener{
                clearAllConfig()
            }
        }
    }

    fun clearAllConfig(){
        binding.layoutTestDevice.switchBtn.isChecked = false
        binding.layoutMockApi.switchBtn.isChecked = false
        binding.layoutMockPrint.switchBtn.isChecked = false
        binding.layoutForceReversal.switchBtn.isChecked = false
        binding.layoutForceIncomplete.switchBtn.isChecked = false
        binding.layoutCorruptIcc.switchBtn.isChecked = false
        binding.layoutFixedBank.switchBtn.isChecked = false
        binding.layoutTestTms.switchBtn.isChecked = false
        Utils.sharedPreferences.put(TEST_DEVICE,false);
        Utils.sharedPreferences.put(TEST_MOCK_API,false);
        Utils.sharedPreferences.put(TEST_MOCK_PRINT,false);
        Utils.sharedPreferences.put(TEST_WITH_TMS,true);
        Utils.sharedPreferences.put(TEST_CORRUPT_ICC,false);
        Utils.sharedPreferences.put(TEST_REVERSAL,false);
        Utils.sharedPreferences.put(TEST_FIXED_BANK,false);
        Utils.sharedPreferences.put(TEST_FORCE_INCOMPLETE,false);
    }

    internal class SwitchChangeListener(var target:String) :
        CompoundButton.OnCheckedChangeListener {

        override fun onCheckedChanged(var1: CompoundButton, state: Boolean) {
            Toast.makeText(EdcApplication.instance, "$target "+state, Toast.LENGTH_SHORT).show()
            Utils.sharedPreferences.put(target,state);
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
    }



}