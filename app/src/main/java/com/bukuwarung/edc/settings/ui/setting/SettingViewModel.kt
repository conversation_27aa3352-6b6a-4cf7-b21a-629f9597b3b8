package com.bukuwarung.edc.settings.ui.setting

import android.annotation.SuppressLint
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.bluetooth_printer.BwBluetoothManager
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.edc.card.domain.usecase.EdcDeviceUseCase
import com.bukuwarung.edc.payments.usecase.PaymentTwoFAUseCase
import com.bukuwarung.edc.settings.data.model.PinChangeResponse
import com.bukuwarung.edc.util.isTrue
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingViewModel @Inject constructor(
    private var paymentTwoFAUseCase: PaymentTwoFAUseCase,
    private var edcDeviceUseCase: EdcDeviceUseCase,
    private val bwBtManager: BwBluetoothManager
) : ViewModel() {

    private val _hasPin = MutableLiveData<Boolean>()
    val hasPin: LiveData<Boolean>
        get() = _hasPin

    private val _pinChangeResponse = MutableLiveData<PinChangeResponse?>()
    val pinChangeResponse: LiveData<PinChangeResponse?>
        get() = _pinChangeResponse

    fun checkPinLength() = viewModelScope.launch(Dispatchers.IO) {
        try {
            paymentTwoFAUseCase.checkPinLength().let {
                if (it.isSuccessful) {
                    _hasPin.postValue(it.body()?.success.isTrue)
                } else {
                    _hasPin.postValue(false)
                }
            }
        } catch (_: Exception) {

        }
    }

    fun checkPinChangeRequest() = viewModelScope.launch(Dispatchers.IO) {
        try {
            paymentTwoFAUseCase.checkPinChangeRequest().let {
                if (it.isSuccessful) {
                    _pinChangeResponse.postValue(it.body())
                } else {
                    _pinChangeResponse.postValue(null)
                }
            }
        } catch (e: Exception) {
            _pinChangeResponse.postValue(null)
        }
    }

    fun printTestSlip() = viewModelScope.launch(Dispatchers.IO) {
        edcDeviceUseCase.printTest().collect { response ->
            Log.d("TEST_PRINT", "printer response $response")
        }
    }

    @RequiresPermission(value = "android.permission.BLUETOOTH_CONNECT")
    fun getPairedBtDevice(deviceType:String): PrinterDataHolder? {
        return bwBtManager.getPairedBtDevice(deviceType).firstOrNull()
    }
}