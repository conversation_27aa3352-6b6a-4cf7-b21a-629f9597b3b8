package com.bukuwarung.edc.settings.ui.setting

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivitySoundSettingsBinding
import com.bukuwarung.edc.util.Utils.CARD_INSERT_SOUND
import com.bukuwarung.edc.util.Utils.ERROR_SOUND
import com.bukuwarung.edc.util.Utils.TRANSACTION_SOUND

class SoundSettingsActivity : AppCompatActivity() {
    private lateinit var binding: ActivitySoundSettingsBinding
    private lateinit var soundsAdapter: SoundSettingsAdapter
    val data = mutableListOf(
        CARD_INSERT_SOUND,
        ERROR_SOUND,
        TRANSACTION_SOUND
    )


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySoundSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        soundsAdapter = SoundSettingsAdapter(this, data)
        with(binding) {
            tbToolbar.apply {
                tvTitle.text = getString(R.string.settings_title)
                btnBack.setOnClickListener {
                    super.onBackPressed()
                }
            }
            lvSound.adapter = soundsAdapter
            lvSound.dividerHeight = 50
        }
    }
}