package com.bukuwarung.edc.util

import android.content.Context
import com.bukuwarung.edc.card.domain.usecase.ConfigureAidUseCase
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.utils.AppBluetoothBridge

class AppBluetoothBridgeImpl: AppBluetoothBridge {

    override fun logout() {
        EncryptedPreferencesHelper.clear()
        Utils.sharedPreferences.clear()
    }

    override fun goToLoginPage(context: Context) {
        context.openActivity(LoginActivity::class.java)
    }

}