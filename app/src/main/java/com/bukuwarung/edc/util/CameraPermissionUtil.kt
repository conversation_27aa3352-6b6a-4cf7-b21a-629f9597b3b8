package com.bukuwarung.edc.util

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

object CameraPermissionUtil {

    const val CAMERA_PERMISSION_REQUEST_CODE = 1001
    private val CAMERA_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA
    )

    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): <PERSON><PERSON><PERSON> {
        return CAMERA_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }



    /**
     * Request camera permission
     */
    fun requestCameraPermission(activity: Activity) {
        ActivityCompat.requestPermissions(
            activity,
            CAMERA_PERMISSIONS,
            CAMERA_PERMISSION_REQUEST_CODE
        )
    }



    /**
     * Check if we should show permission rationale
     */
    fun shouldShowCameraPermissionRationale(activity: Activity): Boolean {
        return CAMERA_PERMISSIONS.any { permission ->
            ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
        }
    }



    /**
     * Handle permission result for camera
     */
    fun handleCameraPermissionResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        when (requestCode) {
            CAMERA_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && 
                    grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    onGranted()
                } else {
                    onDenied()
                }
            }
        }
    }


}
