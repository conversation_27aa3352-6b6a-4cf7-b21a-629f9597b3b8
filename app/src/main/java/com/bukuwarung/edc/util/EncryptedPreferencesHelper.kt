package com.bukuwarung.edc.util

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

object EncryptedPreferencesHelper {

    private const val PREFERENCES_FILE_NAME = "encrypted_prefs"
    private lateinit var sharedPreferences: SharedPreferences

    fun initialize(context: Context) {
        if (!EncryptedPreferencesHelper::sharedPreferences.isInitialized) {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            sharedPreferences = EncryptedSharedPreferences.create(
                context,
                PREFERENCES_FILE_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )
        }
    }

    inline fun<reified  T> put(key: String, value: T) {
        when(T::class){
            Boolean::class -> putBoolean(key, value as Boolean)
            Float::class -> putFloat(key, value as Float)
            Int::class -> putInt(key, value as Int)
            Long::class -> putLong(key, value as Long)
            String::class -> putString(key, value as String?)
            else -> putString(key, value as String?)
        }
        //Following line removes the already stored data in sharedPreferences for existing users.
        Utils.sharedPreferences.remove(key)
    }

    inline fun <reified  T> get(key: String, value: T): T {
        return when(T::class){
            Boolean::class -> getBoolean(key, value as Boolean) as T
            Float::class -> getFloat(key, value as Float) as T
            Int::class -> getInt(key, value as Int) as T
            Long::class -> getLong(key, value as Long) as T
            String::class -> getString(key, value as String?) as T
            else -> getString(key, value as String?) as T
        }
    }

    fun putString(key: String, value: String?) {
        sharedPreferences.edit().putString(key, value).apply()
    }

    fun putFloat(key: String, value: Float) {
        sharedPreferences.edit().putFloat(key, value).apply()
    }

    fun putLong(key: String, value: Long){
        sharedPreferences.edit().putLong(key, value).apply()
    }

    fun getString(key: String, defaultValue: String? = null): String {
        var value = sharedPreferences.getString(key, defaultValue)
        //the following line is written as the existing users don't go through the login flow to start storing in the encrypted shared pref file.
        val sharedPrefValue = Utils.sharedPreferences.get(key, defaultValue)
        if (value.isNullOrBlank() && sharedPrefValue.isNotNullOrBlank()){
            value = sharedPrefValue
            putString(key, value)
        }
        return value.orEmpty()
    }

    fun getLong(key: String, defaultValue: Long): Long {
        return sharedPreferences.getLong(key, defaultValue)
    }

    fun getFloat(key: String, defaultValue: Float): Float {
        return sharedPreferences.getFloat(key, defaultValue)
    }

    fun putInt(key: String, value: Int) {
        sharedPreferences.edit().putInt(key, value).apply()
    }

    fun getInt(key: String, defaultValue: Int = 0): Int {
        return sharedPreferences.getInt(key, defaultValue)
    }

    fun putBoolean(key: String, value: Boolean) {
        sharedPreferences.edit().putBoolean(key, value).apply()
    }

    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return sharedPreferences.getBoolean(key, defaultValue)
    }

    fun remove(key: String) {
        sharedPreferences.edit().remove(key).apply()
    }

    fun clear() {
        sharedPreferences.edit().clear().apply()
    }
}
