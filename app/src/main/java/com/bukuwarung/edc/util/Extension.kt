package com.bukuwarung.edc.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.*
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.DisplayMetrics
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.google.android.material.button.MaterialButton
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import retrofit2.Response
import java.util.regex.Pattern
import java.lang.reflect.Type


inline fun <reified T : Fragment> T.withArgs(argsBuilder: Bundle.() -> Unit): T =
    this.apply {
        arguments = Bundle().apply(argsBuilder)
    }

fun String.cleanPhoneNumber(): String {
    if (isNullOrEmpty()) {
        return this
    }
    var str: String? = this
    str = str?.replace("+65", "")
    str = str?.replace("-", "")
    str = str?.replace("+81", "")
    str = str?.replace("+60", "")
    str = str?.replace("+49", "")
    str = str?.replace("+7", "")
    str = str?.replace("+91", "")
    str = str?.replace("+62", "")
    str = str?.replace("+1", "")

    val sb: StringBuffer = StringBuffer(str)
    while (sb.length > 1 && sb[0] == '0') sb.deleteCharAt(0)
    if (sb.toString().startsWith("62")) {
        sb.delete(0, 2)
    }
    str = sb.toString()
    return Pattern.compile("[^0-9+]").matcher(str).replaceAll("")
}

fun View.showView() {
    this.visibility = View.VISIBLE
}

fun View.hideView() {
    this.visibility = View.GONE
}

fun View.invisibleView() {
    this.visibility = View.INVISIBLE
}

inline fun <reified T> SharedPreferences.get(key: String, defaultValue: T): T {
    when(T::class) {
        Boolean::class -> return this.getBoolean(key, defaultValue as Boolean) as T
        Float::class -> return this.getFloat(key, defaultValue as Float) as T
        Int::class -> return this.getInt(key, defaultValue as Int) as T
        Long::class -> return this.getLong(key, defaultValue as Long) as T
        String::class -> return this.getString(key, defaultValue as String) as T
        else -> {
            if (defaultValue is Set<*>) {
                return this.getStringSet(key, defaultValue as Set<String>) as T
            }
        }
    }

    return defaultValue
}

inline fun <reified T> SharedPreferences.put(key: String, value: T){
    val editor = this.edit()

    when (T::class) {
        Boolean::class -> editor.putBoolean(key, value as Boolean)
        Float::class -> editor.putFloat(key, value as Float)
        Int::class -> editor.putInt(key, value as Int)
        Long::class -> editor.putLong(key, value as Long)
        String::class -> editor.putString(key, value as String?)
        else -> {
            if (value is Set<*>) {
                editor.putStringSet(key, value as Set<String?>)
            }
        }
    }

    editor.apply()
}

fun SharedPreferences.remove(key: String){
    val editor = this.edit()
    editor.remove(key)
    editor.apply()
}

fun SharedPreferences.clear(){
    val editor = this.edit()
    editor.clear().apply()
}

fun <T> Context.openActivity(it: Class<T>, extras: Bundle.() -> Unit = {}) {
    val intent = Intent(this, it)
    intent.putExtras(Bundle().apply(extras))
    startActivity(intent)
}

fun <T> Context.openActivityForResult(
    it: Class<T>,
    launcher: ActivityResultLauncher<Intent>,
    extras: Bundle.() -> Unit = {}
) {
    val intent = Intent(this, it)
    intent.putExtras(Bundle().apply(extras))
    launcher.launch(intent)
}

fun <T> Activity.goToDestination(it: Class<T>) {
    openActivity(it)
    finishAffinity()
}

fun <T> Activity.openActivityAndFinish(it: Class<T>, extras: Bundle.() -> Unit = {}) {
    openActivity(it, extras)
    finish()
}

fun View.singleClick(debounceTime: Long = 600L, action: () -> Unit) {
    this.setOnClickListener(object : View.OnClickListener {
        private var lastClickTime: Long = 0

        override fun onClick(v: View) {
            if (SystemClock.elapsedRealtime() - lastClickTime < debounceTime) return
            else action()

            lastClickTime = SystemClock.elapsedRealtime()
        }
    })
}

fun <T : RecyclerView.ViewHolder> T.listen(event: (position: Int, type: Int) -> Unit): T {
    itemView.singleClick {
        event.invoke(adapterPosition, itemViewType)
    }
    return this
}

fun Long.millisecondsToTime(): String {
    val minutes = this / 1000 / 60
    val seconds = this / 1000 % 60
    val secondsStr = seconds.toString()
    val secs = if (secondsStr.length >= 2) secondsStr.substring(0, 2)
    else "0$secondsStr"
    return "$minutes:$secs"
}

fun SpannableStringBuilder.boldText(strToBold: String, ignoreCase: Boolean = false): SpannableStringBuilder {
    if (indexOf(strToBold, ignoreCase = ignoreCase) < 0) return this
    val start = indexOf(strToBold, ignoreCase = ignoreCase)
    val end = start + (strToBold.length)
    setSpan(
        StyleSpan(Typeface.BOLD),
        start,
        end,
        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    return this
}

fun String.times(times: Int): String {
    val sb = StringBuilder()
    repeat(times) {
        sb.append(this)
    }
    return sb.toString()
}

// For verify Otp
fun setupAutoMove(vararg editTexts: EditText, onOtpInputted: (String) -> Unit) {
    fun getAllOtpText(): String {
        return editTexts.joinToString(separator = "") { it.text.toString() }
    }

    editTexts.forEachIndexed { index, _ ->
        when (index) {
            0 -> editTexts[index].setupAutoMove(null, editTexts[index + 1]) { onOtpInputted(getAllOtpText()) }
            3 -> editTexts[index].setupAutoMove(editTexts[index - 1], null) { onOtpInputted(getAllOtpText()) }
            else -> editTexts[index].setupAutoMove(editTexts[index - 1], editTexts[index + 1]) { onOtpInputted(getAllOtpText()) }
        }
    }
}


// Internal Functions
private fun EditText.setupAutoMove(previousField: EditText?, nextField: EditText?, afterChanged: () -> Unit) {
    addTextChangedListener {
        val number = it.toString()
        when (id) {
            R.id.tv_one, R.id.tv_two, R.id.tv_three -> {
                if (number.isNotEmpty()) {
                    nextField?.isEnabled = true
                    nextField?.requestFocus()
                }
            }
        }
        afterChanged()
    }

    setOnKeyListener { _, keyCode, event ->
        if (event!!.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DEL && id != R.id.tv_one && text.isEmpty()) {
            previousField?.text = null
            previousField?.requestFocus()
            Utils.showKeyboard(context)
            isEnabled = false
            return@setOnKeyListener true
        }

        false
    }

    onFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
        if (!hasFocus && text.toString().isEmpty()){
            isEnabled = false
        }
    }
}

val String?.orDash
    get() = (this ?: "-")

val Boolean?.isFalseOrNull
    get() = this == false || this == null

val Boolean?.isTrue
    get() = (this == true)

fun EditText.afterTextChanged(action: (String) -> Unit) {
    val textWatcher = object : TextWatcher {
        override fun afterTextChanged(editable: Editable?) {
            action(editable.toString())
        }

        override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
        }

        override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
        }

    }

    addTextChangedListener(textWatcher)
}

fun String?.isNotNullOrEmpty() = !this.isNullOrEmpty()

fun Context.getColorCompat(colorRes: Int): Int {
    return ContextCompat.getColor(this, colorRes)
}

fun Context.getDrawableCompat(drawableRes: Int): Drawable? {
    return ContextCompat.getDrawable(this, drawableRes)
}

val Int?.orNil
    get() = (this ?: 0)

val Long?.orNil
    get() = (this ?: 0L)

val Double?.orNil
    get() = (this ?: 0.0)

fun Double?.ifNull(defaultValue: () -> Double?) = this ?: defaultValue()

fun TextView.textHTML(html: String?) {
    html ?: return
    val htmlWithBreaks = html.replace("\n", "<br/>")
    val spannedText: Spanned = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        Html.fromHtml(htmlWithBreaks, Html.FROM_HTML_MODE_COMPACT)
    } else {
        Html.fromHtml(htmlWithBreaks)
    }
    text = spannedText
}

fun String?.orDefault(defaultValue: String)= this ?: defaultValue

fun Int?.orDefault(defaultValue: Int)= this ?: defaultValue

fun String?.isNotNullOrBlank() = !this.isNullOrBlank()

fun Boolean.asVisibility(): Int {
    return if (this) {
        View.VISIBLE
    } else {
        View.GONE
    }
}
fun Boolean.visibleIfTrue() = if (this) View.VISIBLE else View.INVISIBLE


fun Double.isZero() = this == 0.0

fun Double.isNotZero() = this != 0.0

fun Long.milliSecondsToSeconds(): Long = this/1000

fun Double.milliSecondsToSeconds(): Double = this/1000

fun TextView.setDrawable(left: Int = 0, right: Int = 0, top: Int = 0, bottom: Int = 0) {
    this.setCompoundDrawablesWithIntrinsicBounds(
        left,
        top,
        right,
        bottom
    )
}

fun TextView.setDrawableRightListener(listener: () -> Unit) {
    isClickable = true
    setOnTouchListener { _, motionEvent ->
        val drawableRight = 2
        if (motionEvent.action != MotionEvent.ACTION_UP || compoundDrawables[drawableRight] == null)
            return@setOnTouchListener false
        if (motionEvent.rawX >= (right - compoundDrawables[drawableRight].bounds.width() - paddingEnd)) {
            listener()
            return@setOnTouchListener false
        }
        false
    }
}

fun Int.isZero() = this == 0

fun SpannableStringBuilder.colorText(strToColor: String, colorInt : Int, ignoreCase: Boolean = false): SpannableStringBuilder {
    if (indexOf(strToColor, ignoreCase = ignoreCase) < 0) return this
    val start = indexOf(strToColor, ignoreCase = ignoreCase)
    val end = start + (strToColor.length)
    setSpan(
        ForegroundColorSpan(colorInt),
        start,
        end,
        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    return this
}

fun <T> MutableList<T>.addIf(item: T, condition: Boolean){
    if(condition) this.add(item)
}

fun Any.getClassTag(): String = this.javaClass.simpleName

fun <T> Response<T>.errorMessage(): String {
    val msg = errorBody()?.string()
    val errorMsg = if (msg.isNullOrEmpty()) {
        message()
    } else {
        getErrorMessage(msg)
    }
    return errorMsg
}

private fun getErrorMessage(response: String): String {
    if (response.isBlank()) return response
    return try {
        val json = JSONObject(response)

        when {
            json.has("message") -> json.getString("message")
            json.has("errors") -> json.getString("errors")
            else -> response
        }
    } catch (e: Exception) {
        response
    }
}

fun View.getBitmapFromView(): Bitmap {
    //Define a bitmap with the same size as the view
    val returnedBitmap = Bitmap.createBitmap(this.width, this.height, Bitmap.Config.ARGB_8888)
    //Bind a canvas to it
    val canvas = Canvas(returnedBitmap)
    //Get the view's background
    val bgDrawable = this.background
    if (bgDrawable != null) {
        //has background drawable, then draw it on the canvas
        bgDrawable.draw(canvas)
    } else {
        //does not have background drawable, then draw white background on the canvas
        canvas.drawColor(Color.WHITE)
    }
    // draw the view on the canvas
    this.draw(canvas)
    //return the bitmap
    return returnedBitmap
}

fun Long?.secondsToMilliSeconds(): Long = this.orNil*1000

fun <T> Type.returnObject(json: String): T? {
    return try {
        val gson: Gson = GsonBuilder().create()
        gson.fromJson(json, this)
    } catch (e: Exception) {
        null
    }
}

fun Double?.getValueFromPercent(): Double {
    return if (this.orNil.isZero())
        0.0
    else
        this.orNil / 100
}

fun MaterialButton.makeButtonInactive(context: Context?) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.black_20))
    strokeWidth = 0
    setTextColor(ContextCompat.getColor(context, R.color.white))
}

fun MaterialButton.setStyleButtonColorFill(
    context: Context?,
    backgroundColor: Int,
    textColor: Int
) {
    if (context == null) return
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, backgroundColor))
    strokeWidth = 0
    setTextColor(ContextCompat.getColor(context, textColor))
}

fun EditText.setupForSearch(
    coroutineScope: CoroutineScope,
    delay: Long = 1000,
    enableImeAction: Boolean = true,
    onQuerySubmitted: (newQuery: String) -> Unit
) {
    // add UX to submit query after user stop typing for n second
    afterTextChanged {
        coroutineScope.launch {
            delay(delay)
            onQuerySubmitted(text.toString())
        }
    }

    if (enableImeAction) {
        // add action when Ime button clicked
        setOnEditorActionListener { _, actionId, event ->
            if (
                actionId == EditorInfo.IME_ACTION_SEARCH
                || event.action == KeyEvent.ACTION_DOWN
                && event.keyCode == KeyEvent.KEYCODE_ENTER
            ) {
                onQuerySubmitted(text.toString())
            }

            true
        }
    }
}

val Boolean?.isFalse
    get() = this == false

fun getScreenWidth(activity: Activity): Int {
    val displayMetrics = DisplayMetrics()
    activity.windowManager.defaultDisplay.getMetrics(displayMetrics)
    return displayMetrics.widthPixels
}
fun TextView.clearDrawables() {
    this.setDrawable()
}

fun String.addQuery(query: String): String {
    return if (this.contains("?")) {
        this + "&" + query
    } else {
        this + "?" + query
    }
}