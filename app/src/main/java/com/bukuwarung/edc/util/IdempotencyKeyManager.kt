package com.bukuwarung.edc.util

import java.util.UUID

object IdempotencyManager {
    private val keyMap = mutableMapOf<String, String>()

    fun getOrCreateKey(actionId: String): String {
        return keyMap.getOrPut(actionId) {
            UUID.randomUUID().toString()
        }
    }

    fun clearKey(actionId: String) {
        keyMap.remove(actionId)
    }

    fun getExistingKey(actionId: String): String? {
        return keyMap[actionId]
    }
}