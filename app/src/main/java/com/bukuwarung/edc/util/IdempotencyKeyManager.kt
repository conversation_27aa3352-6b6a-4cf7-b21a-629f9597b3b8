package com.bukuwarung.edc.util

import java.util.UUID

object IdempotencyManager {
    private val keyMap = mutableMapOf<String, String>()

    fun getOrCreateKey(actionId: String): String {
        return keyMap.getOrPut(actionId) {
            UUID.randomUUID().toString()
        }
    }

    fun clearKey(actionId: String) {
        keyMap.remove(actionId)
    }

    fun getExistingKey(actionId: String): String? {
        return keyMap[actionId]
    }

    /**
     * Clears key and generates new one for safe retry scenarios
     * Use this for 5xx errors, network timeouts, or connection failures
     */
    fun refreshKeyForRetry(actionId: String): String {
        clearKey(actionId)
        return getOrCreateKey(actionId)
    }

    /**
     * Determines if it's safe to retry based on HTTP status code
     * @param statusCode HTTP response code (null for network errors)
     * @return true if safe to retry with new idempotency key
     */
    fun shouldRetryWithNew<PERSON>ey(statusCode: Int?): Boolean {
        return when {
            statusCode == null -> true // Network error - safe to retry
            statusCode in 500..599 -> true // Server errors - safe to retry
            statusCode == 408 -> true // Request Timeout - safe to retry
            statusCode == 429 -> true // Rate Limited - safe to retry after delay
            statusCode in 400..499 -> false // Client errors - don't retry
            else -> false
        }
    }

    /**
     * Handle response and manage idempotency key accordingly
     * @param actionId The action identifier
     * @param statusCode HTTP response code (null for network errors)
     * @param wasSuccessful Whether the operation completed successfully
     */
    fun handleResponse(actionId: String, statusCode: Int?, wasSuccessful: Boolean) {
        when {
            wasSuccessful -> {
                // Success - clear the key as operation completed
                clearKey(actionId)
            }

            shouldRetryWithNewKey(statusCode) -> {
                // Retryable error - keep key for potential retry
                // Don't clear yet, let the retry mechanism decide
            }

            else -> {
                // Non-retryable error (4xx) - clear key to prevent stuck state
                clearKey(actionId)
            }
        }
    }
}