package com.bukuwarung.edc.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {

    public static boolean isEmpty(ArrayList<?> list) {
        return list == null || list.size() == 0;
    }

    public static boolean isEmpty(List<?> list) {
        return list == null || list.size() == 0;
    }

    public static List<String> convertToList(String str) {
        if (str == null) {
            return new ArrayList();
        }
        try {
            Gson gson = new GsonBuilder().disableHtmlEscaping().serializeNulls().create();
            List<String> list = gson.fromJson(str, new TypeToken<List<String>>() {
            }.getType());
            if (list != null) {
                return list;
            }
        } catch (Exception unused) {
        }
        return new ArrayList();
    }


}
