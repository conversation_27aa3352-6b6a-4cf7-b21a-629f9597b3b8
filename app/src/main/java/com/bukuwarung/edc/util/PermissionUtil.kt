package com.bukuwarung.edc.util

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.EdcApplication

object PermissionUtil {

    fun hasLocationPermission(): <PERSON><PERSON>an {
        var isGranted = true
        if (Build.VERSION.SDK_INT < 23) {
            return true
        }
        val context: Context = EdcApplication.instance
        if (context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
            context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
        ) {
            isGranted = false
        }
        return isGranted
    }

    fun requestLocationPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= 23) {
            activity.requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ), Constant.ACCESS_LOCATION
            )
        }
    }

    fun hasBluetoothPermission(): Boolean {
        var isGranted = true
        val context: Context = EdcApplication.instance
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (context.checkSelfPermission(Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED &&
                context.checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED
            ) {
                isGranted = false
            }
        }
        return isGranted
    }

    fun requestBluetoothPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            activity.requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT
                ), Constant.BLUETOOTH_PERMISSION
            )
        }
    }
}