package com.bukuwarung.edc.util

import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Tracks ongoing requests at the UI level to prevent duplicate submissions
 */
@Singleton
class RequestTracker @Inject constructor() {
    
    companion object {
        // Request timeout (5 minutes)
        private const val REQUEST_TIMEOUT_MS = 5 * 60 * 1000L
    }
    
    // Track ongoing requests with their start time
    private val ongoingRequests = ConcurrentHashMap<String, Long>()
    
    // Mutex for thread-safe operations
    private val mutex = Mutex()
    
    /**
     * Checks if a request is already in progress
     * @param requestKey Unique identifier for the request
     * @return true if request is already in progress, false otherwise
     */
    suspend fun isRequestInProgress(requestKey: String): Boolean = mutex.withLock {
        val startTime = ongoingRequests[requestKey]
        if (startTime != null) {
            val elapsed = System.currentTimeMillis() - startTime
            if (elapsed < REQUEST_TIMEOUT_MS) {
                return true
            } else {
                // Remove expired request
                ongoingRequests.remove(requestKey)
            }
        }
        return false
    }
    
    /**
     * Starts tracking a request
     * @param requestKey Unique identifier for the request
     * @return true if request was successfully started, false if already in progress
     */
    suspend fun startRequest(requestKey: String): Boolean = mutex.withLock {
        if (isRequestInProgress(requestKey)) {
            return false
        }
        ongoingRequests[requestKey] = System.currentTimeMillis()
        return true
    }
    
    /**
     * Stops tracking a request
     * @param requestKey Unique identifier for the request
     */
    suspend fun finishRequest(requestKey: String) = mutex.withLock {
        ongoingRequests.remove(requestKey)
    }
    
    /**
     * Generates a request key for disbursement operations
     */
    fun generateDisbursementKey(
        accountId: String,
        customerId: String,
        amount: String,
        bankAccountId: String?
    ): String {
        return "disbursement:$accountId:$customerId:$amount:$bankAccountId"
    }
    
    /**
     * Generates a request key for transfer operations
     */
    fun generateTransferKey(
        accountId: String,
        amount: String,
        targetAccount: String
    ): String {
        return "transfer:$accountId:$amount:$targetAccount"
    }
    
    /**
     * Generates a request key for cash withdrawal operations
     */
    fun generateCashWithdrawalKey(
        accountId: String,
        amount: String
    ): String {
        return "cash_withdrawal:$accountId:$amount"
    }
    
    /**
     * Cleans up expired requests
     */
    suspend fun cleanup() = mutex.withLock {
        val now = System.currentTimeMillis()
        val expiredKeys = ongoingRequests.entries
            .filter { (_, startTime) -> now - startTime > REQUEST_TIMEOUT_MS }
            .map { it.key }
        
        expiredKeys.forEach { key ->
            ongoingRequests.remove(key)
        }
    }
    
    /**
     * Gets current statistics
     */
    fun getStats(): Map<String, Any> {
        return mapOf(
            "ongoing_requests" to ongoingRequests.size,
            "request_keys" to ongoingRequests.keys.toList()
        )
    }
    
    /**
     * Clears all tracked requests (useful for testing)
     */
    suspend fun clear() = mutex.withLock {
        ongoingRequests.clear()
    }
}
