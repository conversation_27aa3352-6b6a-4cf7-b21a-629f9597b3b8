package com.bukuwarung.edc.util;

import android.util.Log;

import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


public class RequestUtil {

    /*
        Don't use anywhere, it was added to debug arqc cryptogram value
     */
    public static String requestCyptogram(String dol,
                                          String atc,
                                          String iv,
                                          String mk,
                                          String ps,
                                          String edcArqc){
        try {
            RequestBody body = new FormBody.Builder()
                    .add("dol", dol)
                    .add("atc", atc)
                    .add("iv", iv)
                    .add("mk", mk)
                    .add("ps", ps)
                    .build();
            Request request = new Request.Builder().url("https://neapay.com/online-tools/cry2.html").addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .post(body).build();
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(request).execute();
            String generatedArqc = response.body().string().trim();
            Log.d("cryptogram_util", "edcArqc: "+edcArqc+", $generatedArqc: " + generatedArqc);
            Log.d("cryptogram_util", "code:" + response.code());
            return generatedArqc;
        }catch (Exception e){
            e.printStackTrace();
        }
        return edcArqc;
    }
}
