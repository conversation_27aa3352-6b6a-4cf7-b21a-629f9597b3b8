package com.bukuwarung.edc.util

import android.view.View
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.fragment.app.Fragment
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Extension functions for safe button clicks that prevent duplicate requests
 */

/**
 * Sets a click listener that prevents multiple rapid clicks
 * @param debounceTime Time in milliseconds to debounce clicks (default 1000ms)
 * @param action The action to perform on click
 */
fun View.setSafeClickListener(
    debounceTime: Long = 1000L,
    action: (View) -> Unit
) {
    var lastClickTime = 0L
    setOnClickListener { view ->
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime >= debounceTime) {
            lastClickTime = currentTime
            action(view)
        }
    }
}

/**
 * Sets a click listener that tracks request state to prevent duplicate submissions
 * @param requestTracker The request tracker instance
 * @param requestKey Unique key for this request
 * @param lifecycleScope Coroutine scope for the lifecycle
 * @param onRequestStart Called when request starts (for UI updates)
 * @param onRequestFinish Called when request finishes (for UI updates)
 * @param action The action to perform on click
 */
fun View.setRequestSafeClickListener(
    requestTracker: RequestTracker,
    requestKey: String,
    lifecycleScope: LifecycleCoroutineScope,
    onRequestStart: (() -> Unit)? = null,
    onRequestFinish: (() -> Unit)? = null,
    action: suspend () -> Unit
) {
    setOnClickListener { view ->
        lifecycleScope.launch {
            if (requestTracker.startRequest(requestKey)) {
                try {
                    // Disable the view to prevent additional clicks
                    view.isEnabled = false
                    onRequestStart?.invoke()
                    
                    // Execute the action
                    action()
                } finally {
                    // Re-enable the view and finish tracking
                    view.isEnabled = true
                    onRequestFinish?.invoke()
                    requestTracker.finishRequest(requestKey)
                }
            } else {
                // Request already in progress, show feedback
                Utils.showToast("Request is already being processed, please wait...")
            }
        }
    }
}

/**
 * Extension for Fragment to easily set request-safe click listeners
 */
fun Fragment.setRequestSafeClickListener(
    view: View,
    requestTracker: RequestTracker,
    requestKey: String,
    onRequestStart: (() -> Unit)? = null,
    onRequestFinish: (() -> Unit)? = null,
    action: suspend () -> Unit
) {
    view.setRequestSafeClickListener(
        requestTracker = requestTracker,
        requestKey = requestKey,
        lifecycleScope = lifecycleScope,
        onRequestStart = onRequestStart,
        onRequestFinish = onRequestFinish,
        action = action
    )
}

/**
 * Extension for Activity to easily set request-safe click listeners
 */
fun AppCompatActivity.setRequestSafeClickListener(
    view: View,
    requestTracker: RequestTracker,
    requestKey: String,
    onRequestStart: (() -> Unit)? = null,
    onRequestFinish: (() -> Unit)? = null,
    action: suspend () -> Unit
) {
    view.setRequestSafeClickListener(
        requestTracker = requestTracker,
        requestKey = requestKey,
        lifecycleScope = lifecycleScope,
        onRequestStart = onRequestStart,
        onRequestFinish = onRequestFinish,
        action = action
    )
}

/**
 * Creates a one-time click listener that disables itself after first click
 * Useful for critical operations like disbursements
 */
fun View.setOneTimeClickListener(
    resetAfterMs: Long = 5000L,
    action: (View) -> Unit
) {
    val hasBeenClicked = AtomicBoolean(false)
    
    setOnClickListener { view ->
        if (hasBeenClicked.compareAndSet(false, true)) {
            // Disable the view immediately
            view.isEnabled = false
            
            // Execute the action
            action(view)
            
            // Re-enable after specified time
            view.postDelayed({
                view.isEnabled = true
                hasBeenClicked.set(false)
            }, resetAfterMs)
        }
    }
}

/**
 * Extension to show loading state on a view
 */
fun View.showLoadingState(isLoading: Boolean) {
    isEnabled = !isLoading
    alpha = if (isLoading) 0.6f else 1.0f
}

/**
 * Debounced click listener that prevents rapid successive clicks
 */
fun View.setDebouncedClickListener(
    debounceTimeMs: Long = 1000L,
    action: (View) -> Unit
) {
    var lastClickTime = 0L
    setOnClickListener { view ->
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime > debounceTimeMs) {
            lastClickTime = currentTime
            action(view)
        }
    }
}
