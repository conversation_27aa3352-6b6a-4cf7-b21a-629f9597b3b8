package com.bukuwarung.edc.util

import android.annotation.SuppressLint
import android.content.Context
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.printer.util.PrintUtil
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.snackbar.Snackbar.SnackbarLayout

object ToastUtil {
    @SuppressLint("RestrictedApi")
    fun setToast(
        context: Context, code: Int, message: String, view: View,
        duration: Int = Snackbar.LENGTH_LONG,
        dismissCallBack: (() -> Unit)? =null) {
        val snackBar = Snackbar.make(view, "", duration)
        val customSnackView: View = SnackbarLayout.inflate(context, R.layout.custom_toast, null)

        customSnackView.findViewById<AppCompatTextView>(R.id.tv_toast_message).text =
            message
        customSnackView.findViewById<AppCompatImageView>(R.id.iv_toast_image)
            .setImageDrawable(PrintUtil.printUtil.findToastImage(code))
        customSnackView.findViewById<AppCompatTextView>(R.id.tv_toast_message)
            .setTextColor(PrintUtil.printUtil.findTextColor(code))


        val snackbarLayout = snackBar.view as Snackbar.SnackbarLayout
        snackbarLayout.setPadding(16, 0, 16, 0)

        val params = snackbarLayout.layoutParams as FrameLayout.LayoutParams
        params.width = FrameLayout.LayoutParams.WRAP_CONTENT
        params.apply {
            setMargins(50, 135, 50, 12)
        }

        params.gravity = Gravity.TOP or Gravity.CENTER
        snackbarLayout.background = context.resources.getDrawable(R.drawable.bg_toast_printer, null)
        snackbarLayout.layoutParams = params
        snackBar.setBackgroundTintList(PrintUtil.printUtil.findTintColor(code))

        snackbarLayout.addView(customSnackView, 0)
        snackBar.addCallback(object : Snackbar.Callback() {
            override fun onDismissed(transientBottomBar: Snackbar?, event: Int) {
                super.onDismissed(transientBottomBar, event)
                dismissCallBack?.invoke()
            }
        })
        snackBar.show()
    }
}