package com.bukuwarung.edc.util.video

import android.content.Context
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * Example usage of the modern video compression implementation
 * This demonstrates how to use the new APIs for video compression
 */
class VideoCompressionExample(private val context: Context) {
    
    companion object {
        private const val TAG = "VideoCompressionExample"
    }
    
    /**
     * Basic video compression example
     */
    fun basicCompressionExample(videoUri: String) {
        CoroutineScope(Dispatchers.Main).launch {
            Log.d(TAG, "=== Basic Video Compression Example ===")
            
            val compressionManager = VideoCompressionManager.getInstance(context)
            
            val compressedFile = compressionManager.compressVideoFromUri(
                videoUri = videoUri,
                maxSizeMB = 10L // 10MB limit
            )
            
            if (compressedFile != null) {
                Log.d(TAG, "Compression successful: ${compressedFile.absolutePath}")
                Log.d(TAG, "File size: ${compressedFile.length() / 1024 / 1024}MB")
            } else {
                Log.e(TAG, "Compression failed")
            }
        }
    }
    
    /**
     * Advanced compression with progress tracking
     */
    fun advancedCompressionExample(videoUri: String) {
        CoroutineScope(Dispatchers.Main).launch {
            Log.d(TAG, "=== Advanced Video Compression Example ===")
            
            val compressionManager = VideoCompressionManager.getInstance(context)
            
            val compressedFile = compressionManager.compressVideoFromUri(
                videoUri = videoUri,
                maxSizeMB = 25L,
                callback = object : VideoCompressionCallback {
                    override fun onProgressUpdate(progress: VideoCompressionProgress) {
                        Log.d(TAG, "Compression progress: ${progress.progressPercent}%")
                        Log.d(TAG, "  Current time: ${progress.currentTimeUs / 1000 / 1000}s")
                        Log.d(TAG, "  Total time: ${progress.totalTimeUs / 1000 / 1000}s")
                        Log.d(TAG, "  Processed frames: ${progress.processedFrames}")
                        
                        // Update UI progress bar here
                        updateProgressBar(progress.progressPercent)
                    }
                    
                    override fun onCompleted(result: VideoCompressionResult) {
                        when (result) {
                            is VideoCompressionResult.Success -> {
                                Log.d(TAG, "=== Compression Completed Successfully ===")
                                Log.d(TAG, "Output file: ${result.outputFile}")
                                Log.d(TAG, "Original size: ${result.originalSizeBytes / 1024 / 1024}MB")
                                Log.d(TAG, "Compressed size: ${result.compressedSizeBytes / 1024 / 1024}MB")
                                Log.d(TAG, "Compression ratio: ${"%.1f".format(result.compressionRatio * 100)}%")
                                Log.d(TAG, "Processing time: ${result.processingTimeMs}ms")
                                Log.d(TAG, "Hardware acceleration: ${result.usedHardwareAcceleration}")
                                
                                // Handle successful compression
                                onCompressionSuccess(result)
                            }
                            is VideoCompressionResult.Error -> {
                                Log.e(TAG, "=== Compression Failed ===")
                                Log.e(TAG, "Error: ${result.errorCode}")
                                Log.e(TAG, "Message: ${result.message}")
                                
                                // Handle compression error
                                onCompressionError(result)
                            }
                        }
                    }
                    
                    override fun onError(error: VideoCompressionError, message: String, exception: Exception?) {
                        Log.e(TAG, "Compression error: $error - $message", exception)
                        
                        // Handle specific error types
                        when (error) {
                            VideoCompressionError.CODEC_NOT_FOUND -> {
                                Log.w(TAG, "No suitable codec found, trying fallback")
                            }
                            VideoCompressionError.HARDWARE_ENCODER_NOT_AVAILABLE -> {
                                Log.w(TAG, "Hardware encoder not available, using software")
                            }
                            VideoCompressionError.INSUFFICIENT_STORAGE -> {
                                Log.e(TAG, "Not enough storage space")
                            }
                            VideoCompressionError.INVALID_INPUT_FILE -> {
                                Log.e(TAG, "Invalid input video file")
                            }
                            else -> {
                                Log.e(TAG, "Unknown compression error")
                            }
                        }
                    }
                }
            )
            
            // Handle final result
            if (compressedFile != null) {
                Log.d(TAG, "Final compressed file: ${compressedFile.absolutePath}")
            } else {
                Log.e(TAG, "Compression failed completely")
            }
        }
    }
    
    /**
     * Custom configuration example
     */
    fun customConfigurationExample(videoUri: String) {
        CoroutineScope(Dispatchers.Main).launch {
            Log.d(TAG, "=== Custom Configuration Example ===")
            
            // Check device capabilities
            val capabilities = VideoCodecCapabilities()
            Log.d(TAG, "Device capabilities:")
            Log.d(TAG, "  Hardware encoder: ${capabilities.hasHardwareEncoder}")
            Log.d(TAG, "  Max resolution: ${capabilities.maxSupportedWidth}x${capabilities.maxSupportedHeight}")
            Log.d(TAG, "  Device type: ${when {
                capabilities.isHighEndDevice -> "High-end"
                capabilities.isLowEndDevice -> "Low-end"
                else -> "Mid-range"
            }}")
            
            // Create custom configuration
            val customConfig = VideoCompressionConfig(
                targetWidth = 1280,
                targetHeight = 720,
                targetBitrate = 3_000_000, // 3 Mbps
                targetFrameRate = 30,
                quality = CompressionQuality.HIGH,
                useHardwareAcceleration = capabilities.hasHardwareEncoder,
                audioEnabled = true,
                audioBitrate = 128_000 // 128 kbps
            )
            
            // Optimize for device
            val optimizedConfig = customConfig.optimizeForDevice(capabilities)
            Log.d(TAG, "Optimized config: ${optimizedConfig.targetWidth}x${optimizedConfig.targetHeight} @ ${optimizedConfig.targetBitrate / 1000}kbps")
            
            // Use custom configuration
            val compressionManager = VideoCompressionManager.getInstance(context)
            val compressedFile = compressionManager.compressVideoFromUri(
                videoUri = videoUri,
                maxSizeMB = 20L,
                config = optimizedConfig,
                callback = createProgressCallback()
            )
            
            Log.d(TAG, "Custom compression result: ${compressedFile?.absolutePath ?: "Failed"}")
        }
    }
    
    /**
     * Device capability testing example
     */
    fun deviceCapabilityExample() {
        Log.d(TAG, "=== Device Capability Testing ===")
        
        val capabilities = VideoCodecCapabilities()
        
        // Test encoder availability
        val h264Encoder = capabilities.findBestEncoder(VideoCodecCapabilities.H264_MIME_TYPE, true)
        if (h264Encoder != null) {
            Log.d(TAG, "H.264 encoder: ${h264Encoder.name} (HW: ${h264Encoder.isHardwareAccelerated})")
        } else {
            Log.w(TAG, "No H.264 encoder found")
        }
        
        // Test configuration compatibility
        val testConfig = VideoCompressionConfig(
            targetWidth = 1920,
            targetHeight = 1080,
            targetBitrate = 5_000_000
        )
        
        val canHandle = capabilities.canHandleConfiguration(testConfig)
        Log.d(TAG, "Can handle 1080p config: $canHandle")
        
        // Get optimal configuration
        val optimalConfig = capabilities.getOptimalConfiguration(30 * 1024 * 1024) // 30MB
        Log.d(TAG, "Optimal config for 30MB: ${optimalConfig.targetWidth}x${optimalConfig.targetHeight}")
    }
    
    /**
     * Batch compression example
     */
    fun batchCompressionExample(videoUris: List<String>) {
        CoroutineScope(Dispatchers.Main).launch {
            Log.d(TAG, "=== Batch Compression Example ===")
            
            val compressionManager = VideoCompressionManager.getInstance(context)
            val results = mutableListOf<File?>()
            
            videoUris.forEachIndexed { index, videoUri ->
                Log.d(TAG, "Compressing video ${index + 1}/${videoUris.size}: $videoUri")
                
                val compressedFile = compressionManager.compressVideoFromUri(
                    videoUri = videoUri,
                    maxSizeMB = 15L,
                    callback = object : VideoCompressionCallback {
                        override fun onProgressUpdate(progress: VideoCompressionProgress) {
                            Log.d(TAG, "Video ${index + 1} progress: ${progress.progressPercent}%")
                        }
                        
                        override fun onCompleted(result: VideoCompressionResult) {
                            Log.d(TAG, "Video ${index + 1} completed")
                        }
                        
                        override fun onError(error: VideoCompressionError, message: String, exception: Exception?) {
                            Log.e(TAG, "Video ${index + 1} failed: $message")
                        }
                    }
                )
                
                results.add(compressedFile)
                Log.d(TAG, "Video ${index + 1} result: ${compressedFile?.absolutePath ?: "Failed"}")
            }
            
            val successCount = results.count { it != null }
            Log.d(TAG, "Batch compression completed: $successCount/${videoUris.size} successful")
        }
    }
    
    // Helper methods
    private fun updateProgressBar(progressPercent: Int) {
        // Update UI progress bar
        Log.d(TAG, "UI Progress: $progressPercent%")
    }
    
    private fun onCompressionSuccess(result: VideoCompressionResult.Success) {
        // Handle successful compression
        Log.d(TAG, "Handling compression success")
    }
    
    private fun onCompressionError(result: VideoCompressionResult.Error) {
        // Handle compression error
        Log.e(TAG, "Handling compression error: ${result.message}")
    }
    
    private fun createProgressCallback(): VideoCompressionCallback {
        return object : VideoCompressionCallback {
            override fun onProgressUpdate(progress: VideoCompressionProgress) {
                updateProgressBar(progress.progressPercent)
            }
            
            override fun onCompleted(result: VideoCompressionResult) {
                when (result) {
                    is VideoCompressionResult.Success -> onCompressionSuccess(result)
                    is VideoCompressionResult.Error -> onCompressionError(result)
                }
            }
            
            override fun onError(error: VideoCompressionError, message: String, exception: Exception?) {
                Log.e(TAG, "Compression error: $error - $message", exception)
            }
        }
    }
}
