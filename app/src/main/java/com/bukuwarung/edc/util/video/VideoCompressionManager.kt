package com.bukuwarung.edc.util.video

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.util.Log
import androidx.core.net.toUri
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * Modern video compression manager using latest Android APIs
 * Provides hardware-accelerated video compression with fallback mechanisms
 */
class VideoCompressionManager private constructor(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "VideoCompressionManager"
        
        @Volatile
        private var INSTANCE: VideoCompressionManager? = null
        
        fun getInstance(context: Context): VideoCompressionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: VideoCompressionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val capabilities = VideoCodecCapabilities()
    private val activeJobs = mutableMapOf<String, Job>()
    
    /**
     * Compress video from URI with modern Android APIs
     * 
     * @param videoUri Video URI (content:// or file://)
     * @param maxSizeMB Maximum file size in MB
     * @param config Optional compression configuration
     * @param callback Progress and completion callback
     * @return Compressed video file or null if compression fails
     */
    suspend fun compressVideoFromUri(
        videoUri: String,
        maxSizeMB: Long = 10L,
        config: VideoCompressionConfig? = null,
        callback: VideoCompressionCallback? = null
    ): File? = withContext(Dispatchers.IO) {
        
        Log.d(TAG, "=== Modern Video Compression Started ===")
        Log.d(TAG, "URI: $videoUri")
        Log.d(TAG, "Target max size: ${maxSizeMB}MB")
        Log.d(TAG, "Hardware encoder available: ${capabilities.hasHardwareEncoder}")
        
        try {
            // Convert URI to file if needed
            val originalFile = if (videoUri.startsWith("content://")) {
                Log.d(TAG, "Converting content URI to temp file...")
                createTempVideoFileFromUri(videoUri.toUri()) ?: run {
                    Log.e(TAG, "Failed to create temp file from URI")
                    callback?.onError(VideoCompressionError.INVALID_INPUT_FILE, "Failed to access video file", null)
                    return@withContext null
                }
            } else {
                File(videoUri).takeIf { it.exists() && it.canRead() } ?: run {
                    Log.e(TAG, "Input file does not exist or cannot be read")
                    callback?.onError(VideoCompressionError.INVALID_INPUT_FILE, "Input file not accessible", null)
                    return@withContext null
                }
            }
            
            val originalSizeMB = originalFile.length() / (1024.0 * 1024.0)
            Log.d(TAG, "Original file: ${originalFile.name}")
            Log.d(TAG, "Original size: ${"%.2f".format(originalSizeMB)}MB")
            
            // Check if compression is needed
            val maxSizeBytes = maxSizeMB * 1024 * 1024
            if (originalFile.length() <= maxSizeBytes) {
                Log.d(TAG, "Video already under size limit, copying original file")
                val outputFile = File.createTempFile("compressed_video", ".mp4", context.cacheDir)
                originalFile.copyTo(outputFile, overwrite = true)
                
                callback?.onCompleted(VideoCompressionResult.Success(
                    outputFile = outputFile.absolutePath,
                    originalSizeBytes = originalFile.length(),
                    compressedSizeBytes = outputFile.length(),
                    compressionRatio = 0f,
                    processingTimeMs = 0L,
                    usedHardwareAcceleration = false
                ))
                
                return@withContext outputFile
            }
            
            // Get video metadata
            val metadata = getVideoMetadata(originalFile.absolutePath)
            Log.d(TAG, "Video metadata: $metadata")
            
            // Create or optimize compression configuration
            val compressionConfig = config?.optimizeForDevice(capabilities) 
                ?: capabilities.getOptimalConfiguration(maxSizeBytes)
            
            Log.d(TAG, "Using compression config: $compressionConfig")
            
            // Use proper video compression with VideoTranscoder
            Log.d(TAG, "Starting VideoTranscoder-based video compression")

            // Create output file
            val outputFile = File.createTempFile("compressed_video", ".mp4", context.cacheDir)

            // Use VideoTranscoder for actual compression
            val transcoder = VideoTranscoder(capabilities, callback)
            val result = transcoder.transcodeVideo(
                inputPath = originalFile.absolutePath,
                outputPath = outputFile.absolutePath,
                config = compressionConfig
            )
            
            when (result) {
                is VideoCompressionResult.Success -> {
                    Log.d(TAG, "=== Video Compression Completed Successfully ===")
                    Log.d(TAG, "Compression ratio: ${"%.1f".format(result.compressionRatio * 100)}%")
                    Log.d(TAG, "Processing time: ${result.processingTimeMs}ms")
                    Log.d(TAG, "Hardware acceleration used: ${result.usedHardwareAcceleration}")
                    
                    callback?.onCompleted(result)
                    outputFile
                }
                is VideoCompressionResult.Error -> {
                    Log.e(TAG, "Video compression failed: ${result.message}", result.exception)
                    callback?.onError(result.errorCode, result.message, result.exception)
                    
                    // Try fallback compression
                    Log.d(TAG, "Attempting fallback compression...")
                    compressWithFallback(originalFile, compressionConfig, callback)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error during video compression", e)
            callback?.onError(VideoCompressionError.UNKNOWN_ERROR, e.message ?: "Unknown error", e)
            null
        }
    }
    

    
    /**
     * Fallback compression using simple file copy with validation
     */
    private suspend fun compressWithFallback(
        originalFile: File,
        config: VideoCompressionConfig,
        callback: VideoCompressionCallback?
    ): File? {
        Log.w(TAG, "Using fallback compression (file copy)")
        
        return try {
            val outputFile = File.createTempFile("compressed_video_fallback", ".mp4", context.cacheDir)
            originalFile.copyTo(outputFile, overwrite = true)
            
            val result = VideoCompressionResult.Success(
                outputFile = outputFile.absolutePath,
                originalSizeBytes = originalFile.length(),
                compressedSizeBytes = outputFile.length(),
                compressionRatio = 0f,
                processingTimeMs = 0L,
                usedHardwareAcceleration = false
            )
            
            callback?.onCompleted(result)
            outputFile
            
        } catch (e: Exception) {
            Log.e(TAG, "Fallback compression failed", e)
            callback?.onError(VideoCompressionError.UNKNOWN_ERROR, "Fallback compression failed", e)
            null
        }
    }
    
    /**
     * Get video metadata using MediaMetadataRetriever
     */
    private fun getVideoMetadata(videoPath: String): VideoMetadata {
        val retriever = MediaMetadataRetriever()
        
        return try {
            retriever.setDataSource(videoPath)
            
            VideoMetadata(
                width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toIntOrNull() ?: 0,
                height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toIntOrNull() ?: 0,
                duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L,
                bitrate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)?.toIntOrNull() ?: 0,
                frameRate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE)?.toFloatOrNull() ?: 30f,
                mimeType = retriever.extractMetadata(12) ?: "video/mp4" // METADATA_KEY_MIME_TYPE
            )
        } catch (e: Exception) {
            Log.w(TAG, "Failed to extract video metadata", e)
            VideoMetadata()
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.w(TAG, "Error releasing MediaMetadataRetriever", e)
            }
        }
    }
    
    /**
     * Create temporary video file from content URI
     */
    private fun createTempVideoFileFromUri(uri: Uri): File? {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val mimeType = context.contentResolver.getType(uri)
            
            val extension = when {
                mimeType?.contains("mp4") == true -> ".mp4"
                mimeType?.contains("3gp") == true -> ".3gp"
                mimeType?.contains("mov") == true -> ".mov"
                mimeType?.contains("avi") == true -> ".avi"
                else -> ".mp4"
            }
            
            val tempFile = File.createTempFile("temp_video", extension, context.cacheDir)
            val outputStream = FileOutputStream(tempFile)
            
            inputStream?.copyTo(outputStream)
            inputStream?.close()
            outputStream.close()
            
            tempFile
        } catch (e: IOException) {
            Log.e(TAG, "Failed to create temp file from URI", e)
            null
        }
    }
    
    /**
     * Cancel ongoing compression
     */
    fun cancelCompression(jobId: String) {
        activeJobs[jobId]?.cancel()
        activeJobs.remove(jobId)
    }
    
    /**
     * Cancel all ongoing compressions
     */
    fun cancelAllCompressions() {
        activeJobs.values.forEach { it.cancel() }
        activeJobs.clear()
    }


}

/**
 * Video metadata information
 */
data class VideoMetadata(
    val width: Int = 0,
    val height: Int = 0,
    val duration: Long = 0L,
    val bitrate: Int = 0,
    val frameRate: Float = 30f,
    val mimeType: String = "video/mp4"
) {
    val aspectRatio: Float get() = if (height > 0) width.toFloat() / height else 16f / 9f
    val durationSeconds: Float get() = duration / 1000f / 1000f
    val bitrateKbps: Float get() = bitrate / 1000f
}
