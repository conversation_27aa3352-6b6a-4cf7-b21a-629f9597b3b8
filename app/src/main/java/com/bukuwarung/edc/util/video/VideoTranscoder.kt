package com.bukuwarung.edc.util.video

import android.media.MediaCodec
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.MediaMetadataRetriever
import android.media.MediaMuxer
import android.util.Log
import android.view.Surface
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import java.io.File
import kotlin.coroutines.coroutineContext

/**
 * Modern video transcoder using MediaCodec, MediaMuxer, and MediaExtractor APIs
 */
class VideoTranscoder(
    private val capabilities: VideoCodecCapabilities,
    private val callback: VideoCompressionCallback? = null
) {
    
    companion object {
        private const val TAG = "VideoTranscoder"
        private const val TIMEOUT_USEC = 10000L
        private const val PROGRESS_UPDATE_INTERVAL_MS = 500L
    }
    
    private var videoEncoder: MediaCodec? = null
    private var audioEncoder: MediaCodec? = null
    private var videoDecoder: MediaCodec? = null
    private var audioDecoder: MediaCodec? = null
    private var mediaMuxer: MediaMuxer? = null
    private var mediaExtractor: MediaExtractor? = null
    private var inputSurface: Surface? = null
    private var outputSurface: Surface? = null
    
    private var videoTrackIndex = -1
    private var audioTrackIndex = -1
    private var muxerStarted = false
    
    /**
     * Transcode video using modern Android MediaCodec APIs
     */
    suspend fun transcodeVideo(
        inputPath: String,
        outputPath: String,
        config: VideoCompressionConfig
    ): VideoCompressionResult = withContext(Dispatchers.IO) {
        
        val startTime = System.currentTimeMillis()
        var usedHardwareAcceleration = false
        
        try {
            Log.d(TAG, "Starting video transcoding:")
            Log.d(TAG, "  Input: $inputPath")
            Log.d(TAG, "  Output: $outputPath")
            Log.d(TAG, "  Config: $config")

            // Validate input file
            val inputFile = File(inputPath)
            if (!inputFile.exists() || !inputFile.canRead()) {
                throw IllegalArgumentException("Input file does not exist or cannot be read: $inputPath")
            }

            val originalSize = inputFile.length()

            // Setup MediaExtractor for input
            setupMediaExtractor(inputPath)

            // Get input video metadata
            val inputVideoFormat = getVideoTrackFormat() ?: throw IllegalStateException("No video track found")
            val inputAudioFormat = if (config.audioEnabled) getAudioTrackFormat() else null

            val inputWidth = inputVideoFormat.getInteger(MediaFormat.KEY_WIDTH)
            val inputHeight = inputVideoFormat.getInteger(MediaFormat.KEY_HEIGHT)
            val inputDuration = inputVideoFormat.getLong(MediaFormat.KEY_DURATION)

            // Detect video rotation
            val videoRotation = getVideoRotation(inputPath)
            Log.d(TAG, "Input video: ${inputWidth}x${inputHeight}, duration: ${inputDuration}us, rotation: ${videoRotation}°")

            // Setup MediaMuxer for output
            setupMediaMuxer(outputPath, videoRotation)

            // Setup video encoder with rotation-aware configuration
            val adjustedConfig = adjustConfigForRotation(config, videoRotation, inputWidth, inputHeight)
            val videoEncoderInfo = setupVideoEncoder(adjustedConfig)
            usedHardwareAcceleration = videoEncoderInfo.isHardwareAccelerated

            // Setup audio encoder if needed
            var audioEncodingEnabled = config.audioEnabled && inputAudioFormat != null
            if (audioEncodingEnabled) {
                setupAudioEncoder(config)
                // Check if audio encoder was successfully created
                audioEncodingEnabled = audioEncoder != null
                if (!audioEncodingEnabled) {
                    Log.w(TAG, "Audio encoding disabled due to encoder setup failure")
                }
            }

            // Setup video decoder
            setupVideoDecoder(inputVideoFormat)

            // Setup audio decoder if needed
            if (audioEncodingEnabled && inputAudioFormat != null) {
                setupAudioDecoder(inputAudioFormat)
            }

            // Start transcoding process
            val success = performTranscoding(inputDuration, config, audioEncodingEnabled)

            if (!success) {
                throw RuntimeException("Transcoding failed")
            }
            
            val outputFile = File(outputPath)
            val compressedSize = outputFile.length()
            val compressionRatio = (originalSize - compressedSize).toFloat() / originalSize
            val processingTime = System.currentTimeMillis() - startTime
            
            Log.d(TAG, "Transcoding completed successfully:")
            Log.d(TAG, "  Original size: ${originalSize / 1024 / 1024}MB")
            Log.d(TAG, "  Compressed size: ${compressedSize / 1024 / 1024}MB")
            Log.d(TAG, "  Compression ratio: ${(compressionRatio * 100).toInt()}%")
            Log.d(TAG, "  Processing time: ${processingTime}ms")
            Log.d(TAG, "  Hardware acceleration: $usedHardwareAcceleration")
            Log.d(TAG, "  Video rotation preserved: ${videoRotation}°")
            
            VideoCompressionResult.Success(
                outputFile = outputPath,
                originalSizeBytes = originalSize,
                compressedSizeBytes = compressedSize,
                compressionRatio = compressionRatio,
                processingTimeMs = processingTime,
                usedHardwareAcceleration = usedHardwareAcceleration
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Video transcoding failed", e)
            
            val errorCode = when (e) {
                is IllegalArgumentException -> VideoCompressionError.INVALID_INPUT_FILE
                is MediaCodec.CodecException -> VideoCompressionError.ENCODING_FAILED
                else -> VideoCompressionError.UNKNOWN_ERROR
            }
            
            VideoCompressionResult.Error(
                exception = e,
                errorCode = errorCode,
                message = e.message ?: "Unknown error occurred"
            )
        } finally {
            cleanup()
        }
    }
    
    private fun setupMediaExtractor(inputPath: String) {
        mediaExtractor = MediaExtractor().apply {
            setDataSource(inputPath)
        }
    }
    
    private fun setupMediaMuxer(outputPath: String, rotation: Int = 0) {
        mediaMuxer = MediaMuxer(outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4).apply {
            // Preserve video rotation
            if (rotation != 0) {
                setOrientationHint(rotation)
                Log.d(TAG, "Applied rotation hint: ${rotation}°")
            }
        }
    }
    
    private fun setupVideoEncoder(config: VideoCompressionConfig): CodecInfo {
        val encoderInfo = capabilities.findBestEncoder(config.outputFormat, config.useHardwareAcceleration)
            ?: throw IllegalStateException("No suitable video encoder found")
        
        Log.d(TAG, "Using video encoder: ${encoderInfo.name} (HW: ${encoderInfo.isHardwareAccelerated})")
        
        val videoFormat = config.createVideoFormat()
        
        videoEncoder = MediaCodec.createByCodecName(encoderInfo.name).apply {
            configure(videoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
            inputSurface = createInputSurface()
            start()
        }
        
        return encoderInfo
    }
    
    private fun setupAudioEncoder(config: VideoCompressionConfig) {
        val audioFormat = config.createAudioFormat() ?: return

        val encoderInfo = capabilities.findBestEncoder(MediaFormat.MIMETYPE_AUDIO_AAC, false)
        if (encoderInfo == null) {
            Log.w(TAG, "No suitable audio encoder found, disabling audio encoding")
            return
        }

        try {
            Log.d(TAG, "Using audio encoder: ${encoderInfo.name}")

            audioEncoder = MediaCodec.createByCodecName(encoderInfo.name).apply {
                configure(audioFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
                start()
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to setup audio encoder, disabling audio: ${e.message}")
            audioEncoder = null
        }
    }
    
    private fun setupVideoDecoder(inputFormat: MediaFormat) {
        val mimeType = inputFormat.getString(MediaFormat.KEY_MIME) ?: MediaFormat.MIMETYPE_VIDEO_AVC
        val decoderInfo = capabilities.findBestDecoder(mimeType)
            ?: throw IllegalStateException("No suitable video decoder found")

        Log.d(TAG, "Using video decoder: ${decoderInfo.name}")

        // Configure decoder to output to encoder's input surface
        videoDecoder = MediaCodec.createByCodecName(decoderInfo.name).apply {
            configure(inputFormat, inputSurface, null, 0)
            start()
        }
    }
    
    private fun setupAudioDecoder(inputFormat: MediaFormat) {
        val mimeType = inputFormat.getString(MediaFormat.KEY_MIME) ?: MediaFormat.MIMETYPE_AUDIO_AAC
        val decoderInfo = capabilities.findBestDecoder(mimeType)
            ?: throw IllegalStateException("No suitable audio decoder found")
        
        Log.d(TAG, "Using audio decoder: ${decoderInfo.name}")
        
        audioDecoder = MediaCodec.createByCodecName(decoderInfo.name).apply {
            configure(inputFormat, null, null, 0)
            start()
        }
    }
    
    private suspend fun performTranscoding(
        totalDurationUs: Long,
        config: VideoCompressionConfig,
        audioEncodingEnabled: Boolean
    ): Boolean {
        var videoExtractorDone = false
        var videoDecoderDone = false
        var videoEncoderDone = false
        var audioExtractorDone = false
        var audioDecoderDone = false
        var audioEncoderDone = false

        var lastProgressUpdate = 0L
        var currentTimeUs = 0L

        val bufferInfo = MediaCodec.BufferInfo()

        while ((!videoEncoderDone || (audioEncodingEnabled && !audioEncoderDone)) && coroutineContext.isActive) {

            // Feed video data to decoder
            if (!videoExtractorDone) {
                val decoderInputBufferIndex = videoDecoder?.dequeueInputBuffer(TIMEOUT_USEC) ?: -1
                if (decoderInputBufferIndex >= 0) {
                    val inputBuffer = videoDecoder?.getInputBuffer(decoderInputBufferIndex)
                    val sampleSize = mediaExtractor?.readSampleData(inputBuffer!!, 0) ?: -1

                    if (sampleSize >= 0) {
                        val presentationTimeUs = mediaExtractor?.sampleTime ?: 0
                        videoDecoder?.queueInputBuffer(
                            decoderInputBufferIndex, 0, sampleSize, presentationTimeUs, 0
                        )
                        mediaExtractor?.advance()
                    } else {
                        // End of stream
                        videoDecoder?.queueInputBuffer(
                            decoderInputBufferIndex, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM
                        )
                        videoExtractorDone = true
                    }
                }
            }

            // Process video decoder output (renders to encoder surface)
            if (!videoDecoderDone) {
                val decoderStatus = videoDecoder?.dequeueOutputBuffer(bufferInfo, TIMEOUT_USEC) ?: -1

                when {
                    decoderStatus >= 0 -> {
                        val doRender = bufferInfo.size != 0

                        // Render frame to encoder surface
                        videoDecoder?.releaseOutputBuffer(decoderStatus, doRender)

                        if ((bufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                            videoDecoderDone = true
                            videoEncoder?.signalEndOfInputStream()
                        }
                    }
                    decoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED -> {
                        Log.d(TAG, "Video decoder output format changed")
                    }
                }
            }

            // Process video encoder output
            if (!videoEncoderDone) {
                val encoderStatus = videoEncoder?.dequeueOutputBuffer(bufferInfo, TIMEOUT_USEC) ?: -1

                when {
                    encoderStatus >= 0 -> {
                        val encodedData = videoEncoder?.getOutputBuffer(encoderStatus)

                        if (bufferInfo.flags and MediaCodec.BUFFER_FLAG_CODEC_CONFIG != 0) {
                            bufferInfo.size = 0
                        }

                        if (bufferInfo.size != 0) {
                            if (!muxerStarted) {
                                throw RuntimeException("Muxer hasn't started")
                            }

                            encodedData?.position(bufferInfo.offset)
                            encodedData?.limit(bufferInfo.offset + bufferInfo.size)

                            mediaMuxer?.writeSampleData(videoTrackIndex, encodedData!!, bufferInfo)
                            currentTimeUs = bufferInfo.presentationTimeUs

                            // Update progress
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - lastProgressUpdate > PROGRESS_UPDATE_INTERVAL_MS) {
                                updateProgress(currentTimeUs, totalDurationUs)
                                lastProgressUpdate = currentTime
                            }
                        }

                        videoEncoder?.releaseOutputBuffer(encoderStatus, false)

                        if (bufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0) {
                            videoEncoderDone = true
                        }
                    }
                    encoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED -> {
                        val newFormat = videoEncoder?.outputFormat
                        videoTrackIndex = mediaMuxer?.addTrack(newFormat!!) ?: -1

                        if (!muxerStarted && videoTrackIndex >= 0 &&
                            (audioTrackIndex >= 0 || !audioEncodingEnabled)) {
                            mediaMuxer?.start()
                            muxerStarted = true
                        }
                    }
                }
            }

            // Process audio if enabled (simplified for now)
            if (audioEncodingEnabled && !audioEncoderDone) {
                // For now, mark audio as done to focus on video compression
                // Full audio processing would require similar decoder-encoder pipeline
                audioEncoderDone = true
            }

            // Yield to prevent blocking
            yield()
        }

        return true
    }
    
    private fun updateProgress(currentTimeUs: Long, totalTimeUs: Long) {
        val progressPercent = if (totalTimeUs > 0) {
            ((currentTimeUs * 100) / totalTimeUs).toInt().coerceIn(0, 100)
        } else 0
        
        val progress = VideoCompressionProgress(
            progressPercent = progressPercent,
            currentTimeUs = currentTimeUs,
            totalTimeUs = totalTimeUs,
            isCompleted = false
        )
        
        callback?.onProgressUpdate(progress)
    }
    
    private fun getVideoTrackFormat(): MediaFormat? {
        val extractor = mediaExtractor ?: return null
        
        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            val mime = format.getString(MediaFormat.KEY_MIME)
            if (mime?.startsWith("video/") == true) {
                extractor.selectTrack(i)
                return format
            }
        }
        return null
    }
    
    private fun getAudioTrackFormat(): MediaFormat? {
        val extractor = mediaExtractor ?: return null

        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            val mime = format.getString(MediaFormat.KEY_MIME)
            if (mime?.startsWith("audio/") == true) {
                return format
            }
        }
        return null
    }

    /**
     * Get video rotation from metadata
     */
    private fun getVideoRotation(videoPath: String): Int {
        var rotation = 0
        var retriever: MediaMetadataRetriever? = null

        try {
            retriever = MediaMetadataRetriever()
            retriever.setDataSource(videoPath)

            val rotationString = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)
            rotation = rotationString?.toIntOrNull() ?: 0

            Log.d(TAG, "Detected video rotation: ${rotation}°")

        } catch (e: Exception) {
            Log.w(TAG, "Failed to extract video rotation metadata: ${e.message}")
            rotation = 0
        } finally {
            try {
                retriever?.release()
            } catch (e: Exception) {
                Log.w(TAG, "Error releasing MediaMetadataRetriever: ${e.message}")
            }
        }

        return rotation
    }

    /**
     * Adjust compression configuration based on video rotation
     * For 90° and 270° rotations, we may need to swap width/height
     */
    private fun adjustConfigForRotation(
        config: VideoCompressionConfig,
        rotation: Int,
        inputWidth: Int,
        inputHeight: Int
    ): VideoCompressionConfig {

        // For portrait videos (90° or 270° rotation), consider swapping dimensions
        // if the target config doesn't match the rotated aspect ratio
        val isRotatedPortrait = rotation == 90 || rotation == 270

        if (isRotatedPortrait) {
            val inputAspectRatio = inputWidth.toFloat() / inputHeight.toFloat()
            val targetAspectRatio = config.targetWidth.toFloat() / config.targetHeight.toFloat()

            Log.d(TAG, "Rotation adjustment check:")
            Log.d(TAG, "  Input: ${inputWidth}x${inputHeight} (aspect: ${"%.2f".format(inputAspectRatio)})")
            Log.d(TAG, "  Target: ${config.targetWidth}x${config.targetHeight} (aspect: ${"%.2f".format(targetAspectRatio)})")
            Log.d(TAG, "  Rotation: ${rotation}°")

            // If aspect ratios are very different, consider swapping target dimensions
            val aspectRatioDiff = kotlin.math.abs(inputAspectRatio - targetAspectRatio)
            val swappedTargetAspectRatio = config.targetHeight.toFloat() / config.targetWidth.toFloat()
            val swappedAspectRatioDiff = kotlin.math.abs(inputAspectRatio - swappedTargetAspectRatio)

            if (swappedAspectRatioDiff < aspectRatioDiff && aspectRatioDiff > 0.5f) {
                Log.d(TAG, "  Swapping target dimensions for better aspect ratio match")
                return config.copy(
                    targetWidth = config.targetHeight,
                    targetHeight = config.targetWidth
                )
            }
        }

        Log.d(TAG, "Using original target dimensions: ${config.targetWidth}x${config.targetHeight}")
        return config
    }

    private fun cleanup() {
        try {
            videoEncoder?.stop()
            videoEncoder?.release()
            audioEncoder?.stop()
            audioEncoder?.release()
            videoDecoder?.stop()
            videoDecoder?.release()
            audioDecoder?.stop()
            audioDecoder?.release()
            mediaMuxer?.stop()
            mediaMuxer?.release()
            mediaExtractor?.release()
            inputSurface?.release()
            outputSurface?.release()
        } catch (e: Exception) {
            Log.w(TAG, "Error during cleanup", e)
        }
        
        videoEncoder = null
        audioEncoder = null
        videoDecoder = null
        audioDecoder = null
        mediaMuxer = null
        mediaExtractor = null
        inputSurface = null
        outputSurface = null
        muxerStarted = false
    }
}
