package com.bukuwarung.edc.verifyotp.data.model

import com.bukuwarung.edc.global.Constant
import com.google.gson.annotations.SerializedName

data class VerifyOtpRequest(
        @SerializedName("action")
        val action: String? = null,
        @SerializedName("countryCode")
        val countryCode: String,
        @SerializedName("phone")
        val phone: String,
        @SerializedName("otp")
        val otp: String,
        @SerializedName("client")
        val clientId: String = Constant.clientID,
        @SerializedName("clientId")
        val sellersClientId: String = Constant.clientID,
        @SerializedName("clientSecret")
        val clientSecret: String = Constant.clientSecret,
        @SerializedName("deviceId")
        val deviceId: String = "edc",
        @SerializedName("deviceModel")
        val deviceModel: String ="edc_device",
        @SerializedName("deviceBrand")
        val deviceBrand: String = "EDC",
        @SerializedName("androidId")
        val androidId: String? = "",
        @SerializedName("imeiNumber")
        val imeiNumber: String? = "",
        @SerializedName("wideVineId")
        val wideVineId: String? = "",
        @SerializedName("advertisingId")
        val advertisingId: String? = "",
        @SerializedName("rooted")
        val rooted: Boolean = false
)
