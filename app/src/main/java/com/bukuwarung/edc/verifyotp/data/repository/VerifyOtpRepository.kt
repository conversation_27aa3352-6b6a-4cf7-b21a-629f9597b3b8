package com.bukuwarung.edc.verifyotp.data.repository

import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.verifyotp.data.datasource.VerifyOtpDataSource
import com.bukuwarung.edc.verifyotp.data.model.VerifyOtpRequest
import javax.inject.Inject

class VerifyOtpRepository @Inject constructor(private val verifyOtpDataSource: VerifyOtpDataSource) {

    suspend fun verifyOtp(request: VerifyOtpRequest) = verifyOtpDataSource.verifyOtp(
        sessionId = EncryptedPreferencesHelper.get("bureau_event_id", ""),
        otpToken = sharedPreferences.get("op_token", ""),
        request = request
    )

    suspend fun verifyGenericOtp(opsToken: String,request: VerifyOtpRequest) = verifyOtpDataSource.verifyGenericOtp(opsToken, request)
}