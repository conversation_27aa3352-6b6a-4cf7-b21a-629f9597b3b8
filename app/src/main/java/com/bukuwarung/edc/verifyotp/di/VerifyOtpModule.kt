package com.bukuwarung.edc.verifyotp.di

import com.bukuwarung.edc.verifyotp.data.datasource.VerifyOtpDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class VerifyOtpModule {

    @Singleton
    @Provides
    fun provideVerifyOtpDataSource(@Named("normal") retrofit: Retrofit): VerifyOtpDataSource {
        return retrofit.create(VerifyOtpDataSource::class.java)
    }
}