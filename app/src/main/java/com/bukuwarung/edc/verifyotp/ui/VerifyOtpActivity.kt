package com.bukuwarung.edc.verifyotp.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityVerifyotpBinding
import com.bukuwarung.edc.global.enums.CommunicationChannel
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.enums.AuthActions
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.login.ui.LoginViewModel
import com.bukuwarung.edc.session.SessionManager
import com.bukuwarung.edc.util.*
import com.bukuwarung.edc.util.Utils.tryToGetValueOrDefault
import com.bukuwarung.edc.verifyotp.constant.ClassConstants.ONE_SECOND
import com.bukuwarung.edc.verifyotp.constant.ClassConstants.OTP_LENGTH
import com.bukuwarung.edc.verifyotp.constant.ClassConstants.OTP_WAIT_TIME
import com.bukuwarung.edc.verifyotp.constant.ClassConstants.VERIFY_OTP_PARAM_COUNTRY_CODE
import com.bukuwarung.edc.verifyotp.constant.ClassConstants.VERIFY_OTP_PARAM_PHONE
import com.bukuwarung.edc.verifyotp.constant.VerifyOtpAnalyticsConstants
import com.bukuwarung.edc.worker.location.LocationUtil
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.FirebaseFirestore
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VerifyOtpActivity: AppCompatActivity() {

    private lateinit var binding: ActivityVerifyotpBinding
    private val verifyOtpViewModel: VerifyOtpViewModel by viewModels()
    private val loginViewModel: LoginViewModel by viewModels()
    private var cTimer: CountDownTimer? = null
    private var phone = ""
    private var countryCode = ""
    private var otpChannel = CommunicationChannel.SMS
    private var authActions: AuthActions = AuthActions.LOGIN_OTP

    companion object {
        const val OTP_STATUS = "otp_status"
        const val OTP_TOKEN = "otp_token"

        const val OTP_USE_CASE = "otp_use_case"

        enum class UseCase {
            VERIFY_OTP_LOGIN,
            VERIFY_OTP_FORGOT_PASSWORD,
            VERIFY_OTP_SALES_CODE
        }
    }

    private val useCase by lazy { intent.getStringExtra(OTP_USE_CASE).orEmpty() }
    private val useCaseEnum by lazy {
        try {
            UseCase.valueOf(useCase)
        } catch (e: Exception) {
            UseCase.VERIFY_OTP_LOGIN
        }
    }


    @Inject
    lateinit var locationUtil: LocationUtil
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityVerifyotpBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.pbVerifyOtp.hideView()

        setUpToolbar()

        binding.btnRetryRequestOtp.singleClick { tryOTPAgain() }

        // initialize and set pin layout
        readOtp()

        if (intent.hasExtra(VERIFY_OTP_PARAM_PHONE)) {
            val intent = intent
            phone = intent.getStringExtra(VERIFY_OTP_PARAM_PHONE) ?: ""
            countryCode = intent.getStringExtra(VERIFY_OTP_PARAM_COUNTRY_CODE) ?: ""
            FirebaseCrashlytics.getInstance().setUserId(phone)
        } else {
            openActivity(LoginActivity::class.java)
            return
        }

        binding.tvOtpMessage.apply {
            val phoneNum = "$countryCode$phone"
            val newChannel =
                if (otpChannel == CommunicationChannel.SMS) getString(R.string.sms) else getString(
                    R.string.whatsapp
                )
            val message =
                getString(R.string.otp_message_placeholder, newChannel, phoneNum)
            val spannableStringBuilder = SpannableStringBuilder(message)
            text = spannableStringBuilder.boldText(newChannel)
        }

        checkForLiveData()

        binding.btnSwitchOtpChannel.apply {
            val newChannel =
                if (otpChannel == CommunicationChannel.SMS) getString(R.string.sms) else getString(
                    R.string.whatsapp
                )
            val formattedText = getString(R.string.otp_change_channel_placeholder, newChannel)
            text = formattedText
            isEnabled = false
        }

        if (useCaseEnum == UseCase.VERIFY_OTP_SALES_CODE) {
            authActions = AuthActions.SELLER_OTP_REQUEST
            loginViewModel.requestOtp(phone, otpChannel, authActions)
        }
    }

    private fun setUpToolbar() {
        with(binding.tbVerifyOtp) {
            title = "Kode OTP"
            navigationIcon =
                <EMAIL>(R.drawable.ic_back)
            setNavigationOnClickListener {
                Utils.hideKeyboard(this@VerifyOtpActivity)
                finish()
            }
        }
    }

    private fun readOtp() {
        with(binding.otpField) {
            tvOne.text = null
            tvTwo.text = null
            tvThree.text = null
            tvFour.text = null

            tvTwo.isEnabled = false
            tvThree.isEnabled = false
            tvFour.isEnabled = false

            setupAutoMove(tvOne, tvTwo, tvThree, tvFour) {
                if (it.length == OTP_LENGTH) {
                    verifyOtp(it)
                }
            }
        }
        startTimer()
    }

    private fun verifyOtp(otp: String) {
        if (otp.isBlank()) {
            return
        }
        binding.tvErrorMessage.apply {
            text = null
            hideView()
        }

        Utils.hideKeyboard(this)

        // start verify call request
        verifyOtpViewModel.verifyEnteredOtp(
            phone,
            countryCode.replace("+", ""),
            otp,
            useCaseEnum == UseCase.VERIFY_OTP_SALES_CODE
        )
    }

    fun getEnteredOtp(): String {
        with(binding.otpField) {
            return arrayOf(tvOne, tvTwo, tvThree, tvFour)
                .joinToString(separator = "") { it.text.toString() }
        }
    }

    // Timer functions
    private fun startTimer() {
        binding.btnSwitchOtpChannel.hideView()
        BuildConfig.DEBUG
        binding.tvTimer.showView()
        binding.tvErrorMessage.hideView()
        binding.btnRetryRequestOtp.hideView()
        cTimer = object : CountDownTimer(OTP_WAIT_TIME * ONE_SECOND, ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                binding.tvTimer.text = millisUntilFinished.millisecondsToTime()
            }

            override fun onFinish() {
                onTimerFinish()
            }
        }
        cTimer?.start()
    }

    private fun onTimerFinish() {
        cancelTimer()
        with(binding) {
            tvTimer.hideView()
            btnHelp.hideView()
            groupSkipLogin.hideView()
            btnSwitchOtpChannel.showView()
            btnRetryRequestOtp.showView()
            btnSwitchOtpChannel.apply {
                isEnabled = true
                val newChannel =
                    if (otpChannel == CommunicationChannel.SMS) getString(R.string.whatsapp) else getString(
                        R.string.sms
                    )
                val formattedText = getString(R.string.otp_change_channel_placeholder, newChannel)
                setTextColor(getColorCompat(R.color.blue_60))
                text = formattedText

//                visibility = (!showDefaultOtpScreen).asVisibility()
                singleClick {
                    btnSwitchOtpChannel.isEnabled = false
                    btnSwitchOtpChannel.setTextColor(getColorCompat(R.color.black_40))
                    binding.tvOtpMessage.apply {
                        val phoneNum = "$countryCode$phone"
                        val newChannel =
                            if (otpChannel == CommunicationChannel.SMS) getString(R.string.whatsapp) else getString(
                                R.string.sms
                            )
                        val message =
                            getString(R.string.otp_message_placeholder, newChannel, phoneNum)
                        val spannableStringBuilder = SpannableStringBuilder(message)
                        text = spannableStringBuilder.boldText(newChannel)
                    }
                    otpChannel = if (otpChannel == CommunicationChannel.SMS) CommunicationChannel.WA else CommunicationChannel.SMS
                    loginViewModel.requestOtp(phone, otpChannel, authActions)
                    startTimer()
                }
            }
        }
    }

    //cancel timer
    private fun cancelTimer() {
        cTimer?.cancel()
    }

    private fun tryOTPAgain() {
        // Request new otp
        loginViewModel.requestOtp(phone, otpChannel, authActions)
        startTimer()
    }

    private fun checkForLiveData() {
        loginViewModel.login.observe(this){
            when(it.status){
                Status.SUCCESS -> {
                    binding.pbVerifyOtp.hideView()
                    binding.tvErrorMessage.hideView()
                }
                Status.ERROR -> {
                    binding.pbVerifyOtp.hideView()
                    binding.tvErrorMessage.showView()
                    binding.tvErrorMessage.text = it.message
                }
                Status.LOADING -> {
                    binding.pbVerifyOtp.showView()
                }
                Status.NO_INTERNET -> {

                }
            }
        }
        verifyOtpViewModel.verifySellersOtpResponse.observe(this){
            when(it.status){
                Status.SUCCESS -> {
                    setResult(Activity.RESULT_OK, Intent().apply {
                        putExtra(OTP_STATUS, it.data?.status?.status)
                        putExtra(OTP_TOKEN, it.data?.token)
                    })
                    finish()
                }
                else -> {
                    finish()
                }
            }
        }
        verifyOtpViewModel.verifyOtpResponse.observe(this) {
            when(it.status) {
                Status.SUCCESS -> {
                    Analytics.initialize(this)
                    locationUtil.getLocation(this)
                    Analytics.trackEvent(VerifyOtpAnalyticsConstants.REGISTRATION_VERIFY_OTP)
                    binding.pbVerifyOtp.showView()
                    binding.tvErrorMessage.hideView()
                    openActivity(HomePageActivity::class.java)
                    finishAffinity()
                }

                Status.ERROR -> {
                    binding.pbVerifyOtp.hideView()
                    handleWrongOtp()
                }

                Status.LOADING -> {
                    binding.tvErrorMessage.hideView()
                    binding.pbVerifyOtp.showView()
                }
                Status.NO_INTERNET -> {
                    binding.pbVerifyOtp.hideView()
                    onTimerFinish()
                    binding.tvErrorMessage.showView()
                    val stringBuilder = SpannableStringBuilder()
                    stringBuilder.append(getString(R.string.no_internet_error))
                    val clickableSpan: ClickableSpan = object : ClickableSpan() {
                        override fun onClick(view: View) {
                            try {
                                verifyOtp(getEnteredOtp())
                            } catch (ex: Exception) {

                            }
                        }
                    }
                    stringBuilder.setSpan(
                        clickableSpan,
                        75,
                        stringBuilder.length,
                        Spannable.SPAN_INCLUSIVE_INCLUSIVE
                    )
                    // OTP code error
                    binding.tvErrorMessage.apply {
                        text = stringBuilder
                        movementMethod = LinkMovementMethod.getInstance()
                    }
                }
            }
        }
    }

    override fun onBackPressed() {
        when (useCaseEnum) {
            UseCase.VERIFY_OTP_SALES_CODE -> {
                finish()
            }
            else -> {
                super.onBackPressed()
            }
        }
    }

    private fun handleWrongOtp() {
        val message = getString(R.string.otp_error_wrong)
        val stringBuilder = SpannableStringBuilder(message)
        val clickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                tryOTPAgain()
            }
        }
        //bold WA atau SMS
        stringBuilder.setSpan(clickableSpan, 16, 25, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        // OTP code error
        binding.tvErrorMessage.apply {
            text = stringBuilder
            movementMethod = LinkMovementMethod.getInstance()
            showView()
        }
        onTimerFinish()
    }

}