package com.bukuwarung.edc.verifyotp.ui

import Resource
import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.auth0.android.jwt.JWT
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.global.enums.AuthActions
import com.bukuwarung.edc.global.enums.OtpStatus
import com.bukuwarung.network.interceptors.NoConnectivityException
import com.bukuwarung.network.model.request.SessionRequest
import com.bukuwarung.network.session.SessionRepository
import com.bukuwarung.edc.payments.data.model.response.OtpResponse
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.ClaimStoreId
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.verifyotp.data.model.VerifyOtpRequest
import com.bukuwarung.edc.verifyotp.data.model.VerifyOtpResponse
import com.bukuwarung.edc.verifyotp.usecase.VerifyOtpUseCase
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.get
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.NetworkConst.SESSION_TOKEN
import com.bukuwarung.network.refreshtoken.TokenManager
import com.google.firebase.auth.FirebaseAuth
import com.tiktok.TikTokBusinessSdk
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import org.json.JSONObject
import javax.inject.Inject

@HiltViewModel
class VerifyOtpViewModel @Inject constructor(
    private val verifyOtpUseCase: VerifyOtpUseCase,
    private val sessionRepository: SessionRepository
): ViewModel() {

    private val _verifyOtpResponse = MutableLiveData<Resource<VerifyOtpResponse>>()
    private val _verifySellersOtpResponse = MutableLiveData<Resource<OtpResponse>>()

    private val sharedPreferences: SharedPreferences by lazy { EdcApplication.instance.getSharedPreferences("preference_key", Context.MODE_PRIVATE) }

    val verifyOtpResponse: LiveData<Resource<VerifyOtpResponse>>
        get() = _verifyOtpResponse

    val verifySellersOtpResponse: LiveData<Resource<OtpResponse>>
        get() = _verifySellersOtpResponse

    fun verifyEnteredOtp(phone: String, countryCode: String, otp: String, isSalesOtpVerification: Boolean) = viewModelScope.launch {

        _verifyOtpResponse.postValue(Resource.loading(null))
        val verifyOtpRequest = VerifyOtpRequest(
            phone = phone,
            countryCode = countryCode,
            otp = otp,
            action = if (isSalesOtpVerification) AuthActions.VERIFY_SALES_CODE.action else null
        )

        try {
            if (isSalesOtpVerification) {
                val opsToken = Utils.sharedPreferences.get("seller_op_token", "")
                verifyOtpUseCase.verifyGenericOtp(opsToken, verifyOtpRequest).let {
                    if (it.isSuccessful) {
                        if (it.body()?.status != OtpStatus.OTP_VERIFIED) {
                            _verifyOtpResponse.postValue(Resource.error("wrong otp", null))
                        } else {
                            _verifySellersOtpResponse.postValue(Resource.success(it.body()))
                        }
                    } else {
                        val json = JSONObject(it.errorBody()?.string())
                        var message = ""
                        if (json.has("message")) {
                            message = json.getString("message")
                        }
                        _verifyOtpResponse.postValue(Resource.error(message, null))
                    }
                }
            } else {
                verifyOtpUseCase.verifyOtp(verifyOtpRequest).let { it ->
                    if (it.isSuccessful) {
                        val result = it.body()
                        if (result?.success != true) {
                            _verifyOtpResponse.postValue(Resource.error("wrong otp", null))
                        } else {
                            val data =
                                FirebaseAuth.getInstance().signInWithCustomToken(result.idToken!!)
                                    .await()
                            if (data.user != null) {
                                Log.d("login", "success ${data.user}")
                                val parsedJWT = JWT(result.idToken!!)
                                val claim =
                                    parsedJWT.getClaim("claims").asObject(ClaimStoreId::class.java)
                                claim?.let {
                                    EncryptedPreferencesHelper.put("uuid", it.auth_user_id)
                                    Analytics.trackSuperProperty(
                                        Constant.USER_PROP_BW_USER_ID,
                                        it.auth_user_id
                                    )
                                }
                                Analytics.trackSuperProperty(
                                    Constant.USER_PROP_KYC_TIER,
                                    Utils.getKycTierFromToken(result.idToken).name
                                )
                                sharedPreferences.put(SESSION_TOKEN, result.sessionToken)
                                sharedPreferences.put("phone_number", phone)
                                addTikTokIdentity(claim)
                                startOtpVerification(
                                    result.idToken,
                                    result.sessionToken!!,
                                    it.body()
                                )
                            } else {
                                Log.d("login", "fail ${data.user}")
                                _verifyOtpResponse.postValue(
                                    Resource.error(
                                        "firebase login failed",
                                        null
                                    )
                                )
                            }

                        }
                    } else {
                        val json = JSONObject(it.errorBody()?.string())
                        var message = ""
                        if (json.has("message")) {
                            message = json.getString("message")
                        }
                        _verifyOtpResponse.postValue(Resource.error(message, null))
                    }
                }
            }
        } catch (e: NoConnectivityException){
            _verifyOtpResponse.postValue(Resource.noInternet(null))
        } catch (e: Exception) {
            _verifyOtpResponse.postValue(Resource.error("-1", null))
        }
    }

    private fun startOtpVerification(token: String?, sessionToken: String, verifyOtpResponse: VerifyOtpResponse?
        ) = viewModelScope.launch {
        token ?: return@launch
        verifyOtpResponse ?: return@launch
        //keep single place to set idToken, keep refreshing on each session
        EncryptedPreferencesHelper.put(BUKUWARUNG_TOKEN, token)

        val newSessionRequest = SessionRequest(
            token = sessionToken,
            register = false,
            deviceId = "",
            deviceModel = Build.MODEL,
            deviceBrand = Build.MANUFACTURER,
            userId = Utils.getUserId(),
            clientId= Constant.clientID,
            clientSecret = Constant.clientSecret
        )
        val apiResponse = sessionRepository.createNewSession(newSessionRequest)

        if (apiResponse.isSuccessful) {
            val newSession = apiResponse.body()
            newSession?.let { TokenManager.updateTokens(it.idToken, it.refreshToken) }
            _verifyOtpResponse.postValue(Resource.success(verifyOtpResponse))
        } else {
            val errorMessage = apiResponse.errorBody()?.string() ?: "Unknown error"
            _verifyOtpResponse.postValue(Resource.error(errorMessage, null))
            bwLog("Error creating new session: $errorMessage")
        }
    }

    private fun addTikTokIdentity(claim: ClaimStoreId?) {
        claim ?: return
        TikTokBusinessSdk.identify(claim.auth_user_id, "", claim.user_id, "")
    }
}