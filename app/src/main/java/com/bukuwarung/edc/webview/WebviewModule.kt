package com.bukuwarung.edc.webview

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class WebviewModule {

    @Singleton
    @Provides
    fun provideWebviewDataSource(@Named("normal") retrofit: Retrofit): WebviewDataSource = retrofit.create(WebviewDataSource::class.java)
}