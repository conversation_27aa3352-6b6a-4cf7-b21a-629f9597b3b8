package com.bukuwarung.edc.webview

import android.os.Build
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.network.model.request.SessionRequest
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.NetworkConst.SESSION_TOKEN
import com.bukuwarung.network.refreshtoken.TokenManager
import com.google.firebase.auth.FirebaseAuth
import javax.inject.Inject

class WebviewRepository @Inject constructor(private val webviewDataSource: WebviewDataSource) {

    suspend fun refreshToken(): String? {
        sharedPreferences.put("token_refresh_fail", false)

        val token: String? = sharedPreferences.get(SESSION_TOKEN, "")
        if (token.isNullOrEmpty()) {
            return null
        }
        val newSessionRequest = SessionRequest(
            token = token,
            register = false,
            deviceId = "",
            deviceModel = Build.MODEL,
            deviceBrand = Build.MANUFACTURER,
            userId = Utils.getUserId(),
            clientId= Constant.clientID,
            clientSecret = Constant.clientSecret
        )
        val refreshTokenResponse = webviewDataSource.createNewSession(newSessionRequest)
        if (refreshTokenResponse.isSuccessful) {
            val newSession = refreshTokenResponse.body()
            EncryptedPreferencesHelper.put(BUKUWARUNG_TOKEN, newSession?.idToken)
            sharedPreferences.put(SESSION_TOKEN, newSession?.refreshToken)
            newSession?.let { TokenManager.updateTokens(it.idToken, it.refreshToken) }
            try {
                newSession?.idToken?.let { FirebaseAuth.getInstance().signInWithCustomToken(it) }
            }catch (e:Exception){
                bwLog(e = e)
            }
            return newSession?.idToken
        }
        return null
    }
}