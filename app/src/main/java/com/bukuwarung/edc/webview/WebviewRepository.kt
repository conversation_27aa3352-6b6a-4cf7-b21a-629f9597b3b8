package com.bukuwarung.edc.webview

import android.os.Build
import com.bukuwarung.edc.global.network.model.request.SessionRequest
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils.bwLog
import com.google.firebase.auth.FirebaseAuth
import javax.inject.Inject

class WebviewRepository @Inject constructor(private val webviewDataSource: WebviewDataSource) {

    suspend fun refreshToken(): String? {
        sharedPreferences.put("token_refresh_fail", false)

        val token: String? = sharedPreferences.get("session_token", "")
        if (token.isNullOrEmpty()) {
            return null
        }
        val newSessionRequest = SessionRequest(
            token = token,
            register = false,
            deviceId = "",
            deviceModel = Build.MODEL,
            deviceBrand = Build.MANUFACTURER
        )
        val refreshTokenResponse = webviewDataSource.createNewSession(newSessionRequest)
        if (refreshTokenResponse.isSuccessful) {
            val newSession = refreshTokenResponse.body()
            EncryptedPreferencesHelper.put("bukuwarung_token", newSession?.idToken)
            sharedPreferences.put("session_token", newSession?.refreshToken)
            sharedPreferences.put("session_start", System.currentTimeMillis())
            try {
                newSession?.idToken?.let { FirebaseAuth.getInstance().signInWithCustomToken(it) }
            }catch (e:Exception){
                bwLog(e = e)
            }
            return newSession?.idToken
        }
        return null
    }
}