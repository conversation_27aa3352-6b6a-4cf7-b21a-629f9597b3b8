package com.bukuwarung.edc.webview

import android.location.Address
import androidx.lifecycle.ViewModel
import androidx.work.Data
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.worker.location.LocationDto
import com.bukuwarung.edc.worker.location.LocationUtil
import com.bukuwarung.edc.worker.location.repository.LocationRepository
import com.bukuwarung.edc.worker.location.toWorkerData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.*
import javax.inject.Inject

@HiltViewModel
class WebviewViewModel @Inject constructor(
    private val locationRepository: LocationRepository,
    private val webviewUseCase: WebviewUseCase): ViewModel() {

    fun refreshToken(): String? = runBlocking {
        webviewUseCase.refreshToken()
    }

    suspend fun storeLocation(address: Address?): Pair<<PERSON><PERSON><PERSON>, Data>? {
        return withContext(Dispatchers.IO) {
            try {
                val userId = sharedPreferences.getString("phone_number", null) ?: ""
                val deviceId = sharedPreferences.get("t_id", "")
                val currentLocation = address?.let { _ ->
                    LocationDto(
                        deviceId,
                        address.latitude,
                        address.longitude,
                        address.adminArea?.takeIf { it.isNotBlank() } ?: "",
                        address.subAdminArea?.takeIf { it.isNotBlank() } ?: "",
                        address.locality?.takeIf { it.isNotBlank() } ?: "",
                        address.subLocality?.takeIf { it.isNotBlank() } ?: "",
                        address.thoroughfare?.takeIf { it.isNotBlank() } ?: "",
                        address.subThoroughfare?.takeIf { it.isNotBlank() } ?: "",
                        address.postalCode?.takeIf { it.isNotBlank() } ?: ""
                    )
                }

                val data = currentLocation?.toWorkerData()
                if (address?.maxAddressLineIndex != -1){
                    data?.putString(LocationUtil.ADDRESS_LINE, address.getAddressLine(0))
                }

                val saveLocation = locationRepository.storeLocation(userId, currentLocation!!)

                Pair(saveLocation, data!!.build())
            } catch (ex: Exception) {
                null
            }
        }
    }
}