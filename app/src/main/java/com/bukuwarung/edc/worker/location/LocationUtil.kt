package com.bukuwarung.edc.worker.location

import android.annotation.SuppressLint
import android.content.Context
import android.location.Address
import android.location.Location
import android.location.LocationManager
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.work.Constraints
import androidx.work.Data
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.bukuwarung.edc.card.data.model.TerminalLocation
import com.bukuwarung.edc.card.data.repository.TerminalManagementRepository
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.worker.location.constant.LocationConfigConstants
import com.bukuwarung.edc.worker.location.repository.LocationRepository
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.CancellationTokenSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.util.Calendar
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject


class LocationUtil @Inject constructor() {
    companion object {
        const val PROVINCE = "province"
        const val CITY = "city"
        const val DISTRICT = "district"
        const val SUB_DISTRICT = "sub_district"
        const val STREET_NAME = "street_name"
        const val STREET_NUMBER = "street_number"
        const val POSTAL_CODE = "postal_code"
        const val ADDRESS_LINE = "address_line"
        private const val LOCATION_PERIODIC_UNIQUE_WORK_NAME = "locationWork"
    }

    @Inject
    lateinit var locationRepository: LocationRepository

    @Inject
    lateinit var terminalManagementRepository: TerminalManagementRepository

    // The Fused Location Provider provides access to location APIs.
    private val cancellationTokenSource = CancellationTokenSource()

    fun isPeriodicWorkerRunning(context: Context): LiveData<List<WorkInfo>> {
        return WorkManager.getInstance(context)
            .getWorkInfosForUniqueWorkLiveData(LOCATION_PERIODIC_UNIQUE_WORK_NAME)
    }

    fun cancelPeriodicWork(context: Context) {
        WorkManager.getInstance(context).cancelUniqueWork(LOCATION_PERIODIC_UNIQUE_WORK_NAME)
    }

    fun getLocation(context: Context): LiveData<WorkInfo?>? {
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return if (locationManager.isProviderEnabled(LocationManager.PASSIVE_PROVIDER) ||
            locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER) ||
            locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        ) {
            Log.d("LocationStoreWorker", "storing location")

            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()

            // TODO fetch from remoteconfig hours and mins
            val time = LocationConfigConstants.fetchLocationSendTime()
            val hour: Int = time.hour
            val min: Int = time.minute

            val diff = findTimeDiff(hour, min)

            val periodicWorkRequest = PeriodicWorkRequestBuilder<LocationStoreWorker>(
                24, TimeUnit.HOURS
            )
                .setConstraints(constraints)
                .setInitialDelay(diff, TimeUnit.SECONDS)
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    LOCATION_PERIODIC_UNIQUE_WORK_NAME,
                    ExistingPeriodicWorkPolicy.KEEP,
                    periodicWorkRequest
                )

            WorkManager.getInstance(context).getWorkInfoByIdLiveData(periodicWorkRequest.id)
        } else {
            null
        }
    }

    @SuppressLint("MissingPermission")
    suspend fun getCurrentLocation(context: Context): Location? {
        return withContext(Dispatchers.IO) {
            val fusedLocationClient = LocationServices.getFusedLocationProviderClient(context)
            val locationTask = fusedLocationClient.getCurrentLocation(
                Priority.PRIORITY_HIGH_ACCURACY,
                cancellationTokenSource.token
            )
            locationTask.await()
        }
    }


    suspend fun storeLocation(longitude: Float, latitude: Float): Pair<Boolean, Data>? {
        return withContext(Dispatchers.IO) {
            try {
                val address = Address(Locale.getDefault())
                address.latitude = latitude.toDouble()
                address.longitude = longitude.toDouble()
                val userId = Utils.sharedPreferences.getString("phone_number", null) ?: ""
                val deviceId = Utils.sharedPreferences.get("t_id", "")
                val currentLocation = address?.let { _ ->
                    LocationDto(
                        deviceId,
                        address.latitude,
                        address.longitude,
                        address.adminArea?.takeIf { it.isNotBlank() } ?: "",
                        address.subAdminArea?.takeIf { it.isNotBlank() } ?: "",
                        address.locality?.takeIf { it.isNotBlank() } ?: "",
                        address.subLocality?.takeIf { it.isNotBlank() } ?: "",
                        address.thoroughfare?.takeIf { it.isNotBlank() } ?: "",
                        address.subThoroughfare?.takeIf { it.isNotBlank() } ?: "",
                        address.postalCode?.takeIf { it.isNotBlank() } ?: ""
                    )
                }

                val data = currentLocation?.toWorkerData()
                if (address?.maxAddressLineIndex != -1) {
                    data?.putString(LocationUtil.ADDRESS_LINE, address.getAddressLine(0))
                }

                val saveLocation = locationRepository.storeLocation(userId, currentLocation!!)
                Pair(saveLocation, data!!.build())
            } catch (ex: Exception) {
                null
            }
        }
    }

    private fun findTimeDiff(hour: Int, min: Int): Long {
        val calendar: Calendar = Calendar.getInstance()
        val nowMillis: Long = calendar.timeInMillis

        calendar.set(Calendar.HOUR_OF_DAY, hour)
        calendar.set(Calendar.MINUTE, min)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        if (calendar.before(Calendar.getInstance())) {
            calendar.add(Calendar.DATE, 1)
        }

        return calendar.timeInMillis - nowMillis
    }

    suspend fun storeCardTransactionLocation(deviceSerialNumber:String, transactionType:String, longitude: Float, latitude: Float){
        return withContext(Dispatchers.IO) {
            try {
                val address = Address(Locale.getDefault())
                address.latitude = latitude.toDouble()
                address.longitude = longitude.toDouble()
                val terminalLocation = TerminalLocation(
                    serialNumber = deviceSerialNumber,
                    operation = transactionType,
                    location = "",
                    latitude = address.latitude.toString(),
                    longitude = address.longitude.toString(),
                    deviceId = Analytics.getDeviceId(),
                    outOfRange = false
                )

                try{
                    terminalManagementRepository.updateTerminalLocation(
                        deviceSerialNumber,
                        terminalLocation
                    )
                }catch (ex: Exception) {
                    ex.printStackTrace()
                }
            } catch (ex: Exception) {
                null
            }
        }
    }
}