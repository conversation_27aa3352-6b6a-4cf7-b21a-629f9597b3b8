package com.bukuwarung.edc.worker.location.repository

import com.bukuwarung.edc.worker.location.LocationDto
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.Response

interface LocationRemoteService {

    @POST("api/v1/auth/users/{user_id}/location")
    suspend fun storeLocation(@Path("user_id") userId: String?,
                              @Header("x-app-version-name") versionName: String,
                              @Header("x-app-version-code") versionCode: Int,
                              @Body userLocationDto: LocationDto
    ): Response<Void>
}