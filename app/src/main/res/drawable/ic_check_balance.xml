<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
  <path
      android:pathData="M16,0L32,0A16,16 0,0 1,48 16L48,32A16,16 0,0 1,32 48L16,48A16,16 0,0 1,0 32L0,16A16,16 0,0 1,16 0z"
      android:fillColor="#E9F1FC"/>
  <group>
    <clip-path
        android:pathData="M9.6,9.6h28.8v28.8h-28.8z"/>
    <path
        android:pathData="M30.984,16.548C30.732,16.213 30.576,15.813 30.533,15.393C30.49,14.972 30.562,14.548 30.742,14.167L32.502,10.457C32.209,10.122 31.835,9.87 31.417,9.729C30.998,9.588 30.551,9.562 30.119,9.653C28.693,10.024 28.192,10.597 26.918,10.519C25.643,10.442 25.203,9.653 24.262,9.653C23.321,9.653 22.881,10.457 21.607,10.519C20.332,10.581 19.816,10.024 18.405,9.653C17.973,9.562 17.526,9.588 17.108,9.729C16.689,9.87 16.315,10.122 16.022,10.457L17.722,14.167C17.903,14.547 17.978,14.97 17.938,15.391C17.898,15.811 17.744,16.211 17.494,16.548C15.977,18.496 13.2,22.855 13.2,27.539C13.2,34.357 17.555,38.361 24.217,38.361C30.878,38.361 35.233,34.326 35.233,27.539C35.279,22.855 32.441,18.496 30.984,16.548Z"
        android:fillColor="#0091FF"/>
    <path
        android:pathData="M30.498,14.052L30.497,14.052C30.297,14.478 30.217,14.951 30.264,15.42C30.312,15.889 30.487,16.336 30.769,16.711L30.984,16.548L30.768,16.71C32.22,18.652 35.008,22.948 34.963,27.537V27.539C34.963,30.873 33.895,33.504 32.035,35.301C30.174,37.099 27.493,38.091 24.217,38.091C20.94,38.091 18.258,37.107 16.398,35.312C14.538,33.519 13.47,30.889 13.47,27.539C13.47,22.948 16.2,18.649 17.707,16.714L17.707,16.714L17.711,16.709C17.99,16.333 18.162,15.885 18.207,15.416C18.251,14.948 18.168,14.476 17.966,14.053L16.343,10.51C16.584,10.272 16.875,10.093 17.194,9.985C17.564,9.86 17.96,9.836 18.343,9.916C18.847,10.049 19.224,10.203 19.581,10.349C19.709,10.401 19.833,10.452 19.96,10.501C20.447,10.687 20.933,10.822 21.62,10.789C22.319,10.755 22.792,10.514 23.188,10.301C23.202,10.293 23.217,10.285 23.232,10.277C23.609,10.073 23.886,9.923 24.262,9.923C24.642,9.923 24.922,10.073 25.302,10.277C25.314,10.283 25.326,10.289 25.337,10.295C25.732,10.506 26.204,10.746 26.901,10.789C27.59,10.83 28.074,10.694 28.559,10.506C28.683,10.458 28.804,10.408 28.927,10.357C29.287,10.208 29.668,10.05 30.181,9.916C30.563,9.836 30.96,9.86 31.33,9.985C31.648,10.092 31.938,10.271 32.179,10.508L30.498,14.052Z"
        android:strokeAlpha="0.1"
        android:strokeWidth="0.54"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="13.889"
            android:startY="10.907"
            android:endX="35.234"
            android:endY="10.907"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFDB94"/>
          <item android:offset="1" android:color="#FFFE749C"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M30.498,14.052L30.497,14.052C30.297,14.478 30.217,14.951 30.264,15.42C30.312,15.889 30.487,16.336 30.769,16.711L30.984,16.548L30.768,16.71C32.22,18.652 35.008,22.948 34.963,27.537V27.539C34.963,30.873 33.895,33.504 32.035,35.301C30.174,37.099 27.493,38.091 24.217,38.091C20.94,38.091 18.258,37.107 16.398,35.312C14.538,33.519 13.47,30.889 13.47,27.539C13.47,22.948 16.2,18.649 17.707,16.714L17.707,16.714L17.711,16.709C17.99,16.333 18.162,15.885 18.207,15.416C18.251,14.948 18.168,14.476 17.966,14.053L16.343,10.51C16.584,10.272 16.875,10.093 17.194,9.985C17.564,9.86 17.96,9.836 18.343,9.916C18.847,10.049 19.224,10.203 19.581,10.349C19.709,10.401 19.833,10.452 19.96,10.501C20.447,10.687 20.933,10.822 21.62,10.789C22.319,10.755 22.792,10.514 23.188,10.301C23.202,10.293 23.217,10.285 23.232,10.277C23.609,10.073 23.886,9.923 24.262,9.923C24.642,9.923 24.922,10.073 25.302,10.277C25.314,10.283 25.326,10.289 25.337,10.295C25.732,10.506 26.204,10.746 26.901,10.789C27.59,10.83 28.074,10.694 28.559,10.506C28.683,10.458 28.804,10.408 28.927,10.357C29.287,10.208 29.668,10.05 30.181,9.916C30.563,9.836 30.96,9.86 31.33,9.985C31.648,10.092 31.938,10.271 32.179,10.508L30.498,14.052Z"
        android:strokeAlpha="0.5"
        android:strokeWidth="0.54"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="13.889"
            android:startY="10.907"
            android:endX="35.234"
            android:endY="10.907"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.4,22.259C18.4,21.894 18.735,21.6 19.15,21.6H21.025C22.888,21.6 24.4,22.928 24.4,24.565C24.4,25.775 23.575,26.815 22.391,27.276L23.596,29.92C23.751,30.257 23.563,30.64 23.179,30.776C22.794,30.912 22.358,30.747 22.204,30.41L20.891,27.529H19.9V30.165C19.9,30.529 19.565,30.823 19.15,30.823C18.735,30.823 18.4,30.529 18.4,30.165V26.871V22.259ZM19.9,26.212H21.025C22.061,26.212 22.9,25.475 22.9,24.565C22.9,23.655 22.061,22.918 21.025,22.918H19.9V26.212ZM25.9,24.235H27.775C29.226,24.235 30.4,25.267 30.4,26.541C30.4,27.816 29.226,28.847 27.775,28.847H26.65V30.823C26.65,31.188 26.315,31.482 25.9,31.482C25.485,31.482 25.15,31.188 25.15,30.823V24.894C25.15,24.53 25.485,24.235 25.9,24.235ZM27.775,27.529C28.396,27.529 28.9,27.087 28.9,26.541C28.9,25.996 28.396,25.553 27.775,25.553H26.65V27.529H27.775Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29.549"
            android:startY="23.384"
            android:endX="16.189"
            android:endY="23.913"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#33FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M22.265,27.225L22.357,27.189C23.512,26.739 24.306,25.73 24.306,24.565C24.306,22.991 22.848,21.694 21.025,21.694H19.15C18.775,21.694 18.494,21.957 18.494,22.259V26.871V30.165C18.494,30.466 18.775,30.73 19.15,30.73C19.525,30.73 19.806,30.466 19.806,30.165V27.529V27.436H19.9H20.891H20.952L20.977,27.49L22.289,30.371L22.265,27.225ZM22.265,27.225L22.306,27.315M22.265,27.225L22.306,27.315M22.306,27.315L23.511,29.959L23.511,29.959M22.306,27.315L23.511,29.959M23.511,29.959C23.638,30.237 23.49,30.567 23.147,30.688M23.511,29.959L23.147,30.688M23.147,30.688C22.802,30.81 22.421,30.658 22.289,30.371L23.147,30.688ZM19.806,26.212V26.306H19.9H21.025C22.101,26.306 22.994,25.538 22.994,24.565C22.994,23.592 22.101,22.824 21.025,22.824H19.9H19.806V22.918V26.212ZM26.65,28.753H26.556V28.847V30.823C26.556,31.125 26.275,31.389 25.9,31.389C25.525,31.389 25.244,31.125 25.244,30.823V24.894C25.244,24.593 25.525,24.329 25.9,24.329H27.775C29.186,24.329 30.306,25.33 30.306,26.541C30.306,27.753 29.186,28.753 27.775,28.753H26.65ZM26.65,25.459H26.556V25.553V27.529V27.623H26.65H27.775C28.436,27.623 28.994,27.15 28.994,26.541C28.994,25.933 28.436,25.459 27.775,25.459H26.65Z"
        android:strokeAlpha="0.5"
        android:strokeWidth="0.18761"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="19.693"
            android:startY="24.915"
            android:endX="29.882"
            android:endY="24.983"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.139,14.529h12.234v1.646h-12.234z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29.506"
            android:startY="14.826"
            android:endX="16.621"
            android:endY="17.949"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#33FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
