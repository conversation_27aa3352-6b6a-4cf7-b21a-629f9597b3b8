<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="46dp"
    android:height="45dp"
    android:viewportWidth="46"
    android:viewportHeight="45">
  <path
      android:pathData="M35.5,0H10.5C4.9771,0 0.5,4.4771 0.5,10V35C0.5,40.5228 4.9771,45 10.5,45H35.5C41.0228,45 45.5,40.5228 45.5,35V10C45.5,4.4771 41.0228,0 35.5,0Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M9.1942,17.0649C10.0194,17.0649 10.6883,16.396 10.6883,15.5708C10.6883,14.7456 10.0194,14.0767 9.1942,14.0767C8.369,14.0767 7.7001,14.7456 7.7001,15.5708C7.7001,16.396 8.369,17.0649 9.1942,17.0649Z"
      android:fillColor="#0091FF"/>
  <path
      android:pathData="M23.45,31.5004H19.85C13.01,27.5404 13.1,18.4504 14,14.4004C15.8,8.6404 21.05,8.7003 23.45,9.4503V31.5004Z"
      android:fillColor="#DBEAFF"/>
  <path
      android:pathData="M12.1825,11.0884C13.0077,11.0884 13.6766,10.4194 13.6766,9.5942C13.6766,8.769 13.0077,8.1001 12.1825,8.1001C11.3573,8.1001 10.6883,8.769 10.6883,9.5942C10.6883,10.4194 11.3573,11.0884 12.1825,11.0884Z"
      android:fillColor="#0091FF"/>
  <path
      android:pathData="M9.4333,23.8188C10.2584,23.8188 10.9274,23.1499 10.9274,22.3247C10.9274,21.4995 10.2584,20.8306 9.4333,20.8306C8.6081,20.8306 7.9391,21.4995 7.9391,22.3247C7.9391,23.1499 8.6081,23.8188 9.4333,23.8188Z"
      android:fillColor="#0091FF"/>
  <path
      android:pathData="M26.3553,34.5146C25.7269,34.3122 25.0534,34.6571 24.8507,35.2853L24.5203,36.3097H21.4201L21.0896,35.2853C20.8869,34.657 20.2134,34.3118 19.5851,34.5146C18.9568,34.7173 18.6118,35.3909 18.8144,36.0192L19.412,37.8719C19.5713,38.3657 20.0309,38.7003 20.5496,38.7003H25.3906C25.9094,38.7003 26.3689,38.3656 26.5282,37.8719L27.1259,36.0192C27.3285,35.3909 26.9835,34.7173 26.3553,34.5146Z"
      android:fillColor="#0091FF"/>
  <path
      android:pathData="M15.1648,11.1744C17.2087,9.2102 20.0236,8.12 23,8.1001V10.4907C18.0304,10.5116 14.2743,14.2045 14.2743,19.0969C14.2743,24.7397 19.2339,29.26 20.5146,30.3328H25.4313L26.5669,29.6384L28.9999,27.6384L30.3203,28.7364C28.4783,31.0447 26.6441,32.4276 26.5669,32.4853C26.3602,32.6399 26.109,32.7235 25.8509,32.7235H20.0921C19.8324,32.7235 19.5798,32.6389 19.3724,32.4825C19.2958,32.4247 17.4757,31.0414 15.6483,28.7327C13.1854,25.6212 11.8837,22.2892 11.8837,19.097C11.8837,16.0216 13.049,13.2079 15.1648,11.1744Z"
      android:fillColor="#0091FF"/>
  <path
      android:pathData="M26.6748,16.4402H31.4367H26.6748ZM26.6748,19.6152H28.6748H26.6748ZM26.6748,13.2659H31.4367H26.6748ZM23.5,10.091C23.5,9.8805 23.5836,9.6787 23.7325,9.5298C23.8813,9.381 24.0832,9.2974 24.2937,9.2974H33.8175C34.0279,9.2974 34.2298,9.381 34.3787,9.5298C34.5275,9.6787 34.6111,9.8805 34.6111,10.091V25.1704L31.8333,23.1863L29.0556,25.1704L26.2778,23.1863L23.5,25.1704V10.091Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#0091FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M32.6111,21.3435C31.9806,21.0773 31.2445,21.149 30.6709,21.5588L29.0556,22.7126L27.5193,21.6152H28.6748C29.7794,21.6152 30.6748,20.7198 30.6748,19.6152C30.6748,19.1761 30.5333,18.7701 30.2934,18.4402H31.4367C31.8755,18.4402 32.2813,18.2989 32.6111,18.0592V21.3435ZM32.6111,11.6468C32.3725,11.4734 32.0941,11.3515 31.7922,11.2974H32.6111V11.6468ZM25.5,21.3435V21.234C25.5333,21.2582 25.5674,21.2815 25.6023,21.3036C25.5679,21.316 25.5338,21.3293 25.5,21.3435ZM26.3193,11.2974C26.0172,11.3516 25.7387,11.4736 25.5,11.6471V11.2974H26.3193ZM24.2937,7.2974C23.5527,7.2974 22.8422,7.5917 22.3182,8.1156C21.7943,8.6395 21.5,9.3501 21.5,10.091V25.1704C21.5,25.9196 21.9187,26.6059 22.5848,26.9487C23.251,27.2915 24.0528,27.2333 24.6625,26.7978L26.2778,25.6441L27.8931,26.7978C28.5885,27.2946 29.5226,27.2946 30.218,26.7978L31.8333,25.6441L33.4486,26.7978C34.0583,27.2333 34.8601,27.2915 35.5263,26.9487C36.1924,26.6059 36.6111,25.9196 36.6111,25.1704V10.091C36.6111,9.3501 36.3168,8.6395 35.7929,8.1156C35.269,7.5917 34.5584,7.2974 33.8175,7.2974H24.2937Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
