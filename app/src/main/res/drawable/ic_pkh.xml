<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
  <path
      android:pathData="M16,0L32,0A16,16 0,0 1,48 16L48,32A16,16 0,0 1,32 48L16,48A16,16 0,0 1,0 32L0,16A16,16 0,0 1,16 0z"
      android:fillColor="#E6F7F3"/>
  <path
      android:pathData="M21.333,34.667H26.667C31.695,34.667 34.209,34.667 35.771,33.104C37.332,31.541 37.333,29.028 37.333,24C37.333,23.411 37.331,21.856 37.328,21.333H10.667C10.664,21.856 10.667,23.411 10.667,24C10.667,29.028 10.667,31.543 12.228,33.104C13.789,34.665 16.307,34.667 21.333,34.667Z"
      android:strokeAlpha="0.5"
      android:fillColor="#10B18D"
      android:fillAlpha="0.5"/>
  <path
      android:strokeWidth="1"
      android:pathData="M37.828,21.331L37.826,20.833H37.328H10.667H10.169L10.167,21.331C10.165,21.703 10.166,22.597 10.166,23.294C10.167,23.579 10.167,23.83 10.167,24L10.167,24.039C10.167,26.52 10.167,28.433 10.366,29.919C10.569,31.426 10.985,32.569 11.875,33.458C12.764,34.347 13.906,34.764 15.415,34.966C16.903,35.167 18.82,35.167 21.306,35.167H21.333H26.667H26.706C29.187,35.167 31.1,35.167 32.585,34.967C34.093,34.764 35.235,34.347 36.124,33.457C37.013,32.568 37.43,31.426 37.633,29.918C37.833,28.43 37.833,26.514 37.833,24.027V24C37.833,23.41 37.831,21.855 37.828,21.331Z"
      android:strokeAlpha="0.4"
      android:fillColor="#00000000"
      android:fillAlpha="0.5">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="13.538"
          android:startY="25.806"
          android:endX="36.181"
          android:endY="26.056"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M21.326,13.333H26.673C31.715,13.333 34.236,13.333 35.801,14.821C36.929,15.892 37.245,17.435 37.333,20V21.333H10.667V20C10.755,17.433 11.071,15.893 12.198,14.821C13.764,13.333 16.285,13.333 21.326,13.333ZM24.667,28.333C24.401,28.333 24.147,28.439 23.959,28.626C23.772,28.814 23.667,29.068 23.667,29.333C23.667,29.599 23.772,29.853 23.959,30.041C24.147,30.228 24.401,30.333 24.667,30.333H26.667C26.932,30.333 27.186,30.228 27.374,30.041C27.561,29.853 27.667,29.599 27.667,29.333C27.667,29.068 27.561,28.814 27.374,28.626C27.186,28.439 26.932,28.333 26.667,28.333H24.667ZM16,28.333C15.735,28.333 15.48,28.439 15.293,28.626C15.105,28.814 15,29.068 15,29.333C15,29.599 15.105,29.853 15.293,30.041C15.48,30.228 15.735,30.333 16,30.333H21.333C21.598,30.333 21.853,30.228 22.04,30.041C22.228,29.853 22.333,29.599 22.333,29.333C22.333,29.068 22.228,28.814 22.04,28.626C21.853,28.439 21.598,28.333 21.333,28.333H16Z"
      android:fillColor="#10B18D"/>
  <path
      android:strokeWidth="1"
      android:pathData="M37.833,19.992V20V21.333V21.833H37.333H10.667H10.167V21.333V20V19.992L10.167,19.983C10.255,17.417 10.566,15.683 11.854,14.459L37.833,19.992ZM37.833,19.992L37.833,19.983M37.833,19.992L37.833,19.983M37.833,19.983C37.745,17.418 37.434,15.682 36.145,14.459C35.254,13.611 34.111,13.216 32.604,13.023C31.116,12.833 29.2,12.833 26.711,12.833H26.673H21.326M37.833,19.983L21.326,12.833M21.326,12.833H21.289M21.326,12.833H21.289M21.289,12.833C18.8,12.833 16.883,12.833 15.396,13.023M21.289,12.833L15.396,13.023M15.396,13.023C13.889,13.216 12.746,13.611 11.854,14.459L15.396,13.023ZM24.667,27.833C24.269,27.833 23.887,27.991 23.606,28.273C23.324,28.554 23.167,28.935 23.167,29.333C23.167,29.731 23.324,30.113 23.606,30.394C23.887,30.675 24.269,30.833 24.667,30.833H26.667C27.064,30.833 27.446,30.675 27.727,30.394C28.008,30.113 28.167,29.731 28.167,29.333C28.167,28.936 28.008,28.554 27.727,28.273C27.446,27.991 27.064,27.833 26.667,27.833H24.667ZM16,27.833C15.602,27.833 15.22,27.991 14.939,28.273C14.658,28.554 14.5,28.935 14.5,29.333C14.5,29.731 14.658,30.113 14.939,30.394C15.22,30.675 15.602,30.833 16,30.833H21.333C21.731,30.833 22.112,30.675 22.394,30.394C22.675,30.113 22.833,29.731 22.833,29.333C22.833,28.935 22.675,28.554 22.394,28.273C22.112,27.991 21.731,27.833 21.333,27.833H16Z"
      android:strokeAlpha="0.8"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="13.539"
          android:startY="19.036"
          android:endX="36.182"
          android:endY="19.232"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
