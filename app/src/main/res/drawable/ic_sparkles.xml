<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M5,3V7V3ZM3,5H7H3ZM6,17V21V17ZM4,19H8H4ZM13,3L15.286,9.857L21,12L15.286,14.143L13,21L10.714,14.143L5,12L10.714,9.857L13,3Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="3.003"
          android:startY="11.998"
          android:endX="21.001"
          android:endY="11.998"
          android:type="linear">
        <item android:offset="0.035" android:color="#FF1B74CC"/>
        <item android:offset="0.913" android:color="#FF0091FF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
