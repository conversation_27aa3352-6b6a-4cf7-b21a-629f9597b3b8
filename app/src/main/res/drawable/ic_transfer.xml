<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
  <path
      android:pathData="M16,0L32,0A16,16 0,0 1,48 16L48,32A16,16 0,0 1,32 48L16,48A16,16 0,0 1,0 32L0,16A16,16 0,0 1,16 0z"
      android:fillColor="#FDEECE"/>
  <path
      android:pathData="M23.852,38.305C31.723,38.305 38.104,31.879 38.104,23.953C38.104,16.026 31.723,9.6 23.852,9.6C15.981,9.6 9.6,16.026 9.6,23.953C9.6,31.879 15.981,38.305 23.852,38.305Z"
      android:fillColor="#FFA800"/>
  <path
      android:pathData="M37.904,23.953C37.904,31.77 31.611,38.105 23.852,38.105C16.093,38.105 9.8,31.77 9.8,23.953C9.8,16.135 16.093,9.8 23.852,9.8C31.611,9.8 37.904,16.135 37.904,23.953Z"
      android:strokeAlpha="0.5"
      android:strokeWidth="0.4"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="10.491"
          android:startY="10.905"
          android:endX="38.104"
          android:endY="10.905"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M30.52,19.175L26.983,16.189C26.875,16.092 26.742,16.029 26.599,16.008C26.457,15.987 26.311,16.008 26.18,16.069C26.05,16.131 25.94,16.23 25.864,16.353C25.789,16.477 25.751,16.621 25.756,16.766V18.64H17.904C17.611,18.64 17.33,18.757 17.123,18.965C16.916,19.174 16.8,19.457 16.8,19.751C16.8,20.048 16.916,20.333 17.122,20.545C17.329,20.757 17.61,20.879 17.904,20.884H25.756V22.758C25.751,22.903 25.789,23.046 25.864,23.17C25.94,23.294 26.05,23.393 26.18,23.454C26.311,23.515 26.457,23.537 26.599,23.516C26.742,23.494 26.875,23.431 26.983,23.334L30.52,20.349C30.606,20.277 30.675,20.188 30.722,20.086C30.77,19.985 30.794,19.874 30.794,19.762C30.794,19.65 30.77,19.539 30.722,19.438C30.675,19.336 30.606,19.246 30.52,19.175Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.802"
          android:startY="17.359"
          android:endX="14.255"
          android:endY="18.302"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M30.444,19.265L30.444,19.266C30.517,19.326 30.575,19.402 30.615,19.487C30.655,19.573 30.676,19.667 30.676,19.762C30.676,19.857 30.655,19.95 30.615,20.036C30.575,20.122 30.517,20.198 30.444,20.258L30.444,20.258L26.907,23.244L26.907,23.244L26.904,23.247C26.813,23.328 26.702,23.381 26.582,23.399C26.462,23.417 26.34,23.399 26.231,23.347C26.121,23.296 26.029,23.213 25.965,23.109C25.902,23.005 25.87,22.884 25.874,22.762L25.874,22.762V22.758V20.884V20.766H25.756H17.905C17.642,20.761 17.391,20.652 17.207,20.463C17.022,20.273 16.918,20.018 16.918,19.752V19.751C16.918,19.488 17.022,19.235 17.207,19.049C17.392,18.862 17.643,18.758 17.904,18.758H25.756H25.874V18.64V16.766H25.874L25.874,16.762C25.87,16.64 25.902,16.519 25.965,16.415C26.029,16.311 26.121,16.228 26.231,16.176C26.34,16.125 26.462,16.107 26.582,16.125C26.702,16.143 26.813,16.195 26.904,16.277L26.904,16.277L26.907,16.279L30.444,19.265Z"
      android:strokeAlpha="0.5"
      android:strokeWidth="0.236249"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="18.308"
          android:startY="18.524"
          android:endX="30.19"
          android:endY="18.646"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.441,29.292L20.897,32.402C21.008,32.491 21.142,32.547 21.284,32.562C21.425,32.578 21.568,32.554 21.697,32.491C21.825,32.429 21.933,32.331 22.008,32.21C22.084,32.089 22.124,31.948 22.124,31.805V29.951H29.976C30.27,29.946 30.551,29.824 30.758,29.613C30.964,29.401 31.08,29.116 31.08,28.819C31.08,28.524 30.963,28.241 30.756,28.033C30.549,27.824 30.268,27.707 29.976,27.707H22.206V25.833C22.21,25.688 22.173,25.545 22.097,25.421C22.022,25.297 21.912,25.198 21.781,25.137C21.65,25.076 21.505,25.054 21.362,25.075C21.219,25.097 21.086,25.16 20.979,25.257L17.441,28.242C17.373,28.311 17.318,28.393 17.281,28.483C17.244,28.573 17.225,28.67 17.225,28.767C17.225,28.865 17.244,28.962 17.281,29.052C17.318,29.142 17.373,29.224 17.441,29.292Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="30.097"
          android:startY="26.422"
          android:endX="14.704"
          android:endY="27.349"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M22.006,31.805V31.805C22.006,31.926 21.972,32.045 21.908,32.147C21.844,32.25 21.753,32.332 21.645,32.385C21.537,32.438 21.416,32.458 21.297,32.445C21.179,32.432 21.067,32.386 20.973,32.312L17.523,29.207C17.466,29.15 17.421,29.081 17.39,29.007C17.359,28.931 17.343,28.85 17.343,28.767C17.343,28.685 17.359,28.604 17.39,28.528C17.421,28.454 17.466,28.386 17.522,28.329L21.055,25.347L21.055,25.347L21.058,25.344C21.148,25.263 21.26,25.21 21.379,25.192C21.499,25.174 21.621,25.192 21.731,25.244C21.84,25.295 21.933,25.378 21.996,25.482C22.06,25.586 22.091,25.707 22.087,25.829L22.087,25.829V25.833V27.707V27.825H22.206H29.976C30.237,27.825 30.487,27.93 30.673,28.116C30.858,28.302 30.962,28.555 30.962,28.819V28.819C30.962,29.085 30.858,29.34 30.673,29.53C30.488,29.719 30.237,29.828 29.974,29.833H22.124H22.006V29.951V31.805Z"
      android:strokeAlpha="0.5"
      android:strokeWidth="0.236249"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="18.717"
          android:startY="27.583"
          android:endX="30.481"
          android:endY="27.703"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
