<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M16,0.356L16,0.356A15.644,15.644 0,0 1,31.644 16L31.644,16A15.644,15.644 0,0 1,16 31.644L16,31.644A15.644,15.644 0,0 1,0.356 16L0.356,16A15.644,15.644 0,0 1,16 0.356z"
      android:strokeWidth="0.711111"
      android:fillColor="#E5F4FF"
      android:strokeColor="#E5F4FF"/>
  <path
      android:pathData="M11.22,11.515C11.22,10.885 11.73,10.375 12.36,10.375H20.307C20.937,10.375 21.448,10.885 21.448,11.515V13.262C21.448,13.892 20.937,14.403 20.307,14.403H12.36C11.73,14.403 11.22,13.892 11.22,13.262V11.515Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.968478"
      android:fillColor="#B3DEFF"
      android:strokeColor="#0091FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13.24,19.495C13.978,19.495 14.576,18.915 14.576,18.199C14.576,17.483 13.978,16.903 13.24,16.903C12.502,16.903 11.903,17.483 11.903,18.199C11.903,18.915 12.502,19.495 13.24,19.495Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.968478"
      android:fillColor="#B3DEFF"
      android:strokeColor="#0091FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M19.447,19.495C20.185,19.495 20.783,18.915 20.783,18.199C20.783,17.483 20.185,16.903 19.447,16.903C18.709,16.903 18.11,17.483 18.11,18.199C18.11,18.915 18.709,19.495 19.447,19.495Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.968478"
      android:fillColor="#B3DEFF"
      android:strokeColor="#0091FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M15.461,8.019H17.207M13.598,21.856L9.804,26M22,25.057H11M20.53,23.451H12.138M19.07,21.856L22.864,26M20.092,7.042H19.201L19.033,6.63C18.957,6.444 18.825,6.285 18.655,6.172C18.484,6.06 18.283,6 18.077,6H14.59C14.384,6 14.183,6.06 14.013,6.172C13.842,6.285 13.711,6.444 13.635,6.63L13.467,7.042H12.575C12.106,7.041 11.641,7.131 11.207,7.305C10.773,7.48 10.379,7.735 10.047,8.057C9.715,8.379 9.452,8.762 9.272,9.182C9.092,9.603 9,10.054 9,10.51V18.812C9,19.62 9.331,20.394 9.919,20.965C10.507,21.536 11.306,21.856 12.138,21.856H20.529C21.361,21.856 22.159,21.536 22.748,20.965C23.336,20.394 23.667,19.62 23.667,18.812V10.51C23.667,10.054 23.574,9.603 23.395,9.182C23.215,8.762 22.951,8.379 22.619,8.057C22.287,7.735 21.893,7.48 21.459,7.305C21.026,7.131 20.561,7.041 20.091,7.042L20.092,7.042Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.14049"
      android:fillColor="#00000000"
      android:strokeColor="#0091FF"
      android:strokeLineCap="round"/>
</vector>
