<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_back"
        app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/toolBarLabel"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:paddingEnd="16dp"
                android:text="@string/beneficiary_account"
                android:textColor="#ffffff"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/toolBarMenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_baseline_help_outline_white" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="@dimen/_0dp"
        android:layout_marginBottom="@dimen/_20dp"
        app:layout_constraintBottom_toTopOf="@+id/button_save_account"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/benefit_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/banner_benefit_bank_account" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/tutor_input_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/benefit_layout">

                <TextView
                    android:id="@+id/txt_label_bank"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="end"
                    android:lines="1"
                    android:text="@string/bank"
                    android:textColor="@color/body_text"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/button_select_bank"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:background="?attr/selectableItemBackground"
                    app:cardBackgroundColor="@color/white"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_label_bank"
                    app:strokeColor="@color/black_40"
                    app:strokeWidth="1dp">

                    <TextView
                        android:id="@+id/txt_bank_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/roboto"
                        android:hint="@string/select_bank"
                        android:paddingStart="12dp"
                        android:paddingTop="10dp"
                        android:paddingEnd="16dp"
                        android:paddingBottom="16dp"
                        android:textColor="@color/heading_text"
                        android:textColorHint="@color/color_hint"
                        android:textSize="16sp"
                        app:drawableEndCompat="@drawable/ic_chevron_down" />

                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/txt_error_no_bank"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:fontFamily="@font/roboto"
                    android:lineSpacingExtra="3dp"
                    android:text="Rekening tidak ditemukan"
                    android:textColor="@color/red_error"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/button_select_bank" />


                <TextView
                    android:id="@+id/txt_label_account_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="end"
                    android:lines="1"
                    android:text="@string/account_number"
                    android:textColor="@color/body_text"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_error_no_bank" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_input_account_number"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:background="@color/white"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="1dp"
                    app:cardElevation="0dp"
                    app:hintEnabled="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/button_verify"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_label_account_number"
                    app:passwordToggleDrawable="@null">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/input_account_number"
                        style="@style/Body1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="*********"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:singleLine="true"
                        android:textColorHint="@color/hint_color" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_verify"
                    style="@style/ButtonFill.Blue80"
                    android:layout_width="wrap_content"
                    android:layout_height="64dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:enabled="false"
                    android:fontFamily="@font/roboto"
                    android:letterSpacing="0"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/verify"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/layout_input_account_number"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/layout_input_account_number" />

                <ProgressBar
                    android:id="@+id/progress_verification"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/button_verify"
                    app:layout_constraintEnd_toEndOf="@id/button_verify"
                    app:layout_constraintStart_toStartOf="@id/button_verify"
                    app:layout_constraintTop_toTopOf="@id/button_verify" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/txt_error_account"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="3dp"
                android:textColor="@color/red_error"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="Rekening tidak ditemukan"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tutor_input_container" />

            <com.bukuwarung.edc.payments.ui.widgets.BankAccountView
                android:id="@+id/bank_account_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/txt_error_account" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_save_account"
        style="@style/ButtonFill.Yellow"
        android:layout_width="0dp"
        android:layout_height="54dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:enabled="false"
        android:fontFamily="@font/roboto"
        android:letterSpacing="0.03"
        android:padding="8dp"
        android:text="@string/save"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_bias="1" />

    <ProgressBar
        android:id="@+id/progress_verification_add"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginBottom="@dimen/_24dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>