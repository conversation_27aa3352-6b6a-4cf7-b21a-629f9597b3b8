<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/toolbar"
        layout="@layout/widget_toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceListHeader"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:text="@string/your_edc_devices"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <ImageView
        android:id="@+id/ivEmptyView"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:src="@drawable/ic_empty_list"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDeviceListHeader" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDeviceList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:barrierMargin="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDeviceListHeader"
        tools:listitem="@layout/list_item_devices" />


</androidx.constraintlayout.widget.ConstraintLayout>