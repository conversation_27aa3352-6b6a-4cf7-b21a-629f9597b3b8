<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@color/white">

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="60dp"
        android:layout_height="60dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_logon"
        android:indeterminate="true"
        android:layout_marginBottom="@dimen/_24dp"
        android:indeterminateTintMode="src_atop"
        android:indeterminateTint="@color/colorPrimary"
        />

    <TextView
        android:id="@+id/tv_logon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:text="@string/connecting_bt_card_reader"
        android:textColor="@color/black_60"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/_16dp"
        android:textAlignment="center"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_connection_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_8dp"
        android:text="Jangan matikan perangkat bluetooth atau menutup aplikasi. Proses ini memakan waktu hingga 1 menit."
        android:textColor="@color/black_60"
        app:layout_constraintTop_toBottomOf="@id/tv_logon"
        android:textAlignment="center"
        android:textSize="14sp"
         />



    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/firmware_upgrade"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progress_bar"
        android:layout_marginTop="@dimen/_16dp"
        android:paddingHorizontal="16dp">

        <TextView
            android:id="@+id/progressPercentage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textColor="@color/black_60"
            android:textAppearance="?android:textAppearanceLarge"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/statusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/black_80"
            android:text="Sistem Sedang Diperbarui"
            android:textAppearance="?android:textAppearanceMedium"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/progressPercentage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/instructionText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Jangan matikan perangkat bluetooth atau menutup aplikasi. Proses ini memakan waktu hingga 1 menit."
            android:textAppearance="?android:textAppearanceSmall"
            android:textAlignment="center"
            app:layout_constraintTop_toBottomOf="@id/statusText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>