<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_5">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:background="@color/colorPrimary"
            app:theme="@style/ToolbarTheme"
            app:title="@string/payment_confirmation">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/include_payment_method"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:id="@+id/vw_gradient"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:background="@drawable/payment_tab_gradient_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.bukuwarung.edc.payments.ui.widgets.PaymentDetailView
                android:id="@+id/pdv_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_warning"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@drawable/bg_solid_yellow5_stroke_yellow40_corder_10dp"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:visibility="gone"
                app:drawableStartCompat="@drawable/ic_alert_warning"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/pdv_view"
                tools:text="Transaksi pengiriman uang yang dilakukan di luar jam 07.00 – 23.00 WIB, akan diproses esok hari."
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_layout_input_note"
                style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:background="@drawable/bg_solid_white_corner_8dp"
                app:boxBackgroundMode="none"
                app:hintEnabled="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_warning"
                app:startIconDrawable="@drawable/ic_edit_square"
                app:startIconTint="@color/black_40">

                <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                    android:id="@+id/et_input_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:completionThreshold="1"
                    android:fontFamily="@font/roboto"
                    android:hint="@string/add_note_optional"
                    android:imeOptions="actionDone"
                    android:inputType="textAutoComplete|textAutoCorrect"
                    android:lines="2"
                    android:minHeight="?attr/actionBarSize"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_0dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:paddingBottom="@dimen/_0dp"
                    android:singleLine="true"
                    android:textColor="@color/black_80"
                    android:textColorHint="@color/hint_color"
                    android:textSize="14sp" />

            </com.google.android.material.textfield.TextInputLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <include
        android:id="@+id/include_payment_method"
        layout="@layout/layout_order_form_payment_method"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <include
        android:id="@+id/include_payment_loading"
        layout="@layout/payment_loading_screen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone" />

    <LinearLayout
        android:id="@+id/llNext"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnNext"
            style="@style/ButtonFill.Yellow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:text="@string/next" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>