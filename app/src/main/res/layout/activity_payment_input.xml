<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_5">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:background="@color/colorPrimary"
            app:theme="@style/ToolbarTheme"
            app:title="@string/pay" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/cl_buttons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_destination_bank_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_solid_grey_border_8dp"
                android:backgroundTint="@color/white"
                android:paddingBottom="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_destination_bank_account"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="12dp"
                    android:text="@string/destination_account"
                    android:textColor="@color/black40"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.bukuwarung.edc.payments.ui.widgets.BankAccountView
                    android:id="@+id/bank_account_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="4dp"
                    android:layout_marginTop="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_destination_bank_account" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_amount_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@drawable/bg_solid_grey_border_8dp"
                android:backgroundTint="@color/white"
                android:paddingHorizontal="12dp"
                android:paddingBottom="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_destination_bank_account">

                <TextView
                    android:id="@+id/tv_amount"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="@string/amount_of_money"
                    android:textColor="@color/black40"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.bukuwarung.edc.payments.ui.widgets.CurrencyEditText
                    android:id="@+id/et_amount"
                    style="@style/Heading1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@color/white"
                    android:gravity="end"
                    android:hint="@string/zero_amount"
                    android:imeOptions="actionDone"
                    android:inputType="number"
                    android:paddingTop="@dimen/_6dp"
                    android:paddingBottom="@dimen/_6dp"
                    android:singleLine="true"
                    android:text="@string/currency"
                    app:drawableLeftCompat="@drawable/ic_arrow_blue_bg_top_right"
                    app:layout_constraintTop_toBottomOf="@id/tv_amount" />

                <View
                    android:id="@+id/vw_divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_10"
                    app:layout_constraintTop_toBottomOf="@id/et_amount" />

                <TextView
                    android:id="@+id/tv_amount_error"
                    style="@style/Label1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:gravity="end"
                    android:textColor="@color/red_80"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/vw_divider"
                    tools:text="Minimum payment limit" />

                <TextView
                    android:id="@+id/tv_saldo_balance_label"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="@string/saldo_balance"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/tv_saldo_balance_value"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_amount_error" />

                <TextView
                    android:id="@+id/tv_saldo_balance_value"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_saldo_balance_label"
                    app:layout_constraintTop_toBottomOf="@id/vw_divider"
                    app:layout_constraintTop_toTopOf="@id/tv_saldo_balance_label"
                    tools:text="Rp1.000.000" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/include_payment_categories"
                layout="@layout/layout_payment_categories"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_amount_input" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_continue"
            style="@style/ButtonFill.Yellow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/next"
            app:cornerRadius="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:rippleColor="@color/black_40" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>