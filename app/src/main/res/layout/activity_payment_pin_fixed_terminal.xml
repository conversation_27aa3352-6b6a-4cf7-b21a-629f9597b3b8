<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme" />

    <ImageView
        android:id="@+id/iv_lock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20dp"
        android:src="@drawable/ic_lock_120dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20dp"
        android:textAlignment="center"
        app:layout_constraintTop_toBottomOf="@+id/iv_lock" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20dp"
        android:text="@string/use_bukuagen_for_pin_change"
        android:textAlignment="center"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_back"
        style="@style/ButtonOutline.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20dp"
        android:text="@string/back"
        android:textAllCaps="false"
        app:cornerRadius="@dimen/_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
        app:layout_constraintWidth_percent="0.45" />

</androidx.constraintlayout.widget.ConstraintLayout>