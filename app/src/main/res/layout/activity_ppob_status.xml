<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/payment_tab_gradient_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_close"
        app:srcCompat="@drawable/ic_cross_black_80"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/tv_close"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/label_close"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_close"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/pb_loader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_close" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_32dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_close">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:background="@drawable/bg_solid_blue80_corner_12dp">

            <TextView
                android:id="@+id/tv_fast_ppob"
                style="@style/Body3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:text="@string/payment_completed_in"
                android:textAlignment="textEnd"
                android:textColor="@color/white"
                android:visibility="gone"
                app:layout_constraintEnd_toStartOf="@+id/tv_ppob_completion_time"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_ppob_completion_time"
                style="@style/SubHeading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4dp"
                android:drawablePadding="@dimen/_4dp"
                android:textColor="@color/white"
                android:visibility="gone"
                app:drawableStartCompat="@drawable/ic_lightning_bolt"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tv_fast_ppob"
                app:layout_constraintTop_toTopOf="@+id/tv_fast_ppob"
                tools:text="1.6 Detik" />

            <View
                android:id="@+id/vw_reference"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/tv_fast_ppob" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_payment_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_rounded_rectangle_white_16dp"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="@+id/vw_reference">

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lav_animation"
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:lottie_autoPlay="true" />

                <TextView
                    android:id="@+id/tv_ppob_status"
                    style="@style/Heading2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:textAlignment="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/lav_animation"
                    tools:text="Pembayaran Gagal" />

                <TextView
                    android:id="@+id/tv_ppob_status_subtitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/payment_out_status_failed_subtitle"
                    android:textAlignment="center"
                    android:textColor="@color/black40"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_ppob_status" />

                <TextView
                    android:id="@+id/tv_total_payment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/label_total_payment"
                    android:textAlignment="center"
                    android:textColor="@color/black40"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_ppob_status_subtitle" />

                <TextView
                    android:id="@+id/tv_total_payment_amount"
                    style="@style/Heading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:textAlignment="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_total_payment"
                    tools:text="Rp1.002.000" />

                <View
                    android:id="@+id/vw_dotted_line1"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/horizontal_dotted_line"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_total_payment_amount" />

                <TextView
                    android:id="@+id/tv_category"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/payment_out_destination_account"
                    android:textColor="@color/black40"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/vw_dotted_line1" />

                <include
                    android:id="@+id/include_biller_info"
                    layout="@layout/layout_biller_info2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_category" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_view_ppob_details"
                    style="@style/ButtonFill.Yellow"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/include_biller_info" />

                <View
                    android:id="@+id/vw_dotted_line2"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/horizontal_dotted_line"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/btn_view_ppob_details" />

                <include
                    android:id="@+id/include_payment_receipt"
                    layout="@layout/layout_payment_receipt"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_dotted_line2" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/vw_scrolling"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@+id/cl_payment_details"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>