<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_bank_account_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_verified_icon"
        android:layout_width="24dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="24dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_bank_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_bank_info"
        app:srcCompat="@drawable/ic_check_circle_60" />

    <TextView
        android:id="@+id/tv_bank_info"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14dp"
        android:text="@string/account_found"
        android:visibility="gone"
        tools:visibility="visible"
        android:textColor="@color/blue_60"
        app:layout_constraintEnd_toStartOf="@id/tv_bank_change"
        app:layout_constraintStart_toEndOf="@id/iv_verified_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="5dp" />

    <TextView
        android:id="@+id/tv_bank_change"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/edit_txt"
        tools:visibility="visible"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="5dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bank_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        android:background="@drawable/bg_solid_white_corner_8dp_stroke_black10"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_bank_change">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_bank_logo"
            android:layout_width="56dp"
            android:layout_height="44dp"
            android:background="@drawable/bg_solid_white_stroke_grey_border_4dp"
            android:padding="1.5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_bank" />

        <TextView
            android:id="@+id/tv_bank_holder_name"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintBottom_toTopOf="@+id/tv_bank_account_number"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_bank_logo"
            app:layout_constraintTop_toTopOf="@id/iv_bank_logo"
            tools:text="Dea Clarissa Safitri" />

        <TextView
            android:id="@+id/tv_bank_account_number"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:layout_marginTop="@dimen/_2dp"
            android:textColor="@color/black40"
            app:layout_constraintBottom_toBottomOf="@id/iv_bank_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_bank_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_bank_holder_name"
            tools:text="Mandiri - 3883134xxxx" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_bank_error"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@color/red_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_bank_details"
        tools:text="Demi keamanan pembayaran, rekening ini kami kunci. Silakan pilih rekening yang lain." />

    <TextView
        android:id="@+id/tv_bank_error_with_bg"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@drawable/bg_solid_red5_corner_8dp_stroke_red40"
        android:paddingHorizontal="@dimen/_12dp"
        android:paddingVertical="@dimen/_8dp"
        android:textColor="@color/red_60"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_bank_error"
        tools:text="Demi keamanan pembayaran, rekening ini kami kunci. Silakan pilih rekening yang lain. Cari tahu lebih lanjut." />

    <TextView
        android:id="@+id/tv_bank_info_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_bank_details"
        tools:text="Demi keamanan pembayaran, rekening ini kami kunci. Silakan pilih rekening yang lain. Cari tahu lebih lanjut." />

</androidx.constraintlayout.widget.ConstraintLayout>