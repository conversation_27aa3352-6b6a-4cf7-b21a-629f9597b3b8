<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_filters_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="16dp"
        android:text="@string/select_date"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:padding="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        app:srcCompat="@drawable/ic_close"
        app:tint="@color/black_80" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_date_filters"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/btn_confirm_filter"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:itemCount="4"
        tools:listitem="@layout/item_date_filter" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_confirm_filter"
        style="@style/ButtonFill.Yellow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="16dp"
        android:text="@string/confirm_label"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
