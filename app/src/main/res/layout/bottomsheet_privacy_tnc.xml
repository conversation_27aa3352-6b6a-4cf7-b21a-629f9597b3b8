<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintTop_toTopOf="parent" >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_tnc"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            style="@style/Heading2"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text = "Terms and Conditions"/>

        <WebView
            android:id="@+id/wv_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constrainedHeight="true"
            android:layout_marginVertical="24dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintBottom_toTopOf="@id/btn_okay">

        </WebView>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_okay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/ButtonFill"
            android:text="@string/ok_button"
            android:layout_margin="24dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
