<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@color/black_5"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:orientation="horizontal"
                android:layout_marginBottom="1dp"
                android:layout_marginTop="@dimen/_8dp"
                >

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn1"
                    android:layout_width="fill_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center_horizontal|center_vertical"
                    android:textColor="@color/black_60"
                    android:layout_marginRight="1dp"
                    android:text="1"
                    android:textSize="24sp" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn2"
                    android:layout_width="fill_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center_horizontal|center_vertical"
                    android:layout_marginRight="1dp"
                    android:textColor="@color/black_60"
                    android:text="2"
                    android:textSize="24sp" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn3"
                    android:layout_width="fill_parent"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center_horizontal|center_vertical"
                    android:textColor="@color/black_60"
                    android:layout_marginRight="1dp"
                    android:text="3"
                    android:textSize="24sp" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn_cancel"
                    android:layout_width="fill_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@color/red"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="X"
                    android:textColor="@color/white"
                    android:textSize="24sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:orientation="horizontal"
                android:layout_marginBottom="1dp">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn4"
                    android:layout_width="fill_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center_horizontal|center_vertical"
                    android:textColor="@color/black_60"
                    android:layout_marginRight="1dp"
                    android:text="4"
                    android:textSize="24sp" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn5"
                    android:layout_width="fill_parent"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center_horizontal|center_vertical"
                    android:textColor="@color/black_60"
                    android:layout_marginRight="1dp"
                    android:text="5"
                    android:textSize="24sp" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn6"
                    android:layout_width="fill_parent"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center_horizontal|center_vertical"
                    android:textColor="@color/black_60"
                    android:layout_marginRight="1dp"
                    android:text="6"
                    android:textSize="24sp" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn_delete"
                    android:layout_width="fill_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/ic_keyboard_back"
                    android:gravity="center_horizontal|center_vertical"
                    android:text=""
                    android:textColor="@color/black_60"
                    android:textSize="24sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!--<androidx.appcompat.widget.AppCompatButton-->
                <!--android:id="@+id/keyboard_btn_delete"-->
                <!--android:layout_width="fill_parent"-->
                <!--android:layout_height="140dp"-->
                <!--android:layout_weight="1"-->
                <!--android:background="@color/black_pearl"-->
                <!--android:gravity="center_horizontal|center_vertical"-->
                <!--android:text="Backspace"-->
                <!--android:textColor="@color/black_60"-->
                <!--android:textSize="18sp" />-->

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="1dp"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/keyboard_btn7"
                        android:layout_width="match_parent"
                        android:layout_height="65dp"
                        android:layout_weight="1"
                        android:background="@color/white"
                        android:gravity="center_horizontal|center_vertical"
                        android:textColor="@color/black_60"
                        android:layout_marginBottom="1dp"
                        android:text="7"
                        android:textSize="24sp" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btn_dummy_left"
                        android:layout_width="fill_parent"
                        android:layout_height="65dp"
                        android:layout_weight="1"
                        android:background="@color/white"
                        android:gravity="center_horizontal|center_vertical"
                        android:textColor="@color/black_60"
                        android:textSize="24sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="1dp"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/keyboard_btn8"
                        android:layout_width="fill_parent"
                        android:layout_height="65dp"
                        android:layout_weight="1"
                        android:text="8"
                        android:background="@color/white"
                        android:gravity="center_horizontal|center_vertical"
                        android:textColor="@color/black_60"
                        android:layout_marginBottom="1dp"
                        android:textSize="24sp" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/keyboard_btn0"
                        android:layout_width="fill_parent"
                        android:layout_height="65dp"
                        android:layout_weight="1"
                        android:background="@color/white"
                        android:gravity="center_horizontal|center_vertical"
                        android:textColor="@color/black_60"
                        android:text="0"
                        android:textSize="24sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="1dp"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/keyboard_btn9"
                        android:layout_width="fill_parent"
                        android:layout_height="65dp"
                        android:layout_weight="1"
                        android:background="@color/white"
                        android:gravity="center_horizontal|center_vertical"
                        android:textColor="@color/black_60"
                        android:layout_marginBottom="1dp"
                        android:text="9"
                        android:textSize="24sp" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btn_dummy_right"
                        android:layout_width="fill_parent"
                        android:layout_height="65dp"
                        android:layout_weight="1"
                        android:background="@color/white"
                        android:gravity="center_horizontal|center_vertical"
                        android:textColor="@color/black_60"
                        android:textSize="24sp" />
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/keyboard_btn_confirm"
                    android:layout_width="fill_parent"
                    android:layout_height="130dp"
                    android:layout_weight="1"
                    android:background="@drawable/ic_enter_keyboard"
                    android:gravity="center_horizontal|center_vertical"
                    android:text=""
                    android:textColor="@color/black_60"
                    android:textSize="18sp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>