<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_search_fragment"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/contact_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/utang_contact_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:layout_marginStart="@dimen/_16dp"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:srcCompat="@drawable/ic_utang_contact_icon_red" />

        <TextView
            android:id="@+id/tv_modal_main_title1"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_toEndOf="@id/utang_contact_icon"
            android:text="@string/contact_header_new"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/search_input"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:layout_toEndOf="@+id/tv_modal_main_title1"
            android:background="@null"
            android:ellipsize="end"
            android:fontFamily="@font/roboto_bold"
            android:gravity="end"
            android:hint="@string/nama_pelanggan"
            android:inputType="textPersonName"
            android:maxLength="18"
            android:singleLine="true"
            android:textColor="@color/black_80"
            android:textCursorDrawable="@null"
            android:textSize="@dimen/text_18sp" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:paddingBottom="@dimen/_10dp"
        android:text="@string/same_name_error"
        android:textColor="@color/red_60"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/contact_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>