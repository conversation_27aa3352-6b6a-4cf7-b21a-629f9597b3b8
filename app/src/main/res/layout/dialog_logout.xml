<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="20dp"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_close"
        android:padding="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_logout_image"
        android:layout_width="0dp"
        android:layout_marginTop="10dp"
        android:layout_height="300dp"
        app:srcCompat="@drawable/ic_logout"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_close" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_logout_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Keluar dari Aplikasi?"
        style="@style/Heading2"
        app:layout_constraintTop_toBottomOf="@id/iv_logout_image"
        app:layout_constraintStart_toStartOf="@id/iv_logout_image"
        app:layout_constraintEnd_toEndOf="@id/iv_logout_image" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_logout_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="10dp"
        android:text="Setelah keluar, data tetap tersimpan dengan aman dan kamu bisa masuk kembali."
        style="@style/Body2"
        app:layout_constraintTop_toBottomOf="@id/tv_logout_prompt"
        app:layout_constraintStart_toStartOf="@id/iv_logout_image"
        app:layout_constraintEnd_toEndOf="@id/iv_logout_image" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_deny"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Keluar"
        android:textAllCaps="false"
        style="@style/ButtonOutline.Black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_logout_description"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.45"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_accept"
        android:layout_width="0dp"
        android:text="@string/batal"
        android:textAllCaps="false"
        android:layout_height="wrap_content"
        style="@style/ButtonFill.Yellow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_logout_description"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.45"/>

</androidx.constraintlayout.widget.ConstraintLayout>