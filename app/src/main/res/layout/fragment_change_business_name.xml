<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tbChangeBusinessName"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:maxLines="1"
                android:paddingStart="0dp"
                android:paddingEnd="16dp"
                android:text="@string/ubah_nama_toko"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivBack"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_24dp"
        android:text="@string/nama_toko"
        android:textColor="@color/black_80"
        app:fontFamily="@font/roboto_bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbChangeBusinessName" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etBusinessName"
        style="@style/Base.Widget.MaterialComponents.TextInputEditText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@drawable/bg_edit_text_enabled_disabled"
        android:textColor="@color/black"
        android:textColorHint="@color/lightBlack"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvName" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/shop_would_be_created"
        android:textColor="#595959"
        app:fontFamily="@font/roboto"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/etBusinessName" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvErrorMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textColor="@color/red"
        app:fontFamily="@font/roboto"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvDescription"
        tools:text="Silakan gunakan kata lain yang lebih sesuai" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStoreAddress"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/alamat_toko"
        android:textColor="@color/black_80"
        app:fontFamily="@font/roboto_bold"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvErrorMessage" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etAddress"
        style="@style/Base.Widget.MaterialComponents.TextInputEditText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@drawable/bg_edit_text_enabled_disabled"
        android:enabled="false"
        android:textColor="@color/black"
        android:textColorHint="@color/lightBlack"
        android:textCursorDrawable="@null"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvStoreAddress" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnConfirm"
        style="@style/ButtonFill.Blue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:enabled="false"
        android:text="@string/simpan"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ProgressBar
        android:id="@+id/progressLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>