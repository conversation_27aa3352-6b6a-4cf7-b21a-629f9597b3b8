<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- Toolbar -->
    <include
        android:id="@+id/toolbar"
        layout="@layout/widget_toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivEdcImage"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_marginTop="@dimen/dimen_48dp"
        android:src="@drawable/ic_edc_android"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />


    <TextView
        android:id="@+id/tvTitle"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="Aktifkan EDC Android"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivEdcImage" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grpAndroid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="ivInstruction1,ivInstruction2,ivInstruction3,tvInstruction1,tvInstruction2,tvInstruction3"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grpAtmPro"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvActivateAtmProDes,btnActivateEdc"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvActivateAtmProDes"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_32dp"
        android:gravity="center_horizontal"
        android:text="@string/you_do_not_have_edc_activate_first"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <ImageView
        android:id="@+id/ivInstruction1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_32dp"
        android:src="@drawable/ic_circle_number_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <!-- Instruction 1 -->
    <TextView
        android:id="@+id/tvInstruction1"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:text="@string/activate_edc_device_connect_internet"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/ivInstruction1"
        app:layout_constraintStart_toEndOf="@id/ivInstruction1"
        app:layout_constraintTop_toTopOf="@id/ivInstruction1" />


    <ImageView
        android:id="@+id/ivInstruction2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_32dp"
        android:src="@drawable/ic_circle_number_2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivInstruction1" />

    <TextView
        android:id="@+id/tvInstruction2"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:text="@string/login_with_register_phone_number"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/ivInstruction2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivInstruction2"
        app:layout_constraintTop_toTopOf="@id/ivInstruction2" />


    <ImageView
        android:id="@+id/ivInstruction3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_32dp"
        android:src="@drawable/ic_circle_number_3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivInstruction2" />
    <!-- Instruction 3 -->
    <TextView
        android:id="@+id/tvInstruction3"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:text="@string/login_successful_access_all_features"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/ivInstruction3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivInstruction3"
        app:layout_constraintTop_toTopOf="@id/ivInstruction3" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnActivateEdc"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="4dp"
        android:letterSpacing="0"
        android:text="@string/buy_edc"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rippleColor="@color/black_40" />

    <ProgressBar
        android:id="@+id/pbLoader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:progressTint="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
