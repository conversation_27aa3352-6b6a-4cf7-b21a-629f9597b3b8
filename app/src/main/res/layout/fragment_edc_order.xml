<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:layout_marginBottom="@dimen/_12dp"
    app:cardCornerRadius="@dimen/_12dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="@dimen/_16dp">

        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:scaleType="fitXY"
            android:src="@drawable/ic_edc_devices"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            android:text="Beli EDC Baru"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_goneMarginEnd="@dimen/_12dp"
            app:layout_constraintEnd_toStartOf="@+id/iv_next"
            app:layout_constraintStart_toEndOf="@+id/iv_image"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_description"
            style="@style/Body3"
            android:textColor="@color/black40"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="Bisa beli mulai dari Rp749,000 aja!"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/_12dp"
            app:layout_constraintEnd_toStartOf="@+id/iv_next"
            app:layout_constraintStart_toEndOf="@+id/iv_image"
            app:layout_constraintTop_toBottomOf="@id/tv_title"/>

        <ImageView
            android:src="@drawable/ic_chevron_right_black20"
            android:id="@+id/iv_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>