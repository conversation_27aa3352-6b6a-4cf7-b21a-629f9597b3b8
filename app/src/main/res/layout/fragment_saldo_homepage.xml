<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginBottom="@dimen/_12dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_blue_gradient_bottom_rounded"
        app:layout_constraintBottom_toTopOf="@+id/vw_guideline_saldo_center"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_leaderboard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vw_guideline_saldo_center"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/cv_saldo"
        app:layout_constraintTop_toTopOf="@+id/cv_saldo" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_saldo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:elevation="@dimen/_16dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_leaderboard"
        app:layout_goneMarginTop="@dimen/_12dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_16dp">

            <TextView
                android:id="@+id/tv_saldo_title"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/_8dp"
                android:text="@string/saldo_bukuwarung"
                app:drawableEndCompat="@drawable/ic_eye_open"
                app:drawableStartCompat="@drawable/vector_saldo_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_saldo_amount"
                style="@style/SubHeading1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:background="@null"
                android:clickable="false"
                android:cursorVisible="false"
                android:focusable="false"
                app:layout_constraintTop_toBottomOf="@+id/tv_saldo_title" />

            <TextView
                android:id="@+id/tv_saldo_error"
                style="@style/SubHeading1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:text="@string/failed_to_show_balance"
                app:drawableEndCompat="@drawable/ic_rotate_red"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_saldo_amount" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Body3.black40"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_saldo_error"
                tools:text="Transaksi Tanpa Kartu" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_items"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                tools:itemCount="3"
                tools:listitem="@layout/saldo_homepage_item" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>