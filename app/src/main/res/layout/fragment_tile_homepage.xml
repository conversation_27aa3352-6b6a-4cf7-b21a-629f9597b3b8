<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:layout_marginBottom="@dimen/_12dp"
    android:elevation="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/_12dp"
    app:layout_constraintBottom_toBottomOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/cvPendingEdcOrder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/yellow_10"
                android:elevation="@dimen/_16dp"
                android:padding="@dimen/_16dp">

                <ImageView
                    android:id="@+id/iv_image"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_warning_yellow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_title_draft_edc_order"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:text="Yuk Selesaikan Verifikasi"
                    app:drawableEndCompat="@drawable/ic_chevron_right_black20"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_image"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>


        <androidx.constraintlayout.widget.ConstraintLayout xmlns:tools="http://schemas.android.com/tools"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginVertical="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/cvPendingEdcOrder">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_title"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Menu" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_subtitle"
                style="@style/Body5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintStart_toStartOf="@+id/tv_title"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                tools:text="Menu" />

            <ImageView
                android:id="@+id/iv_bluetooth_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_bluetooth_selector"
                app:layout_constraintBottom_toBottomOf="@id/tv_title"
                app:layout_constraintEnd_toStartOf="@id/iv_printer_state"
                app:layout_constraintTop_toTopOf="@id/tv_title" />

            <ImageView
                android:id="@+id/iv_printer_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_printer_selector"
                app:layout_constraintBottom_toBottomOf="@id/tv_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_title" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_tile_items"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
                tools:itemCount="3"
                tools:listitem="@layout/homepage_tile_item" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>
