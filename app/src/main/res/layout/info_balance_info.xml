<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_blue15_corner_8dp_stroke_blue40">

    <ImageView
        android:id="@+id/iv_info"
        android:layout_width="@dimen/_20dp"
        android:layout_height="@dimen/_20dp"
        android:layout_marginVertical="@dimen/_14dp"
        android:layout_marginStart="@dimen/_12dp"
        android:src="@drawable/ic_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_info_title"
        style="@style/SubHeading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_8dp"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:text="@string/info_bal_info"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_info"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        style="@style/Body3"
        android:id="@+id/tv_info_text"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_8dp"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:text="@string/info_bal_info"
        android:textColor="@color/black_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_info"
        app:layout_constraintTop_toBottomOf="@id/tv_info_title" />


</androidx.constraintlayout.widget.ConstraintLayout>