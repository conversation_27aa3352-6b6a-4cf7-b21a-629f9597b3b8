<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/device_item"
    android:layout_marginTop="@dimen/_16dp"
    android:paddingHorizontal="16dp">

    <!-- Left aligned text -->
    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/left_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Heading3"
        android:gravity="center_vertical"
        android:paddingVertical="14dp"
        android:text="Printer"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Right aligned text -->
    <TextView
        android:id="@+id/right_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Heading3"
        android:text=""
        android:textColor="#00A171"
        android:textSize="12sp"
        android:paddingRight="@dimen/_8dp"
        app:layout_constraintEnd_toStartOf="@id/chevron_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/right_text_subtitle"
        tools:text="Right Text" />

    <TextView
        android:id="@+id/right_text_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_8dp"
        android:text=""
        android:textColor="@color/cta_button_text"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/chevron_icon"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintTop_toBottomOf="@id/right_text"
        tools:text="Right Text Subtitle" />

    <ImageView
        android:id="@+id/chevron_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_chevron_right"
        android:paddingRight="@dimen/_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <View
        android:layout_width="match_parent"
        android:background="@color/grey_217"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
