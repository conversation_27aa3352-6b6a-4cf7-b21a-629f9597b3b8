<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_invoice_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@drawable/bg_solid_red5_corner_8dp"
        android:backgroundTint="@color/blue_5"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_code_label"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/destination_account"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_account_name"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_code_label"
            tools:text="07P677UA66SXV50H" />

        <TextView
            android:id="@+id/tv_account_number"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_8dp"
            android:gravity="center"
            android:textColor="@color/blue_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_account_name"
            tools:text="Mandiri - ********" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_status"
        android:layout_width="@dimen/_24dp"
        android:layout_height="@dimen/_24dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_status"
        app:layout_constraintEnd_toStartOf="@id/tv_status"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_status"
        app:srcCompat="@drawable/ic_progress_completed"
        app:tint="@color/black_80" />

    <TextView
        android:id="@+id/tv_status"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:paddingVertical="@dimen/_12dp"
        android:text="@string/payment_successful"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_status"
        app:layout_constraintTop_toBottomOf="@id/cl_invoice_code" />

    <TextView
        android:id="@+id/tv_mount"
        style="@style/Heading1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:gravity="center_horizontal"
        android:textColor="@color/black"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/tv_status"
        tools:text="Rp1.000.000"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/tv_pending_info_guarantee"
        style="@style/Body2"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_4dp"
        android:textSize="14sp"
        android:layout_marginHorizontal="@dimen/_14dp"
        android:text="@string/pending_information_guarantee"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mount"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_status, iv_status" />

</androidx.constraintlayout.widget.ConstraintLayout>