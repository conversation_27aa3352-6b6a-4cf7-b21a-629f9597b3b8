<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground">

    <androidx.appcompat.widget.AppCompatRadioButton
        android:id="@+id/radio_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:clickable="false"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@id/txt_account_number"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/txt_bank_title" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_bank_image"
        android:layout_width="56dp"
        android:layout_height="46dp"
        android:layout_marginStart="@dimen/_16dp"
        app:cardBackgroundColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/txt_account_number"
        app:layout_constraintStart_toEndOf="@id/radio_default"
        app:layout_constraintTop_toTopOf="@id/txt_bank_title">

        <ImageView
            android:id="@+id/image_bank"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_bank" />
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/txt_bank_title"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="16dp"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toStartOf="@id/icon_menu"
        app:layout_constraintStart_toEndOf="@id/layout_bank_image"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="BNI - Dea Clarissa Safitri" />

    <TextView
        android:id="@+id/txt_account_number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="center_vertical"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="@id/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layout_bank_image"
        app:layout_constraintTop_toBottomOf="@id/txt_bank_title"
        app:layout_constraintVertical_bias="0"
        tools:text="3883134xxxx" />

    <ImageView
        android:id="@+id/icon_menu"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="@null"
        android:padding="3dp"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_main_menu_dot"
        app:layout_constraintBottom_toBottomOf="@id/txt_account_number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/txt_bank_title"
        app:tint="@color/black_40" />

    <View
        android:id="@+id/divider"
        style="@style/Divider.Horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/v_disabled"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:alpha="0.5"
        android:background="@color/black_5"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_info"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_6dp"
        android:background="@drawable/bg_yellow_outline"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_4dp"
        android:text="@string/blocked_info_text"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txt_account_number"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_6dp"
        android:background="@drawable/bg_solid_red5_corner_8dp_stroke_red40"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_4dp"
        android:text="@string/blocked_info_text"
        android:textColor="@color/red_80"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txt_account_number"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
