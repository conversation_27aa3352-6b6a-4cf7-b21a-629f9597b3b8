<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_order_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_order_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@drawable/ic_payment_in" />

    <TextView
        android:id="@+id/tv_order_title"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintEnd_toStartOf="@id/tv_amount"
        app:layout_constraintStart_toEndOf="@id/iv_order_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Name" />

    <TextView
        android:id="@+id/tv_date"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/tv_tag"
        app:layout_constraintStart_toStartOf="@id/tv_order_title"
        app:layout_constraintTop_toBottomOf="@id/tv_order_title"
        tools:text="9 Dec 2020, 14:50" />

    <TextView
        android:id="@+id/tv_transaction_desc"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:ellipsize="end"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_date"
        app:layout_constraintTop_toBottomOf="@id/tv_date"
        tools:text="Cashback saldo Rp1.500 dikirimkan 19 Mar 2022" />

    <TextView
        android:id="@+id/tv_tag"
        style="@style/Label1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="2dp"
        android:background="@drawable/bg_rounded_rectangle_blue_5_4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingHorizontal="4dp"
        android:textColor="@color/blue_40"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_status"
        app:layout_constraintEnd_toStartOf="@id/tv_edc_tag"
        app:layout_constraintTop_toTopOf="@id/tv_status"
        tools:text="Lebih Cepat \u26A1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_edc_tag"
        style="@style/Label1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@drawable/bg_rounded_rectangle_black20_8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/_8dp"
        android:paddingVertical="@dimen/_2dp"
        android:text="@string/edc_caps"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_status"
        app:layout_constraintEnd_toStartOf="@id/tv_status"
        app:layout_constraintTop_toTopOf="@id/tv_status"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Rp100000" />

    <TextView
        android:id="@+id/tv_status"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="16dp"
        android:maxLines="1"
        app:layout_constraintEnd_toEndOf="@id/tv_amount"
        app:layout_constraintTop_toBottomOf="@id/tv_amount"
        tools:text="Berhasil" />

    <View
        android:id="@+id/vw_click_listener"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="?selectableItemBackground"
        app:layout_constraintBottom_toTopOf="@id/tv_linked_orders_count"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vw_divider"
        style="@style/Divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_order_title"
        app:layout_constraintTop_toBottomOf="@id/tv_linked_orders_count" />

    <TextView
        android:id="@+id/tv_linked_orders_count"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="@id/tv_order_title"
        app:layout_constraintTop_toBottomOf="@id/tv_transaction_desc"
        tools:text="2 item lainnya" />

    <com.bukuwarung.edc.payments.ui.widgets.AccordionText
        android:id="@+id/at_linked_orders"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/tv_amount"
        app:layout_constraintTop_toTopOf="@id/tv_linked_orders_count" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_linked_orders"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/f8f8f8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gr_linked_orders"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_linked_orders_count, at_linked_orders, rv_linked_orders"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>