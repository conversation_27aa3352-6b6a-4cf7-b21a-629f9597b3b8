<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ic_kyc_kyb"
            android:layout_width="match_parent"
            android:layout_height="260dp"
            android:scaleType="fitXY"
            android:src="@mipmap/ic_kyc_kyb"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_margin="@dimen/_16dp"
            android:src="@drawable/ic_cross_circle_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/Heading2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/verify_kyc_kyb"
            android:textAlignment="center"
            app:layout_constraintTop_toBottomOf="@+id/ic_kyc_kyb" />

        <include
            android:id="@+id/layout_saku"
            layout="@layout/layout_image_title"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginTop="@dimen/_10dp"
            app:layout_constraintEnd_toStartOf="@+id/layout_payment"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <include
            android:id="@+id/layout_payment"
            layout="@layout/layout_image_title"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginTop="@dimen/_20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layout_saku"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <include
            android:id="@+id/layout_saldo"
            layout="@layout/layout_image_title"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginTop="@dimen/_20dp"
            app:layout_constraintEnd_toStartOf="@+id/layout_ppob"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_saku" />

        <include
            android:id="@+id/layout_ppob"
            layout="@layout/layout_image_title"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginTop="@dimen/_20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layout_saldo"
            app:layout_constraintTop_toBottomOf="@+id/layout_saku" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_verify"
            style="@style/ButtonFill.Yellow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:layout_marginTop="@dimen/_32dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:paddingVertical="@dimen/dimen_11dp"
            android:text="@string/verify_now"
            android:textAppearance="@style/Heading3"
            app:cornerRadius="@dimen/dimen_4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_saldo" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>