<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="@dimen/_4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/_16dp">

        <View
            android:id="@+id/vw_shimmer_image"
            android:layout_width="60dp"
            android:layout_height="@dimen/_30dp"
            android:background="@drawable/shimmer_grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vw_shimmer_header_1"
            android:layout_width="150dp"
            android:layout_height="@dimen/_10dp"
            android:background="@drawable/shimmer_grey"
            android:layout_marginStart="@dimen/_12dp"
            app:layout_constraintStart_toEndOf="@+id/vw_shimmer_image"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vw_shimmer_header_2"
            android:layout_width="120dp"
            android:layout_height="@dimen/_10dp"
            android:layout_marginTop="@dimen/_4dp"
            android:background="@drawable/shimmer_grey"
            app:layout_constraintStart_toStartOf="@+id/vw_shimmer_header_1"
            app:layout_constraintTop_toBottomOf="@id/vw_shimmer_header_1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>