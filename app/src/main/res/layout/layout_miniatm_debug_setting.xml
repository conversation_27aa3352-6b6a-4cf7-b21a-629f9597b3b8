<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_blue">

    <include
        android:id="@+id/tb_test_config"
        layout="@layout/widget_toolbar"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/sv_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingBottom="@dimen/_20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tb_test_config">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/layout_test_device"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <include
                android:id="@+id/layout_test_tms"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_test_device" />

            <include
                android:id="@+id/layout_mock_api"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_test_tms" />

            <include
                android:id="@+id/layout_force_reversal"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_mock_api" />

            <include
                android:id="@+id/layout_force_incomplete"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_force_reversal" />

            <include
                android:id="@+id/layout_corrupt_icc"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_force_incomplete" />

            <include
                android:id="@+id/layout_fixed_bank"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_corrupt_icc" />

            <include
                android:id="@+id/layout_mock_print"
                layout="@layout/item_label_switch"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_fixed_bank" />

            <include
                android:id="@+id/layout_device_info"
                layout="@layout/item_payment_category_info"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_5dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_mock_print" />

            <View
                android:id="@+id/vw_device_info_divider"
                style="@style/Divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_device_info" />

            <include
                android:id="@+id/layout_dummy_card_info"
                layout="@layout/item_payment_category_info"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_device_info_divider" />

            <View
                android:id="@+id/vw_dummy_card_divider"
                style="@style/Divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_dummy_card_info" />

            <include
                android:id="@+id/layout_jwt_token"
                layout="@layout/item_jwt_token"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_dummy_card_divider" />

            <View
                android:id="@+id/vw_card_divider"
                style="@style/Divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_jwt_token" />

            <View
                android:id="@+id/view_free_space"
                android:layout_width="0dp"
                android:layout_height="42dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_card_divider" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit"
        style="@style/ButtonFill.Yellow"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_10dp"
        android:text="Back"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_reset"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_reset"
        style="@style/ButtonFill.Yellow"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_10dp"
        android:text="Reset"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_submit" />


</androidx.constraintlayout.widget.ConstraintLayout>