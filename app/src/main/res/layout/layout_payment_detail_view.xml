<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_destination_bank_account"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/destination_account"
        android:textColor="@color/black40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.edc.payments.ui.widgets.BankAccountView
        android:id="@+id/bank_account_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_destination_bank_account" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bank_account_view" />

    <TextView
        android:id="@+id/tv_total_amount"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/total_payment"
        android:textColor="@color/black40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    <TextView
        android:id="@+id/tv_total_amount_value"
        style="@style/Heading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_amount"
        tools:text="Rp1.002.000" />

    <TextView
        android:id="@+id/tv_total_amount_received_by_customer"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/total_received_by_customer"
        android:textColor="@color/black40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_amount_value" />

    <TextView
        android:id="@+id/tv_total_amount_received_by_customer_value"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_6dp"
        android:textColor="@color/black40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_total_amount_received_by_customer"
        tools:text="Rp1.000.000" />

    <TextView
        android:id="@+id/tv_admin_fee_title"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/platform_fee"
        android:textColor="@color/black40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_total_amount_received_by_customer" />

    <TextView
        android:id="@+id/tv_admin_fee"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@drawable/strike_through"
        android:textColor="@color/black40"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/tv_discounted_fee"
        app:layout_constraintTop_toTopOf="@id/tv_admin_fee_title"
        tools:text="Rp2.500"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_discounted_fee"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_admin_fee_title"
        tools:text="Rp2.000"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_fee"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_admin_fee, tv_discounted_fee" />

    <View
        android:id="@+id/vw_divider_2"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_admin_fee_title" />

    <TextView
        android:id="@+id/tvCommentsOptional"
        style="@style/Body1"

        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/news_optional"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider_2"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvCommentsOptionalValue"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:textColor="@color/black_80"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCommentsOptional"
        tools:text="Pembayaran hutang"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_loyalty_tier"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/br_fee" />

    <TextView
        android:id="@+id/tv_loyalty_discount"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_loyalty_tier" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gp_fee"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="br_fee, tv_admin_fee, tv_admin_fee_title, tv_total_amount_received_by_customer_value, tv_total_amount_received_by_customer, tv_total_amount_value, tv_total_amount, vw_divider" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gpOnlyTotalAmount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tv_total_amount_received_by_customer,tv_total_amount_received_by_customer_value,tv_admin_fee_title,tv_admin_fee,tv_discounted_fee" />
</androidx.constraintlayout.widget.ConstraintLayout>