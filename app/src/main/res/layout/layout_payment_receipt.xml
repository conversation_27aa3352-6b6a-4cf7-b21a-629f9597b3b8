<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_rectangle_white_8dp"
    android:paddingBottom="@dimen/_12dp">

    <TextView
        android:id="@+id/tv_receipt_proof"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/proof_of_trx"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_edit_info"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_receipt_proof"
        tools:text="Terakhir diubah 13 Okt 2020 pada jam 12:34 Detail transaksi otomatis diperbarui" />

    <TextView
        android:id="@+id/tv_edit"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_14dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/edit"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:drawableRightCompat="@drawable/ic_edit_with_border"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_receipt_proof" />

    <com.bukuwarung.edc.payments.util.OrderInvoice
        android:id="@+id/order_invoice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_receipt_proof" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_print"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/blue_60"
        android:gravity="center"
        android:padding="@dimen/_8dp"
        android:text="@string/transaction_print"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:cornerRadius="@dimen/_4dp"
        app:icon="@drawable/ic_printer"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:iconTintMode="src_in"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/order_invoice"
        app:strokeColor="@color/black_10"
        app:strokeWidth="1dp" />


</androidx.constraintlayout.widget.ConstraintLayout>