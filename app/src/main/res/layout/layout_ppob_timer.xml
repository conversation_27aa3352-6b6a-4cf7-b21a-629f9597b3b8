<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/blue_5"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingVertical="@dimen/_12dp">

    <TextView
        android:id="@+id/tv_message"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/payment_timer_limit"
        android:textColor="@color/blue_80"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_expired_time"
        app:layout_constraintEnd_toStartOf="@+id/tv_expired_time"
        app:layout_constraintTop_toTopOf="@+id/tv_expired_time"
        app:srcCompat="@drawable/ic_clock_icon" />

    <TextView
        android:id="@+id/tv_expired_time"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/blue_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="00:02:00:00" />

    <TextView
        android:id="@+id/tv_time"
        style="@style/Label1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/time"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_expired_time" />
</androidx.constraintlayout.widget.ConstraintLayout>