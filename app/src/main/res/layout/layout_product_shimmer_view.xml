<?xml version="1.0" encoding="utf-8"?>

<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/sfl_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include
            layout="@layout/layout_product_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            layout="@layout/layout_product_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"/>

        <include
            layout="@layout/layout_product_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"/>

        <include
            layout="@layout/layout_product_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"/>

        <include
            layout="@layout/layout_product_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"/>
    </LinearLayout>

</com.facebook.shimmer.ShimmerFrameLayout>