<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/secureInfo"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_16dp"
    android:background="@drawable/bg_solid_grey100_corner_4dp_stroke_white">

    <ImageView
        android:id="@+id/secureInfoImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:src="@drawable/vector_secure"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/secureInfoHeading"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/dont_share_pin_title"
        app:layout_constraintBottom_toTopOf="@+id/secureInfoBody"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/secureInfoImage"
        app:layout_constraintTop_toTopOf="@id/secureInfoImage" />

    <TextView
        android:id="@+id/secureInfoBody"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/dont_share_pin_and_otp_body"
        app:layout_constraintBottom_toBottomOf="@+id/secureInfoImage"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/secureInfoImage"
        app:layout_constraintTop_toBottomOf="@+id/secureInfoHeading" />
</androidx.constraintlayout.widget.ConstraintLayout>