<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/tv_name"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Kota Bandung" />

    <TextView
        android:id="@+id/tv_pdam_name"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        tools:text="TIRTAWENING PDAM" />

    <TextView
        android:id="@+id/tv_error"
        style="@style/Label1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="22dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/not_available"
        android:textColor="@color/red_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Sedang gangguan" />

    <ImageView
        android:id="@+id/iv_selected"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_11dp"
        android:layout_marginTop="22dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_selection" />

    <View
        android:layout_width="@dimen/_0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_pdam_name" />

</androidx.constraintlayout.widget.ConstraintLayout>