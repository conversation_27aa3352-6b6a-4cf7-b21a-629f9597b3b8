<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:layout_marginVertical="@dimen/_8dp"
    android:background="@drawable/bg_corner_4dp_stroke_black5"
    android:padding="@dimen/_16dp">

    <ImageView
        android:id="@+id/iv_trx"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:contentDescription="@string/icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_edc_device" />

    <TextView
        android:id="@+id/tvDeviceBrand"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        app:layout_constraintStart_toEndOf="@+id/iv_trx"
        app:layout_constraintTop_toTopOf="@+id/iv_trx"
        tools:text="Cek Saldo" />

    <!-- Status Badge -->
    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/statusBadgeCompose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="@+id/tvDeviceBrand"
        app:layout_constraintTop_toBottomOf="@+id/tvConnectedStatus" />

    <TextView
        android:id="@+id/tvSid"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="@+id/tvDeviceBrand"
        app:layout_constraintTop_toBottomOf="@+id/tvDeviceBrand"
        tools:text="10 Feb 2024 11:53" />

    <TextView
        android:id="@+id/tvTid"
        style="@style/Body3.black40"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:textAlignment="textStart"
        app:layout_constraintEnd_toStartOf="@id/ivOpenDevice"
        app:layout_constraintStart_toEndOf="@+id/tvSid"
        app:layout_constraintTop_toTopOf="@+id/tvSid"
        app:layout_constraintBottom_toBottomOf="@id/tvSid"
        tools:text="Berhasil" />

    <TextView
        android:id="@+id/tvWarrantyExpiredDate"
        style="@style/Body3.black40"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="textStart"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/ivOpenDevice"
        app:layout_constraintStart_toStartOf="@+id/tvSid"
        app:layout_constraintTop_toBottomOf="@+id/tvSid"
        tools:text="Garansi aktif sampai 15 Nov 2025" />


    <TextView
        android:id="@+id/tvConnectedStatus"
        style="@style/Body3.green80"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvWarrantyExpiredDate"
        app:layout_constraintTop_toBottomOf="@+id/tvWarrantyExpiredDate"
        tools:text="Ref. No. 73900243841" />

    <ImageView
        android:id="@+id/ivOpenDevice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_chevron_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>