<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/help_title"
        app:layout_constraintBottom_toBottomOf="@id/iv_help"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_help" />

    <TextView
        android:id="@+id/tv_help"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        android:gravity="end"
        android:text="@string/label_help_wa_btn"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="@id/iv_help"
        app:layout_constraintEnd_toStartOf="@+id/iv_help"
        app:layout_constraintStart_toEndOf="@+id/tv_title"
        app:layout_constraintTop_toTopOf="@id/iv_help" />

    <ImageView
        android:id="@+id/iv_help"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_help"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>