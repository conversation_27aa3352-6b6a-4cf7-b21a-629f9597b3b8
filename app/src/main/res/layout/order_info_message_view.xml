<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_info_message_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_blue15_corner_8dp_stroke_blue40"
    android:padding="12dp">

    <TextView
        android:id="@+id/tv_info_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="6dp"
        android:drawablePadding="@dimen/_10dp"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/_2dp"
        android:textColor="@color/black_60"
        app:drawableStartCompat="@drawable/ic_info"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_learn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Pengembalian uang akan dikembalikan secara otomatis ke Saldo BukuWarung."
        tools:visibility="visible" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_learn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:letterSpacing="0.05"
        android:paddingHorizontal="14dp"
        android:paddingVertical="@dimen/_10dp"
        android:text="@string/learn"
        android:textAllCaps="false"
        android:textColor="@color/cta_text_button"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:backgroundTint="@color/buku_CTA"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>