<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp"
    android:padding="16dp">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.45" />

    <TextView
        android:id="@+id/tv_payment_amount"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/payment_amount"
        app:layout_constraintBottom_toBottomOf="@id/tv_payment_amount_value"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_payment_amount_value" />

    <TextView
        android:id="@+id/tv_payment_amount_value"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Rp250.000" />

    <TextView
        android:id="@+id/tv_trx_type_info"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/pay_money_to"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_payment_amount_value" />

    <TextView
        android:id="@+id/tv_trx_type_info_value"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_4dp"
        android:ellipsize="end"
        android:gravity="end|center_vertical"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_trx_type_info"
        tools:text="Rizky Budianto" />

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/_2dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_trx_type_info" />

    <TextView
        android:id="@+id/tv_transaction_code"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/payment_code"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <TextView
        android:id="@+id/tv_transaction_code_value"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_4dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:drawableEndCompat="@drawable/ic_copy"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_transaction_code"
        tools:text="RIYYMMDDXXXXXXXX" />

    <TextView
        android:id="@+id/tv_customer_account"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/beneficiary_account"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_transaction_code" />

    <TextView
        android:id="@+id/tv_customer_account_value"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_customer_account"
        tools:text="Mandiri - 3883134xxxx" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_customer_account"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_customer_account, tv_customer_account_value"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_ppob1_name"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_customer_account"
        tools:text="@string/beneficiary_account" />

    <TextView
        android:id="@+id/tv_ppob1_value"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_ppob1_name"
        tools:text="Mandiri - 3883134xxxx" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_ppob1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_ppob1_name, tv_ppob1_value"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_ppob2_name"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_ppob1_name"
        tools:text="@string/beneficiary_account" />

    <TextView
        android:id="@+id/tv_ppob2_value"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_ppob2_name"
        tools:text="Mandiri - 3883134xxxx" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_ppob2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_ppob2_name, tv_ppob2_value"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>