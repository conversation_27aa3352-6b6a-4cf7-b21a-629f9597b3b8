<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp">

    <TextView
        android:id="@+id/tv_payment_detail"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/payment_detail"
        app:layout_constraintEnd_toStartOf="@id/iv_detail_collapse"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_detail_collapse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_chevron_up" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_details_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/tv_payment_detail">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_50v"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <TextView
            android:id="@+id/tv_capital_cost"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/capital_cost"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_capital_cost_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_capital_cost"
            tools:text="Rp249.000" />

        <TextView
            android:id="@+id/tv_admin_fee"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:drawablePadding="@dimen/_8dp"
            android:text="@string/biaya_admin"
            android:textColor="@color/black_40"
            app:drawableEndCompat="@drawable/ic_info_black20"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_capital_cost" />

        <TextView
            android:id="@+id/tv_admin_fee_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_4dp"
            android:background="@drawable/strike_through"
            android:textColor="@color/black_40"
            app:layout_constraintBottom_toBottomOf="@+id/tv_admin_fee"
            app:layout_constraintEnd_toStartOf="@id/tv_discounted_fee_value"
            app:layout_constraintTop_toTopOf="@+id/tv_admin_fee"
            tools:text="@string/free" />

        <TextView
            android:id="@+id/tv_discounted_fee_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/tv_admin_fee_value"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_admin_fee_value"
            tools:text="@string/free" />

        <TextView
            android:id="@+id/tv_discount"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/discount"
            android:textColor="@color/blue_80"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_admin_fee" />

        <TextView
            android:id="@+id/tv_discount_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/blue_80"
            app:layout_constraintBottom_toBottomOf="@+id/tv_discount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_discount"
            tools:text="-Rp100" />

        <TextView
            android:id="@+id/tv_saldo_bonus"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/saldo_bonus_used"
            android:textColor="@color/blue_80"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_discount" />

        <TextView
            android:id="@+id/tv_saldo_bonus_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/blue_80"
            app:layout_constraintBottom_toBottomOf="@+id/tv_saldo_bonus"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_saldo_bonus"
            tools:text="-Rp100" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/grp_saldo_bonus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_saldo_bonus, tv_saldo_bonus_value" />

        <TextView
            android:id="@+id/tv_total_payment"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/label_total_payment"
            android:textColor="@color/black40"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_saldo_bonus" />

        <TextView
            android:id="@+id/tv_total_payment_value"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_total_payment"
            tools:text="Rp250.000" />

        <View
            android:id="@+id/vw_divider_1"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_total_payment" />

        <TextView
            android:id="@+id/tv_selling_price"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/selling_price"
            android:textColor="@color/black40"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider_1" />

        <TextView
            android:id="@+id/tv_selling_price_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_selling_price"
            tools:text="Rp 100.000" />

        <TextView
            android:id="@+id/tv_profit"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/capital_cost"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_selling_price" />

        <TextView
            android:id="@+id/tv_profit_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_profit"
            tools:text="Rp249.000" />

        <View
            android:id="@+id/vw_divider2"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_profit" />

        <TextView
            android:id="@+id/tv_payment_method"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/label_payment_method"
            android:textColor="@color/black40"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider2" />

        <TextView
            android:id="@+id/tv_saldo"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/saldo"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_payment_method" />

        <TextView
            android:id="@+id/tv_saldo_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_saldo"
            tools:text="Rp 100.000" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>