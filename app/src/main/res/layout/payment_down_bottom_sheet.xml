<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingBottom="@dimen/_20dp">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        android:src="@drawable/close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="@dimen/_20dp"/>

    <ImageView
        android:id="@+id/iv_ic_payment_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_no_inet"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"/>

    <TextView
        style="@style/Heading3"
        android:id="@+id/tv_payment_down_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/inet_lost"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_ic_payment_down"/>

    <TextView
        style="@style/Body2"
        android:id="@+id/tv_payment_down_body"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_40dp"
        android:layout_marginEnd="@dimen/_40dp"
        android:text="@string/no_connection_message"
        android:textAlignment="center"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_payment_down_title"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_payment_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_payment_down_body"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/reload"
        style="@style/ButtonOutline.Blue"
        app:strokeWidth="1dp"
        android:paddingTop="12dp"
        android:layout_marginEnd="@dimen/_16dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>