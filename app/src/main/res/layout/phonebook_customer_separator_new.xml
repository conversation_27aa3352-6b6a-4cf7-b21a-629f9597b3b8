<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/contactLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center">

    <TextView
        android:id="@+id/new_contact"
        style="@style/SubHeading1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black_5"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingLeft="@dimen/_16dp"
        android:paddingTop="8dp"
        android:paddingRight="@dimen/_16dp"
        android:paddingBottom="@dimen/_8dp"
        android:text="@string/existing_customer_contact" />

</RelativeLayout>


