<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_bank_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_bank_indonesia" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/_12dp"
        android:background="@color/black_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_bank_logo"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_powered_by"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:lineSpacingExtra="4sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/vw_divider"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Pembayaran didukung oleh Xendit, OY!, dan DOKU yang berlisensi Bank Indonesia." />

</androidx.constraintlayout.widget.ConstraintLayout>