<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black_5"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_heading"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_18dp"
            android:text="@string/fragment_payment_confirmation_title"
            app:layout_constraintEnd_toStartOf="@+id/iv_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_cross_black20" />

        <View
            android:id="@+id/vw_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@color/black_10"
            app:layout_constraintTop_toBottomOf="@+id/iv_close" />

        <TextView
            android:id="@+id/tv_error_message"
            style="@style/SubHeading2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/red_80"
            android:background="@color/red_5"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_14dp"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider"
            tools:text="Tagihan sampai bulan terpilih sudah terbayar. Silakan pilih bulan selanjutnya untuk melanjutkan transaksi"/>

        <View
            android:id="@+id/vw_divider_error_message"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/red_10"
            app:layout_constraintTop_toBottomOf="@+id/tv_error_message"/>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/grp_error_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_error_message, vw_divider_error_message" />

        <include
            android:id="@+id/include_biller_info"
            layout="@layout/layout_biller_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider_error_message" />

        <com.bukuwarung.edc.ppob.common.compoundviews.BillDetailExpandableView
            android:id="@+id/bill_detail_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include_biller_info"
            tools:layout_editor_absoluteX="16dp"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_next"
        style="@style/ButtonFill.Yellow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:padding="@dimen/_12dp"
        android:text="@string/next"
        app:cornerRadius="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_view"
        app:layout_goneMarginTop="@dimen/_18dp"
        app:rippleColor="@color/black_40" />

</androidx.constraintlayout.widget.ConstraintLayout>