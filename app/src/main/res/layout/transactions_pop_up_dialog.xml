<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/transactions_dialog_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/fui_transparent"
    android:backgroundTint="@color/black_40"
    android:padding="@dimen/_8dp">

    <LinearLayout
        android:id="@+id/close_dialog"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:background="@drawable/circle_with_white_bg"
        android:elevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints">

        <ImageView
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:layout_margin="7dp"
            android:src="@drawable/close"
            app:tint="@color/black_60" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/fui_transparent"
        android:padding="14dp"
        tools:ignore="MissingConstraints">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_rounded_rectangle_white_16dp"
            tools:ignore="MissingConstraints">

            <ImageView
                android:id="@+id/transactions_image"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toTopOf="@+id/transactions_heading"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="MissingConstraints" />

            <TextView
                android:id="@+id/transactions_heading"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/roboto"
                android:lineHeight="24dp"
                android:text="heading"
                android:textColor="@color/black_80"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@+id/transactions_body"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/transactions_image"
                tools:ignore="MissingConstraints" />

            <TextView
                android:id="@+id/transactions_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/roboto"
                android:lineHeight="20dp"
                android:text="body"
                android:textColor="@color/black_60"
                android:textSize="14sp"
                app:layout_constraintBottom_toTopOf="@+id/button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/transactions_heading"
                tools:ignore="MissingConstraints" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button"
                style="@style/DefaultMaterialButtonStyleAdjacent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:gravity="center_horizontal"
                android:padding="12dp"
                android:text="@string/save"
                android:textAllCaps="false"
                android:textColor="@color/cta_button_text"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/transactions_body"
                tools:ignore="MissingConstraints" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
