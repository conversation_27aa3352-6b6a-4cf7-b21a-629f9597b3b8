package com.bukuwarung.analytic

import com.bukuwarung.analytic.BtAnalyticConstant.MOBILE_PHONE
import com.bukuwarung.analytic.BtAnalyticConstant.USER_PROP_DEVICE

object BtAnalytics {

    private lateinit var analyticCb: (eventName: String, eventProps: HashMap<String, *>?) -> Unit
    private lateinit var deviceType: (eventName: String) -> Unit
    private lateinit var logCb: (msg: String?, e: Exception?) -> Unit

    fun initialize(
        cb: (eventName: String, eventProps: HashMap<String, *>?) -> Unit,
        type: (name: String) -> Unit,
        log: (msg: String?, e: Exception?) -> Unit
    ) {
        analyticCb = cb
        deviceType = type
        logCb = log
    }

    fun trackEventMobile(eventName: String, eventProps: HashMap<String, String>? = HashMap()) {
        eventProps?.set(USER_PROP_DEVICE, MOBILE_PHONE)
        analyticCb.invoke(eventName, eventProps)
    }

    fun setDeviceName(name: String) {
        deviceType.invoke(name)
    }

    fun bwLog(msg: String? = null) {
        logCb.invoke(msg, null)
    }

    fun bwLog(e: Exception? = null) {
        logCb.invoke(null, e)
    }

    fun bwLog(msg: String?, e:Exception){
        logCb.invoke(msg, e)
    }
}