package com.bukuwarung.bluetooth_printer

import android.annotation.SuppressLint
import android.content.Context
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.bluetooth_printer.model.PairedDevice
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter
import com.bukuwarung.bluetooth_printer.model.basePrinter.PrinterProperty
import com.bukuwarung.bluetooth_printer.utils.PrintingHelper
import com.bukuwarung.bluetooth_printer.utils.isNotNullOrBlank
import io.paperdb.Paper

@SuppressLint("StaticFieldLeak")
object BluetoothDevices {

    public var context: Context? = null
    private var printingHelper: PrintingHelper? = null

    fun init(context: Context) {
        this.context = context
        Paper.init(context)
    }

    fun getPairedCardReaderList(): List<PrinterDataHolder>? {
        context?.let { return PrinterPrefManager(it).installedCardReaders }
        return null
    }

    fun getPairedCardReader(): String? {
        context?.let { return PrinterPrefManager(it).cardReaderAddress }
        return null
    }

    fun hasPairedCardReader(): Boolean {
        context?.let { return PrinterPrefManager(it).cardReaderAddress.isNotNullOrBlank() == true }
        return false
    }

    fun getPairedPrinterList(): List<PrinterDataHolder>? {
        context?.let { return PrinterPrefManager(it).installedPrinters }
        return null
    }

    fun setPrinter(name: String?, address: String) = PairedDevice.setPairedPrinter(PairedDevice(name, address))

    fun getPairedPrinter(): PairedDevice? = PairedDevice.getPairedPrinter()

    fun hasPairedPrinter(): Boolean {
        context?.let { return PrinterPrefManager(it).installedPrinters?.isNotEmpty() == true }
        return false
    }

    fun removeCurrentPrinter() = PairedDevice.removePairedPrinter()

    fun printer(printer: PrinterProperty, pairedPrinter: PairedDevice): PrintingHelper = PrintingHelper(
        printer,
        pairedPrinter,
        context ?: error("You must call BukuPrinter.init()")
    )

    fun printer(printer: PrinterProperty): PrintingHelper = PrintingHelper(
        printer,
        getPairedPrinter() ?: error("No paired printer saved, Save one and retry!!"),
        context ?: error("You must call BukuPrinter.init()")
    )

    fun printer(pairedPrinter: PairedDevice) = PrintingHelper(
        BasePrinter(),
        pairedPrinter,
        context ?: error("You must call BukuPrinter.init()")
    )

    fun printer(): PrintingHelper {
        return if (printingHelper == null) {
            printingHelper = PrintingHelper(
                BasePrinter(),
                getPairedPrinter()
                    ?: error("No paired printer saved, Save one and retry!!"),
                context ?: error("You must call BukuPrinter.init()")
            )
            printingHelper!!
        } else {
            printingHelper!!
        }
    }

    fun isSelectedDeviceARegisteredDevice(
        deviceName: String,
        registeredDevices: List<String>?
    ): Boolean {
        if (registeredDevices.isNullOrEmpty()) return false
        val pairedDevice = registeredDevices.any { device ->
            val lastSixDigits = device.takeLast(6)
            val deviceNameSn = deviceName.filter { char -> char.isDigit() }.takeLast(6)
            lastSixDigits == deviceNameSn
        }
        return pairedDevice
    }
}