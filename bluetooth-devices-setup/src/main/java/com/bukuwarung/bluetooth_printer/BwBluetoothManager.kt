package com.bukuwarung.bluetooth_printer

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import androidx.annotation.RequiresPermission
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity.Companion.CARD_READER
import com.bukuwarung.bluetooth_printer.utils.containsIgnoreCase

class BwBluetoothManager(
    private val pref: PrinterPrefManager,
    private val btAdapter: BluetoothAdapter?
) {

    companion object {
        const val SUPPAY = "Suppay"
        const val MP = "MP-"
        const val PRINT = "print"
        const val PENCETAK = "pencetak"
        const val PRINTER = "printer"
    }

    @RequiresPermission(value = "android.permission.BLUETOOTH_CONNECT")
    fun getPairedBtDevice(deviceType: String): List<PrinterDataHolder> {
        val pairedDeviceList: ArrayList<BluetoothDevice> = getPairedBtDeviceList(deviceType)
        return getPairedBtDevice(deviceType, pairedDeviceList)
    }

    @RequiresPermission(value = "android.permission.BLUETOOTH_CONNECT")
    private fun getPairedBtDeviceList(deviceType: String): ArrayList<BluetoothDevice> {
        val pairedRecognizeDevices = ArrayList<BluetoothDevice>()
        btAdapter?.let {
            val pairedDevices: Set<BluetoothDevice> = it.bondedDevices
            for (device in pairedDevices) {
                if (isRecognizeBtDevice(deviceType, device)) {
                    pairedRecognizeDevices.add(device)
                }
            }
        }
        return pairedRecognizeDevices
    }

    @RequiresPermission(value = "android.permission.BLUETOOTH_CONNECT")
    private fun isRecognizeBtDevice(deviceType: String, device: BluetoothDevice): Boolean {
        if (device.name.isNullOrBlank() || device.address.isNullOrBlank()) {
            return false
        }

        val majorDeviceClass = device.bluetoothClass.majorDeviceClass
        val deviceClass = device.bluetoothClass.deviceClass
        val imagingDeviceCode = BluetoothClass.Device.Major.IMAGING
        val uncategorizedDeviceCode = BluetoothClass.Device.Major.UNCATEGORIZED

        if (deviceType == CARD_READER) {
            return try {
                device.name.containsIgnoreCase(SUPPAY) || device.name.containsIgnoreCase(MP)
            } catch (e: Exception) {
                majorDeviceClass == BluetoothClass.Device.Major.AUDIO_VIDEO && (deviceClass == 1060)
            }
        } else {
            if (majorDeviceClass == imagingDeviceCode && (deviceClass == imagingDeviceCode || deviceClass == 1664)) {
                return true
            }
            // Hack to recognise the bluetooth/thermal printer, sometimes Bluetooth SIG doesn't
            // assign the device class, so banking on name contains Printer
            else if (majorDeviceClass == uncategorizedDeviceCode && deviceClass == uncategorizedDeviceCode
                && (device.name.containsIgnoreCase(PRINT) || device.name.containsIgnoreCase(PENCETAK))
            ) {
                return true
            } else if (device.name.containsIgnoreCase(PRINTER)
                || device.name.containsIgnoreCase(PENCETAK)
            ) {
                return true
            }
            return false
        }
    }

    @RequiresPermission(value = "android.permission.BLUETOOTH_CONNECT")
    private fun getPairedBtDevice(
        deviceType: String,
        btDevices: ArrayList<BluetoothDevice>
    ): MutableList<PrinterDataHolder> {
        val savedBtDevices: MutableList<PrinterDataHolder> = mutableListOf()

        if (deviceType == CARD_READER) {
            pref.installedCardReaders?.let { savedBtDevices.addAll(it) }
        } else {
            pref.installedPrinters?.let { savedBtDevices.addAll(it) }
        }

        // Remove Bluetooth devices that have been removed from the phone settings.
        savedBtDevices.retainAll { storedDevice ->
            btDevices.any { it.address == storedDevice.macAddress }
        }

        btDevices.forEach { btDevice ->
            val isBtDevicePresent = savedBtDevices.firstOrNull { it.macAddress == btDevice.address }
            if (isBtDevicePresent == null) {
                val deviceToBeSave = PrinterDataHolder(
                    btDevice.name,
                    btDevice.address,
                    isPairing = false,
                    device = btDevice
                )
                savedBtDevices.add(deviceToBeSave)
            }
        }

        if (deviceType == CARD_READER) {
            pref.installedCardReaders = savedBtDevices
        } else {
            pref.installedPrinters = savedBtDevices
        }
        return savedBtDevices
    }
}