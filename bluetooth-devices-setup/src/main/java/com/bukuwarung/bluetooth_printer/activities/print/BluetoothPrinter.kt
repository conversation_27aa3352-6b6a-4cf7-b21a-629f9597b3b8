package com.bukuwarung.bluetooth_printer.activities.print

import android.content.Context
import android.util.Log
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.R
import com.bukuwarung.bluetooth_printer.model.PairedDevice
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.ALIGNMENT_LEFT
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.EMPHASIZED_MODE_BOLD
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.FONT_SIZE_LARGE
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.FONT_SIZE_NORMAL
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.bluetooth_printer.model.printTypes.TextPrintType
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.bluetooth_printer.utils.PrinterCallBack
import com.bukuwarung.bluetooth_printer.utils.PrintingHelper
import com.bukuwarung.bluetooth_printer.utils.times


class BluetoothPrinter(private val context: Context, private val entryPoint: String) {

    val printingDialog: PrintingDialog = PrintingDialog(context)

    init {
        printingDialog.show()
    }

    companion object {
        private const val MAX_NO_OF_CHAR_IN_A_LINE: Int = 32
        private val stringBuilder = StringBuilder(MAX_NO_OF_CHAR_IN_A_LINE)

        private fun getTextPrintable(
            text: String,
            fontSize: Byte = BasePrinter.FONT_SIZE_NORMAL,
            alignment: Byte = BasePrinter.ALIGNMENT_CENTER,
            emphasized: Byte = BasePrinter.EMPHASIZED_MODE_NORMAL,
            underline: Byte = BasePrinter.UNDERLINED_MODE_OFF,
            lineSpacing: Byte = BasePrinter.LINE_SPACING_30
        ): TextPrintType {
            return TextPrintType.Builder()
                .setText(text)
                .setAlignment(alignment)
                .setEmphasizedMode(emphasized)
                .setFontSize(fontSize)
                .setUnderlined(underline)
                .setLineSpacing(lineSpacing)
                .setNewLinesAfter(0)
                .setCharacterCode(BasePrinter.CHARCODE_PC437_ENGLISH)
                .build() as TextPrintType
        }

        private fun setSpacingForLeftAndRightTextInSingleLine(leftText: CharSequence?, rightText: CharSequence?): String {
            val leftTextCount = leftText?.length ?: 0
            val rightTextCount = rightText?.length ?: 0
            var spaceCount = MAX_NO_OF_CHAR_IN_A_LINE - (leftTextCount + rightTextCount + 1)
            stringBuilder.clear()
            when {
                // Case 1: Left text > MAX_NO_OF_CHAR_IN_A_LINE
                leftTextCount >= MAX_NO_OF_CHAR_IN_A_LINE -> {
                    // We can print the complete left text first and then append right text
                    stringBuilder.append(leftText)
                    // Add right text right aligned
                    stringBuilder.append("\n")
                    spaceCount = MAX_NO_OF_CHAR_IN_A_LINE - rightTextCount
                    // Right text is also bigger than MAX_NO_OF_CHAR_IN_A_LINE
                    if (spaceCount < 0) {
                        // Total character count is more than MAX_NO_OF_CHAR_IN_A_LINE
                        // value can be divided into parts and right aligned with padding equal to the
                        // length of leftText
                        val remainingSpaceOnRight = (MAX_NO_OF_CHAR_IN_A_LINE - 1) / 2
                        val rightTextChunks = rightText?.chunked(remainingSpaceOnRight)
                        rightTextChunks?.forEach { text ->
                            stringBuilder.append(" ".times(MAX_NO_OF_CHAR_IN_A_LINE - text.length))
                            stringBuilder.append("$text\n")
                        }
                    } else {
                        stringBuilder.append(" ".times(MAX_NO_OF_CHAR_IN_A_LINE - rightTextCount))
                        stringBuilder.append(rightText)
                    }
                }
                // Case 2: Left + Right combined text > MAX_NO_OF_CHAR_IN_A_LINE
                spaceCount < 0 -> {
                    stringBuilder.append(leftText)
                    stringBuilder.append("  ")
                    var remainingSpaceOnRight = MAX_NO_OF_CHAR_IN_A_LINE - leftTextCount - 2
                    // If remaining space on right is lesser than half width, we can put it in the next line
                    if (remainingSpaceOnRight < MAX_NO_OF_CHAR_IN_A_LINE / 2) {
                        stringBuilder.append("\n")
                        remainingSpaceOnRight = MAX_NO_OF_CHAR_IN_A_LINE - 2
                    }
                    val rightTextChunks = rightText?.chunked(remainingSpaceOnRight)
                    rightTextChunks?.forEachIndexed { index, text ->
                        if (index > 0) {
                            stringBuilder.append(" ".times(MAX_NO_OF_CHAR_IN_A_LINE - text.length))
                        }
                        stringBuilder.append("$text\n")
                    }
                }
                // Case 3: Left + Right text < MAX_NO_OF_CHAR_IN_A_LINE
                else -> {
                    spaceCount = MAX_NO_OF_CHAR_IN_A_LINE - (leftTextCount + rightTextCount)
                    stringBuilder.append(leftText)
                    while (spaceCount != 0) {
                        stringBuilder.append(" ")
                        spaceCount--
                    }
                    stringBuilder.append(rightText)
                }
            }
            return stringBuilder.toString()
        }

        private fun setSpacingForLeftWithCenterAndRightTextInSingleLine(leftText: CharSequence?, centerText: CharSequence?, rightText: CharSequence?): String {
            val centerStarTextIndex = 16
            val centerTextCount = centerText?.length ?: 0
            val rightTextCount = rightText?.length ?: 0
            var spaceCount = centerStarTextIndex - (rightTextCount + centerTextCount)
            stringBuilder.clear()
            stringBuilder.append(leftText)
            while (stringBuilder.length < centerStarTextIndex) {
                stringBuilder.append(" ")
            }
            stringBuilder.append(centerText)
            while (spaceCount != 0) {
                stringBuilder.append(" ")
                spaceCount--
            }
            stringBuilder.append(rightText)
            return stringBuilder.toString()
        }

        fun appendLeft(txt: CharSequence?) = getTextPrintable(txt.toString(), alignment = ALIGNMENT_LEFT)
        fun appendLeftWithNewLine(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n", alignment = ALIGNMENT_LEFT)
        fun appendLeftWithNewLineBold(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n", alignment = ALIGNMENT_LEFT, emphasized = EMPHASIZED_MODE_BOLD)
        fun appendLeftWithNewLineBoldAndLarge(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n", alignment = ALIGNMENT_LEFT, emphasized = EMPHASIZED_MODE_BOLD, fontSize = FONT_SIZE_LARGE)

        fun appendCenter(txt: CharSequence?) = getTextPrintable(txt.toString())
        fun appendCenterWithNewLine(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n")
        fun appendCenterWithNewLineBold(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n", emphasized = EMPHASIZED_MODE_BOLD)
        fun appendCenterWithNewLineBoldAndLarge(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n", emphasized = EMPHASIZED_MODE_BOLD, fontSize = FONT_SIZE_LARGE)

        fun appendRight(txt: CharSequence?) = getTextPrintable(txt.toString(), alignment = BasePrinter.ALIGNMENT_RIGHT)
        fun appendRightWithNewLine(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n", alignment = BasePrinter.ALIGNMENT_RIGHT)
        fun appendRightWithNewLineBold(txt: CharSequence?) = getTextPrintable("${txt.toString()}\n", alignment = BasePrinter.ALIGNMENT_RIGHT, emphasized = EMPHASIZED_MODE_BOLD)

        fun appendLeftAndRight(leftText: CharSequence?, rightText: CharSequence?) = getTextPrintable(
            setSpacingForLeftAndRightTextInSingleLine(leftText, rightText), alignment = ALIGNMENT_LEFT)
        fun appendLeftWithCenterAndRight(leftText: CharSequence?, centerText: CharSequence?, rightText: CharSequence?) = getTextPrintable(
            setSpacingForLeftWithCenterAndRightTextInSingleLine(leftText, centerText, rightText), alignment = ALIGNMENT_LEFT)
        fun appendLeftAndRightLargeAndBold(leftText: CharSequence?, rightText: CharSequence?) = getTextPrintable(
            setSpacingForLeftAndRightTextInSingleLine(leftText, rightText), alignment = ALIGNMENT_LEFT, emphasized = EMPHASIZED_MODE_BOLD, fontSize = FONT_SIZE_LARGE)
        fun appendLeftAndRightBold(leftText: CharSequence?, rightText: CharSequence?) = getTextPrintable(
            setSpacingForLeftAndRightTextInSingleLine(leftText, rightText), alignment = ALIGNMENT_LEFT, emphasized = EMPHASIZED_MODE_BOLD, fontSize = FONT_SIZE_NORMAL)


        fun appendDashLine() = getTextPrintable("- - - - - - - - - - - - - - - -\n")
        fun appendExtraLine() = getTextPrintable("\n") // 32 whitespaces
    }

    fun printText(text: String, callback: PermissionCallback) {
        printTextToAllInstalledPrinters(text, callback)
    }

    fun printPrintables(printableList: ArrayList<BasePrintType>, callback: PermissionCallback) {
        val printerList = PrinterPrefManager(context).installedPrinters
        printerList?.forEach {
            val printingHelper = BluetoothDevices.printer(PairedDevice(it.name, it.macAddress))
            setPrintingCallback(printingHelper, it)
            printingHelper.print(printableList, callback)
        }
    }

    private fun printTextToAllInstalledPrinters(text: String, callback: PermissionCallback) {
        val printerList = PrinterPrefManager(context).installedPrinters
        printerList?.forEach {
            val printingHelper = BluetoothDevices.printer(PairedDevice(it.name, it.macAddress))
            setPrintingCallback(printingHelper, it)
            printPrintables(text, printingHelper, callback)
        }
    }

    private fun printPrintables(text: String, printingHelper: PrintingHelper, callback: PermissionCallback) {
        val printable = java.util.ArrayList<BasePrintType>()
        val printables = TextPrintType.Builder()
            .setText(text) //The text you want to print
            .setAlignment(BasePrinter.ALIGNMENT_CENTER)
            .setEmphasizedMode(BasePrinter.EMPHASIZED_MODE_BOLD) //Bold or normal
            .setFontSize(BasePrinter.FONT_SIZE_NORMAL)
            .setUnderlined(BasePrinter.UNDERLINED_MODE_OFF) // Underline on/off
            .setCharacterCode(BasePrinter.CHARCODE_PC437_ENGLISH) // Character code to support languages
            .setLineSpacing(BasePrinter.LINE_SPACING_30)
            .setNewLinesAfter(0) // To provide n lines after sentence
            .build() as TextPrintType

        printable.add(printables)
        printingHelper.print(printable, callback)
    }

    private fun setPrintingCallback(printingHelper: PrintingHelper, printerDataHolder: PrinterDataHolder) {
        printingHelper.printerCallBack = object : PrinterCallBack {
            override fun connectingWithPrinter() {
                Log.d("BluetoothPrinter", "Connecting with Printer")
            }

            override fun printingOrderSentSuccessfully() {
                printingDialog.setState(PrintingDialog.SUCCESS, context.getString(R.string.receipt_printed))
                triggerAnalyticEvent(true, printerDataHolder, null)
                printingHelper.printerCallBack = null
                Log.d("BluetoothPrinter", "Print Order sent successfully")
            }

            override fun connectionFailed(error: String) {
                printingDialog.setState(PrintingDialog.FAILED, context.getString(R.string.cant_reach_printer))
                triggerAnalyticEvent(false, printerDataHolder, error)
                printingHelper.printerCallBack = null
                Log.d("BluetoothPrinter", "Connection Failed $error")
            }

            override fun onError(error: String) {
                printingDialog.setState(PrintingDialog.FAILED, context.getString(R.string.something_when_wrong_with_code).format(error))
                triggerAnalyticEvent(false, printerDataHolder, null)
                printingHelper.printerCallBack = null
                Log.d("BluetoothPrinter", "Connection on Error $error")
            }

            override fun onMessage(message: String) {
                Log.d("BluetoothPrinter", "Connection OnMessage $message")
            }

            override fun disconnected() {
                printingHelper.printerCallBack = null
                Log.d("BluetoothPrinter", "Connection disconnected")
            }
        }
    }

    private fun triggerAnalyticEvent(isSuccess: Boolean, printer: PrinterDataHolder, message: String?) {

    }
}
