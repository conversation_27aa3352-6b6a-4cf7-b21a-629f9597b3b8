package com.bukuwarung.bluetooth_printer.activities.print

import android.content.Context
import android.os.Bundle
import com.bukuwarung.bluetooth_printer.base.BaseDialog
import com.bukuwarung.bluetooth_printer.R
import com.google.android.material.button.MaterialButton

class BluetoothRequestDialog(
    context: Context,
    private val entryPoint: String,
    private val isAgree: (agreeToEnableBT: Boolean) -> Unit
) : BaseDialog(context) {
    override fun getResId(): Int = R.layout.dialog_enable_bluetooth_request

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        super.onCreate(savedInstanceState)
        setupView()
    }

    private fun setupView() {
        findViewById<MaterialButton>(R.id.btn_agree).setOnClickListener {
//            val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
//            AppAnalytics.trackEvent(AnalyticsConst.EVENT_BT_ADVANCE_TO_ENABLE, prop)
            isAgree(true)
            dismiss()
        }

        setOnCancelListener {
            isAgree(false)
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        isAgree(false)
    }
}