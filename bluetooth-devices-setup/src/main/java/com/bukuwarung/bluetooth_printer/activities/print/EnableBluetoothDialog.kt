package com.bukuwarung.dialogs.printer

import android.content.Context
import android.os.Bundle
import com.bukuwarung.bluetooth_printer.base.BaseDialog
import com.bukuwarung.bluetooth_printer.R
import com.google.android.material.button.MaterialButton

/**
 * created by <PERSON><PERSON><PERSON><PERSON> at 19/06/20
 */

class EnableBluetoothDialog(
        context: Context,
        private val entryPoint: String,
        private val onDismiss: (EnableBluetoothDialog) -> Unit,
        private val onCancel: (EnableBluetoothDialog) -> Unit
) : BaseDialog(context) {
    override fun getResId(): Int = R.layout.dialog_enable_bluetooth_request

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        val minWidth = (context.resources.displayMetrics.widthPixels*0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        setupView()
    }

    private fun setupView() {
         findViewById<MaterialButton>(R.id.btn_agree).setOnClickListener {
            onDismiss(this)
        }

        setOnCancelListener {
            onCancel(this)
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        onCancel(this)
    }
}