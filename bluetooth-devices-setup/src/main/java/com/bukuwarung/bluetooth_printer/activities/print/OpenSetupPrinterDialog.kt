package com.bukuwarung.bluetooth_printer.activities.print

import android.content.Context
import android.os.Bundle
import com.bukuwarung.bluetooth_printer.base.BaseDialog
import com.bukuwarung.bluetooth_printer.R
import com.google.android.material.button.MaterialButton

class OpenSetupPrinterDialog(
        context: Context,
        private val callBack: () -> Unit
) : BaseDialog(context) {
    override fun getResId(): Int = R.layout.dialog_open_setup_printer

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        super.onCreate(savedInstanceState)


        findViewById<MaterialButton>(R.id.btnClose).setOnClickListener { dismiss() }
        findViewById<MaterialButton>(R.id.btnGo).setOnClickListener {
            dismiss()
            callBack()
        }
    }

}