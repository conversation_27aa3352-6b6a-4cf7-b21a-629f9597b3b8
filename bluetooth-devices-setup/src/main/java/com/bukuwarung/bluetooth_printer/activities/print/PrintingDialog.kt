package com.bukuwarung.bluetooth_printer.activities.print

import android.animation.Animator
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.widget.TextView
import com.airbnb.lottie.LottieAnimationView
import com.bukuwarung.bluetooth_printer.base.BaseDialog
import com.bukuwarung.bluetooth_printer.R

class PrintingDialog(context: Context) : BaseDialog(context) {
    private val handler = Handler()
    private val animationListener = object : Animator.AnimatorListener {
        override fun onAnimationStart(animation: Animator) {}

        override fun onAnimationEnd(animation: Animator) {
            handler.postDelayed({ dismiss() }, 1000)
        }

        override fun onAnimationCancel(animation: Animator) {}

        override fun onAnimationRepeat(animation: Animator) {}
    }

    override fun getResId(): Int = R.layout.dialog_printing_process

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(false)
        val minWidth = (context.resources.displayMetrics.widthPixels*0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        setState(LOADING, context.getString(R.string.printing))
    }

    // 0-324 || 418-693 loading
    // 325 - 390 success
    // 693 - 822 fail

    fun setState(state: String, message: String) {
        findViewById<TextView>(R.id.tvMessage).text = message
        when (state) {
            LOADING -> {
                findViewById<LottieAnimationView>(R.id.lottie_progress).setMinAndMaxFrame(0, 324)
            }
            SUCCESS -> {
                findViewById<LottieAnimationView>(R.id.lottie_progress).apply {
                    repeatCount = 0
                    setMinAndMaxFrame(325, 390)
                    addAnimatorListener(animationListener)
                }
            }
            FAILED -> {
                findViewById<LottieAnimationView>(R.id.lottie_progress).apply {
                    repeatCount = 0
                    setMinAndMaxFrame(693, 822)
                    addAnimatorListener(animationListener)
                }
            }
        }
    }

    companion object {
        const val LOADING = "LOADING"
        const val SUCCESS = "SUCCESS"
        const val FAILED = "FAILED"
    }
}