package com.bukuwarung.bluetooth_printer.activities.print.adapter

import android.annotation.SuppressLint
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity.Companion.CARD_READER
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity.Companion.PRINTER
import com.bukuwarung.bluetooth_printer.databinding.PairedCardReaderItemBinding
import com.bukuwarung.bluetooth_printer.databinding.PairedPrinterItemBinding
import com.bukuwarung.bluetooth_printer.databinding.PrinterItemBinding

class PrinterAdapter(
    context: Context,
    private val printerCallback: ((PrinterDataHolder) -> Unit)?,
    private val testPrinterCallBack: ((PrinterDataHolder) -> Unit)?,
    private val settingPrinterCallBack: ((PrinterDataHolder) -> Unit)?,
    private val deviceType: String,
    private val registeredDevicesSN: List<String>
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val btAdapter = (context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager).adapter
    private val pref = PrinterPrefManager(context)
    private val data = mutableListOf<BasePrinterDataHolder>()

    var paringDevice:PrinterDataHolder? = null

    fun resetClickFlag(printer: PrinterDataHolder?) {
        paringDevice = printer
        notifyDataSetChanged()
    }
    fun printers() = data
        .filter { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER || it.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER }
        .map { it as PrinterDataHolder }

    fun addInstalledPrinter(btDevice: BluetoothDevice, updatedName: String?) {
        val isPrinterPresent = printers().firstOrNull { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER && it.macAddress == btDevice.address }
        if (isPrinterPresent == null) {
            val installedPrinter = PrinterDataHolder(updatedName ?: btDevice.name, btDevice.address, isPairing = false, device = btDevice).apply {
                viewType = BasePrinterDataHolder.INSTALLED_PRINTER
            }
            data.clear()
            data.add(installedPrinter)
            saveInstalledPrinters()
        }
        if(deviceType == CARD_READER) {
            saveCardReaderAddress(btDevice.address)
        }else{
            savePrinterAddress(btDevice.address)
        }
    }

    fun showInstalledDevices() {
        data.clear()
        val installedDevices = if(deviceType == SetupBluetoothDeviceActivity.PRINTER) pref.installedPrinters else pref.installedCardReaders
        installedDevices?.forEach { printer ->
            printer.viewType = BasePrinterDataHolder.INSTALLED_PRINTER
            printer.device = getDevice(printer.macAddress)
            data.add(printer)
        }
        getInstalledPrinterDeviceList().forEach { btDevice ->
            addInstalledPrinter(btDevice, null)
        }
        notifyDataSetChanged()
    }

    fun getDevice(address: String): BluetoothDevice? {
        if (!btAdapter.isEnabled) btAdapter.enable()
        val pairedDevices: Set<BluetoothDevice> = btAdapter.bondedDevices
        for (d in pairedDevices) if (d.address.equals(address, ignoreCase = true)) return d
        return null
    }

    private fun getInstalledPrinterDeviceList(): ArrayList<BluetoothDevice> {
        val installedPrinterList = ArrayList<BluetoothDevice>()
        if (!btAdapter.isEnabled) btAdapter.enable()
        val pairedDevices: Set<BluetoothDevice> = btAdapter.bondedDevices
        for (d in pairedDevices) {
            if (isPrinterDevice(d)) {
                installedPrinterList.add(d)
            }
        }
        return installedPrinterList
    }

    @SuppressLint("MissingPermission")
    private fun isPrinterDevice(device: BluetoothDevice?): Boolean {
        if (device?.name.isNullOrBlank() || device?.address.isNullOrBlank()) {
            return false
        }
        device?.let {
            val majorDeviceClass = device.bluetoothClass.majorDeviceClass
            val deviceClass = device.bluetoothClass.deviceClass
            val imagingDeviceCode = BluetoothClass.Device.Major.IMAGING
            val uncategorizedDeviceCode = BluetoothClass.Device.Major.UNCATEGORIZED

            if(deviceType == SetupBluetoothDeviceActivity.CARD_READER){
                try {
                    val nameMatch = device.name.contains("Suppay", true) || device.name.contains("MP-", true)
                    Log.d("card_reader","name match ${device.name}: $nameMatch")
                    return nameMatch
                }catch (e:Exception){
                    return majorDeviceClass == BluetoothClass.Device.Major.AUDIO_VIDEO && (deviceClass == 1060)
                }
            }

            if (majorDeviceClass == imagingDeviceCode && (deviceClass == imagingDeviceCode || deviceClass == 1664)) {
                return true
            }
            // Hack to recognise the bluetooth/thermal printer, sometimes Bluetooth SIG doesn't assign the device class, so banking on name contains Printer
            else if (majorDeviceClass == uncategorizedDeviceCode && deviceClass == uncategorizedDeviceCode && (device.name.contains("print", true) || device.name.contains("pencetak", true))) {
                return true
            } else if (device.name.contains("printer", true) || device.name.contains("pencetak", true)) {
                return true
            }
        }
        return false
    }

    private fun saveInstalledPrinters() {
        val installedPrinterList = printers().filter { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER }
        if(deviceType == SetupBluetoothDeviceActivity.PRINTER)
            pref.installedPrinters = installedPrinterList
        else
            pref.installedCardReaders = installedPrinterList
    }

    private fun saveCardReaderAddress(btAddress:String) {
        pref.cardReaderAddress = btAddress
    }

    private fun savePrinterAddress(btAddress:String) {
        pref.printerAddress = btAddress
    }

    fun updatePrinterName(printer: PrinterDataHolder, updatedName: String) {
        getDevice(printer.macAddress)?.let {
            removeInstalledPrinter(printer)
            addInstalledPrinter(it, updatedName)
        }
    }

    fun removeInstalledPrinter(printer: PrinterDataHolder) {
        val basePrinterDataHolder = printer.apply {
            viewType = BasePrinterDataHolder.INSTALLED_PRINTER
        }
        try {
            if (pref.cardReaderAddress == printer.macAddress) {
                saveCardReaderAddress("")
            } else if (pref.printerAddress == printer.macAddress) {
                savePrinterAddress("")
            }
        }catch (e:Exception){
            e.printStackTrace()
        }
        data.remove(basePrinterDataHolder)
        notifyDataSetChanged()
        saveInstalledPrinters()
    }

    fun addAvailablePrinter(btDevice: BluetoothDevice) {
        if (btDevice.name == null || btDevice.address == null) {
            return
        }

        // check if the device is already added
        val existingPrinter = printers().find { it.macAddress == btDevice.address }
        if (existingPrinter != null) return

        val newPrinter = PrinterDataHolder(btDevice.name, btDevice.address, isPairing = false, device = btDevice).apply {
            viewType = BasePrinterDataHolder.AVAILABLE_PRINTER
        }
        data.add(newPrinter)
        notifyDataSetChanged()
    }

    fun setScanningStatus(isScanning: Boolean) {
        if (isScanning) {
            // remove all discovered printer
            printers().filter { it.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER }
                .forEach { data.remove(it) }
        }
    }

    fun isAvailablePrinterEmpty() = printers().none { it.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER }

    fun isInstalledPrinterEmpty() = printers().none { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER }

    fun isInstalledDevicesEmpty() = printers().none { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER }

    override fun getItemViewType(position: Int): Int = data[position].viewType

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            BasePrinterDataHolder.AVAILABLE_PRINTER -> {
                val binding = PrinterItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                AvailablePrinterViewHolder(binding,this, (deviceType == PRINTER))
            }
            BasePrinterDataHolder.INSTALLED_PRINTER -> {
                if(deviceType == CARD_READER){
                    val binding = PairedCardReaderItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                    InstalledCardReaderViewHolder(binding)
                }else {
                    val binding = PairedPrinterItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                    InstalledPrinterViewHolder(binding, deviceType)
                }
            }
            else -> {
                val binding = PrinterItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                AvailablePrinterViewHolder(binding,this, (deviceType == PRINTER))
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val singleData = data[position]) {
            is PrinterDataHolder -> {
                if (holder is InstalledPrinterViewHolder) {
                    if (testPrinterCallBack != null && settingPrinterCallBack != null) {
                        holder.bind(singleData, testPrinterCallBack, settingPrinterCallBack)
                    }
                } else if (holder is InstalledCardReaderViewHolder) {
                    if (testPrinterCallBack != null && settingPrinterCallBack != null) {
                        holder.bind(singleData, registeredDevicesSN, settingPrinterCallBack)
                    }
                } else {
                    if (printerCallback != null) {
                        (holder as AvailablePrinterViewHolder).bind(
                            singleData,
                            registeredDevicesSN,
                            printerCallback
                        )
                    }
                }
            }
        }
    }

    override fun getItemCount(): Int = data.size
}