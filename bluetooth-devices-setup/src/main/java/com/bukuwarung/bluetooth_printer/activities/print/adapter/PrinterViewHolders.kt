package com.bukuwarung.bluetooth_printer.activities.print.adapter

import android.bluetooth.BluetoothDevice
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.bluetooth_printer.databinding.PairedCardReaderItemBinding
import com.bukuwarung.bluetooth_printer.databinding.PairedPrinterItemBinding
import com.bukuwarung.bluetooth_printer.databinding.PrinterItemBinding
import com.bukuwarung.bluetooth_printer.utils.hideView
import com.bukuwarung.bluetooth_printer.utils.showView


abstract class BasePrinterDataHolder {
    var viewType: Int = 0

    companion object TAG {
        // keep the order for easier sorting
        const val INSTALLED_PRINTER = 2
        const val AVAILABLE_PRINTER = 4
    }
}

data class PrinterDataHolder(
    var name: String,
    val macAddress: String,
    var device: BluetoothDevice? = null,
    var isDefault: Boolean = false,
    var isPairing: Boolean = false
) : BasePrinterDataHolder()

class AvailablePrinterViewHolder(
    private val binding: PrinterItemBinding,
    private val adapter: PrinterAdapter,
    private val isPrinterDevice: Boolean
) : RecyclerView.ViewHolder(binding.root) {

    fun bind(
        printer: PrinterDataHolder,
        registeredDeviceSNList: List<String>?,
        callBack: (PrinterDataHolder) -> Unit
    ) {
        binding.apply {
            if (adapter.paringDevice != null) {
                Log.d("--->","paringDevice is not null")
                if (adapter.paringDevice?.device?.name == printer.device?.name){
                    Log.d("--->","paringDevice is not null and name is same" )
                    binding.tvConnect.hideView()
                    binding.progressBarPairPrinter.showView()
                } else{
                    Log.d("--->","paringDevice is not null and name is not same" )
                    binding.tvConnect.hideView()
                    binding.progressBarPairPrinter.hideView()
                }
            } else {
                Log.d("--->","paringDevice is not null")
                binding.tvConnect.showView()
                binding.progressBarPairPrinter.hideView()
            }
            tvConnect.setOnClickListener {
                Log.d("--->", "connect ${printer.macAddress}"+printer.name)
                callBack(printer)
                if(isPrinterDevice || BluetoothDevices.isSelectedDeviceARegisteredDevice(printer.name, registeredDeviceSNList)) {
                    binding.tvConnect.hideView()
                    binding.progressBarPairPrinter.showView()
                }
            }

            if (BluetoothDevices.isSelectedDeviceARegisteredDevice(
                    printer.name,
                    registeredDeviceSNList
                )
            ) {
                binding.tvDeviceStatus.showView()
            } else {
                binding.tvDeviceStatus.hideView()
            }


            binding.tvPrinterName.text = printer.name
            binding.tvPrinterAddress.text = printer.macAddress
        }
    }
}

class InstalledPrinterViewHolder(private val binding: PairedPrinterItemBinding,private val deviceType:String) : RecyclerView.ViewHolder(binding.root) {

    fun bind(printer: PrinterDataHolder, testCallBack: (PrinterDataHolder) -> Unit, settingCallBack: (PrinterDataHolder) -> Unit) {
        binding.root.apply {
            binding.tvPrinterName.text = printer.name
            binding.tvPrinterAddress.text = printer.macAddress
        }
        binding.tvTestPrinter.setOnClickListener {
            testCallBack(printer)
        }
        if(deviceType == SetupBluetoothDeviceActivity.CARD_READER){
            binding.tvTestPrinter.visibility = View.GONE
        }

        binding.ivSetting.setOnClickListener {
            settingCallBack(printer)
        }
    }
}

class InstalledCardReaderViewHolder(private val binding: PairedCardReaderItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

    fun bind(
        printer: PrinterDataHolder,
        registeredDeviceSNList: List<String>?,
        settingCallBack: (PrinterDataHolder) -> Unit
    ) {
        binding.root.apply {
            binding.tvDeviceName.text = printer.name
            binding.tvDeviceAddress.text = printer.macAddress
        }


        if (BluetoothDevices.isSelectedDeviceARegisteredDevice(
                printer.name,
                registeredDeviceSNList
            )
        ) {
            binding.tvDeviceStatus.showView()
        } else {
            binding.tvDeviceStatus.hideView()
        }



        binding.ivSetting.setOnClickListener {
            settingCallBack(printer)
        }
    }
}