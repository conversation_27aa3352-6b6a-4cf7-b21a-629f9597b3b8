package com.bukuwarung.bluetooth_printer.activities.print.setup

import android.annotation.SuppressLint
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import android.content.Intent
import android.util.Log
import androidx.activity.viewModels
import androidx.appcompat.content.res.AppCompatResources
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.analytic.BtAnalyticConstant.DEVICE_SERVICE_MOREFUN
import com.bukuwarung.analytic.BtAnalyticConstant.DEVICE_SERVICE_TIANYU
import com.bukuwarung.analytic.BtAnalyticConstant.EDC_BRAND
import com.bukuwarung.analytic.BtAnalyticConstant.EVENT_CONFIRMED_TO_PAIR_SAKU_POP_UP
import com.bukuwarung.analytic.BtAnalyticConstant.EVENT_EDC_ACTIVATION_API_HIT_FRONTEND
import com.bukuwarung.analytic.BtAnalyticConstant.EVENT_EDC_ACTIVATION_RESULT
import com.bukuwarung.analytic.BtAnalyticConstant.FAILED
import com.bukuwarung.analytic.BtAnalyticConstant.MOREFUN
import com.bukuwarung.analytic.BtAnalyticConstant.MP_
import com.bukuwarung.analytic.BtAnalyticConstant.PAIRING_STATUS
import com.bukuwarung.analytic.BtAnalyticConstant.PAIR_INDIVIDUAL_SAKU_CLICKED
import com.bukuwarung.analytic.BtAnalyticConstant.SAKU_NAME
import com.bukuwarung.analytic.BtAnalyticConstant.SAKU_PAIRING_RESULT
import com.bukuwarung.analytic.BtAnalyticConstant.SUCCESS
import com.bukuwarung.analytic.BtAnalyticConstant.SUPPAY
import com.bukuwarung.analytic.BtAnalyticConstant.TIANYU
import com.bukuwarung.analytic.BtAnalytics
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.R
import com.bukuwarung.bluetooth_printer.activities.print.adapter.BasePrinterDataHolder
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterAdapter
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.bluetooth_printer.base.BaseActivity
import com.bukuwarung.bluetooth_printer.databinding.ActivityBluetoothPrinterScanBinding
import com.bukuwarung.bluetooth_printer.model.BluetoothDeviceDiscoveryCallBack
import com.bukuwarung.bluetooth_printer.utils.BluetoothConnection
import com.bukuwarung.bluetooth_printer.utils.StateDialog
import com.bukuwarung.bluetooth_printer.utils.hideView
import com.bukuwarung.bluetooth_printer.utils.showSnackbar
import com.bukuwarung.bluetooth_printer.utils.showView
import com.bukuwarung.bluetooth_printer.utils.singleClick
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.cardreader.CardReaderType
import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.Status
import com.bukuwarung.utils.AppBluetoothBridge
import com.bukuwarung.zoho.BtZohoChat
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class BluetoothDeviceScanActivity : BaseActivity() {
    private lateinit var binding: ActivityBluetoothPrinterScanBinding
    private lateinit var printerAdapter: PrinterAdapter
    private var bluetoothConnection: BluetoothConnection? = null
    private var unrecognisedDeviceFound: Boolean = false
    private var deviceType: String = SetupBluetoothDeviceActivity.PRINTER
    private var registeredDevices = emptyList<String>()
    private var selectedBtDevice: BluetoothDevice? = null
    @Inject
    lateinit var appBluetoothBridge: AppBluetoothBridge
    private val viewModel: BluetoothDeviceScanViewModel by viewModels()
    var dialog: StateDialog? = null
    private lateinit var intentForPairDevice: String

    @SuppressLint("MissingPermission")
    private val printerCallback: (PrinterDataHolder) -> Unit = { printer ->
        // removed this as this would be always be false now.

        if (printer.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER) {
            printer.device?.let {
                Log.d("--->", "printerCallback device = $it")
                when (it.bondState) {
                    BluetoothDevice.BOND_NONE -> {
                        selectedBtDevice = it
                        if (deviceType == SetupBluetoothDeviceActivity.CARD_READER) {
                            intentForPairDevice =
                                if (BluetoothDevices.isSelectedDeviceARegisteredDevice(
                                        printer.name,
                                        registeredDevices
                                    )
                                ) {
                                    IntentForPairDevice.TRANSACTION.name
                                } else {
                                    IntentForPairDevice.ACTIVATION.name
                                }
                            val props = HashMap<String,String>()
                            props[EDC_BRAND] = if(it.name.contains(MP_,true)) MOREFUN else TIANYU
                            props[SAKU_NAME] = it.name
                            BtAnalytics.trackEventMobile(PAIR_INDIVIDUAL_SAKU_CLICKED, props)
                        }

                        bluetoothConnection?.pair(it)
                        printerAdapter.resetClickFlag(printer)
                    }
                    else -> {}
                }
            }
        }
//        if(deviceType == SetupBluetoothDeviceActivity.PRINTER || BluetoothDevices.isSelectedDeviceARegisteredDevice(printer.name, registeredDevices)){
//            Log.d("--->", "printerCallback called = " + printer.toString())
//        } else {
//            // added this to show dialog when selected device is not registered
//            //This logic not required now.
////            viewModel.getMappedPhoneNumber(printer.name)
//        }
    }

    private fun showConfirmDeviceActivationDialog(serialNumber: String, deviceName: String) {
        dialog?.dismiss()
        val props = HashMap<String, String>()
        props["sn_selected"] = serialNumber
        BtAnalytics.trackEventMobile(EVENT_CONFIRMED_TO_PAIR_SAKU_POP_UP, props)
        dialog = StateDialog(this)
            .setTitle(getString(R.string.activate_device))
            .setSubTitle(getString(R.string.activate_device_message))
            .setBtnLeftText(getString(R.string.batal))
            .setBtnRightText(getString(R.string.connect_printer))
            .setBtnLeftListener { dialog?.dismiss() }
            .setDialogType(StateDialog.DialogType.ACTIVATION)
            .sendSerialNumber(deviceName)
            .setBtnRightListener {
                props["order_type"] = "non_partnerships"
                BtAnalytics.trackEventMobile(EVENT_EDC_ACTIVATION_API_HIT_FRONTEND, props)
                dialog?.setLoader(true)
                val request = ActivateDeviceRequest(
                    serialNumber = serialNumber,
                    type = AppType.NON_PARTNERSHIP.name
                )
                viewModel.activateDevice(request)
            }
            .setBtnLeftListener {
                selectedBtDevice?.let { device ->
                    // Unpair the device
                    bluetoothConnection?.unPair(device)
                    // Find the PrinterDataHolder in the adapter
                    val printer = printerAdapter.printers().find { it.macAddress == device.address }
                    printer?.let {
                        // Remove from installed and add as available
                        printerAdapter.removeInstalledPrinter(it)
                        printerAdapter.addAvailablePrinter(device)
                        printerAdapter.resetClickFlag(null) // Reset pairing flag
                    }
                }
            }
            .showDialog()
    }

    private fun handleWhenSelectedDeviceNotRegistered() {
        dialog?.dismiss()
        dialog = StateDialog(this)
            .setTitle(getString(R.string.terminal_inactive_title))
            .setSubTitle(getString(R.string.terminal_inactive_message).plus("\n[E06]"))
            .setImage(R.drawable.ic_system_error)
            .setBtnRightText(getString(R.string.contact_cs))
            .setBtnRightListener { BtZohoChat.redirectToZohoChat() }
            .showDialog()
        bwLog(Exception("E06"))
    }

    override fun onResume() {
        super.onResume()
        Log.d("--->", "onResume called $localClassName")
    }

    override fun setViewBinding() {
        deviceType = intent.getStringExtra(SetupBluetoothDeviceActivity.DEVICE_TYPE)
            ?: SetupBluetoothDeviceActivity.PRINTER
        registeredDevices =
            intent.getStringArrayListExtra(SetupBluetoothDeviceActivity.DEVICE_SN_LIST)
                ?: emptyList()
        binding = ActivityBluetoothPrinterScanBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        Log.d("--->", "onCreate called $localClassName")
        bluetoothConnection = BluetoothConnection(this)

        binding.info.tvInfoTitle.text = getString(R.string.registered_edc_device)
        binding.ivBack.setOnClickListener {
            finish()
        }
        binding.noPrinterView.tvNoPrinter.text =
            if (deviceType == SetupBluetoothDeviceActivity.PRINTER) getString(R.string.belum_ada_printer_ditambahkan) else getString(
                R.string.no_card_reader
            )
        binding.noPrinterView.tvPrinterHint.text =
            if (deviceType == SetupBluetoothDeviceActivity.PRINTER) getString(R.string.no_printer_hint) else getString(
                R.string.no_card_reader_hint
            )
        binding.noPrinterView.ivNoPrinter.setImageDrawable(
            AppCompatResources.getDrawable(
                this,
                if (deviceType == SetupBluetoothDeviceActivity.PRINTER)
                    R.drawable.vector_no_printer
                else
                    R.drawable.ic_card_reader
            )
        )

        if(deviceType == SetupBluetoothDeviceActivity.CARD_READER){
            binding.title.text = getString(R.string.card_reader_title)
            binding.info.root.showView()
        }else {
            binding.info.root.hideView()
            binding.title.text = "Bluetooth"
        }

        binding.ivRefresh.singleClick {
            startScan()
        }

        binding.noPrinterView.btnRefreshScanPrinter.singleClick {
            startScan()
        }

        if (!::printerAdapter.isInitialized) {
            printerAdapter =
                PrinterAdapter(this, printerCallback, null, null, deviceType, registeredDevices)
        }

        binding.rvPrinter.apply {
            layoutManager = LinearLayoutManager(this@BluetoothDeviceScanActivity)
            adapter = printerAdapter
        }

        binding.ivHelp.setOnClickListener {
            BtZohoChat.redirectToZohoChat()
        }
    }

    override fun onStart() {
        super.onStart()
        Log.d("--->", "onStart called $localClassName")
        initScanDeviceCallback()
        bluetoothConnection?.onStart()
        startScan()
    }

    @SuppressLint("MissingPermission")
    private fun initScanDeviceCallback() {
        bluetoothConnection?.setDiscoveryCallback(object : BluetoothDeviceDiscoveryCallBack {
            override fun onDiscoveryStarted() {
                Log.d("--->", "onDiscoveryStarted")
                printerAdapter.setScanningStatus(true)
            }

            override fun onDiscoveryFinished() {
                Log.d("--->", "onDiscoveryFinished")
                printerAdapter.setScanningStatus(false)
                if (printerAdapter.isAvailablePrinterEmpty()) {
                    setUpNoDevicesAvailable()
                    triggerSearchCompleteEvent(false, unrecognisedDeviceFound)
                } else {
                    resetLayout(false)
                    triggerSearchCompleteEvent(true, unrecognisedDeviceFound)
                }
                unrecognisedDeviceFound = false
            }

            override fun onDeviceFound(device: BluetoothDevice) {
                Log.d("--->", "onDeviceFound = $device")
                if (device.bondState != BluetoothDevice.BOND_BONDED && isPrinterDevice(device)) {
                    Log.d("--->", "device not bound device = $device")
                    binding.scanningPrinterView.layoutScanningPrinter.hideView()
                    printerAdapter.addAvailablePrinter(device)
                } else if (device.bondState != BluetoothDevice.BOND_BONDED && device.bluetoothClass.majorDeviceClass == BluetoothClass.Device.Major.UNCATEGORIZED) {
                    unrecognisedDeviceFound = true
                }
            }

            override fun onDevicePaired(device: BluetoothDevice) {
                // automatically go back to previous activity once the printer is successfully paired
                printerAdapter.addInstalledPrinter(device, null)
                triggerPairingCompleteEvent(true)
                if (deviceType == SetupBluetoothDeviceActivity.CARD_READER) {
                    val deviceBrand = if (device.name.startsWith(MP_, true)) MOREFUN
                    else if (device.name.startsWith(SUPPAY, true)) TIANYU
                    else "-"

                    if (deviceBrand == TIANYU) {
                        CardReaderHelper.getInstance().initCardReaderHelper(CardReaderType.TIANYU)
                        BtAnalytics.setDeviceName(DEVICE_SERVICE_TIANYU)
                    } else if (deviceBrand == MOREFUN) {
                        CardReaderHelper.getInstance().initCardReaderHelper(CardReaderType.MOREFUN)
                        BtAnalytics.setDeviceName(DEVICE_SERVICE_MOREFUN)
                    }

                    val props = HashMap<String, String>()
                    props[EDC_BRAND] = deviceBrand
                    props[SAKU_NAME] = device.name
                    props[PAIRING_STATUS] = SUCCESS
                    BtAnalytics.trackEventMobile(SAKU_PAIRING_RESULT, props)
                    val isConnected = BluetoothDevices.getPairedCardReader()?.let {
                        CardReaderHelper.getInstance().connectToDevice(it)
                    }
                    if (intentForPairDevice == IntentForPairDevice.ACTIVATION.name) {
                        if (isConnected == true) {
                            val cardInfo = CardReaderHelper.getInstance().getCardReaderInfo()
                            showConfirmDeviceActivationDialog(cardInfo.deviceSn, device.name)
                        }
                    } else {
                        setResult(RESULT_OK)
                        finish()
                    }
                } else {
                    setResult(RESULT_OK)
                    finish()
                }
            }

            override fun onDeviceUnpaired(device: BluetoothDevice) {
                val pairedPrinter = BluetoothDevices.getPairedPrinter()
                if (pairedPrinter != null && pairedPrinter.address == device.address)
                    BluetoothDevices.removeCurrentPrinter()
            }

            override fun onError(message: String) {
                binding.scanningPrinterView.layoutScanningPrinter.hideView()
                if (deviceType == SetupBluetoothDeviceActivity.CARD_READER) {
                    val props = HashMap<String, String>()
                    selectedBtDevice?.let {
                        props[EDC_BRAND] = if (it.name.contains(MP_, true)) MOREFUN else TIANYU
                        props[SAKU_NAME] = it.name
                    }
                    props[PAIRING_STATUS] = FAILED
                    BtAnalytics.trackEventMobile(SAKU_PAIRING_RESULT, props)
                }
                showSnackBarForPairingFailed()
                printerAdapter.resetClickFlag(null)
                triggerPairingCompleteEvent(false, message)
                Log.d("sacnprinter", message)
            }
        })
    }

    private fun startScan() {
        resetLayout(true)
        bluetoothConnection?.startScanning()
        bluetoothConnection?.stopScanning(16000) // Stop scanning for devices after 16 secs
        printerAdapter.setScanningStatus(true)
        binding.scanningPrinterView.layoutScanningPrinter.showView()
        binding.scanningPrinterView.tvScanningPrinterDesc.text =
            if (deviceType == SetupBluetoothDeviceActivity.PRINTER) getString(R.string.scanning_bluetooth_printer_desc) else getString(
                R.string.scanning_bluetooth_edc_desc)
    }

    @SuppressLint("MissingPermission")
    private fun isPrinterDevice(device: BluetoothDevice?): Boolean {
        Log.d("--->", "inside isPrinterDevice")
        device?.let {
            val majorDeviceClass = device.bluetoothClass.majorDeviceClass
            val deviceClass = device.bluetoothClass.deviceClass
            val imagingDeviceCode = BluetoothClass.Device.Major.IMAGING
            if(deviceType == SetupBluetoothDeviceActivity.PRINTER) {
                return majorDeviceClass == imagingDeviceCode && (deviceClass == imagingDeviceCode || deviceClass == 1664)
            }else{
                try {
                    if (device?.name.isNullOrBlank() || device?.address.isNullOrBlank()) {
                        return false
                    }
                    val nameMatch = device.name.contains("Suppay", true) || device.name.contains("MP-", true)
                    Log.d("--->","name match ${device.name}: $nameMatch")
                    return nameMatch
                }catch (e:Exception){
                    return majorDeviceClass == BluetoothClass.Device.Major.AUDIO_VIDEO && (deviceClass == 1060)
                }
            }
        }
        return false
    }

    private fun resetLayout(clearItems: Boolean) {
        binding.rvPrinter.showView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.scanningPrinterView.layoutScanningPrinter.hideView()
        if (clearItems) {
            binding.tvBluetoothTitle.text = getString(R.string.devices_available)
        } else {
            binding.tvBluetoothTitle.text = getString(R.string.my_devices)
        }
    }

    private fun setUpNoDevicesAvailable() {
        binding.noPrinterView.layoutNoPrinter.showView()
        binding.noPrinterView.tvPrinterHint.showView()
        binding.noPrinterView.btnRefreshScanPrinter.showView()
        binding.scanningPrinterView.layoutScanningPrinter.hideView()
        binding.rvPrinter.hideView()
        binding.noPrinterView.btnAddPrinter.hideView()
    }

    override fun onStop() {
        super.onStop()
        bluetoothConnection?.stopScanning(0)
        bluetoothConnection?.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        bluetoothConnection = null
    }

    override fun subscribeState() {

        viewModel.activateDeviceState.observe(this) {
            val map = HashMap<String, String>()
            map["order_type"] = "non_partnerships"
            when (it.status) {
                Status.SUCCESS -> {
                    map["status"] = "success"
                    map["sn_activated"] = it.data?.serialNumber.orEmpty()
                    map["tid"] = it?.data?.tid.orEmpty()
                    map["store_name"] = it?.data?.storeName.orEmpty()
                    selectedBtDevice?.let { device ->
                        // Unpair the device if still bonded
                        if (device.bondState == BluetoothDevice.BOND_BONDED) {
                            bluetoothConnection?.unPair(device)
                        }
                        // Find the PrinterDataHolder in the adapter
                        val printer = printerAdapter.printers().find { it.macAddress == device.address }
                        printer?.let {
                            // Remove from installed and add as available
                            printerAdapter.removeInstalledPrinter(it)
                            printerAdapter.addAvailablePrinter(device)
                            printerAdapter.resetClickFlag(null) // Reset pairing flag
                        }
                    }
                    setResult(RESULT_OK, Intent().apply {
                        putExtra("isActivation", true)
                    })
                    finish()
                }

                Status.LOADING -> {

                }
                Status.ERROR -> {
                    map["status"] = "failure"
                    map["failed_reason"] = it.message.orEmpty()

                    dialog?.dismiss()
                    dialog = StateDialog(this)
                        .setTitle(getString(R.string.failed_to_connect_device))
                        .setSubTitle(
                            getString(
                                R.string.failed_to_connect_device_message,
                            ))
                        .setImage(R.drawable.img_wrong_phone_number)
                        .setBtnLeftText(getString(R.string.contact_cs))
                        .setBtnLeftListener { dialog?.dismiss() }
                        .setBtnRightText(getString(R.string.retry))
                        .setBtnRightListener {
                            // need to redirection here.
                            dialog?.dismiss()
                            selectedBtDevice?.let { device ->
                                // Unpair the device if still bonded
                                if (device.bondState == BluetoothDevice.BOND_BONDED) {
                                    bluetoothConnection?.unPair(device)
                                }
                                // Find the PrinterDataHolder in the adapter
                                val printer = printerAdapter.printers().find { it.macAddress == device.address }
                                printer?.let {
                                    // Remove from installed and add as available
                                    printerAdapter.removeInstalledPrinter(it)
                                    printerAdapter.addAvailablePrinter(device)
                                    printerAdapter.resetClickFlag(null) // Reset pairing flag
                                }
                                // Optionally restart scanning to ensure the device is discoverable
//                                startScan()
                            }
                        }
                        .setBtnLeftListener {
                            dialog?.dismiss()
                            BtZohoChat.redirectToZohoChat()
                        }
                        .showDialog()
                }
                Status.NO_INTERNET -> {

                }
            }
            BtAnalytics.trackEventMobile(EVENT_EDC_ACTIVATION_RESULT, map)
        }
        viewModel.getMappedPHoneNumberState.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    dialog?.dismiss()
                    dialog = StateDialog(this)
                        .setTitle(getString(R.string.different_phone_number))
                        .setSubTitle(
                            getString(
                                R.string.different_phone_number_message,
                                it.data?.serialNumber,
                                it.data?.userPhone
                            ))
                        .setImage(R.drawable.img_wrong_phone_number)
                        .setBtnRightText(getString(R.string.login_with_registered_phone_number))
                        .setBtnRightListener { logoutAndRedirectToLoginPage() }
                        .showDialog()
                    try {
                        val props = HashMap<String, String>()
                        props["error_code"] = "E06"
                        props["source"] = "device_pairing"
                        props["dialog_type"] = "device_phone_mapping_mismatch"
                        props["device_sn_prefix"] = it.data?.serialNumber ?: ""
                        props["registered_phone"] = it.data?.userPhone ?: ""
                        BtAnalytics.trackEventMobile("show_edc_dialog", props)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                Status.LOADING -> {
                    dialog?.dismiss()
                    dialog = StateDialog(this).showLoading()
                }

                else -> {
                    dialog?.dismiss()
                    handleWhenSelectedDeviceNotRegistered()
                }
            }
        }
    }

    private fun showSnackBarForPairingFailed() {
        this.showSnackbar(
            anchorView = binding.printerSnackbarGuideline,
            message = getString(R.string.bluetooth_paired_failed_message),
            textColor = R.color.red_100,
            bgResId = R.drawable.bg_corner_red,
            icon = R.drawable.ic_warning_circle_solid,
        )

    }

    private fun triggerPairingCompleteEvent(isPaired: Boolean, failReason: String = "") {
    }

    private fun triggerSearchCompleteEvent(isSuccess: Boolean, unknownDevice: Boolean) {

    }

    private fun logoutAndRedirectToLoginPage() {
        appBluetoothBridge.clearDataAndLogout()
    }

    enum class AppType {
        PARTNERSHIP,
        NON_PARTNERSHIP
    }
    enum class IntentForPairDevice {
        ACTIVATION,
        TRANSACTION
    }
}