package com.bukuwarung.bluetooth_printer.activities.print.setup

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.ActivateDeviceResponse
import com.bukuwarung.network.model.MappedPhoneNumberResponse
import com.bukuwarung.network.model.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import javax.inject.Inject

@HiltViewModel
class BluetoothDeviceScanViewModel @Inject constructor(
    private val getMappedPhoneNumberUseCase: GetMappedPhoneNumberUseCase
) : ViewModel() {

    private val _getMappedPHoneNumberState = MutableLiveData<Resource<MappedPhoneNumberResponse>>()
    val getMappedPHoneNumberState: LiveData<Resource<MappedPhoneNumberResponse>>
        get() = _getMappedPHoneNumberState

    private val _activateDeviceState = MutableLiveData<Resource<ActivateDeviceResponse>>()
    val activateDeviceState: LiveData<Resource<ActivateDeviceResponse>>
        get() = _activateDeviceState

    fun getMappedPhoneNumber(deviceName:String) = viewModelScope.launch {
        _getMappedPHoneNumberState.postValue(Resource.loading(null))
        val response = getMappedPhoneNumberUseCase(deviceName)
        if (response.isSuccessful) {
            _getMappedPHoneNumberState.postValue(Resource.success(response.body()?.data))
        } else {
            _getMappedPHoneNumberState.postValue(
                Resource.error(
                    msg = response.errorBody().toString(), data = null
                )
            )
        }
    }

    fun activateDevice(request: ActivateDeviceRequest)= viewModelScope.launch(Dispatchers.IO) {
        val response = getMappedPhoneNumberUseCase.activateDevice(request)
        if(response.isSuccessful){
            _activateDeviceState.postValue(Resource.success(response.body()?.data))
        }else{
            try{
                val errorBody = JSONObject(response.errorBody()?.string().orEmpty())
                var message = if (errorBody.has("message")) {
                    errorBody.getString("message")
                } else {
                    "Some Unknown Error occurred"
                }
                _activateDeviceState.postValue(Resource.error(message, null))
            }catch (e:Exception){
                bwLog(e)
            }
        }
    }
}