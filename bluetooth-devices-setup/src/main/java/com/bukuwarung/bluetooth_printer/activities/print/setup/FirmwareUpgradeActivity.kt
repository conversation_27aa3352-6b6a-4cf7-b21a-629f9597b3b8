package com.bukuwarung.bluetooth_printer.activities.print.setup

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.analytic.BtAnalytics
import com.bukuwarung.bluetooth_printer.R
import com.bukuwarung.bluetooth_printer.databinding.ActivityFirmwareUpgradeBinding
import com.bukuwarung.bluetooth_printer.utils.BukuDialog
import com.bukuwarung.bluetooth_printer.utils.Utility
import com.bukuwarung.bluetooth_printer.utils.singleClick
import com.bukuwarung.cardreader.CardReaderHelper.Companion.getInstance
import com.bukuwarung.zoho.BtZohoChat
import com.whty.device.delegate.UpgradeListener
import java.io.InputStream

class FirmwareUpgradeActivity : AppCompatActivity() {
    private var progressPercentage: TextView? = null
    private var firmwareUpgradeInstruction: View? = null
    private var firmwareUpgradeInprogress: View? = null
    private var firmwareUpgradeFailedNoInternet: View? = null
    private var firmwareUpgradeFailed: View? = null

    private lateinit var binding: ActivityFirmwareUpgradeBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFirmwareUpgradeBinding.inflate(layoutInflater)
        setContentView(binding.root)


        progressPercentage = findViewById(R.id.progressPercentage)
        firmwareUpgradeInstruction = findViewById(R.id.firmware_upgrade_instruction)
        firmwareUpgradeInprogress = findViewById(R.id.firmware_upgrade_inprogress)
        firmwareUpgradeFailedNoInternet = findViewById(R.id.firmware_upgrade_fail_no_internet)
        firmwareUpgradeFailed = findViewById(R.id.firmware_upgrade_fail)
        binding.btnStartUpgrade.singleClick {
            binding.btnStartUpgrade.isEnabled = false
            startFirmwareUpgrade()
        }
        binding.btnRetryUpgradeNoInternet.singleClick {
            binding.btnRetryUpgradeNoInternet.isEnabled = false
            startFirmwareUpgrade()
        }
        binding.btnCustomerSupport.singleClick {
            BtAnalytics.trackEventMobile("firmware_upgrade_contact_support")
            BtZohoChat.redirectToZohoChat()
        }
    }

    private fun showConfirmationDialog() {
        if (!isFinishing && !isDestroyed) {
            BukuDialog(
                this,
                "Update Aplikasi Berhasil",
                "Update Aplikasi telah selesai.",
                R.drawable.ic_success_ok,
                false,
                {
                    BtAnalytics.trackEventMobile("firmware_upgrade_complete")
                    finish()
                },
                {

                },
                getString(R.string.ok_button),
                ""
            ).show()
        }
    }

    override fun onBackPressed() {

    }

    private fun startFirmwareUpgrade() {
        try {
            if (getInstance().isDeviceConnected()) {
                if (!getInstance().isFirmwareUpgradeRequired()) {
                    showConfirmationDialog()
                    BtAnalytics.trackEventMobile("firmware_upgrade_not_required")
                } else {
                    BtAnalytics.trackEventMobile("firmware_upgrade_start")
                    Thread {
                        var `is`: InputStream? = null
                        try {
                            `is` = assets.open(
                                "pkg_MP45V_V11.00.01.R5014_0_4M.dat"
                            )
                            val len = `is`.available()
                            if (len > 0) {
                                val content = ByteArray(len)
                                `is`.read(content)
                                getInstance().getDevice()!!
                                    .upgrade(content, object : UpgradeListener {
                                        override fun upgradeDeviceSuccess() {
                                            Log.d(TAG, "upgradeDeviceSuccess")
                                            runOnUiThread { showConfirmationDialog() }
                                        }

                                        override fun showProgress(i: Int) {
                                            Log.d(TAG, "showProgress: $i")
                                            if (i % 2 == 0 && i <= 100) {
                                                runOnUiThread {
                                                    firmwareUpgradeInprogress!!.visibility =
                                                        View.VISIBLE
                                                    firmwareUpgradeInstruction!!.visibility =
                                                        View.GONE
                                                    firmwareUpgradeFailedNoInternet!!.visibility =
                                                        View.GONE
                                                    firmwareUpgradeFailed!!.visibility = View.GONE
                                                    progressPercentage!!.text = "$i%"
                                                }
                                            } else if (i > 100) {
                                                runOnUiThread { showConfirmationDialog() }
                                            }
                                        }

                                        override fun upgradeFail(i: Int) {
                                            Log.d(TAG, "upgradeFail: $i")
                                            runOnUiThread {
                                                firmwareUpgradeInprogress!!.visibility = View.GONE
                                                firmwareUpgradeInstruction!!.visibility = View.GONE
                                                if (Utility.hasInternet()) {
                                                    firmwareUpgradeFailed!!.visibility =
                                                        View.VISIBLE
                                                    firmwareUpgradeFailedNoInternet!!.visibility =
                                                        View.GONE
                                                } else {
                                                    firmwareUpgradeFailed!!.visibility = View.GONE
                                                    binding.btnRetryUpgradeNoInternet.isEnabled =
                                                        true
                                                    firmwareUpgradeFailedNoInternet!!.visibility =
                                                        View.VISIBLE
                                                }

                                                if (i == 43 || i == 55 || i == 48) {
                                                    showConfirmationDialog()
                                                }
                                                BtAnalytics.trackEventMobile("firmware_upgrade_fail")
                                            }
                                        }
                                    })
                            }
                        } catch (e: Exception) {
                            // TODO: handle exception
                            e.printStackTrace()
                        }
                    }.start()
                }
            } else {
                Toast.makeText(this, "Device not connected", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Failed to read firmware file", Toast.LENGTH_SHORT).show()
        }
    }

    companion object {
        private const val PICK_FILE_REQUEST_CODE = 1
        private const val TAG = "FirmwareUpgradeActivity"
    }
}