package com.bukuwarung.bluetooth_printer.activities.print.setup

import com.bukuwarung.network.TsmRepository
import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.ActivateDeviceResponse
import com.bukuwarung.network.model.BaseResponse
import com.bukuwarung.network.model.MappedPhoneNumberResponse
import retrofit2.Response
import javax.inject.Inject

interface GetMappedPhoneNumberUseCase {
    suspend operator fun invoke(btDeviceName: String): Response<BaseResponse<MappedPhoneNumberResponse>>

    suspend fun activateDevice(request: ActivateDeviceRequest): Response<BaseResponse<ActivateDeviceResponse>>
}

class GetMappedPhoneNumberUseCaseImpl @Inject constructor(val tsmRepository: TsmRepository) :
    GetMappedPhoneNumberUseCase {
    override suspend fun invoke(btDeviceName: String): Response<BaseResponse<MappedPhoneNumberResponse>> {
        return tsmRepository.getRegisteredUser(btDeviceName)
    }

    override suspend fun activateDevice(request: ActivateDeviceRequest): Response<BaseResponse<ActivateDeviceResponse>> {
        return tsmRepository.activateDevice(request)
    }
}