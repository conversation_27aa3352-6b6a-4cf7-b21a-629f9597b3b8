package com.bukuwarung.bluetooth_printer.activities.print.setup

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Build
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.R
import com.bukuwarung.bluetooth_printer.activities.print.BluetoothRequestDialog
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterAdapter
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.bluetooth_printer.base.BaseActivity
import com.bukuwarung.bluetooth_printer.databinding.ActivitySetupPrinterBinding
import com.bukuwarung.bluetooth_printer.databinding.LayoutPrinterSnackbarBinding
import com.bukuwarung.bluetooth_printer.model.PairedDevice
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.ALIGNMENT_CENTER
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.CHARCODE_PC437_ENGLISH
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.EMPHASIZED_MODE_BOLD
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.FONT_SIZE_NORMAL
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.LINE_SPACING_30
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.UNDERLINED_MODE_OFF
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.bluetooth_printer.model.printTypes.TextPrintType
import com.bukuwarung.bluetooth_printer.utils.*
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.hasBluetoothPermission
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.hasLocationPermission
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.isSystemBluetoothEnabled
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.requestBluetoothPermission
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.requestLocationPermission
import com.bukuwarung.webview.BtWebView
import com.bukuwarung.zoho.BtZohoChat
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class SetupBluetoothDeviceActivity : BaseActivity() {
    private var intentRequestCode = 0
    private var bluetoothConnection: BluetoothConnection? = null
    private var deviceType = CARD_READER
    private lateinit var printerAdapter: PrinterAdapter
    private lateinit var binding: ActivitySetupPrinterBinding
    private var registeredDevices = arrayListOf<String>()

    private lateinit var REQUIRED_PERMISSION_LIST: Array<String>
    private val mMissPermissions: MutableList<String> = java.util.ArrayList()
    private val REQUEST_CODE = 1

    private val testPrinterCallBack: (PrinterDataHolder) -> Unit = { printer ->
        if(deviceType == PRINTER){
            printTestPrinterOK(printer)
        }
    }

    private val printerSettingCallback: (PrinterDataHolder) -> Unit = { printer ->
//        if(deviceType == CARD_READER){
//            printerAdapter.removeInstalledPrinter(printer)
//        }else {
            openPrinterSetting(printer)
//        }
    }


    override fun setViewBinding() {
        binding = ActivitySetupPrinterBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        //check permission and bluetooth
        Log.d("--->","onCreate called $localClassName")

        deviceType = intent.getStringExtra(DEVICE_TYPE) ?: CARD_READER
        registeredDevices = intent.extras?.getStringArrayList(DEVICE_SN_LIST) ?: arrayListOf()
        bluetoothConnection = BluetoothConnection(this)
        intentRequestCode = intent.getIntExtra("requestCode", 0)
        binding.title.text =
            if (deviceType == PRINTER) getString(R.string.printer_text) else getString(R.string.card_reader_title)
        initClickListeners()
        checkAndRequestPermissions()
        binding.ivHelp.setOnClickListener {
            BtZohoChat.redirectToZohoChat()
        }
    }

    private fun setUpNoPrinterPaired() {
        binding.noPrinterView.layoutNoPrinter.showView()
        binding.noPrinterView.tvPrinterHint.showView()
        binding.rvPrinter.hideView()
        binding.noPrinterView.btnRefreshScanPrinter.hideView()
        binding.noInternetView.layoutNoInternet.hideView()
        binding.floatingBtnScanPrinter.hideView()
        binding.printerSettingView.editPrinterView.hideView()
        binding.noPrinterView.tvNoPrinter.text = if(deviceType == PRINTER) getString(R.string.belum_ada_printer_ditambahkan) else getString(R.string.no_card_reader)
        binding.noPrinterView.tvPrinterHint.text = if(deviceType == PRINTER) getString(R.string.no_printer_hint) else getString(R.string.no_card_reader_hint)
        binding.noPrinterView.btnAddPrinter.text = if(deviceType == PRINTER) getString(R.string.tambah_printer) else getString(R.string.add_card_reader)

        binding.noPrinterView.ivNoPrinter.setImageDrawable(
            AppCompatResources.getDrawable(
                this,
                        if(deviceType == PRINTER)
                            R.drawable.vector_no_printer
                        else
                            R.drawable.ic_card_reader
            ))
    }

    private fun setUpNoInternet() {
        binding.noInternetView.layoutNoInternet.showView()
        binding.rvPrinter.hideView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.floatingBtnScanPrinter.hideView()
        binding.printerSettingView.editPrinterView.hideView()
    }

    private fun openPrinterSetting(printer: PrinterDataHolder) {
        binding.printerSettingView.editPrinterView.showView()
        binding.printerSettingView.etPrinterName.setText(printer.name)
        binding.printerSettingView.tvPrinterAddress.text = printer.macAddress
        binding.printerSettingView.tvDisconnectPrinter.singleClick {
            printer.device.let { btDevice ->
                if (btDevice == null) {
                    printerAdapter.getDevice(printer.macAddress)?.let { device -> bluetoothConnection?.unPair(device) }
                } else {
                    bluetoothConnection?.unPair(btDevice)
                }
            }
            printerAdapter.removeInstalledPrinter(printer)
            if (BluetoothDevices.getPairedPrinter()?.address == printer.macAddress) {
                BluetoothDevices.removeCurrentPrinter()
            }

            showInstalledDevicesList()
        }
        binding.printerSettingView.btnSave.isEnabled = false
        binding.printerSettingView.btnSave.singleClick {
            val oldPrinterName = printer.name
            val updatedPrinterName = binding.printerSettingView.etPrinterName.text.toString()
//            printerAdapter.updatePrinterName(printer, updatedPrinterName)
            showInstalledDevicesList()

        }

        binding.printerSettingView.etPrinterName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.printerSettingView.btnSave.isEnabled = true
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        binding.rvPrinter.hideView()
        binding.tvBluetoothTitle.hideView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.noInternetView.layoutNoInternet.hideView()
        binding.floatingBtnScanPrinter.hideView()
    }

    private fun showInstalledDevicesList() {
        binding.rvPrinter.showView()
        binding.tvBluetoothTitle.showView()
        binding.floatingBtnScanPrinter.hideView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.noPrinterView.tvPrinterHint.hideView()
        binding.noPrinterView.btnRefreshScanPrinter.hideView()
        binding.noInternetView.layoutNoInternet.hideView()
        binding.printerSettingView.editPrinterView.hideView()
        printerAdapter.showInstalledDevices()
        if (deviceType == PRINTER && printerAdapter.isInstalledPrinterEmpty()) {
            setUpNoPrinterPaired()
        }else if(deviceType == CARD_READER && printerAdapter.isInstalledDevicesEmpty()){
            setUpNoPrinterPaired()
        }
    }

    private fun initClickListeners() {
        binding.ivBack.singleClick {
            finish()
        }

        binding.ivInfo.setOnClickListener {
            BtWebView.redirectToWebViewActivity(printerInfoURL, this)
        }

        binding.noPrinterView.btnAddPrinter.singleClick {
            openScanActivity()
            PrinterPrefManager(this).isFirstConnect = false
        }

        binding.noInternetView.btnAddPrinter.singleClick {
            openScanActivity()
        }

        binding.floatingBtnScanPrinter.singleClick {
            openScanActivity()
        }
    }

    private fun openScanActivity() {
        Log.d("--->","no device found, open scan activity, device type $deviceType")
        val intent = Intent(this, BluetoothDeviceScanActivity::class.java)
        intent.putExtra(DEVICE_TYPE,deviceType)
        intent.putStringArrayListExtra(DEVICE_SN_LIST, ArrayList(registeredDevices))
        startActivityForResult(intent, intentRequestCode)
    }

    override fun subscribeState() {}

    override fun onStart() {
        super.onStart()
        bluetoothConnection?.onStart()
    }

    override fun onResume() {
        super.onResume()
        Log.d("--->","on resume called $localClassName")
    }

    private fun initViews() {
        if (!::printerAdapter.isInitialized) {
            printerAdapter = PrinterAdapter(
                this,
                null,
                testPrinterCallBack,
                printerSettingCallback,
                deviceType,
                registeredDevices
            )
        }

        binding.rvPrinter.apply {
            layoutManager = LinearLayoutManager(this@SetupBluetoothDeviceActivity)
            adapter = printerAdapter
        }

        showInstalledDevicesList()

        if (!Utility.hasInternet()) {
            setUpNoInternet()
        }
    }

    private fun checkPermissionAndBluetooth(withDialog: Boolean = true) {
        Log.d("--->","checkPermissionAndBluetooth")
        Log.d("--->","hasBluetoothPermission: ${!hasBluetoothPermission()}")
        Log.d("--->","bluetoothConnection?.isEnabled: ${(bluetoothConnection?.isEnabled == false)}")
        Log.d("--->","hasBluetoothSystemPermissionMethod1 : = ${isSystemBluetoothEnabled(this)}")
        if (!hasLocationPermission() || (bluetoothConnection?.isEnabled == false) || !hasBluetoothPermission() || !isSystemBluetoothEnabled(this)) {
            if (withDialog) {
               val dialog =  BluetoothRequestDialog(this, "setup") {
                    if (it) {
                        // at this stage location would already be enabled
                        // if we want we can check for bluetooth permission directly
                        requestLocationPermission(this)
                    }
                }
                Utility.showDialogIfActivityAlive(this,dialog)
            } else {
                requestLocationPermission(this)
            }
        } else {
            initViews()
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            0 -> {
                initViews()
                val isActivation = data?.getBooleanExtra("isActivation", false) == true
                if (resultCode == RESULT_OK) {
                    if (isActivation) {
                        // for activation don't want to show snack bar.
                        setResult(RESULT_OK)
                        finish()
                    } else {
                        showPairedBluetoothSuccess()
                    }
                }
            }
            REQUEST_TO_ENABLE_BT -> {
                Log.d("--->", "bluetooth enabled from settings")
                proceedWithOperationAfterBluetoothEnabled()
            }
            else -> {
                finish()
            }

        }

    }

    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        val firstResult = grantResults.getOrNull(0)
        val secondResult = grantResults.getOrNull(1)
        when (requestCode) {
            PermissionConst.ACCESS_LOCATION -> {
                if (firstResult == PackageManager.PERMISSION_GRANTED) {
                    // location permission granted
                    // Now request to enable bluetooth
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        Log.d("--->","check for bluetooth permission")
                        requestBluetoothPermission(this)
                    } else {
                        startActivityForResult(Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE), REQUEST_TO_ENABLE_BT)
                    }
                } else {
                    // permission request denied
                    Toast.makeText(this, getString(R.string.location_permission_denied_message), Toast.LENGTH_SHORT).apply {
                        setGravity(Gravity.CENTER, 0, 0)
                    }.show()
                    finish()
                }
            }
            PermissionConst.BLUETOOTH_PERMISSION -> {
                if (firstResult == PackageManager.PERMISSION_GRANTED && secondResult == PackageManager.PERMISSION_GRANTED) {
                    Log.d("--->","bluetooth permission granted")

                    // here app bluetooth permission is granted, but system bluetooth permission is not granted
                    val isSystemBluetoothPermission = isSystemBluetoothEnabled(this)
                    Log.d("--->","check bluetooth turned on $isSystemBluetoothPermission")
                    if (isSystemBluetoothPermission) {
                        // system Bluetooth is enabled, proceed with your operations
                        proceedWithOperationAfterBluetoothEnabled()
                    } else {
                        // system Bluetooth is not enabled, prompt the user to enable it
                        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                        startActivityForResult(enableBtIntent, REQUEST_TO_ENABLE_BT)
                    }
                } else {
                    Log.d("--->","bluetooth permission denied")
                    // permission request denied
                    Toast.makeText(this, getString(R.string.location_permission_denied_message), Toast.LENGTH_SHORT).apply {
                        setGravity(Gravity.CENTER, 0, 0)
                    }.show()
                    finish()
                }
            }
        }
        if (requestCode == REQUEST_CODE) {
            for (i in grantResults.indices.reversed()) {
                if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                    mMissPermissions.remove(permissions[i])
                }
            }
        }
    }

    private fun proceedWithOperationAfterBluetoothEnabled() {
        if (!::printerAdapter.isInitialized) {
            printerAdapter = PrinterAdapter(
                this,
                null,
                testPrinterCallBack,
                printerSettingCallback,
                deviceType,
                registeredDevices
            )
        }
        if (intentRequestCode == RECORD_DETAIL && !printerAdapter.isInstalledPrinterEmpty()) {
            setResult(RESULT_OK)
            finish()
        } else {
            initViews()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        bluetoothConnection = null
    }

    override fun onStop() {
        super.onStop()
        bluetoothConnection?.onStop()
    }

    private fun printTestPrinterOK(printer: PrinterDataHolder) {
        val printingHelper = BluetoothDevices.printer(PairedDevice(printer.name, printer.macAddress))
        setPrintingCallback(printingHelper)

        val printable = java.util.ArrayList<BasePrintType>()
        val printables = TextPrintType.Builder()
            .setText("PRINTER OK") //The text you want to print
            .setAlignment(ALIGNMENT_CENTER)
            .setEmphasizedMode(EMPHASIZED_MODE_BOLD) //Bold or normal
            .setFontSize(FONT_SIZE_NORMAL)
            .setUnderlined(UNDERLINED_MODE_OFF) // Underline on/off
            .setCharacterCode(CHARCODE_PC437_ENGLISH) // Character code to support languages
            .setLineSpacing(LINE_SPACING_30)
            .setNewLinesAfter(2) // To provide n lines after sentence
            .build() as TextPrintType

        printable.add(printables)
        printingHelper.print(printable, object : PermissionCallback {
            override fun onPermissionRequired(permissions: Array<String>) {
                checkPermissionAndBluetooth()
            }
        })
    }

    private fun setPrintingCallback(printingHelper: PrintingHelper) {
        printingHelper.printerCallBack = object : PrinterCallBack {
            override fun connectingWithPrinter() {
                Log.d("SetupPrinterActivity", "Connecting with Printer")
            }

            override fun printingOrderSentSuccessfully() {
                showSnackBarForTestPrint(false)
                triggerTestPrintEvent(false)
                printingHelper.printerCallBack = null
                Log.d("SetupPrinterActivity", "Print Order sent successfully")
            }

            override fun connectionFailed(error: String) {
                showSnackBarForTestPrint(true)
                triggerTestPrintEvent(true, "connection_issue")
                printingHelper.printerCallBack = null
                Log.d("SetupPrinterActivity", "Connection Failed $error")
            }

            override fun onError(error: String) {
                showSnackBarForTestPrint(true)
                printingHelper.printerCallBack = null
                triggerTestPrintEvent(true, "printer_issue")
                Log.d("SetupPrinterActivity", "Connection on Error $error")
            }

            override fun onMessage(message: String) {
                Log.d("SetupPrinterActivity", "Connection OnMessage $message")
            }

            override fun disconnected() {
                printingHelper.printerCallBack = null
                Log.d("SetupPrinterActivity", "Connection disconnected")
            }
        }
    }

    companion object {
        private const val REQUEST_TO_ENABLE_BT = 11
        const val RECORD_DETAIL = 2311
        const val printerInfoURL = "https://bukuwarung.com/bukuagen/edc-saku/faq/bluetooth-printer/"
        const val PRINTER = "printer"
        const val CARD_READER = "card_reader"
        const val DEVICE_TYPE = "device_type"
        const val DEVICE_SN_LIST ="deviceSerialNumberList"
    }

    private fun triggerTestPrintEvent(testFailed: Boolean, errorMessage: String = "") {

    }

    private fun showSnackBarForTestPrint(testFailed: Boolean) {
        val snackbar: Snackbar = Snackbar.make(binding.printerSnackbarGuideline, "", Snackbar.LENGTH_SHORT)
        val customSnackBinding = LayoutPrinterSnackbarBinding.inflate(layoutInflater)
        snackbar.view.setBackgroundColor(Color.TRANSPARENT)
        val snackbarLayout: Snackbar.SnackbarLayout = snackbar.view as Snackbar.SnackbarLayout
        snackbarLayout.setPadding(0, 0, 0, 0)

        val snackTV = customSnackBinding.tvSnackbarText
        snackTV.text = if (testFailed) {
            getString(R.string.test_printer_failed)
        } else {
            getString(R.string.testing_printer)
        }
        val closeIV = customSnackBinding.ivCloseSnackbar
        closeIV.setOnClickListener {
            snackbar.dismiss()
        }
        snackbarLayout.addView(customSnackBinding.root, 0)
        snackbar.show()
    }

    private fun showPairedBluetoothSuccess() {
        this.showSnackbar(
            anchorView = binding.printerSnackbarGuideline,
            message = getString(R.string.bluetooth_paired_success_message),
            bgResId = R.drawable.bg_corner_green,
            icon = R.drawable.ic_checklist_rounded,
        )
        {
            setResult(RESULT_OK)
            finish()
        }

    }

    private fun checkAndRequestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            REQUIRED_PERMISSION_LIST = arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.BLUETOOTH
            )
        } else {
            REQUIRED_PERMISSION_LIST = arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.BLUETOOTH
            )
        }
        mMissPermissions.clear()
        for (permission in REQUIRED_PERMISSION_LIST) {
            val result = ContextCompat.checkSelfPermission(this, permission)
            if (result != PackageManager.PERMISSION_GRANTED) {
                mMissPermissions.add(permission)
            }
        }

        if (!mMissPermissions.isEmpty()) {
            ActivityCompat.requestPermissions(
                this,
                mMissPermissions.toTypedArray<String>(),
                REQUEST_CODE
            )
        }else{
            //reconfirm all permission and require missing premission if required
            checkPermissionAndBluetooth(withDialog = false)
        }
    }
}