package com.bukuwarung.bluetooth_printer.base

import android.os.Bundle
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar

abstract class BaseActivity: AppCompatActivity() {
    protected var TAG: String? = null
    abstract fun setViewBinding()
    abstract fun setupView()
    abstract fun subscribeState()

    @CallSuper
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewBinding()
        setupView()
        subscribeState()
    }
}
