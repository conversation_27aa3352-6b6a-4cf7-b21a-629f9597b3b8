package com.bukuwarung.bluetooth_printer.base;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.bukuwarung.bluetooth_printer.R;

/**
 * Base for Android dialogs.
**/
public abstract class BaseDialog extends Dialog {
    /**
     * Type of this dialog. Available options are Full Screen and Popup.
     * Defaults to Popup.
     **/
    private final BaseDialogType type;

    /**
     * Additional Window Feature to be requested.
     * Defaults to -1.
     **/
    private int optionalWindowFeature = -1;

    /**
     * Set if the dialog is cancellable (by clicking back btn) or not.
     * Defaults to false.
     **/
    private boolean isCancellable = false;

    /**
     * Set if the dialog is full width of the device.
     * Defaults to true.
     **/
    private boolean useFullWidth = true;

    /**
     * Set if the dialog has min width.
     * Defaults to 0 (a.k.a not taking this value).
     **/
    private int minWidth = 0;

    /**
     * Creates a Popup dialog.
     **/
    public BaseDialog(@NonNull Context context) {
        super(context, R.style.DefaultDialogTheme);

        this.type = BaseDialogType.POPUP;
    }

    /**
     * Creates a dialog according to the base dialog type,
     * either Full screen or popup.
     **/
    public BaseDialog(@NonNull Context context, BaseDialogType type) {
        super(context,
                type == BaseDialogType.FULL_SCREEN
                        ? R.style.FullScreenDialogTheme
                        : R.style.DefaultDialogTheme);

        this.type = type;
    }

    public abstract int getResId();

    public void setOptionalWindowFeature(int optionalWindowFeature) {
        this.optionalWindowFeature = optionalWindowFeature;
    }

    public void setCancellable(boolean isCancellable) {
        this.isCancellable = isCancellable;
    }

    public void setUseFullWidth(boolean useFullWidth) {
        this.useFullWidth = useFullWidth;
    }

    public void setMinWidth(int minWidth) {
        this.minWidth = minWidth;
    }

    /*
    * Reason : kotlin synthetic is deprecated
    * Propose : Using view binding object inside the dialogue
    * Requirement : return 0 when overriding getResId() and just pass a ViewBinding.root object
    * */
    public void setupViewBinding(View rootView){
        setContentView(rootView);
        setupView();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setupView();
    }

    private void setupView() {
        // setting up optional window feature if there's any
        if (optionalWindowFeature >= 0)
            requestWindowFeature(optionalWindowFeature);

        // Setting up content view
        if (getResId() != 0){
            setContentView(getResId());
        }

        // setting up window layout based on type && setting up anim
        if (getWindow() != null) {
            if (type == BaseDialogType.POPUP)
                getWindow().setBackgroundDrawableResource(R.drawable.base_dialog_rounded);
            int layoutParams = type == BaseDialogType.FULL_SCREEN
                    ? WindowManager.LayoutParams.MATCH_PARENT
                    : WindowManager.LayoutParams.WRAP_CONTENT;

            int popupWidth = minWidth == 0 ? WindowManager.LayoutParams.WRAP_CONTENT
                    : minWidth;
            getWindow().setLayout(
                    useFullWidth ? WindowManager.LayoutParams.MATCH_PARENT
                            : popupWidth,
                    layoutParams
            );
        }

        // setting up isCancellable flag on dialog
        setCancelable(isCancellable);
    }
}
