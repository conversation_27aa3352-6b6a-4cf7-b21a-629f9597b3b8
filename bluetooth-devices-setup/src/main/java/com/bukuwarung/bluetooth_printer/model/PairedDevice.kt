package com.bukuwarung.bluetooth_printer.model

import io.paperdb.Paper
import java.io.Serializable


open class PairedDevice(name: String?, address: String) : Serializable {
    var name: String? = name
    var address: String = address

    companion object {
        private const val PAIRED_DEVICE = "paired_device"

        fun getPairedPrinter(): PairedDevice? {
            return Paper.book().read(PAIRED_DEVICE, null)
        }

        fun setPairedPrinter(printer: PairedDevice) {
            Paper.book().write(PAIRED_DEVICE, printer)
        }

        fun removePairedPrinter() {
            Paper.book().delete(PAIRED_DEVICE)
        }
    }

}