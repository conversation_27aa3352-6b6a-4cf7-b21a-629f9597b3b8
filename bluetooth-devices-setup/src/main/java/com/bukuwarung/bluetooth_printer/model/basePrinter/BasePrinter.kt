package com.bukuwarung.bluetooth_printer.model.basePrinter

import com.bukuwarung.bluetooth_printer.model.DefaultPrintingImagesHelper
import com.bukuwarung.bluetooth_printer.model.ImagePrintingHelper
import com.bukuwarung.bluetooth_printer.model.printConverter.BaseConverter


open class BasePrinter : PrinterProperty() {
    override fun useConverter(): BaseConverter = BaseConverter()

    override fun initPrintingImagesHelper(): ImagePrintingHelper = DefaultPrintingImagesHelper()

    override fun initLineSpacingCommand(): ByteArray = byteArrayOf(0x1B, 0x33)

    override fun initInitPrinterCommand(): ByteArray = byteArrayOf(0x1b, 0x40)

    override fun initJustificationCommand(): ByteArray = byteArrayOf(27, 97)

    override fun initFontSizeCommand(): ByteArray = byteArrayOf(29, 33)

    override fun initEmphasizedModeCommand(): ByteArray = byteArrayOf(27, 69) //1 on , 0 off

    override fun initUnderlineModeCommand(): ByteArray = byteArrayOf(27, 45) //1 on , 0 off

    override fun initCharacterCodeCommand(): ByteArray = byteArrayOf(27, 116)

    override fun initFeedLineCommand(): ByteArray = byteArrayOf(27, 100)


    companion object {
        val ALIGNMENT_RIGHT: Byte = 2
        val ALIGNMENT_LEFT: Byte = 0
        val ALIGNMENT_CENTER: Byte = 1
        val EMPHASIZED_MODE_BOLD: Byte = 1
        val EMPHASIZED_MODE_NORMAL: Byte = 0
        val UNDERLINED_MODE_ON: Byte = 1
        val UNDERLINED_MODE_OFF: Byte = 0
        val LINE_SPACING_60: Byte = 60
        val LINE_SPACING_30: Byte = 30
        val FONT_SIZE_NORMAL: Byte = 0x00
        val FONT_SIZE_LARGE: Byte = 0x10

        val CHARCODE_PC437_ENGLISH: Byte = 0x00
    }
}