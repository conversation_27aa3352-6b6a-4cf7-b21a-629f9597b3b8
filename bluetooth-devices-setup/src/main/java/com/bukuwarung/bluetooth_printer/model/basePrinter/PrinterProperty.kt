package com.bukuwarung.bluetooth_printer.model.basePrinter

import com.bukuwarung.bluetooth_printer.model.printConverter.BaseConverter
import com.bukuwarung.bluetooth_printer.model.ImagePrintingHelper

abstract class PrinterProperty {
    var initPrinterCommand = initInitPrinterCommand()
    var justificationCommand = initJustificationCommand()
    var fontSizeCommand = initFontSizeCommand()
    var emphasizedModeCommand = initEmphasizedModeCommand()
    var underlineModeCommand = initUnderlineModeCommand()
    var characterCodeCommand = initCharacterCodeCommand()
    var feedLineCommand = initFeedLineCommand()
    var lineSpacingCommand = initLineSpacingCommand()
    var converter: BaseConverter = useConverter()
    var imagePrintingHelper: ImagePrintingHelper = initPrintingImagesHelper()

    abstract fun initInitPrinterCommand(): ByteArray
    abstract fun initJustificationCommand(): ByteArray
    abstract fun initFontSizeCommand(): ByteArray
    abstract fun initEmphasizedModeCommand(): ByteArray
    abstract fun initUnderlineModeCommand(): ByteArray
    abstract fun initCharacterCodeCommand(): ByteArray
    abstract fun initFeedLineCommand(): ByteArray
    abstract fun initLineSpacingCommand(): ByteArray
    abstract fun useConverter(): BaseConverter
    abstract fun initPrintingImagesHelper(): ImagePrintingHelper
}