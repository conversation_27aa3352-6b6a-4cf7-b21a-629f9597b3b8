package com.bukuwarung.bluetooth_printer.model.printTypes

import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter
import com.bukuwarung.bluetooth_printer.model.basePrinter.PrinterProperty

data class ImagePrintType private constructor(
    val image: Bitmap,
    val alignment: Byte,
    val newLinesAfter: Int
) : BasePrintType {

    override fun getPrintableByteArray(printer: PrinterProperty): List<ByteArray> {
        val operations = mutableListOf(
            printer.justificationCommand.plus(alignment),
            printer.imagePrintingHelper.getBitmapAsByteArray(image)
        )

        if (newLinesAfter > 0) {
            operations.add(printer.feedLineCommand.plus(newLinesAfter.toByte()))
        }

        return operations
    }

    class Builder() {
        private var alignment: Byte = BasePrinter.ALIGNMENT_LEFT
        private var newLinesAfter = 0
        private lateinit var image: Bitmap

        constructor(src: Int, resources: Resources) : this() {
            image = BitmapFactory.decodeResource(resources, src)
        }

        constructor(image: Bitmap) : this() {
            this.image = image
        }

        fun setAlignment(alignment: Byte): Builder {
            this.alignment = alignment
            return this
        }

        fun setNewLinesAfter(lines: Int): Builder {
            this.newLinesAfter = lines
            return this
        }

        fun build(): ImagePrintType {
            return ImagePrintType(image, alignment, newLinesAfter)
        }
    }

}