package com.bukuwarung.bluetooth_printer.model.printTypes

import com.bukuwarung.bluetooth_printer.model.basePrinter.PrinterProperty

data class RawPrintType private constructor(
    val command: ByteArray,
    val newLinesAfter: Int
) : BasePrintType {

    override fun getPrintableByteArray(printer: PrinterProperty): List<ByteArray> {
        val operations = mutableListOf(command)

        if (newLinesAfter > 0) {
            operations.add(printer.feedLineCommand.plus(newLinesAfter.toByte()))
        }

        return operations
    }

    class Builder(private var raw: ByteArray) {
        private var newLinesAfter = 0

        fun setNewLinesAfter(lines: Int): Builder {
            this.newLinesAfter = lines
            return this
        }

        fun build(): RawPrintType {
            return RawPrintType(raw, newLinesAfter)
        }
    }

}
