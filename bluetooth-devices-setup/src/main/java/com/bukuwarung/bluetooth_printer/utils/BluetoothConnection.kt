package com.bukuwarung.bluetooth_printer.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothSocket
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import com.bukuwarung.bluetooth_printer.model.BluetoothCallBack
import com.bukuwarung.bluetooth_printer.model.BluetoothDeviceDiscoveryCallBack
import com.bukuwarung.bluetooth_printer.model.BluetoothPrintingDeviceCallBack
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.io.OutputStream
import java.util.*


class BluetoothConnection {
    private var context: Context? = null
    private var uuid: UUID? = null
    private var bluetoothManager: BluetoothManager? = null
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var socket: BluetoothSocket? = null
    private var device: BluetoothDevice? = null
    private var devicePair: BluetoothDevice? = null
    private var input: BufferedReader? = null
    private var out: OutputStream? = null
    private var deviceCallback: BluetoothPrintingDeviceCallBack? = null
    private var discoveryCallback: BluetoothDeviceDiscoveryCallBack? = null
    private var bluetoothCallback: BluetoothCallBack? = null
    private var permissionCallback: PermissionCallback? = null
    private var isConnected = false
    private var insecureConnection = false
    private var timeDelayMillis = 16000L

    companion object {
        private const val REQUEST_ENABLE_BT = 1111
    }

    constructor(context: Context) {
        initialize(context, UUID.fromString("00001101-0000-1000-8000-00805f9b34fb"))
    }

    constructor(context: Context, uuid: UUID) {
        initialize(context, uuid)
    }

    private fun initialize(context: Context, uuid: UUID) {
        this.context = context
        this.uuid = uuid
        deviceCallback = null
        discoveryCallback = null
        bluetoothCallback = null
        isConnected = false
    }

    private val pairReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action: String? = intent.action
            if (BluetoothDevice.ACTION_BOND_STATE_CHANGED == action) {
                val state: Int = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.ERROR)
                val prevState: Int = intent.getIntExtra(BluetoothDevice.EXTRA_PREVIOUS_BOND_STATE, BluetoothDevice.ERROR)
                if (state == BluetoothDevice.BOND_BONDED && prevState == BluetoothDevice.BOND_BONDING) {
                    context.unregisterReceiver(this)
                    devicePair?.let {
                        discoveryCallback?.onDevicePaired(it)
                    }
                } else if (state == BluetoothDevice.BOND_NONE && prevState == BluetoothDevice.BOND_BONDED) {
                    context.unregisterReceiver(this)
                    devicePair?.let {
                        discoveryCallback?.onDeviceUnpaired(it)
                    }
                } else if(state == BluetoothDevice.BOND_NONE && prevState == BluetoothDevice.BOND_BONDING){
                    context.unregisterReceiver(this)
                    devicePair?.let {
                        discoveryCallback?.onError("Failed to pair device")
                    }
                }
            }
        }
    }

    fun onStop() {
        context?.unregisterReceiver(bluetoothReceiver)
    }

    fun enable() {
        bluetoothAdapter?.let {
            if (!it.isEnabled) {
                checkForPermissionBeforeAction(
                    BluetoothConnectionAction.ENABLE_ADAPTER,
                    arrayOf(BluetoothPermission.CONNECT, BluetoothPermission.SCAN)
                )
            }
        }
    }

    private var actionToPerform: BluetoothConnectionAction? = null

    enum class BluetoothConnectionAction {
        ENABLE_ADAPTER,
        CONNECT_TO_DEVICE,
        STOP_SCANNING,
        START_SCANNING,
        UNPAIR_DEVICE
    }

    enum class BluetoothPermission {
        CONNECT, SCAN
    }

    @SuppressLint("MissingPermission")
    private fun continueWithAction() {
        when (actionToPerform) {
            BluetoothConnectionAction.ENABLE_ADAPTER -> {
                bluetoothAdapter?.enable()
                if (!isEnabled) {
                    showBluetoothEnableFailedDialog()
                }
            }
            BluetoothConnectionAction.CONNECT_TO_DEVICE -> {
                device?.let {
                    ConnectThread(it, insecureConnection).start()
                }
            }
            BluetoothConnectionAction.STOP_SCANNING -> {
                if (bluetoothAdapter?.isDiscovering == true) {
                    Handler(Looper.getMainLooper()).postDelayed({
                        bluetoothAdapter?.cancelDiscovery()
                    }, timeDelayMillis)
                }
            }
            BluetoothConnectionAction.START_SCANNING -> {
                stopScanning(0)
                bluetoothAdapter?.startDiscovery()
                Log.d("--->", "startDiscovery ${bluetoothAdapter?.startDiscovery()}")
            }
            BluetoothConnectionAction.UNPAIR_DEVICE -> {
                if (pairedDevices?.contains(devicePair) == true) {
                    devicePair?.let { unPair(it) }
                }
            }
            null -> {}
        }
    }

    private fun checkForPermissionBeforeAction(
        bluetoothConnectionAction: BluetoothConnectionAction,
        permissions: Array<BluetoothPermission>
    ) {
        this.actionToPerform = bluetoothConnectionAction
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val manifestPermissions = permissions.map {
                when (it) {
                    BluetoothPermission.CONNECT -> Manifest.permission.BLUETOOTH_CONNECT
                    BluetoothPermission.SCAN -> Manifest.permission.BLUETOOTH_SCAN
                }
            }
            if (PermissionUtils.hasPermissions(context!!, manifestPermissions.toTypedArray())) {
                Log.d("--->", "has permission")
                continueWithAction()
            } else {
                permissionCallback?.onPermissionRequired(manifestPermissions.toTypedArray())
            }
        } else {
            Log.d("--->", "has permission less than 31")
            continueWithAction()
        }
    }

    private val bluetoothReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action: String? = intent.action
            if (action != null && action == BluetoothAdapter.ACTION_STATE_CHANGED) {
                val state: Int = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                if (bluetoothCallback != null) {
                    when (state) {
                        BluetoothAdapter.STATE_OFF -> bluetoothCallback?.onBluetoothOff()
                        BluetoothAdapter.STATE_TURNING_OFF -> bluetoothCallback?.onBluetoothTurningOff()
                        BluetoothAdapter.STATE_ON -> bluetoothCallback?.onBluetoothOn()
                        BluetoothAdapter.STATE_TURNING_ON -> bluetoothCallback?.onBluetoothTurningOn()
                    }
                }
            }
        }
    }

    @JvmOverloads
    fun connectToAddress(address: String?, insecureConnection: Boolean = false) {
        device = bluetoothAdapter?.getRemoteDevice(address)
        connectToDevice(insecureConnection)
    }

    private fun connectToDevice(insecureConnection: Boolean) {
        this.insecureConnection = insecureConnection
        checkForPermissionBeforeAction(
            BluetoothConnectionAction.CONNECT_TO_DEVICE,
            arrayOf(BluetoothPermission.SCAN, BluetoothPermission.CONNECT)
        )
    }

    private val scanReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Log.d("--->", "onReceive of start scanning")
            val action: String? = intent.action
            if (action != null) {
                Log.d("--->", "action $action")
                when (action) {
                    BluetoothAdapter.ACTION_STATE_CHANGED -> {
                        val state: Int = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                        if (state == BluetoothAdapter.STATE_OFF) discoveryCallback?.onError("Bluetooth turned off")
                    }
                    BluetoothAdapter.ACTION_DISCOVERY_STARTED -> discoveryCallback?.onDiscoveryStarted()
                    BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                        context.unregisterReceiver(this)
                        discoveryCallback?.onDiscoveryFinished()
                    }
                    BluetoothDevice.ACTION_FOUND -> {
                        val device: BluetoothDevice? = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                        if (discoveryCallback != null) device?.let { discoveryCallback?.onDeviceFound(it) }
                    }
                }
            }
        }
    }

    fun send(msg: ByteArray?) {
        sendMessage(msg)
    }

    fun sendImage(byteArray: ByteArray?) {
        try {
            out?.write(byteArray)
            out?.write(byteArrayOf(0x0b, 0x0c))
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    private val pairedDevices: ArrayList<BluetoothDevice>?
        get() = if (ActivityCompat.checkSelfPermission(
                context!!,
                Manifest.permission.BLUETOOTH_CONNECT
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            bluetoothAdapter?.bondedDevices?.let { ArrayList(it) }
        } else null

    fun startScanning() {
        val filter = IntentFilter()
        filter.addAction(BluetoothDevice.ACTION_FOUND)
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
        context?.registerReceiver(scanReceiver, filter)
        checkForPermissionBeforeAction(
            BluetoothConnectionAction.START_SCANNING,
            arrayOf(BluetoothPermission.SCAN)
        )
    }

    fun stopScanning(timeDelayMillis: Long) {
        this.timeDelayMillis = timeDelayMillis
        checkForPermissionBeforeAction(
            BluetoothConnectionAction.STOP_SCANNING,
            arrayOf(BluetoothPermission.SCAN)
        )
    }

    fun onStart() {
        bluetoothManager = context?.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        if (bluetoothManager != null) bluetoothAdapter = bluetoothManager?.adapter
        context?.registerReceiver(bluetoothReceiver, IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED))
    }

    val isEnabled: Boolean
        get() = bluetoothAdapter != null && bluetoothAdapter?.isEnabled == true

    fun onActivityResult(requestCode: Int, resultCode: Int) {
        if (bluetoothCallback != null) {
            if (requestCode == REQUEST_ENABLE_BT) {
                if (resultCode == Activity.RESULT_CANCELED) bluetoothCallback?.onUserDeniedActivation()
            }
        }
    }

    fun disconnect() {
        try {
            socket?.close()
        } catch (e: IOException) {
            if (deviceCallback != null) e.message?.let { deviceCallback?.onError(it) }
        }
    }

    private fun sendMessage(msg: ByteArray?) {
        try {
            out?.write(msg)
        } catch (e: IOException) {
            isConnected = false
            if (deviceCallback != null) device?.let { e.message?.let { it1 -> deviceCallback?.onDeviceDisconnected(it, it1) } }
        }
    }

    fun pair(device: BluetoothDevice) {
        context?.registerReceiver(pairReceiver, IntentFilter(BluetoothDevice.ACTION_BOND_STATE_CHANGED))
        devicePair = device
        try {
            device.javaClass.getMethod("createBond").invoke(device)
        } catch (e: Exception) {
            e.message?.let { discoveryCallback?.onError(it) }
        }
    }

    fun unPair(device: BluetoothDevice) {
        context?.registerReceiver(pairReceiver, IntentFilter(BluetoothDevice.ACTION_BOND_STATE_CHANGED))
        devicePair = device
        try {
            device.javaClass.getMethod("removeBond").invoke(device)
            checkForPermissionBeforeAction(
                BluetoothConnectionAction.UNPAIR_DEVICE,
                arrayOf(BluetoothPermission.CONNECT)
            )
        } catch (e: Exception) {
            e.message?.let { discoveryCallback?.onError(it) }
        }
    }

    fun setDeviceCallback(deviceCallback: BluetoothPrintingDeviceCallBack?) {
        this.deviceCallback = deviceCallback
    }

    fun setDiscoveryCallback(discoveryCallback: BluetoothDeviceDiscoveryCallBack?) {
        this.discoveryCallback = discoveryCallback
    }

    private inner class ReceiveThread : Thread(), Runnable {
        override fun run() {
            var msg = ""
            try {
                while (input?.readLine()?.also { msg = it } != null) {
                    val msgCopy = msg
                    Handler(Looper.getMainLooper()).post { deviceCallback?.onMessage(msgCopy) }
                }
            } catch (e: IOException) {
                isConnected = false
                if (deviceCallback != null) Handler(Looper.getMainLooper()).post { device?.let { e.message?.let { it1 -> deviceCallback?.onDeviceDisconnected(it, it1) } } }
            }
        }
    }

    fun setBluetoothCallback(bluetoothCallback: BluetoothCallBack?) {
        this.bluetoothCallback = bluetoothCallback
    }

    fun setBluetoothPermissionCallback(callback: PermissionCallback) {
        permissionCallback = callback
    }

    /**
     * It is expected that connection thread will be called only when
     * Manifest.permission.BLUETOOTH_SCAN and Manifest.permission.BLUETOOTH_CONNECT is provided.
     */
    @SuppressLint("MissingPermission")
    private inner class ConnectThread(device: BluetoothDevice, insecureConnection: Boolean) : Thread() {
        override fun run() {
            bluetoothAdapter?.cancelDiscovery()
            try {
                socket?.connect()
                out = socket?.outputStream
                input = BufferedReader(InputStreamReader(socket?.inputStream))
                isConnected = true
                ReceiveThread().start()
                if (deviceCallback != null) Handler(Looper.getMainLooper()).post { device?.let { deviceCallback?.onDeviceConnected(it) } }
            } catch (e: IOException) {
                if (deviceCallback != null) Handler(Looper.getMainLooper()).post { device?.let { e.message?.let { it1 -> deviceCallback?.onConnectError(it, it1) } } }
                try {
                    socket?.close()
                } catch (closeException: IOException) {
                    if (deviceCallback != null) Handler(Looper.getMainLooper()).post { closeException.message?.let { deviceCallback?.onError(it) } }
                }
            }
        }

        init {
            <EMAIL> = device
            try {
                socket = if (insecureConnection) {
                    device.createInsecureRfcommSocketToServiceRecord(uuid)
                } else {
                    device.createRfcommSocketToServiceRecord(uuid)
                }
            } catch (e: IOException) {
                e.message?.let { deviceCallback?.onError(it) }
            }
        }
    }

    private fun showNoBluetoothSupportDialog() {
        AlertDialog.Builder(context)
            .setTitle("No Bluetooth Support")
            .setMessage("This device does not support Bluetooth.")
            .setPositiveButton("OK", null)
            .show()
    }

    private fun showBluetoothEnableFailedDialog() {
        AlertDialog.Builder(context)
            .setTitle("Enable Bluetooth")
            .setMessage("Failed to enable Bluetooth. Please enable it in the settings.")
            .setPositiveButton("Open Settings") { _, _ -> openPhoneSettings() }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun openPhoneSettings() {
        val intent = Intent(Settings.ACTION_SETTINGS)
        context?.startActivity(intent)
    }
}