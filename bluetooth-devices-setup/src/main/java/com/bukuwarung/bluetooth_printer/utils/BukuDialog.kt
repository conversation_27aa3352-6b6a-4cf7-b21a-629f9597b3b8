package com.bukuwarung.bluetooth_printer.utils

import android.content.Context
import android.os.Bundle
import com.bukuwarung.bluetooth_printer.base.BaseDialog
import com.bukuwarung.bluetooth_printer.base.BaseDialogType
import com.bukuwarung.bluetooth_printer.databinding.DialogBukuBinding

class BukuDialog(
    context: Context,
    private val title: String?,
    private val subTitle: String?,
    private val image: Int?,
    private val isLoader: Boolean,
    private val btnLeftListener: () -> Unit,
    private val btnRightListener: () -> Unit,
    private val btnLeftText: String,
    private val btnRightText: String
) : BaseDialog(context, BaseDialogType.POPUP) {

    private val binding by lazy {
        DialogBukuBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    init {
        setUseFullWidth(false)
        setCancellable(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)

        with(binding) {
            ivImage.visibility = (image!=null).asVisibility()
            ivImage.setImageResource(image.orNil)
            tvTitle.text = title
            tvTitle.visibility = title.isNotNullOrBlank().asVisibility()
            tvSubtitle.text = subTitle
            tvSubtitle.visibility = subTitle.isNotNullOrBlank().asVisibility()
            btnRight.visibility = btnRightText.isNotNullOrBlank().asVisibility()
            btnLeft.visibility = btnLeftText.isNotNullOrBlank().asVisibility()
            if (isLoader) {
                btnLeft.hideView()
                btnRight.hideView()
            }
            btnLeft.setOnClickListener {
                dismiss()
                btnLeftListener()
            }
            btnLeft.text = btnLeftText

            btnRight.setOnClickListener {
                dismiss()
                btnRightListener()
            }
            btnRight.text = btnRightText
        }
    }

    override fun getResId(): Int {
        return 0
    }

}