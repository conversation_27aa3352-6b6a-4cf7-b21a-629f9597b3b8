package com.bukuwarung.bluetooth_printer.utils

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import com.bukuwarung.bluetooth_printer.R
import com.bukuwarung.bluetooth_printer.databinding.BluetoothCustomToastBinding
import com.google.android.material.snackbar.Snackbar

fun String.times(times: Int): String {
    val sb = StringBuilder()
    for (i in 1..times) {
        sb.append(this)
    }
    return sb.toString()
}

fun View.showView() {
    this.visibility = View.VISIBLE
}

fun View.hideView() {
    this.visibility = View.GONE
}

fun View.invisibleView() {
    this.visibility = View.INVISIBLE
}

fun Boolean.asVisibility(): Int {
    return if (this) {
        View.VISIBLE
    } else {
        View.GONE
    }
}
fun String?.isNotNullOrBlank() = !this.isNullOrBlank()

val Int?.orNil
    get() = (this ?: 0)

val Int.dp: Int
get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()


fun Context.showSnackbar(
    anchorView: View,
    message: String = getString(R.string.bluetooth_paired_success_message),
    @ColorRes textColor:Int = R.color.dark_green,
    @DrawableRes bgResId: Int = R.drawable.bg_corner_green,
    @DrawableRes icon: Int = R.drawable.ic_checklist_rounded,
    onDismissListener: ()->Unit = {}
){
    val snackbar = Snackbar.make(anchorView, message, Snackbar.LENGTH_SHORT)
    val customSnackBinding = BluetoothCustomToastBinding.inflate(LayoutInflater.from(this))
    snackbar.view.apply {
        setBackgroundColor(Color.TRANSPARENT)
        setPadding(0, 0, 0, 0)
        (this as ViewGroup).removeAllViews()
        addView(customSnackBinding.root)
        customSnackBinding.apply {
            clToast.background = ContextCompat.getDrawable(context, bgResId)
            tvToastMessage.text = message
            tvToastMessage.setTextColor(ContextCompat.getColor(context, textColor))
            ivToastImage.setImageDrawable(AppCompatResources.getDrawable(context, icon))
        }
    }
    snackbar.show()
    snackbar.addCallback(object : Snackbar.Callback() {
        override fun onDismissed(transientBottomBar: Snackbar?, event: Int) {
            super.onDismissed(transientBottomBar, event)
            onDismissListener.invoke()
        }
    })
}

fun View.singleClick(debounceTime: Long = 600L, action: () -> Unit) {
    this.setOnClickListener(object : View.OnClickListener {
        private var lastClickTime: Long = 0

        override fun onClick(v: View) {
            if (SystemClock.elapsedRealtime() - lastClickTime < debounceTime) return
            else action()

            lastClickTime = SystemClock.elapsedRealtime()
        }
    })
}

fun String.containsIgnoreCase(other: String): Boolean {
    return contains(other = other, ignoreCase = true)
}