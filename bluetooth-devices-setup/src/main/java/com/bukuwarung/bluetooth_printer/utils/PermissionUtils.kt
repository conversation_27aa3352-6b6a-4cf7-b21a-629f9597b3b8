package com.bukuwarung.bluetooth_printer.utils

import android.Manifest
import android.app.Activity
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.pm.PackageManager
import android.location.LocationManager
import android.os.Build
import androidx.core.app.ActivityCompat
import com.bukuwarung.bluetooth_printer.BluetoothDevices.context

object PermissionUtils {

    fun hasPermissions(context: Context, permissions: Array<String>): Boolean =
        permissions.all {
            ActivityCompat.checkSelfPermission(context, it) == PackageManager.PERMISSION_GRANTED
        }

    fun hasLocationPermission(): Boolean {
        if (Build.VERSION.SDK_INT < 23) {
            return true
        }
        val context = context ?: return false
        return (context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
                context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED
                )
    }

    fun isSystemLocationServiceEnabled(context: Context): Boolean {
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
                locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
    }

    fun isSystemBluetoothEnabled(context: Context): Boolean {
        val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        return bluetoothManager.adapter.isEnabled
    }

    fun requestLocationPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= 23) {
            activity.requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ), PermissionConst.ACCESS_LOCATION
            )
        }
    }

    fun hasBluetoothPermission(): Boolean {
        var isGranted = true
        val context = context ?: return false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (context.checkSelfPermission(Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED &&
                context.checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED
            ) {
                isGranted = false
            }
        }
        return isGranted
    }

    fun requestBluetoothPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            activity.requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT
                ), PermissionConst.BLUETOOTH_PERMISSION
            )
        }
    }

    //Call via this function to remove the warning
    fun checkPermission(permission:Int):Boolean{
        when (permission) {
            PermissionConst.BLUETOOTH_PERMISSION -> return hasBluetoothPermission()
        }
        return false
    }
}