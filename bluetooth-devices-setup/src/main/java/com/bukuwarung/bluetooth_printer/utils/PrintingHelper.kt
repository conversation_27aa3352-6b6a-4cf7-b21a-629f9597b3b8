package com.bukuwarung.bluetooth_printer.utils

import android.bluetooth.BluetoothDevice
import android.content.Context
import android.os.Handler
import android.os.Looper
import com.bukuwarung.bluetooth_printer.model.BluetoothCallBack
import com.bukuwarung.bluetooth_printer.model.BluetoothPrintingDeviceCallBack
import com.bukuwarung.bluetooth_printer.model.PairedDevice
import com.bukuwarung.bluetooth_printer.model.basePrinter.PrinterProperty
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType

class PrintingHelper(private var printerProperty: PrinterProperty, private var pairedPrinter: PairedDevice, context: Context) {
    private lateinit var basePrintType: List<BasePrintType>
    private var bluetoothConnection = BluetoothConnection(context)
    var printerCallBack: PrinterCallBack? = null
    var extraLinesAtEnd: Byte = 0

    init {
        initBluetoothCallback()
        initBluetoothDeviceCallback()
    }

    private fun initBluetoothCallback() {
        bluetoothConnection.setBluetoothCallback(object : BluetoothCallBack {
            override fun onBluetoothTurningOn() {}

            override fun onBluetoothOn() {
                bluetoothConnection.connectToAddress(pairedPrinter.address)
            }

            override fun onBluetoothTurningOff() {}

            override fun onBluetoothOff() {}

            override fun onUserDeniedActivation() {}
        })
    }

    private fun initBluetoothDeviceCallback() {
        bluetoothConnection.setDeviceCallback(object : BluetoothPrintingDeviceCallBack {
            override fun onDeviceConnected(device: BluetoothDevice) {
                printPrintables()
                printerCallBack?.printingOrderSentSuccessfully()
            }

            override fun onDeviceDisconnected(device: BluetoothDevice, message: String) {
                printerCallBack?.disconnected()
            }

            override fun onMessage(message: String) {
                printerCallBack?.onMessage(message)
            }

            override fun onError(message: String) {
                printerCallBack?.onError(message)
            }

            override fun onConnectError(device: BluetoothDevice, message: String) {
                printerCallBack?.connectionFailed(message)
            }
        })
    }

    private fun printPrintables() {
        bluetoothConnection.send(printerProperty.initPrinterCommand) // init printer
        this.basePrintType.forEach {
            it.getPrintableByteArray(printerProperty).forEach { ops ->
                bluetoothConnection.send(ops)
            }
        }

        //Feed 2 lines to cut the paper
        if (extraLinesAtEnd > 0) {
            bluetoothConnection.send(printerProperty.feedLineCommand.plus(extraLinesAtEnd))
        }

        Handler(Looper.getMainLooper()).postDelayed({
            bluetoothConnection.disconnect()
        }, 2000)
    }

    fun print(printType: ArrayList<BasePrintType>, callback: PermissionCallback) {
        this.basePrintType = printType
        printerCallBack?.connectingWithPrinter()
        bluetoothConnection.setBluetoothPermissionCallback(callback)
        bluetoothConnection.onStart()
        if (!bluetoothConnection.isEnabled) bluetoothConnection.enable() else bluetoothConnection.connectToAddress(pairedPrinter.address)
    }

    fun printAll(printType: ArrayList<BasePrintType>, callback: PermissionCallback, printCallback: PrinterCallBack) {
        this.basePrintType = printType
        printerCallBack = printCallback
        bluetoothConnection.setBluetoothPermissionCallback(callback)
        bluetoothConnection.onStart()
        if (!bluetoothConnection.isEnabled) bluetoothConnection.enable() else bluetoothConnection.connectToAddress(pairedPrinter.address)
    }
}