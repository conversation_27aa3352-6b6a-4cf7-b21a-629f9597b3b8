package com.bukuwarung.bluetooth_printer.utils

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.bluetooth_printer.R
import com.bukuwarung.bluetooth_printer.base.BaseDialog
import com.bukuwarung.bluetooth_printer.base.BaseDialogType
import com.bukuwarung.bluetooth_printer.databinding.DialogStateBinding

class StateDialog(context: Context) : BaseDialog(context, BaseDialogType.POPUP) {
    private var title: String = ""
    private var subTitle: String = ""
    private var image: Int? = null
    private var isLoader: Boolean = false
    private var btnLeftText: String = ""
    private var btnRightText: String = ""
    private var btnLeftListener: () -> Unit = {}
    private var btnRightListener: () -> Unit = {}
    private var dialogType: DialogType = DialogType.NORMAL
    private var serialNumber: String = ""

    private val binding by lazy {
        DialogStateBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    init {
        setUseFullWidth(false)
        setCancellable(false)
    }

    override fun getResId(): Int {
        return 0
    }

    fun setTitle(title: String): StateDialog {
        this.title = title
        return this
    }

    fun setSubTitle(subTitle: String): StateDialog {
        this.subTitle = subTitle
        return this
    }

    fun setImage(image: Int): StateDialog {
        this.image = image
        return this
    }

    fun setLoader(isLoader: Boolean): StateDialog {
        this.isLoader = isLoader
        return this
    }

    fun setBtnLeftText(btnLeftText: String): StateDialog {
        this.btnLeftText = btnLeftText
        return this
    }

    fun setBtnRightText(btnRightText: String): StateDialog {
        this.btnRightText = btnRightText
        return this
    }

    fun setBtnLeftListener(btnLeftListener: () -> Unit): StateDialog {
        this.btnLeftListener = btnLeftListener
        return this
    }

    fun setBtnRightListener(btnRightListener: () -> Unit): StateDialog {
        this.btnRightListener = btnRightListener
        return this
    }

    fun setDialogType(type: DialogType): StateDialog {
        this.dialogType = type
        return this
    }

    fun sendSerialNumber(serialNumber: String): StateDialog {
        this.serialNumber = serialNumber
        return this
    }

    fun showDialog(): StateDialog {
        setDialog(
            title,
            subTitle,
            image,
            isLoader,
            btnLeftListener,
            btnRightListener,
            btnLeftText,
            btnRightText
        )
        return this
    }


    private fun setDialog(
        title: String?,
        subTitle: String?,
        image: Int?,
        isLoader: Boolean,
        btnLeftListener: () -> Unit,
        btnRightListener: () -> Unit,
        btnLeftText: String,
        btnRightText: String
    ) {
        this.btnLeftListener = btnLeftListener
        this.btnRightListener = btnRightListener
        with(binding) {
            ivImage.visibility = (image != null).asVisibility()
            ivImage.setImageResource(image.orNil)
            tvTitle.text = title
            tvTitle.visibility = title.isNotNullOrBlank().asVisibility()
            tvSubtitle.text = subTitle
            tvSubtitle.visibility = subTitle.isNotNullOrBlank().asVisibility()
            btnRight.visibility = btnRightText.isNotNullOrBlank().asVisibility()
            btnLeft.visibility = btnLeftText.isNotNullOrBlank().asVisibility()
            btnRight.text = btnRightText
            btnLeft.text = btnLeftText
            if (dialogType == DialogType.ACTIVATION) {
                tvDeviceId.visibility = View.VISIBLE
                tvDeviceId.text = serialNumber
            }
            if (isLoader) {
                btnLeft.hideView()
                btnRight.hideView()
            }
            btnLeft.setOnClickListener {
                dismiss()
                btnLeftListener()
            }

            btnRight.setOnClickListener {
                dismiss()
                btnRightListener()
            }
            rootDialog.showView()
            pbLoadingDialog.hideView()
        }
        window?.let {
            it.setLayout(
                (context.resources.displayMetrics.widthPixels * 0.90).toInt(),
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            it.setBackgroundDrawableResource(R.drawable.base_dialog_rounded)
        }
        show()
    }

    fun showLoading(): StateDialog {
        binding.rootDialog.hideView()
        binding.pbLoadingDialog.showView()
        window?.let {
            it.setLayout(
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            it.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }
        show()
        return this
    }

    enum class DialogType {
        NORMAL,
        ACTIVATION,
    }
}