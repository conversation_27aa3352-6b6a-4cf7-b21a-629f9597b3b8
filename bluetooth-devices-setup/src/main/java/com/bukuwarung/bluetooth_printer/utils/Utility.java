package com.bukuwarung.bluetooth_printer.utils;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.bukuwarung.bluetooth_printer.BluetoothDevices;


public class Utility {

    public static boolean isBlank(String str) {
        return str == null || str.length() < 1;
    }

    public static boolean hasInternet() {
        NetworkInfo activeNetworkInfo = ((ConnectivityManager) BluetoothDevices.INSTANCE.getContext().getSystemService(Context.CONNECTIVITY_SERVICE)).getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    public static void showDialogIfActivityAlive(Activity activity, Dialog dialog) {
        if (dialog == null) return;
        if (!activity.isFinishing() && !activity.isDestroyed()) {
            dialog.show();
        }
    }
}
