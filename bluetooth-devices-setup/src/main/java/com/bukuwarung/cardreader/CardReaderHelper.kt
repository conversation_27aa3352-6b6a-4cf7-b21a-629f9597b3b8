package com.bukuwarung.cardreader

import android.app.Application
import android.os.Bundle
import com.bukuwarung.analytic.BtAnalyticConstant.DEVICE_SERVICE_MOREFUN
import com.bukuwarung.analytic.BtAnalyticConstant.DEVICE_SERVICE_TIANYU
import com.bukuwarung.analytic.BtAnalytics
import com.bukuwarung.cardreader.dto.CardReaderInfo
import com.bukuwarung.cardreader.morefun.impl.MorefunCardReader
import com.bukuwarung.cardreader.dto.CardReaderResult
import com.bukuwarung.cardreader.dto.OnlineAuthResult
import com.bukuwarung.cardreader.tianyu.impl.TianyuCardReader
import com.whty.tymposapi.DeviceApi

class CardReaderHelper(private val application: Application) {

    private var cardReader: ICardReader? = null
    private var onCardReaderConnectedListener: OnCardReaderConnectionListener? = null

    companion object {
        private const val TAG = "CardReaderHelper"

        private var instance: CardReaderHelper? = null

        fun getInstance(application: Application): CardReaderHelper {
            if (instance == null) {
                instance = CardReaderHelper(application)
            }
            return instance!!
        }

        fun getInstance(): CardReaderHelper {
            if (instance == null) {
                throw error("You must call CardReaderHelper.getInstance(application: Application)")
            }
            return instance!!
        }
    }

    fun initCardReaderHelper(cardReaderType: CardReaderType) {
        cardReader = when (cardReaderType) {
            CardReaderType.TIANYU -> {
                BtAnalytics.setDeviceName(DEVICE_SERVICE_TIANYU)
                TianyuCardReader(onCardReaderConnectedListener)
            }
            CardReaderType.MOREFUN -> {
                BtAnalytics.setDeviceName(DEVICE_SERVICE_MOREFUN)
                MorefunCardReader(onCardReaderConnectedListener)
            }
        }

        cardReader?.init(application)
    }

    fun setOnServiceConnectedListener(listener: OnCardReaderConnectionListener) {
        this.onCardReaderConnectedListener = listener
    }

    fun terminate() {
        cardReader?.destroy()
    }

    fun connectToDevice(address: String): Boolean {
        return cardReader?.connect(address) == true
    }

    fun disconnectFromDevice() {
        cardReader?.disconnect()
    }

    fun getDevice(): DeviceApi? {
        return cardReader?.getDevice()
    }

    fun isDeviceConnected(): Boolean {
        return cardReader?.isConnected() == true
    }

    fun readCard(emvParam: Bundle): CardReaderResult {
        return cardReader?.readCard(emvParam)!!
    }

    fun updateEmvConfig():Boolean?{
        return cardReader?.updateEmvConfig()
    }
    fun updateRid(): Boolean? {
        return (cardReader?.updateRid() == true) && (cardReader?.updateCapk() == true)
    }
    fun confirmOnlineResult(resp: String, iccData:String): OnlineAuthResult? {
        return cardReader?.confirmOnlineResult(resp, iccData)!!
    }

    fun updateMainKey(key:String):Boolean {
        return cardReader?.loadMasterKey(key) ==true
    }

    fun updateWorkingKey(workKye:String): Boolean {
        return cardReader?.updateWorkingKey(workKye) == true
    }

    fun confirmTransaction(message: String): Boolean {
        return cardReader?.confirmTransaction(message) == true
    }

    fun getCardReaderInfo(): CardReaderInfo {
        return cardReader?.getCardReaderInfo()!!
    }

    fun upgrade(content: ByteArray, listener: FirmwareUpgradeListener){
        cardReader?.upgrade(content,listener)
    }

    fun isCardReaderConnected():Boolean{
        return cardReader?.isConnected() == true
    }

    fun isFirmwareUpgradeRequired():Boolean{
        return cardReader?.isFirmwareUpgradeRequired() == true
    }
}

enum class CardReaderType {
    TIANYU,
    MOREFUN
}



interface OnCardReaderConnectionListener {
    fun onConnected()
    fun onInitFailed()
}
