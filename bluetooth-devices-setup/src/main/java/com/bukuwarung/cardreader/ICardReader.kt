package com.bukuwarung.cardreader

import android.content.Context
import android.os.Bundle
import com.bukuwarung.cardreader.dto.CardReaderInfo
import com.bukuwarung.cardreader.dto.CardReaderResult
import com.bukuwarung.cardreader.dto.OnlineAuthResult
import com.whty.tymposapi.DeviceApi

interface ICardReader {
    fun init(context: Context)
    fun destroy()
    fun getDevice():DeviceApi
    fun connect(address: String): Boolean
    fun disconnect()
    fun isConnected(): Boolean
    fun updateEmvConfig(): Boolean
    fun updateRid(): Boolean
    fun updateCapk(): Boolean
    fun readCard(emvParam: Bundle): CardReaderResult
    fun loadMasterKey(keyHex: String): Boolean
    fun updateWorkingKey(workKye: String): Boolean
    fun encryptData(keyId: Int, data: String): String?
    fun decryptData(keyId: Int, data: String): String?
    fun confirmTransaction(message: String): Boolean
    fun confirmOnlineResult(responseCode: String, iccData: String): OnlineAuthResult?
    fun getCardReaderInfo(): CardReaderInfo
    fun upgrade(content: ByteArray, listener: FirmwareUpgradeListener)
    fun isFirmwareUpgradeRequired():Boolean
}


interface FirmwareUpgradeListener {
    fun upgradeDeviceSuccess()
    fun showProgress(progress: Int)
    fun upgradeFail(errorCode: Int)
}
