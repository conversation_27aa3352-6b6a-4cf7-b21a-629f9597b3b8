package com.bukuwarung.cardreader.contant

object CardConstants {

    val BALANCE_CHECK_TAGS = arrayOf( "9F26", "9F27","9F10","9F37","9F36","5F34","5F2A","9F1A","9F02","9A","82","9C","84","95")
    val TRANSFER_TAGS = arrayOf("9F26", "9F27","9F10","9F37","9F36","5F34","5F2A","9F1A","9F02","9F03","9A","82","9C","84","95")

    object EmvIntentConst {
        const val KEY_AUTH_AMOUNT_String = "authAmount"
        const val KEY_TERMINAL_TIME_String = "terminalTime"
        const val KEY_transProcessCode_byte = "transProcessCode"
        const val KEY_TRANSACTION_TIMEOUT_byte = "timeout"
        const val KEY_FORCE_ONLINE_boolean = "isForceOnline"
        const val KEY_IS_FALLBACK_boolean = "isFallback"

    }

    object OnlineAuthConst {
        const val TC = 0
        const val Online_AAC = 1
        const val ERROR = 107
        const val KEY_TC_DATA_String = "TC_DATA"
        const val KEY_SCRIPT_DATA_String = "SCRIPT_DATA"
        const val KEY_REVERSAL_DATA_String = "REVERSAL_DATA"
    }

    object ResultBundleConst {
        const val KEY_PAN_String = "PAN"
        const val KEY_TRACK2_String = "TRACK2"
        const val KEY_SERVICE_CODE_String = "SERVICE_CODE"
        const val KEY_EXPIRED_DATE_String = "EXPIRED_DATE"
        const val KEY_CARD_ENTRY_MODE_Int = "CARD_ENTRY_MODE"
        const val KEY_PIN_BLOCK = "PIN_BLOCK"
        const val KEY_IC_DATA = "icData"
        const val VAL_CARD_ENTRY_MODE_MAG = 1
        const val VAL_CARD_ENTRY_MODE_IC = 2
    }
}