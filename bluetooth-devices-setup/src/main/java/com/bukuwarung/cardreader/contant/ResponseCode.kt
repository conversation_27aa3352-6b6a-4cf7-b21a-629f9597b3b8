package com.bukuwarung.cardreader.tianyu.contant

enum class ResponseCode(val code: String) {
    ERROR_TIMEOUT("8004"),
    SUCCESS("9000"),
    ERROR_UNKNOWN("-1"),
    ERROR_PIN_LENGTH("10"),
    ERROR_READING_CARD("8530"),
    ERROR_CANCEL("8005");

    companion object {
        fun findByCode(target: String): ResponseCode {
            return values().find { it.code == target } ?: ERROR_UNKNOWN
        }
    }
}