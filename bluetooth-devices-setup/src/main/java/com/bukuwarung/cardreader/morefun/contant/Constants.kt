package com.bukuwarung.cardreader.morefun.contant

object Constants {

    object DefaultKeys {
        const val TDK_RANDOM = "222222222222222233333333333333334444444444444444"
        const val MAK_RANDOM = "444444444444444455555555555555556666666666666666"
        const val MOREFUN_MAC_KEY = "F40379AB9E0EC533F40379AB9E0EC533"
        const val MOREFUN_TDK_KEY = "00000000000000000000000000000000"
        const val KEK = "11111111111111111111111111111111"
        const val TMS_KEK = "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"
    }

    object NsiccsConfig{
        const val AID_STRING = "9F0607A0000006021010DF0101009F08020020DF1105D84000A800DF1205D84004F800DF130500100000009F1B0400000000DF150400000000DF160199DF170199DF14039F3704DF1801019F7B06000000200000DF1906000000200000DF2006000002000000DF2106000200000000"
        const val EMV_PARAM = "9F0106" +
                "313233343536" +
                "9F4005" +
                "E000F0A001" +
                "9F15023031" +
                "9F160F" +
                "313233343536373839303132333435" +
                "9F390105" +
                "9F3303204000" +
                "9F1A020360" +
                "9F1C083132333435363738" +
                "9F350122" +
                "5F2A020360" +
                "5F360102" +
                "9F3C020360" +
                "9F3D0102" +
                "9F660434000080"
    }

    val rids = arrayOf(
        "9F0605" +
                "A000000602" +
                "9F220105" +
                "DF050420211231" +
                "DF070101" +
                "DF0281B0B48CC63D71A486DFC920608A3E42D7C305472BF76B8E50C8C02FB8387E788F72931A29DC15F913E7D69E43AD4C38A5C4317E36D15DE5F49FA2327D9754799D2484A6E156941ACA9632417E5C92931A85E1BB5F2A2C1B847D5008C7B30591F1ACBF3B98DFB0CF2849B6C7CDC7435AEA85F3A58BAC3B8C990416A5E19EC4EA08DC91CEF2FBE5940FA6622926D2AD0523D109A7024EB1035BBE37260B30F41AA52EEB36E60DD37120B9401C3850920F0E03" +
                "DF040103" +
                "DF03141CAB162A1BE81492BB952C2846617B756F833C07" +
                "DF060101",
        ("9F0605" +
                "A000000602" +
                "9F220109" +
                "DF050420211231" +
                "DF070101" +
                "DF0281F8A517A338854E0856EE4AFDBF4BDA5DD3F9EB3895CBD8971B1E58A8EB167BF9935E0752DAEA7EAFB25E79D601EB201895A93F8B0A16D95A230366C05FEC55858C94D6097B2FB1EDDD2C6A3647DD0B71BC1DCDDC68B4E9ECC919FB544070952443159733471292993AB23E5B8C00E6A8526DF04A0B6E65E0F9D0378F71497E12FA83540B49FC05D0A86DC3D66FC4BB291A69B2EBB98D057C8F1EE7CB8E942FD05E9E4FAD0361BC184C13418C313C042C547DEF41310BA1850EF59CAF8CC7B14DAEE72FA4689C1047434024D565A3FA46EDCA3F53E236235268C893F268AA24AB2D20EB7AE06FF3123318041CB23E30839C58DFD4991D7C88CBDF040103" +
                "DF0314E78686DB119C1CBFAD2149EF3CBE9CF54AC6321E" +
                "DF060101"),
        ("9F0605" +
                "A000000602" +
                "9F2201F5" +
                "DF050420211231" +
                "DF070101" +
                "DF0281B09F2E972FF3F00759772AF850CBCD0B8BE411FE2D104CEB1B218E91F9F008DCC30DE61F42224C6A6BE07DAC048DA16CF24C43E600CEC4BBD4C5674494788F703AA4469E7DBC3B9923C2C54B50D4971D176A0E62F6875611B9CE5E247D24AC26926F22D0705D39A65808790232702E8C1A80291CA2C8AD890925B0EA20069761DF863B374FFB41A53ECB229867FBC6475C054B407C41E170265F08634E61324228A32466EF26E6BD5A2C150740A2F5D7B5DF040103" +
                "DF031472454EB38A360FB9735963E8A17E8256C92816DB" +
                "DF060101")
    )
}