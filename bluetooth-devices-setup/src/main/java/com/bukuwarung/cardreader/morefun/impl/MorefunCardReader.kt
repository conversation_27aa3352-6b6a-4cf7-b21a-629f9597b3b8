package com.bukuwarung.cardreader.morefun.impl

import android.content.Context
import android.os.Bundle
import android.util.Log
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.cardreader.CardReaderType
import com.bukuwarung.cardreader.FirmwareUpgradeListener
import com.bukuwarung.cardreader.ICardReader
import com.bukuwarung.cardreader.OnCardReaderConnectionListener
import com.bukuwarung.cardreader.contant.CardConstants
import com.bukuwarung.cardreader.contant.CardConstants.BALANCE_CHECK_TAGS
import com.bukuwarung.cardreader.contant.CardConstants.TRANSFER_TAGS
import com.bukuwarung.cardreader.dto.CardReaderInfo
import com.bukuwarung.cardreader.dto.CardReaderResult
import com.bukuwarung.cardreader.dto.OnlineAuthResult
import com.bukuwarung.cardreader.morefun.contant.Constants
import com.bukuwarung.cardreader.morefun.contant.Constants.DefaultKeys.MOREFUN_MAC_KEY
import com.bukuwarung.cardreader.morefun.contant.Constants.DefaultKeys.MOREFUN_TDK_KEY
import com.bukuwarung.cardreader.morefun.contant.Constants.DefaultKeys.TMS_KEK
import com.bukuwarung.cardreader.tianyu.contant.ResponseCode
import com.bukuwarung.cardreader.tianyu.util.DesUtils
import com.bukuwarung.cardreader.tianyu.util.TianyuDataUtils
import com.bukuwarung.cardreader.tianyu.util.TlvUtils
import com.bukuwarung.cardreader.utils.BytesUtil
import com.bukuwarung.cardreader.utils.SecurityUtil
import com.mf.mpos.pub.CommEnum
import com.mf.mpos.pub.Controler
import com.mf.mpos.pub.EmvTagDef
import com.mf.mpos.pub.param.ReadCardParam
import com.mf.mpos.util.Misc
import com.whty.device.utils.GPMethods
import com.whty.tymposapi.DeviceApi
import javax.inject.Inject

class MorefunCardReader @Inject constructor(private val onCardReaderConnectedListener: OnCardReaderConnectionListener?): ICardReader {

    private var context: Context? = null

    private var plainMainKey = ""
    private var hsmMainKey = ""

    override fun init(context: Context) {
        this.context = context
        Controler.Init(context, CommEnum.CONNECTMODE.BLUETOOTH, 0)
    }

    override fun destroy() {
        Controler.disconnectPos()
    }

    override fun getDevice(): DeviceApi {
        TODO("Not yet implemented")
    }

    override fun connect(address: String): Boolean {
        return Controler.connectPos(address).bConnected
    }

    override fun disconnect() {
        Controler.disconnectPos()
    }

    override fun isConnected(): Boolean {
        return Controler.posConnected()
    }

    private fun clearCa(): Boolean {
        val result = Controler.ICPublicKeyManage(CommEnum.ICPUBLICKEYACTION.CLEAR, null)
        return (result.commResult == CommEnum.COMMRET.NOERROR)
    }

    override fun updateEmvConfig(): Boolean {
        val emvConfigResult = Controler.SetEmvParamTlv(Constants.NsiccsConfig.EMV_PARAM)
        val clearAidResult = Controler.ICAidManage(CommEnum.ICAIDACTION.CLEAR, null);
        val aidResult = Controler.ICAidManage(CommEnum.ICAIDACTION.ADD, Misc.asc2hex(Constants.NsiccsConfig.AID_STRING))
        val updateAidResult = aidResult.commResult == CommEnum.COMMRET.NOERROR

        bwLog("card_reader=>clearAid=$clearAidResult\n" +
                "updateEmvConfig: $emvConfigResult\n" +
                "updateAidResult: $updateAidResult")

        //skip here, the AID will update in updateRid, to make sure updateCapk is executed
        //return (clearAidResult.commResult == CommEnum.COMMRET.NOERROR) && updateAidResult && emvConfigResult
        return false
    }

    override fun updateRid(): Boolean {
        val emvConfigResult = Controler.SetEmvParamTlv(Constants.NsiccsConfig.EMV_PARAM)
        val clearAidResult = Controler.ICAidManage(CommEnum.ICAIDACTION.CLEAR, null);
        val aidResult = Controler.ICAidManage(CommEnum.ICAIDACTION.ADD, Misc.asc2hex(Constants.NsiccsConfig.AID_STRING))
        val updateAidResult = aidResult.commResult == CommEnum.COMMRET.NOERROR

        bwLog("card_reader=>clearAid=$clearAidResult\n" +
                "updateEmvConfig: $emvConfigResult\n" +
                "updateAidResult: $updateAidResult")

        return (clearAidResult.commResult == CommEnum.COMMRET.NOERROR) && updateAidResult && emvConfigResult
    }

    override fun updateCapk(): Boolean {
        val clearRidResult = clearCa()
        bwLog("card_reader=>clearRidResult=$clearRidResult")
        for (rid in Constants.rids) {
            val result =
                Controler.ICPublicKeyManage(CommEnum.ICPUBLICKEYACTION.ADD, Misc.asc2hex(rid))
            if (result.commResult != CommEnum.COMMRET.NOERROR) {
                bwLog("card_reader=>updateCapkResult=false")
                return false
            } else {
                bwLog("card_reader=>updateCapkResult=true")
            }
        }
        return true
    }

    override fun decryptData(keyId:Int, data:String):String?{
        return ""
    }

    override fun confirmTransaction(message: String): Boolean {
        Controler.CancelComm()
        Controler.screen_show(message, 5, false, false)
        return true
    }

    override fun encryptData(keyId:Int, data:String):String?{
        TODO("implement encrypt method")
    }

    override fun loadMasterKey(testKey: String): Boolean {
        loadKek()
        var masterKey = testKey

        //if masterkey is 56 in length that means it includes KCV as suffix (length 8), remove KCV from masterKey before processing
        if(masterKey.length == 56){
            val encKeyValue =  masterKey.substring(0,48); // KEY from HSM is 48 in length
            //Decrypt key with tms key encryption key
            val decKeyByte: ByteArray = DesUtils.decrypt3DES(
                GPMethods.str2bytes(encKeyValue), GPMethods.str2bytes(
                    TMS_KEK
                )
            )
            //convert dec key byte array to get original master key
            val decKeyValue = GPMethods.bytesToHexString(decKeyByte)
            masterKey = decKeyValue;
        }

        val data = "0000000000000000"
        hsmMainKey = masterKey
        val encKey: ByteArray = SecurityUtil.doubleDes(
            BytesUtil.hexString2ByteArray(masterKey),
            BytesUtil.hexString2ByteArray(Constants.DefaultKeys.KEK)
        )
        val plain: ByteArray = SecurityUtil.doubleUnDes(BytesUtil.hexString2ByteArray(Constants.DefaultKeys.KEK), encKey)
        val kcv: ByteArray = SecurityUtil.doubleDes(plain, BytesUtil.hexString2ByteArray(data))
        plainMainKey = BytesUtil.bytes2Hex(plain)
        val kvc:String = BytesUtil.bytes2Hex(kcv).substring(0, 8)

        val key = BytesUtil.bytes2Hex(encKey)

        val keyBuf = BytesUtil.hexString2ByteArray(key)
        val kekD1 = BytesUtil.subBytes(keyBuf, 0, 8)
        val kekD2 = BytesUtil.subBytes(keyBuf, 8, 8)
        val kvcBuf = BytesUtil.hexString2ByteArray(kvc)

        Misc.traceHex("card_reader", "updateMainKey kekD1", kekD1)
        Misc.traceHex("card_reader", "updateMainKey kekD2", kekD2)
        Misc.traceHex("card_reader", "updateMainKey kvc", kvcBuf)

        val loadMainKeyResult = Controler.LoadMainKey(
            CommEnum.MAINKEYENCRYPT.KEK,
            CommEnum.KEYINDEX.INDEX0,
            CommEnum.MAINKEYTYPE.DOUBLE,
            kekD1, kekD2, kvcBuf
        )
        Log.d("card_reader", "updateMainKey=${loadMainKeyResult.loadResult} hsm=$masterKey plainMainKey=$plainMainKey")
        return true
    }

    private fun loadKek(){
        val key = "11111111111111111111111111111111"
        val kvc = "82E13665"

        val keyBuf = BytesUtil.hexString2ByteArray(key)

        val kekD1 = BytesUtil.subBytes(keyBuf, 0, 8)
        val kekD2 = BytesUtil.subBytes(keyBuf, 8, 8)
        val kvcBuf = BytesUtil.hexString2ByteArray(kvc)

        Controler.LoadKek(CommEnum.KEKTYPE.DOUBLE, kekD1, kekD2, kvcBuf)
    }

    override fun updateWorkingKey(workKey:String): Boolean {
        var pinKey = workKey
        val macKey = MOREFUN_MAC_KEY
        val tdkKey = MOREFUN_TDK_KEY

        val data = "0000000000000000"

        pinKey = BytesUtil.bytes2Hex(
            SecurityUtil.threeUnDes(
                BytesUtil.hexString2ByteArray(hsmMainKey),
                BytesUtil.hexString2ByteArray(pinKey)
            )
        )
        pinKey = BytesUtil.bytes2Hex(
            SecurityUtil.doubleDes(
                BytesUtil.hexString2ByteArray(plainMainKey),
                BytesUtil.hexString2ByteArray(pinKey)
            )
        )

        val plainPinKey = SecurityUtil.doubleUnDes(
            BytesUtil.hexString2ByteArray(plainMainKey),
            BytesUtil.hexString2ByteArray(pinKey)
        )
        val kvcPin = SecurityUtil.doubleDes(plainPinKey, BytesUtil.hexString2ByteArray(data))
        var pinKvc = BytesUtil.bytes2Hex(kvcPin).substring(0, 8)
        val plainMacKey = SecurityUtil.doubleUnDes(
            BytesUtil.hexString2ByteArray(plainMainKey),
            BytesUtil.hexString2ByteArray(macKey)
        )
        val kvcMac = SecurityUtil.doubleDes(plainMacKey, BytesUtil.hexString2ByteArray(data))
        var macKvc = BytesUtil.bytes2Hex(kvcMac).substring(0, 8)
        val plainTdkKey = SecurityUtil.doubleUnDes(
            BytesUtil.hexString2ByteArray(plainMainKey),
            BytesUtil.hexString2ByteArray(tdkKey)
        )
        val kvcTdk = SecurityUtil.doubleDes(plainTdkKey, BytesUtil.hexString2ByteArray(data))
        var tdkKvc = BytesUtil.bytes2Hex(kvcTdk).substring(0, 8)

        val key = pinKey + pinKvc + macKey + macKvc + tdkKey + tdkKvc

        val keyArrays = Misc.asc2hex(key)
        Log.d("card_reader", "updateWorkingKey key:$key")

        val result = Controler.LoadWorkKey(
            CommEnum.KEYINDEX.INDEX0,
            CommEnum.WORKKEYTYPE.DOUBLEMAG,
            keyArrays,
            keyArrays.size
        )
        Log.d("card_reader", "updateworkingKey=${result.loadResult}")
        return result.loadResult
    }

    override fun confirmOnlineResult(responseCode: String, iccData:String): OnlineAuthResult? {
        var data = Bundle();
        var result = CardConstants.OnlineAuthConst.Online_AAC
        try {
            val arqcScript = Misc.asc2hex(iccData)
            val tags: MutableList<ByteArray> = ArrayList()
            val r = Controler.EmvDealOnlineRsp(true, arqcScript, arqcScript.size, responseCode)
            if (r.commResult == CommEnum.COMMRET.NOERROR) {
                if (r.authResult == CommEnum.EMVDEALONLINERSP.SUCC) {
                    tags.add(byteArrayOf(0x9F.toByte(), 0x26.toByte()))
                    tags.add(byteArrayOf(0x95.toByte()))
                    tags.add(byteArrayOf(0x4F.toByte()))
                    tags.add(byteArrayOf(0x5F.toByte(), 0x34.toByte()))
                    tags.add(byteArrayOf(0x9B.toByte()))
                    tags.add(byteArrayOf(0x9F.toByte(), 0x36.toByte()))
                    tags.add(byteArrayOf(0x82.toByte()))
                    tags.add(byteArrayOf(0x9F.toByte(), 0x37.toByte()))
                    tags.add(byteArrayOf(0x50.toByte()))
                    val rdata = Controler.GetEmvData(tags, false)
                    if (rdata.commResult == CommEnum.COMMRET.NOERROR) {
                        data.putString(CardConstants.OnlineAuthConst.KEY_TC_DATA_String,Misc.hex2asc(rdata.tlvData))
                        result = CardConstants.OnlineAuthConst.TC
                    }
                    data.putString(CardConstants.OnlineAuthConst.KEY_REVERSAL_DATA_String,iccData)
                } else {
                    Log.d("confirmOnlineResult", r.authResult.name)
                    result = CardConstants.OnlineAuthConst.Online_AAC
                }
            } else {
                Log.d("confirmOnlineResult", r.commResult.toDisplayName())
            }
        } catch (e: NullPointerException) {
            e.printStackTrace()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        } finally {
            Controler.EndEmv()
        }
        return OnlineAuthResult(
            result = result,
            data = data
        )
    }

    override fun upgrade(content: ByteArray, listener: FirmwareUpgradeListener) {

    }

    override fun readCard(emvIntent: Bundle): CardReaderResult {
        val readCardParam = getReadCardParam(emvIntent)
        val result = Controler.ReadCard(readCardParam)
        val data = Bundle()
        var responseCode = ResponseCode.ERROR_UNKNOWN
        if (result.commResult != CommEnum.COMMRET.NOERROR) {
            responseCode = ResponseCode.ERROR_UNKNOWN

        }
        when (result.cardType) {
            0 -> {
                responseCode = ResponseCode.ERROR_CANCEL
            }
            1, 2, 3 -> {
                if(result.pinLen==null || result.pinLen!=6){
                    responseCode = ResponseCode.ERROR_PIN_LENGTH
                }else {
                    responseCode = ResponseCode.SUCCESS

                    data.putInt(
                        CardConstants.ResultBundleConst.KEY_CARD_ENTRY_MODE_Int,
                        CardConstants.ResultBundleConst.VAL_CARD_ENTRY_MODE_IC
                    )

                    data.putString(
                        CardConstants.ResultBundleConst.KEY_PAN_String,
                        result.pan
                    )

                    val track2Plain = if (result.track2.startsWith(result.pan)) {
                        result.track2
                    } else {
                        encodeTrack2(result.track2)
                    }
                    data.putString(
                        CardConstants.ResultBundleConst.KEY_TRACK2_String,
                        cleanTrack2(track2Plain)
                    )

                    data.putString(
                        CardConstants.ResultBundleConst.KEY_SERVICE_CODE_String,
                        result.serviceCode?.replace("f", "")
                    )
                    data.putString(
                        CardConstants.ResultBundleConst.KEY_EXPIRED_DATE_String,
                        result.expData?.let {
                            TianyuDataUtils.getFormattedExpiryDate(
                                it
                            )
                        }
                    )
                    data.putString(CardConstants.ResultBundleConst.KEY_PIN_BLOCK, result.pinblock)
                    data.putInt(
                        CardConstants.ResultBundleConst.KEY_CARD_ENTRY_MODE_Int,
                        result.cardType
                    )
                    if (result.cardType == CardConstants.ResultBundleConst.VAL_CARD_ENTRY_MODE_MAG) {
                        data.putString(CardConstants.ResultBundleConst.KEY_IC_DATA, "")
                    } else {
                        try {
                            val iccData: String = TlvUtils.getAppTLVList(
                                result.icData,
                                if (readCardParam?.emvTransactionType == 0x40.toByte()) TRANSFER_TAGS else BALANCE_CHECK_TAGS,
                                result.pansn
                            )
                            data.putString(CardConstants.ResultBundleConst.KEY_IC_DATA, iccData)
                        } catch (e: Exception) {
                            data.putString(
                                CardConstants.ResultBundleConst.KEY_IC_DATA,
                                result.icData
                            )
                        }
                    }
                }
            }
            4 -> {
                responseCode = ResponseCode.ERROR_READING_CARD
            }
            5 -> {
                responseCode = ResponseCode.ERROR_TIMEOUT
            }
            6 -> {
                responseCode = ResponseCode.ERROR_READING_CARD
            }
            else -> {
                responseCode = ResponseCode.ERROR_READING_CARD
            }
        }
        return CardReaderResult(
            responseCode,
            data
        )
    }

    private fun getCardMode(): Byte {
        var cardMode: Byte = 0
        cardMode = (cardMode + 1).toByte()
        cardMode = (cardMode + 2).toByte()
        return cardMode
    }

    private fun getReadCardParam(emvIntent:Bundle): ReadCardParam? {
        val transProcessCode = emvIntent.getByte(CardConstants.EmvIntentConst.KEY_transProcessCode_byte)
        val amount = emvIntent.getString(CardConstants.EmvIntentConst.KEY_AUTH_AMOUNT_String)?.toLong()!!
        val param = ReadCardParam()
        param.trackEncryptType = 0x1.toByte()
        param.isAllowfallback = false
        param.amount = amount
        param.isPinInput = 1.toByte()
        param.pinMaxLen = 6.toByte()
        param.cardTimeout = 60.toByte()
        param.isForceonline = emvIntent.getBoolean(CardConstants.EmvIntentConst.KEY_FORCE_ONLINE_boolean)
        param.transName = if(transProcessCode == 0x40.toByte()) "Transfer" else "Cek Saldo"
        param.tags = getTags(transProcessCode)
        param.cardmode = getCardMode()
        param.emvTransactionType = transProcessCode
        param.inputPinLine1Tip = if(transProcessCode == 0x40.toByte()) "Transfer" else "Cek Saldo"
        param.inputPinLine2Tip = "Masukkan pin"
        param.inputPinLine3Tip = ""
        param.inputPinLine4Tip = ""
        param.swipeInsertTapCardTip = "Masukkan/Gesek Kartu"
        param.swipeCardTip = "Gesek Kartu"
        param.insertCardTip = "Masukkan Kartu"
        param.insertTapCardTip = "Tap card"
        param.readCardLine2Tip = ""
        param.tipTimeout = 60.toByte()
        param.encryMode = CommEnum.trackAndPasswordEncryptEnum.MKSK
        param.inputPinMode = 1.toByte();
        param.isSupportBypassPine = 0.toByte()
        return param
    }

    private fun getTags(transProcessCode: Byte): List<ByteArray>? {
        val tags: MutableList<ByteArray> = ArrayList()
        tags.add(EmvTagDef.EMV_TAG_9F26_IC_AC)
        tags.add(EmvTagDef.EMV_TAG_9F27_IC_CID)
        tags.add(EmvTagDef.EMV_TAG_9F10_IC_ISSAPPDATA)
        tags.add(EmvTagDef.EMV_TAG_9F37_TM_UNPNUM)
        tags.add(EmvTagDef.EMV_TAG_9F36_IC_ATC)
        tags.add(EmvTagDef.EMV_TAG_5F34_IC_PANSN)
        tags.add(EmvTagDef.EMV_TAG_5F2A_TM_CURCODE)
        tags.add(EmvTagDef.EMV_TAG_9F1A_TM_CNTRYCODE)
        tags.add(EmvTagDef.EMV_TAG_9F02_TM_AUTHAMNTN)
        tags.add(EmvTagDef.EMV_TAG_9A_TM_TRANSDATE)
        tags.add(EmvTagDef.EMV_TAG_82_IC_AIP)
        tags.add(EmvTagDef.EMV_TAG_9C_TM_TRANSTYPE)
        tags.add(EmvTagDef.EMV_TAG_84_IC_DFNAME)
        tags.add(EmvTagDef.EMV_TAG_95_TM_TVR)
        if(transProcessCode == 0x40.toByte()){
            tags.add(EmvTagDef.EMV_TAG_9F03_TM_OTHERAMNTN)
        }
        return tags
    }

    override fun getCardReaderInfo(): CardReaderInfo {
        return CardReaderInfo(
            deviceSn = Controler.ReadPosInfo_ktc().sn,
            hardwareVersion = Controler.ReadPosInfo_ktc().dataVer,
            softwareVersion = Controler.ReadPosInfo_ktc().posVer,
            devicePn = Controler.ReadPosInfo().model,
            manufacturer = CardReaderType.MOREFUN.name
        )
    }

    override fun isFirmwareUpgradeRequired(): Boolean {
        return false
    }

    private fun encodeTrack2(data: String): String {
        val tracks = data.split("D")
        val plain1 = decodeHex("${tracks[0]}D").replace("=", "")
        val plain2 = decodeHex(tracks[1])
        return "${plain1}D${plain2}"
    }

    private fun decodeHex(data:String): String {
        require(data.length % 2 == 0) {"Must have an even length"}
        return data.chunked(2)
            .map { it.toInt(16).toByte() }
            .toByteArray()
            .toString(Charsets.ISO_8859_1)
    }

    private fun cleanTrack2(track2: String): String {
        val track2Formatted = track2.replace("=", "D")
        if (track2Formatted.endsWith("F")) {
            return track2.trimEnd('F').uppercase()
        }
        return track2Formatted.uppercase()
    }
}
