package com.bukuwarung.cardreader.tianyu.contant

object Constants {
    object DefaultKeys {
        const val TDK_RANDOM = "222222222222222233333333333333334444444444444444"
        const val MAK_RANDOM = "444444444444444455555555555555556666666666666666"
        const val KEK = "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"
    }

    const val TIANYU_LATEST_FIRMWARE = "V11.00.01.R5014"

    object ResultMapConst {
        const val KEY_CARD_TYPE = "cardType"
        const val KEY_CARD_NUMBER = "cardNumber"
        const val KEY_CARD_SEQ_NUMBER = "cardSeqNum" //tag 5F34
        const val KEY_ENC_TRACK2 = "encTrack2Ex"
        const val KEY_SERVICE_CODE = "serviceCode"
        const val KEY_EXPIRED_DATE = "expiryDate"
        const val KEY_PIN_BLOCK = "pin"
        const val KEY_IC_DATA = "icData"
        const val KEY_ERROR_CODE = "errorCode"
        const val VAL_CARD_ENTRY_MODE_MAG = "00"
        const val VAL_CARD_ENTRY_MODE_IC = "01"
    }
}