package com.bukuwarung.cardreader.tianyu.impl

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import com.bukuwarung.analytic.BtAnalyticConstant
import com.bukuwarung.analytic.BtAnalytics
import com.bukuwarung.cardreader.CardReaderType
import com.bukuwarung.cardreader.FirmwareUpgradeListener
import com.bukuwarung.cardreader.ICardReader
import com.bukuwarung.cardreader.OnCardReaderConnectionListener
import com.bukuwarung.cardreader.contant.CardConstants
import com.bukuwarung.cardreader.dto.CardReaderInfo
import com.bukuwarung.cardreader.dto.CardReaderResult
import com.bukuwarung.cardreader.dto.OnlineAuthResult
import com.bukuwarung.cardreader.tianyu.contant.Constants
import com.bukuwarung.cardreader.tianyu.contant.ResponseCode
import com.bukuwarung.cardreader.tianyu.util.DesUtils
import com.bukuwarung.cardreader.tianyu.util.TianyuDataUtils
import com.example.yourpackage.AID_STRING
import com.example.yourpackage.CAPK_LIST
import com.example.yourpackage.EMV_CONFIG_STRING
import com.example.yourpackage.RID_STRING
import com.whty.comm.inter.ICommunication
import com.whty.device.delegate.UpgradeListener
import com.whty.device.utils.GPMethods
import com.whty.tymposapi.Constant
import com.whty.tymposapi.DeviceApi
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

class TianyuCardReader @Inject constructor(private val onCardReaderConnectedListener: OnCardReaderConnectionListener?): ICardReader {


    private var context: Context? = null
    private lateinit var device: DeviceApi
    private var isInited = false

    override fun init(context: Context) {
        this.context = context
        this.device = DeviceApi(context)
        if (!isInited) {
            if (device.initDevice(ICommunication.BLUETOOTH_DEVICE)) {
                isInited = true
                onCardReaderConnectedListener?.onConnected()
            } else {
                onCardReaderConnectedListener?.onInitFailed()
                Log.d("card_reader","initialisation failed")
            }
        }
    }

    override fun destroy() {
        device.disconnectDevice()
    }

    override fun getDevice(): DeviceApi {
        return device
    }

    override fun connect(address: String): Boolean {
        return device.connectDevice(address)
    }

    override fun disconnect() {
        device.disconnectDevice()
    }

    override fun isConnected(): Boolean {
        return device.isConnected()
    }

    override fun updateEmvConfig(): Boolean {
        val clearAidResult = device.clearAID()
        val updateAidResult = device.updateAID(AID_STRING.toByteArray())
        val updateEmvConfigResult = device.uploadEmvConfig(EMV_CONFIG_STRING.toByteArray())
        Log.d("card_reader", "clearAid=$clearAidResult\n" +
                "updateEmvConfig: $updateEmvConfigResult\n" +
                "updateAidResult: $updateAidResult")
        try {
            val props = HashMap<String, String>()
            props["load_emv_config"] = updateEmvConfigResult.toString()
            props["load_aid"] = updateAidResult.toString()
            props["clear_aid"] = clearAidResult.toString()
            BtAnalytics.trackEventMobile("update_emv_config",props)
        }catch (e:Exception){
            e.printStackTrace()
        }
        return updateAidResult && updateEmvConfigResult

    }

    override fun updateRid(): Boolean {
        val clearRidResult = device.clearRID()
        val updateRIDResult = device.updateRID(RID_STRING.toByteArray())
        Log.d("card_reader", "clearRid=$clearRidResult\nupdateRid=$updateRIDResult")
        try {
            val props = HashMap<String, String>()
            props["load_rid"] = updateRIDResult.toString()
            props["clear_aid"] = clearRidResult.toString()
            BtAnalytics.trackEventMobile("update_rid",props)
        }catch (e:Exception){
            e.printStackTrace()
        }
        return updateRIDResult;
    }

    override fun updateCapk(): Boolean {
        val clearCapk = device.clearCA()
        Log.d("card_reader", "clearCapk=$clearCapk")

        var capkUpdateSuccess = true
        for (capk in CAPK_LIST) {
            var result = device.updateCA(capk.toByteArray())
            Log.d("card_reader", "updateCA=$result\n" +
                    "capk=$capk")
            capkUpdateSuccess = capkUpdateSuccess && result
        }

        return capkUpdateSuccess;
    }

    override fun readCard(param: Bundle): CardReaderResult {
        val format = SimpleDateFormat(
            "yyyyMMddHHmmss", Locale.getDefault()
        )
        val terminalTime = format.format(Date())
        val isForceOnline:Boolean = param.getBoolean(CardConstants.EmvIntentConst.KEY_FORCE_ONLINE_boolean)
        val transProcessCode = param.getByte(CardConstants.EmvIntentConst.KEY_transProcessCode_byte)
        val result = device.readCardWithTradeData(
            param.getString(CardConstants.EmvIntentConst.KEY_AUTH_AMOUNT_String),
            terminalTime.substring(2),
            transProcessCode,
            param.getByte(CardConstants.EmvIntentConst.KEY_TRANSACTION_TIMEOUT_byte),
            true,
            false,
            if(isForceOnline) "FF500101" else null,
            0x07.toByte()
        )

        val responseCode = ResponseCode.findByCode(result[Constants.ResultMapConst.KEY_ERROR_CODE]!!)

        if(responseCode!=ResponseCode.SUCCESS){
            return CardReaderResult(
                response = responseCode,
                TianyuDataUtils.createBundleFromResult(result,transProcessCode,"", "" )
            )
        }

        val track2Plain = if(result[Constants.ResultMapConst.KEY_ENC_TRACK2]?.startsWith(result[Constants.ResultMapConst.KEY_CARD_NUMBER]!!) == true){
            result[Constants.ResultMapConst.KEY_ENC_TRACK2]!!
        } else {
            decryptData(Constant.WorkingKey.TDK, result[Constants.ResultMapConst.KEY_ENC_TRACK2]!!)
        }
        var pinBlock = ""
        if(result[Constants.ResultMapConst.KEY_CARD_TYPE] == Constants.ResultMapConst.VAL_CARD_ENTRY_MODE_MAG){
            val pinBlockResult: HashMap<String, String> =
                device.getEncPinblock(
                    "",
                    0x00.toByte(),
                    false,
                    0x60
                )
            pinBlock = pinBlockResult[Constants.ResultMapConst.KEY_PIN_BLOCK].toString()
        }else{
            pinBlock = result[Constants.ResultMapConst.KEY_PIN_BLOCK]?:""
        }

        val cardReaderResult = CardReaderResult(
            response = responseCode,
            TianyuDataUtils.createBundleFromResult(result,transProcessCode,track2Plain!!, pinBlock )
        )
        return cardReaderResult
    }

    override fun decryptData(keyId:Int, data:String):String?{
        val decryptResult = device.encryptData(
            keyId,
            Constant.EncryptMode.DECRYPT,
            GPMethods.str2bytes(data)
        )
        return if(decryptResult!=null){
            decryptResult["data"]
        }else{
            data
        }
    }

    override fun confirmTransaction(message: String): Boolean {
        return device.confirmTransaction(message)
    }

    override fun encryptData(keyId:Int, data:String):String?{
        val decryptResult = device.encryptData(
            keyId,
            Constant.EncryptMode.ENCRYPT,
            GPMethods.str2bytes(data)
        )
        return if(decryptResult!=null){
            decryptResult["data"]
        }else{
            data
        }
    }

    override fun loadMasterKey(masterKey: String): Boolean {
        var kekResult = loadKek()
        val props = HashMap<String, String>()
        var result = false;
        if(masterKey.length > 48){
            result = device.updateMainKey(GPMethods.str2bytes(masterKey))
            try {
                props["TMK_SUFFIX"] = masterKey.takeLast(8)
                props["TMK_TYPE"] = "encrypted"
            }catch (e:Exception){
                e.printStackTrace()
            }
            Log.d("loadKey", "masterKey:$masterKey, tdk:$masterKey, result:$result")
        }else{
            val encNew: ByteArray = DesUtils.encrypt3DES(
                GPMethods.str2bytes(masterKey), GPMethods.str2bytes(
                    Constants.DefaultKeys.KEK
                )
            )
            val data = "00000000000000000000000000000000"
            val kcv = GPMethods.bytesToHexString(
                DesUtils.encrypt3DES(
                    GPMethods.str2bytes(data),
                    GPMethods.str2bytes(masterKey)
                )
            ).substring(0, 8)
            val tdk =
                GPMethods.bytesToHexString(encNew).uppercase() + kcv.uppercase()
            try {
                props["TMK_KCV"] = kcv.uppercase()
                props["TMK_SUFFIX"] = masterKey.takeLast(8)
                props["TMK_TYPE"] = "plain"
            }catch (e:Exception){
                e.printStackTrace()
            }
            result = device.updateMainKey(GPMethods.str2bytes(tdk))
            Log.d("loadKey", "masterKey:$masterKey, tdk:$tdk, result:$result")
        }
        try {
            props["KEK"] = kekResult.toString()
            props["TMK"] = result.toString()
            BtAnalytics.trackEventMobile("update_main_key",props)
        }catch (e:Exception){
            e.printStackTrace()
        }
        return result
    }

    override fun updateWorkingKey(workKey:String): Boolean {
        val result: BooleanArray? = device.updateWorkingKey(Constants.DefaultKeys.TDK_RANDOM, workKey, Constants.DefaultKeys.MAK_RANDOM)
        try {
            val props = HashMap<String, String>()
            props["TDK_RANDOM"] = result?.get(0).toString()
            props["PIK_RANDOM"] = result?.get(1).toString()
            props["MAK_RANDOM"] = result?.get(2).toString()
            BtAnalytics.trackEventMobile("update_work_key",props)
        }catch (e:Exception){
            e.printStackTrace()
        }
        Log.d("loadKey", "workKey:$workKey, tdk:${result?.get(1)}")
        return result?.get(1) == true
    }

    override fun confirmOnlineResult(resp:String, iccData: String): OnlineAuthResult? {
        val resultMap = device.ICTradeResponse(
            "3030", iccData)
        var data = Bundle();
        data.putString(CardConstants.OnlineAuthConst.KEY_TC_DATA_String, resultMap?.get("IC55"))
        data.putString(CardConstants.OnlineAuthConst.KEY_REVERSAL_DATA_String, resultMap?.get("IC55"))
        var result = if(resultMap?.get("errorCode") == "9000") CardConstants.OnlineAuthConst.TC else CardConstants.OnlineAuthConst.Online_AAC
        return OnlineAuthResult(
            result,
            data
        )
    }

    override fun getCardReaderInfo(): CardReaderInfo {
        return CardReaderInfo(
            deviceSn = device.deviceSN,
            hardwareVersion = device.hardwareVersion,
            softwareVersion = device.deviceVersion,
            devicePn = device.devicePN,
            manufacturer = CardReaderType.TIANYU.name
        )
    }

    override fun upgrade(content: ByteArray, listener: FirmwareUpgradeListener) {
        device.upgrade(content, object : UpgradeListener {
            override fun upgradeDeviceSuccess() {
                listener.upgradeDeviceSuccess()
            }

            override fun showProgress(i: Int) {
                listener.showProgress(i)
            }

            override fun upgradeFail(i: Int) {
                listener.upgradeFail(i)
            }
        })

    }

    override fun isFirmwareUpgradeRequired(): Boolean {
        Log.d("card_reader",getCardReaderInfo().softwareVersion)
        return (getCardReaderInfo().softwareVersion != Constants.TIANYU_LATEST_FIRMWARE)
    }

    fun loadKek():Boolean{
        val srcData = GPMethods.str2bytes(Constants.DefaultKeys.KEK)
        val cmd_head = GPMethods.str2bytes("F0ED000010")
        cmd_head[4] = srcData.size.toByte()
        val cmd_send = ByteArray(cmd_head.size + srcData.size)
        System.arraycopy(cmd_head, 0, cmd_send, 0, cmd_head.size)
        System.arraycopy(srcData, 0, cmd_send, cmd_head.size, srcData.size)

        val response = ByteArray(10)
        val ret: Int = device.transCommand(cmd_send, cmd_send.size, response, 1000)
        Log.e("ret", ret.toString())
        if (ret > 0) {
            val res = ByteArray(ret)
            System.arraycopy(response, 0, res, 0, ret)
            if (res[ret - 2] == 0x90.toByte() && res[ret - 1].toInt() == 0x00) {
                Log.d("card_reader","updateTransKey success")
                return true;
            } else {
                Log.d("card_reader","updateTransKey fail")
                return false;
            }
        }
        return false;
    }
}
