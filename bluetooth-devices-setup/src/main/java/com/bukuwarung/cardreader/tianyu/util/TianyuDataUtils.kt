package com.bukuwarung.cardreader.tianyu.util

import android.os.Bundle
import com.bukuwarung.cardreader.contant.CardConstants
import com.bukuwarung.cardreader.contant.CardConstants.BALANCE_CHECK_TAGS
import com.bukuwarung.cardreader.contant.CardConstants.TRANSFER_TAGS
import com.bukuwarung.cardreader.tianyu.contant.Constants
import com.bukuwarung.cardreader.tianyu.contant.ResponseCode
import com.whty.device.utils.GPMethods

object TianyuDataUtils {

    fun createBundleFromResult(
        result: Map<String, String>,
        transProcessCode:Byte,
        track2Plain: String,
        pinBlock: String
    ): Bundle {
        val bundle = Bundle()

        bundle.putString(
            CardConstants.ResultBundleConst.KEY_PAN_String,
            result[Constants.ResultMapConst.KEY_CARD_NUMBER]
        )
        bundle.putString(CardConstants.ResultBundleConst.KEY_TRACK2_String, getTrack2(track2Plain))
        bundle.putString(
            CardConstants.ResultBundleConst.KEY_SERVICE_CODE_String,
            result[Constants.ResultMapConst.KEY_SERVICE_CODE]?.replace("f", "")
        )
        bundle.putString(
            CardConstants.ResultBundleConst.KEY_EXPIRED_DATE_String,
            result[Constants.ResultMapConst.KEY_EXPIRED_DATE]?.let { getFormattedExpiryDate(it) }
        )
        bundle.putString(CardConstants.ResultBundleConst.KEY_PIN_BLOCK, pinBlock)
        val cardType = result["cardType"]
        if (cardType == Constants.ResultMapConst.VAL_CARD_ENTRY_MODE_MAG) {
            bundle.putInt(
                CardConstants.ResultBundleConst.KEY_CARD_ENTRY_MODE_Int,
                CardConstants.ResultBundleConst.VAL_CARD_ENTRY_MODE_MAG
            )
            bundle.putString(CardConstants.ResultBundleConst.KEY_IC_DATA, "")
        } else {
            bundle.putInt(
                CardConstants.ResultBundleConst.KEY_CARD_ENTRY_MODE_Int,
                CardConstants.ResultBundleConst.VAL_CARD_ENTRY_MODE_IC
            )
            try {
                val iccData: String = TlvUtils.getAppTLVList(
                    result[Constants.ResultMapConst.KEY_IC_DATA]!!,
                    if(transProcessCode == 0x30.toByte()) BALANCE_CHECK_TAGS else TRANSFER_TAGS,
                    result[Constants.ResultMapConst.KEY_CARD_SEQ_NUMBER]!!
                )
                bundle.putString(CardConstants.ResultBundleConst.KEY_IC_DATA, iccData)
            } catch (e: Exception) {
                bundle.putString(
                    CardConstants.ResultBundleConst.KEY_IC_DATA,
                    result[Constants.ResultMapConst.KEY_IC_DATA]
                )
            }
        }
        try {
            if(result[Constants.ResultMapConst.KEY_ERROR_CODE]!=ResponseCode.SUCCESS.code){
                bundle.putString(
                    "error",
                    result[Constants.ResultMapConst.KEY_ERROR_CODE]
                )
            }
        }catch(e:Exception){
            e.printStackTrace()
        }

        return bundle
    }

    fun getTrack2(track2:String):String?{
        var track2Formatted = track2.replace("=","D")
        if (track2Formatted.endsWith("F")){
            return track2.trimEnd('F').uppercase()
        }
        return track2Formatted.uppercase()
    }

    fun getFormattedExpiryDate(expiryDate: String): String {
        return when (expiryDate.length) {
            4 -> {
                // Format is 'MMYY'
                expiryDate
            }
            6 -> {
                // Format is 'MMYYDD'
                expiryDate.substring(0, 4)
            }
            else -> {
                // Invalid format
                throw IllegalArgumentException("Invalid expiry date format. It should be 'MMYY' or 'MMYYDD'.")
            }
        }
    }

    fun getDecodedKey(keyValue:String):String{
        if(keyValue.length == 56) {
            val encKeyValue = keyValue.substring(0, 48); // KEY from HSM is 48 in length
            //Decrypt key with tms key encryption key
            val decKeyByte: ByteArray = DesUtils.decrypt3DES(
                GPMethods.str2bytes(encKeyValue), GPMethods.str2bytes(
                    com.bukuwarung.cardreader.morefun.contant.Constants.DefaultKeys.TMS_KEK
                )
            )
            val decKeyValue = GPMethods.bytesToHexString(decKeyByte)
            return decKeyValue;
        }
        return keyValue
    }
}
