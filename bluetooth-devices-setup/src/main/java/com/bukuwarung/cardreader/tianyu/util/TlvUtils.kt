package com.bukuwarung.cardreader.tianyu.util

object TlvUtils {

    /**
     * Extracts the value associated with a given tag in a TLV string.
     *
     * @param tlvString The TLV formatted string.
     * @param tag The tag to search for.
     * @return A Pair containing the tag and its associated value or null if the tag is not found.
     *         The second element of the Pair is the modified TLV string without the found tag and its length.
     */
    fun getTagValue(tlvString: String, tag: String, cardSeq: String): Pair<String?, String> {
        val index = tlvString.indexOf(tag)

        return if (index != -1) {
            // Calculate the start position for the length field and extract length
            val lengthHexStart = index + tag.length
            val lengthHex = tlvString.substring(lengthHexStart, lengthHexStart + 2)
            val length = lengthHex.toInt(16) * 2

            // Extract the tag value based on the length
            val valueStart = lengthHexStart + 2
            val tagValue = tlvString.substring(valueStart, valueStart + length)

            // Remove the tag and its value from the original TLV string
            val tlvWithoutTag = tlvString.removeRange(index, valueStart + length)

            Pair(tag, tag + lengthHex + tagValue)
        } else if (tag == "5F34") {
            // Special case for the "5F34" tag
            Pair("5F34", "5F3401"+cardSeq)
        } else {
            // Tag not found
            Pair(null, tlvString)
        }
    }

    /**
     * Extracts a list of TLV formatted strings for specified tags from a TLV string.
     *
     * @param tlvString The TLV formatted string.
     * @param tagList An array of tags to search for.
     * @return A concatenated string of TLV formatted substrings corresponding to the provided tags.
     */
    fun getAppTLVList(tlvString: String, tagList: Array<String>, cardSeq: String): String {
        val outputTlvString = StringBuilder()
        var tlvStringUpdated = tlvString
        tagList.forEach { tag ->
            val (foundTag, tagValue) = getTagValue(tlvStringUpdated, tag, cardSeq)

            if (foundTag != null) {
                tlvStringUpdated = tlvStringUpdated.replace(tagValue,"")
                outputTlvString.append(tagValue)
                println("Tag: $foundTag")
                println("Value: $tagValue")
            } else {
                println("Tag $tag not found in the TLV string.")
            }
        }

        return outputTlvString.toString()
    }

    /**
     * Converts a hexadecimal string to a byte array.
     *
     * @param hexString The hexadecimal string.
     * @return The corresponding byte array.
     */
    fun hexStringToByteArray(hexString: String): ByteArray {
        val len = hexString.length
        return ByteArray(len / 2) { i ->
            ((Character.digit(hexString[i * 2], 16) shl 4) +
                    Character.digit(hexString[i * 2 + 1], 16)).toByte()
        }
    }

    /**
     * Converts a byte array to a hexadecimal string.
     *
     * @param byteArray The byte array.
     * @return The corresponding hexadecimal string.
     */
    fun byteArrayToHexString(byteArray: ByteArray): String {
        return byteArray.joinToString("") { "%02X".format(it) }
    }
}

fun main() {
    // Example usage
    val tlvString = "8407A00000060210109F26087E95330657E48E6E9F2701809F101C0101A000000000AEBC231900000000000000000000000000000000009F3704EE8F7D149F3602017C950508000400009A032407069C01309F02060000000000005F2A020360820274009F1A0203605F340100"

    val tagValuePair = TlvUtils.getTagValue(tlvString, "95", "01")
    println("Extracted Tag Value: ${tagValuePair.first} -> ${tagValuePair.second}")
//    val appTlvList = TlvUtils.getAppTLVList(tlvString, BALANCE_CHECK_TAGS, "01")
//    println("App TLV List: $appTlvList")
}
