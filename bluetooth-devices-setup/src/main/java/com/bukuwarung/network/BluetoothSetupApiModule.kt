package com.bukuwarung.network

import com.bukuwarung.bluetooth_printer.activities.print.setup.GetMappedPhoneNumberUseCase
import com.bukuwarung.bluetooth_printer.activities.print.setup.GetMappedPhoneNumberUseCaseImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class BluetoothSetupApiModule {

    @Provides
    @Singleton
    fun provideGetMappedPhoneNumberUseCaseImpl(repo: TsmRepository): GetMappedPhoneNumberUseCase = GetMappedPhoneNumberUseCaseImpl(repo)
}