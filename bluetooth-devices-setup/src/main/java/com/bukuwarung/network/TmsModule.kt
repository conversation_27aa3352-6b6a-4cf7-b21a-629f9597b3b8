package com.bukuwarung.network

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object TsmModule {

    @Provides
    @Singleton
    fun provideTerminalApi(@Named("accounting-retrofit") retrofit: Retrofit): TsmRemoteDataSource {
        return retrofit.create(TsmRemoteDataSource::class.java)
    }

    @Singleton
    @Provides
    fun provideTsmRepository(dataSource: TsmRemoteDataSource): TsmRepository = TsmRepository(dataSource)
}
