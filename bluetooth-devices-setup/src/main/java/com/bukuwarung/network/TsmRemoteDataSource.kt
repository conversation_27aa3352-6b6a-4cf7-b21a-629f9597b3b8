package com.bukuwarung.network

import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.ActivateDeviceResponse
import com.bukuwarung.network.model.BaseResponse
import com.bukuwarung.network.model.MappedPhoneNumberResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query


interface TsmRemoteDataSource {
    @GET("/ac/api/v2/edc/registered-user")
    suspend fun getRegisteredUser(@Query("device-name") btDeviceName: String): Response<BaseResponse<MappedPhoneNumberResponse>>

    @POST("/ac/api/v2/edc/activate-device")
    suspend fun activateDevice(@Body request: ActivateDeviceRequest): Response<BaseResponse<ActivateDeviceResponse>>
}