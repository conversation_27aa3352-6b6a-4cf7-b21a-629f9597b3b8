package com.bukuwarung.network

import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.ActivateDeviceResponse
import com.bukuwarung.network.model.BaseResponse
import com.bukuwarung.network.model.MappedPhoneNumberResponse
import retrofit2.Response
import javax.inject.Inject

class TsmRepository @Inject constructor(private val api: TsmRemoteDataSource) {

    suspend fun getRegisteredUser(btDeviceName: String): Response<BaseResponse<MappedPhoneNumberResponse>> {
        return api.getRegisteredUser(btDeviceName)
    }

    suspend fun activateDevice(request: ActivateDeviceRequest) = api.activateDevice(request)
}