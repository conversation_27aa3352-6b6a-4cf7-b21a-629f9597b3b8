package com.bukuwarung.network.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class ActivateDeviceResponse(

	@SerializedName("payment_account_id")
	val paymentAccountId: String? = null,

	@SerializedName("beneficiary_name")
	val beneficiaryName: String? = null,

	@SerializedName("user_id")
	val userId: String? = null,

	@SerializedName("janus_account_id")
	val janusAccountId: String? = null,

	@SerializedName("bank_name")
	val bankName: String? = null,

	@SerializedName("store_address")
	val storeAddress: String? = null,

	@SerializedName("store_name")
	val storeName: String? = null,

	@SerializedName("serial_number")
	val serialNumber: String? = null,

	@SerializedName("tid")
	val tid: String? = null,

	@SerializedName("bank_account")
	val bankAccount: String? = null,

	val message: String? = null
)
