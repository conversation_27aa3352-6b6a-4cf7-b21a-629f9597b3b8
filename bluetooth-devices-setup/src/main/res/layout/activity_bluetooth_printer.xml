<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/tb_bluetooth"
        layout="@layout/toolbar_edc"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_bluetooth_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/black_34"
        android:textSize="14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb_bluetooth" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_printer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_bluetooth_title"
        tools:listitem="@layout/printer_item" />

    <include
        android:id="@+id/no_printer_view"
        layout="@layout/layout_no_printer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tb_bluetooth" />

    <include
        android:id="@+id/scanning_printer_view"
        layout="@layout/layout_scanning_printer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tb_bluetooth" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/printer_snackbar_guideline"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="16dp" />

</androidx.constraintlayout.widget.ConstraintLayout>