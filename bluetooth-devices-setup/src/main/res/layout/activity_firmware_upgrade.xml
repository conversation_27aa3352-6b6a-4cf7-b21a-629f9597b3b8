<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:gravity="center"
    android:background="@color/white"
    android:padding="16dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/firmware_upgrade_instruction"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:paddingHorizontal="8dp"
        android:visibility="visible"
        android:layout_marginBottom="@dimen/_10dp"
        >

    <ImageView
        android:id="@+id/firmware_upgrade_img"
        android:layout_width="192dp"
        android:layout_height="144dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:src="@drawable/ic_firmware_upgrade"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/upgrade_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:textColor="@color/black_80"
        android:text="Update Aplikasi"
        android:textAppearance="?android:textAppearanceMedium"
        app:layout_constraintTop_toBottomOf="@id/firmware_upgrade_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textStyle="bold"
        android:textSize="18dp" />

    <TextView
        android:id="@+id/upgrade_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        android:gravity="center"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/upgrade_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:text="Silakan update aplikasi agar tetap dapat menggunakan perangkat EDC Saku"
        android:layout_centerHorizontal="true"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_start_upgrade"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Update Aplikasi"
            android:textAllCaps="false"
            style="@style/ButtonOutline1"
            android:padding="8dp"
            android:textColor="@color/black_80"
            android:backgroundTint="@color/buku_CTA"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/firmware_upgrade_inprogress"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="48dp"
        android:paddingHorizontal="8dp"
        android:visibility="gone"
        tools:visibility="gone"
        >

        <ProgressBar
            android:id="@+id/progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="16dp"
            />

        <TextView
            android:id="@+id/progressPercentage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            android:textAppearance="?android:textAppearanceLarge"
            app:layout_constraintTop_toBottomOf="@+id/progress"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/statusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/black_80"
            android:text="@string/firmware_update_title"
            android:textAppearance="?android:textAppearanceMedium"
            android:textStyle="bold"
            android:textSize="18dp"
            app:layout_constraintTop_toBottomOf="@id/progressPercentage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/instructionText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/firmware_update_desc"
            android:textAlignment="center"
            android:textAppearance="?android:textAppearanceSmall"
            android:textSize="14dp"
            android:textColor="@color/black_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/statusText" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/firmware_upgrade_fail_no_internet"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:paddingHorizontal="8dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginBottom="@dimen/_10dp"
        >

        <ImageView
            android:id="@+id/firmware_upgrade_fail_img_no_internet"
            android:layout_width="192dp"
            android:layout_height="144dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:src="@drawable/ic_firmware_fail"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/upgrade_fail_title_no_internet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:textColor="@color/black_80"
            android:text="Koneksi Terputus"
            android:textAppearance="?android:textAppearanceMedium"
            app:layout_constraintTop_toBottomOf="@id/firmware_upgrade_fail_img_no_internet"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textStyle="bold"
            android:textSize="18dp" />

        <TextView
            android:id="@+id/upgrade_fail_subtitle_no_internet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_60"
            android:gravity="center"
            android:textSize="14dp"
            app:layout_constraintTop_toBottomOf="@id/upgrade_fail_title_no_internet"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_16dp"
            android:text="Update aplikasi gagal karena masalah koneksi internet terputus"
            android:layout_centerHorizontal="true"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_retry_upgrade_no_internet"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Coba Lagi"
            android:textAllCaps="false"
            style="@style/ButtonOutline1"
            android:padding="8dp"
            android:textColor="@color/black_80"
            android:backgroundTint="@color/buku_CTA"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/firmware_upgrade_fail"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:paddingHorizontal="8dp"
        android:visibility="gone"
        tools:visibility="gone"
        android:layout_marginBottom="@dimen/_10dp"
        >

        <ImageView
            android:id="@+id/firmware_upgrade_fail_img"
            android:layout_width="192dp"
            android:layout_height="144dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:src="@drawable/ic_firmware_update_failed"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/upgrade_fail_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:textColor="@color/black_80"
            android:text="Update Aplikasi Gagal"
            android:textAppearance="?android:textAppearanceMedium"
            app:layout_constraintTop_toBottomOf="@id/firmware_upgrade_fail_img"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textStyle="bold"
            android:textSize="18dp" />

        <TextView
            android:id="@+id/upgrade_fail_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_60"
            android:gravity="center"
            android:textSize="14dp"
            app:layout_constraintTop_toBottomOf="@id/upgrade_fail_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_16dp"
            android:text="Terjadi kesalahan pada perangkat EDC Saku. Silakan hubungi CS."
            android:layout_centerHorizontal="true"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_customer_support"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Hubungi CS"
            android:textAllCaps="false"
            style="@style/ButtonOutline1"
            android:padding="8dp"
            android:textColor="@color/black_80"
            android:backgroundTint="@color/buku_CTA"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
