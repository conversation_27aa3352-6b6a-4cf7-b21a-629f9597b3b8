<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_toast"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp"
    android:layout_margin="10dp"
    android:background="@drawable/bg_corner_green"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_toast_image"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:srcCompat="@drawable/ic_checklist_rounded"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_toast_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:textColor="@color/green_dark"
        app:layout_constraintStart_toEndOf="@id/iv_toast_image"
        app:layout_constraintTop_toTopOf="@id/iv_toast_image"
        app:layout_constraintBottom_toBottomOf="@id/iv_toast_image"
        tools:text="Tes cetak struk pembayaran berhasil!"/>

</androidx.constraintlayout.widget.ConstraintLayout>