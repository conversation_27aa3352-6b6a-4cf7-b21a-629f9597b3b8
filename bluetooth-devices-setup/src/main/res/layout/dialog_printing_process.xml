<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottie_progress"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/print_loading" />

    <TextView
        android:id="@+id/tvMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/printing"
        android:textAlignment="center"
        android:textColor="@color/black_80"
        android:textSize="14sp" />

</LinearLayout>