<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:padding="@dimen/_16dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_image"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@drawable/ic_system_error" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_image"
            tools:text="Cetak struk bank" />

        <TextView
            android:id="@+id/tvDeviceId"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/bg_yellow_10_round_rectangle"
            android:padding="8dp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="suppay_012345"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_subtitle"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvDeviceId"
            tools:text="Mohon tunggu hingga proses selesai." />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_left"
            style="@style/ButtonOutline.Black"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:maxLines="1"
            android:text="@string/batal"
            android:textAllCaps="false"
            app:layout_constraintEnd_toStartOf="@+id/btn_right"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_subtitle" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_right"
            style="@style/ButtonFill.Yellow"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="@dimen/_16dp"
            android:maxLines="1"
            android:text="@string/print"
            android:textAllCaps="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btn_left"
            app:layout_constraintTop_toBottomOf="@id/tv_subtitle" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/pb_loading_dialog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>