<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_blue15_corner_8dp_stroke_blue40">

    <ImageView
        android:id="@+id/iv_info"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginVertical="14dp"
        android:layout_marginStart="12dp"
        android:src="@drawable/ic_info"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_info_title"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="12dp"
        android:text="@string/info_bal_info"
        android:textColor="@color/black_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_info"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>