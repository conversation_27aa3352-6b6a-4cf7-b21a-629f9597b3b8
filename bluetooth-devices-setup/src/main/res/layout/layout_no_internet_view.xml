<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_no_internet"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:visibility="gone"
    tools:visibility="visible">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_no_internet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3"
        app:srcCompat="@drawable/ic_no_internet_connection" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_no_internet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="45dp"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/no_internet_printer"
        android:textColor="@color/heading_text"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/iv_no_internet"
        app:layout_constraintStart_toStartOf="@id/iv_no_internet"
        app:layout_constraintTop_toBottomOf="@id/iv_no_internet" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/no_internet_printer_hint"
        android:textColor="@color/black_60"
        android:textSize="14sp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="@id/iv_no_internet"
        app:layout_constraintStart_toStartOf="@id/iv_no_internet"
        app:layout_constraintTop_toBottomOf="@id/tv_no_internet" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_add_printer"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:backgroundTint="@color/bar_dashboard_ppob_3"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/tambah_printer"
        android:textAllCaps="false"
        android:textColor="@color/black_800"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
