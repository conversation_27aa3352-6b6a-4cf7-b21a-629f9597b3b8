<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_no_printer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:visibility="gone"
    tools:visibility="visible">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_no_printer"
        android:layout_width="136dp"
        android:layout_height="100dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3"
        app:srcCompat="@drawable/vector_no_printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_no_printer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="45dp"
        android:fontFamily="@font/roboto_bold"
        android:textColor="@color/heading_text"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/iv_no_printer"
        app:layout_constraintStart_toStartOf="@id/iv_no_printer"
        app:layout_constraintTop_toBottomOf="@id/iv_no_printer"
        tools:text="@string/belum_ada_printer_ditambahkan" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:paddingHorizontal="24dp"
        android:text="@string/no_printer_hint"
        android:textColor="@color/black_60"
        android:textSize="14sp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_no_printer"
        app:layout_constraintStart_toStartOf="@id/iv_no_printer"
        app:layout_constraintTop_toBottomOf="@id/tv_no_printer"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_refresh_scan_printer"
        android:layout_width="112dp"
        android:layout_height="36dp"
        android:layout_margin="16dp"
        android:background="@drawable/bg_blue_round_rectangle"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/retry"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_hint" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_add_printer"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_margin="16dp"
        android:backgroundTint="@color/bar_dashboard_ppob_3"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/tambah_printer"
        android:textAllCaps="false"
        android:textColor="@color/black_800"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
