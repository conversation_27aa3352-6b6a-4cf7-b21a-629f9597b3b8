<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_scanning_printer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:visibility="gone"
    tools:visibility="visible">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/bluetooth_scan_gif"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/bluetooth_loader" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_scanning_printer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/scanning_bluetooth_printer"
        android:textAlignment="center"
        android:textColor="@color/black_000000"
        android:textSize="20sp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_scanning_printer_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/scanning_bluetooth_printer_desc"
        android:textAlignment="center"
        android:textColor="@color/black_80"
        android:textSize="14sp" />

</LinearLayout>