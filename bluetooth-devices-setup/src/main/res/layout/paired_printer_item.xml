<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:backgroundTint="@color/blue_5"
    android:background="@drawable/bg_blue_round_rectangle">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_printer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/vector_printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:fontFamily="@font/roboto"
        android:gravity="top"
        android:textColor="@color/black_34"
        android:textSize="14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toEndOf="@id/iv_printer"
        app:layout_constraintTop_toTopOf="@id/iv_printer"
        tools:text="Eppos Printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:textColor="@color/grey_91"
        android:textSize="12sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tv_printer_name"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_name"
        tools:text="86:67:23:00:4A:C2" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/vector_setting" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="8dp"
        android:background="@color/grey_217"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_address" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_test_printer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:padding="10dp"
        android:text="TES CETAK"
        android:textColor="@color/colorPrimary"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />

</androidx.constraintlayout.widget.ConstraintLayout>