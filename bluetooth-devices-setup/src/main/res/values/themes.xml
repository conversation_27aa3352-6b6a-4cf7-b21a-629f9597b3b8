<resources xmlns:ns2="http://schemas.android.com/apk/res-auto">
    <!-- Base application theme. -->
    <style name="Theme.EDC" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/colorBackground</item>
        <item name="colorSecondaryVariant">@color/colorBackground</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="Widget.Design.TextInputEditText">
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="android:background">@null</item>
        <item name="android:paddingStart" ns2:ignore="NewApi">12dp</item>
        <item name="android:paddingEnd" ns2:ignore="NewApi">12dp</item>
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="textInputLayoutFocusedRectEnabled">true</item>
    </style>

    <style name="ToolbarTheme">
        <item name="android:background">@color/colorPrimary</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="android:titleTextAppearance">@style/Heading2</item>
        <item name="titleTextColor">@color/white</item>
    </style>

    <style name="BaseTextView" parent="Widget.AppCompat.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:lineSpacingExtra">6sp</item>
        <item name="android:textColor">@color/black_80</item>
    </style>

    <style name="BaseHeading" parent="BaseTextView">
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="Heading1" parent="BaseHeading">
        <item name="android:textSize">20sp</item>
    </style>

    <style name="Heading2" parent="BaseHeading">
        <item name="android:textSize">18sp</item>
    </style>

    <style name="Heading3" parent="BaseHeading">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="SubHeading1" parent="BaseHeading">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="Body1" parent="BaseTextView">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="Body2" parent="BaseTextView">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="Body3" parent="BaseTextView">
        <item name="android:textSize">12sp</item>
    </style>

    <style name="ButtonFill" parent="Widget.MaterialComponents.Button.TextButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/btn_text_color_state</item>
        <item name="backgroundTint">@color/btn_color_state</item>
        <item name="iconGravity">textStart</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="ButtonFill.Yellow" parent="@style/ButtonFill">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/btn_text_color_state_yellow</item>
        <item name="backgroundTint">@color/btn_color_state_yellow</item>
    </style>

    <style name="ButtonOutline" parent="Widget.MaterialComponents.Button.OutlinedButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/web_orange</item>
        <item name="strokeColor">@color/web_orange</item>
        <item name="cornerRadius">4dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="ButtonOutline.Blue" parent="ButtonOutline">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
        <item name="strokeColor">@color/colorPrimary</item>
    </style>

    <style name="ButtonOutline.White.Text" parent="ButtonOutline">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="strokeColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">13dp</item>
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
    </style>

    <style name="ButtonOutline.Black" parent="ButtonOutline">
        <item name="android:textColor">@color/black_80</item>
        <item name="strokeColor">@color/black_80</item>
    </style>

    <style name="ButtonOutline1" parent="Widget.MaterialComponents.Button.OutlinedButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/web_orange</item>
        <item name="strokeColor">@color/web_orange</item>
        <item name="cornerRadius">4dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="ButtonOutline.Blue1" parent="ButtonOutline1">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="strokeColor">@color/colorPrimary</item>
    </style>

    <style name="DefaultDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="FullScreenDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
    </style>

    <style name="PaymentFilterChoiceChipStyle">
        <item name="android:textAppearance">@style/Body2</item>
        <item name="chipBackgroundColor">@color/payment_chip_background_tint</item>
        <item name="chipStrokeColor">@color/payment_chip_stroke_tint</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="android:textColor">@color/payment_chip_text_tint</item>
        <item name="android:checkable">true</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="TextInputLayout.Grey" parent="Widget.Design.TextInputLayout">
        <item name="android:textColor">@color/primary_material_dark</item>
        <item name="colorControlNormal">@color/primary_dark_material_dark</item>
        <item name="colorControlActivated">@color/primary_dark_material_dark</item>
        <item name="colorControlHighlight">@color/primary_dark_material_dark</item>
        <item name="colorAccent">@color/primary_dark_material_dark</item>
        <item name="android:textColorHint">@color/primary_dark_material_light</item>
        <item name="android:textColorHighlight">@color/primary_dark_material_light</item>
        <item name="android:textColorLink">@color/primary_dark_material_light</item>
        <item name="passwordToggleDrawable">@null</item>
    </style>

    <style name="EditText.VerificationCode" parent="Base.Widget.AppCompat.EditText">
        <item name="android:theme">@style/TextInputLayout.Grey</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">22sp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">96dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:layout_marginRight">96dp</item>
        <item name="android:background">@drawable/bg_verification_code_edit_text</item>
        <item name="android:inputType">number</item>
        <item name="android:maxLength">4</item>
        <item name="android:hint">* * * *</item>
        <item name="android:textColorHint">@color/primary_material_dark</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/primary_material_dark</item>
    </style>

    <style name="EditTextBordered" parent="Widget.AppCompat.EditText">
        <item name="android:padding">@dimen/_16dp</item>
        <item name="android:drawablePadding">@dimen/_16dp</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="drawableTint">@color/black_40</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:background">@drawable/bg_edittext_selector</item>
        <item name="android:lineSpacingExtra">2sp</item>
        <item name="android:textColor">@color/black_80</item>
        <item name="android:textColorHint">@color/black_20</item>
    </style>

    <style name="Button" parent="Widget.MaterialComponents.Button.UnelevatedButton" />

    <style name="BukuErrorButtonOutline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textSize">16sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="android:textColor">@color/blue_40</item>
        <item name="cornerRadius">4dp</item>
        <item name="strokeColor">@color/blue_60</item>
        <item name="strokeWidth">1dp</item>
        <item name="backgroundColor">@color/black_5</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="FilterTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="TabLayout_Theme" parent="@style/Theme.EDC">
        <item name="android:singleLine">true</item>
    </style>

    <style name="SearchTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeColor">@color/search_input_box_stroke</item>
        <item name="boxBackgroundColor">@color/white</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/_10dp</item>
        <item name="boxCornerRadiusBottomStart">@dimen/_10dp</item>
        <item name="boxCornerRadiusTopEnd">@dimen/_10dp</item>
        <item name="boxCornerRadiusTopStart">@dimen/_10dp</item>
    </style>

    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="backgroundColor">@color/fui_transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
    </style>

    <style name="MaterialCalendarTheme" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <!-- just override the colors used in the default style -->
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimary">@color/colorPrimary</item>
    </style>

    <style name="CardMaterialCalendarTheme" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <!-- just override the colors used in the default style -->
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/greySecondary</item>
    </style>

    <style name="Body3.black60" parent="Body3">
        <item name="android:textColor">@color/black_60</item>
    </style>

    <style name="Body3.green80" parent="Body3">
        <item name="android:textColor">@color/green_80</item>
    </style>

    <style name="Body3.red80Bold" parent="Body3">
        <item name="android:textColor">@color/red_80</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="Body3.black40" parent="Body3">
        <item name="android:textColor">@color/black_40</item>
    </style>

    <style name="SubHeading1.red80" parent="SubHeading1">
        <item name="android:textColor">@color/red_80</item>
    </style>

    <style name="SubHeading1.green80" parent="SubHeading1">
        <item name="android:textColor">@color/green_80</item>
    </style>

    <style name="SubHeading1.black40" parent="SubHeading1">
        <item name="android:textColor">@color/black_40</item>
    </style>

    <style name="Divider">
        <item name="android:background">#EAEAEA</item>
    </style>

    <style name="FullScreenDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:padding">0dp</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>

</resources>