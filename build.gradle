// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.hilt_version = "2.48"
    ext.kotlin_version = '1.9.0'
    ext.agp_version = '8.0.0'

    dependencies {
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
        classpath "com.google.gms:google-services:4.3.15"
    }
}
plugins {
    id 'com.android.application' version '8.0.0' apply false
    id 'com.android.library' version '8.0.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.0' apply false
    id 'com.google.dagger.hilt.android' version '2.48.1' apply false
    id("com.google.firebase.crashlytics") version "2.9.9" apply false
    id("com.google.firebase.firebase-perf") version "1.4.2" apply false
}

// Repositories are now handled in settings.gradle

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}

