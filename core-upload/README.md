# Core Upload Module

A reusable, feature-agnostic upload module for the BukuWarung EDC application that provides background file upload capabilities with progress tracking, notifications, and retry logic.

## Features

- **Background Uploads**: Uses WorkManager to ensure uploads survive app kills and system reboots
- **Progress Tracking**: Real-time upload progress with percentage and status updates
- **Notification System**: Persistent notifications with click navigation back to originating forms
- **Retry Logic**: Automatic retry with exponential backoff for failed uploads
- **Network Constraints**: Handles network availability and connectivity changes
- **Configurable**: Support for different endpoints, authentication, validation rules, and file size limits
- **Generic Design**: Feature-agnostic architecture that can be used by any feature requiring file uploads

## Architecture

The module follows Clean Architecture principles with clear separation of concerns:

```
core-upload/
├── domain/
│   ├── model/          # Data models (UploadConfig, UploadRequest, UploadProgress, etc.)
│   ├── repository/     # Repository interface
│   └── usecase/        # Use cases for upload operations
├── data/
│   ├── api/           # Retrofit API interface
│   ├── repository/    # Repository implementation
│   ├── storage/       # Progress storage
│   └── notification/ # Notification management
├── worker/            # WorkManager worker for background uploads
├── util/              # Utilities and constants
└── di/                # Dependency injection module
```

## Usage

### Basic Upload

```kotlin
@Inject
lateinit var coreUploadManager: CoreUploadManager

// Create upload configuration
val config = UploadConfig(
    endpoint = "https://api.example.com/upload",
    authHeaders = mapOf("Authorization" to "Bearer $token"),
    validation = UploadValidation(
        maxFileSizeBytes = 10 * 1024 * 1024, // 10MB
        allowedMimeTypes = listOf("image/*", "video/*")
    )
)

// Start upload
val result = coreUploadManager.startUpload(
    filePath = "/path/to/file.jpg",
    category = "PHOTO",
    config = config,
    originatingActivity = MyActivity::class.java.name
)

result.onSuccess { uploadId ->
    // Monitor progress
    coreUploadManager.getUploadProgress(uploadId).collect { progress ->
        when (progress.status) {
            UploadStatus.UPLOADING -> {
                // Update UI with progress.progressPercent
            }
            UploadStatus.COMPLETED -> {
                // Handle success
            }
            UploadStatus.FAILED -> {
                // Handle error: progress.errorMessage
            }
        }
    }
}
```

### Replacement Feature Integration

For the replacement feature, use the replacement-specific configuration:

```kotlin
@Inject
lateinit var createReplacementUploadConfigUseCase: CreateReplacementUploadConfigUseCase

// Create replacement-specific configuration
val config = createReplacementUploadConfigUseCase.invoke(
    baseUrl = BuildConfig.API_BASE_URL,
    authHeaders = getAuthHeaders()
)

// Start upload with replacement config
val result = coreUploadManager.startUpload(
    filePath = selectedFilePath,
    category = "PHOTO", // or "VIDEO"
    config = config,
    metadata = mapOf("feature" to "replacement"),
    originatingActivity = ReplacementFormActivity::class.java.name,
    originatingFeature = "replacement"
)
```

## Configuration Options

### UploadConfig

- **endpoint**: Upload endpoint URL
- **authHeaders**: Authentication headers
- **fieldNames**: Multipart field names (file, category, fileName)
- **validation**: File validation rules
- **retryConfig**: Retry behavior configuration
- **notificationConfig**: Notification appearance and behavior

### UploadValidation

- **maxFileSizeBytes**: Maximum file size (default: Long.MAX_VALUE)
- **allowedMimeTypes**: Allowed MIME types
- **allowedExtensions**: Allowed file extensions

### NotificationConfig

- **channelId/channelName**: Notification channel configuration
- **notificationTitle/successTitle/errorTitle**: Notification titles
- **showProgress**: Whether to show progress bar
- **enableSound/enableVibration**: Notification behavior
- **clickAction**: Activity to open when notification is clicked

## Migration from Existing Upload

The existing `UploadReplacementEvidenceUseCase` can be replaced with `UploadReplacementEvidenceWithCoreUseCase`:

### Before (Original)
```kotlin
uploadReplacementEvidenceUseCase(filePath, category)
    .onSuccess { response ->
        // Handle success
    }
    .onFailure { error ->
        // Handle error
    }
```

### After (Core Upload)
```kotlin
// Start upload
uploadReplacementEvidenceWithCoreUseCase.startUpload(filePath, category)
    .onSuccess { uploadId ->
        // Monitor progress
        uploadReplacementEvidenceWithCoreUseCase.getUploadProgress(uploadId)
            .collect { progress ->
                // Handle progress updates
            }
    }
```

## Background Processing

The module uses WorkManager to handle uploads in the background:

- **Survives app kills**: Uploads continue even if the app is terminated
- **Network constraints**: Automatically retries when network becomes available
- **Battery optimization**: Respects system battery optimization settings
- **Foreground service**: Shows persistent notification during upload

## Error Handling

The module provides comprehensive error handling:

- **Validation errors**: File size, type, existence checks
- **Network errors**: Automatic retry with exponential backoff
- **Server errors**: HTTP error code handling
- **Cancellation**: Graceful upload cancellation

## Testing

The module is designed for testability with:

- **Repository pattern**: Easy to mock for unit tests
- **Use case separation**: Individual components can be tested in isolation
- **Dependency injection**: All dependencies are injected and can be mocked

## Dependencies

- **WorkManager**: Background task execution
- **Retrofit/OkHttp**: Network operations
- **Hilt**: Dependency injection
- **Kotlin Coroutines**: Asynchronous operations
- **Gson**: JSON serialization

## Future Enhancements

- **Room database**: Persistent storage for upload progress
- **Upload queuing**: Multiple concurrent uploads with priority
- **Compression integration**: Built-in file compression
- **Analytics**: Upload success/failure metrics
- **Chunked uploads**: Support for large file uploads
