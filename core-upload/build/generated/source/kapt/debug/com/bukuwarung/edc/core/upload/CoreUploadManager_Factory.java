package com.bukuwarung.edc.core.upload;

import android.content.Context;
import com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository;
import com.bukuwarung.edc.core.upload.domain.usecase.CancelUploadUseCase;
import com.bukuwarung.edc.core.upload.domain.usecase.GetUploadProgressUseCase;
import com.bukuwarung.edc.core.upload.domain.usecase.StartUploadUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CoreUploadManager_Factory implements Factory<CoreUploadManager> {
  private final Provider<Context> contextProvider;

  private final Provider<StartUploadUseCase> startUploadUseCaseProvider;

  private final Provider<GetUploadProgressUseCase> getUploadProgressUseCaseProvider;

  private final Provider<CancelUploadUseCase> cancelUploadUseCaseProvider;

  private final Provider<CoreUploadRepository> repositoryProvider;

  public CoreUploadManager_Factory(Provider<Context> contextProvider,
      Provider<StartUploadUseCase> startUploadUseCaseProvider,
      Provider<GetUploadProgressUseCase> getUploadProgressUseCaseProvider,
      Provider<CancelUploadUseCase> cancelUploadUseCaseProvider,
      Provider<CoreUploadRepository> repositoryProvider) {
    this.contextProvider = contextProvider;
    this.startUploadUseCaseProvider = startUploadUseCaseProvider;
    this.getUploadProgressUseCaseProvider = getUploadProgressUseCaseProvider;
    this.cancelUploadUseCaseProvider = cancelUploadUseCaseProvider;
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public CoreUploadManager get() {
    return newInstance(contextProvider.get(), startUploadUseCaseProvider.get(), getUploadProgressUseCaseProvider.get(), cancelUploadUseCaseProvider.get(), repositoryProvider.get());
  }

  public static CoreUploadManager_Factory create(Provider<Context> contextProvider,
      Provider<StartUploadUseCase> startUploadUseCaseProvider,
      Provider<GetUploadProgressUseCase> getUploadProgressUseCaseProvider,
      Provider<CancelUploadUseCase> cancelUploadUseCaseProvider,
      Provider<CoreUploadRepository> repositoryProvider) {
    return new CoreUploadManager_Factory(contextProvider, startUploadUseCaseProvider, getUploadProgressUseCaseProvider, cancelUploadUseCaseProvider, repositoryProvider);
  }

  public static CoreUploadManager newInstance(Context context,
      StartUploadUseCase startUploadUseCase, GetUploadProgressUseCase getUploadProgressUseCase,
      CancelUploadUseCase cancelUploadUseCase, CoreUploadRepository repository) {
    return new CoreUploadManager(context, startUploadUseCase, getUploadProgressUseCase, cancelUploadUseCase, repository);
  }
}
