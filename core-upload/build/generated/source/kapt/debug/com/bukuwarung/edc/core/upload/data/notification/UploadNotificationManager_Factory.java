package com.bukuwarung.edc.core.upload.data.notification;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UploadNotificationManager_Factory implements Factory<UploadNotificationManager> {
  private final Provider<Context> contextProvider;

  public UploadNotificationManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public UploadNotificationManager get() {
    return newInstance(contextProvider.get());
  }

  public static UploadNotificationManager_Factory create(Provider<Context> contextProvider) {
    return new UploadNotificationManager_Factory(contextProvider);
  }

  public static UploadNotificationManager newInstance(Context context) {
    return new UploadNotificationManager(context);
  }
}
