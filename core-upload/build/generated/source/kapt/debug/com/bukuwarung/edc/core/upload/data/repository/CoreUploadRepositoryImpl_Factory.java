package com.bukuwarung.edc.core.upload.data.repository;

import android.content.Context;
import androidx.work.WorkManager;
import com.bukuwarung.edc.core.upload.data.api.CoreUploadApi;
import com.bukuwarung.edc.core.upload.data.notification.UploadNotificationManager;
import com.bukuwarung.edc.core.upload.data.storage.UploadProgressStorage;
import com.google.gson.Gson;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CoreUploadRepositoryImpl_Factory implements Factory<CoreUploadRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<WorkManager> workManagerProvider;

  private final Provider<CoreUploadApi> uploadApiProvider;

  private final Provider<UploadProgressStorage> progressStorageProvider;

  private final Provider<UploadNotificationManager> notificationManagerProvider;

  private final Provider<Gson> gsonProvider;

  public CoreUploadRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<WorkManager> workManagerProvider, Provider<CoreUploadApi> uploadApiProvider,
      Provider<UploadProgressStorage> progressStorageProvider,
      Provider<UploadNotificationManager> notificationManagerProvider,
      Provider<Gson> gsonProvider) {
    this.contextProvider = contextProvider;
    this.workManagerProvider = workManagerProvider;
    this.uploadApiProvider = uploadApiProvider;
    this.progressStorageProvider = progressStorageProvider;
    this.notificationManagerProvider = notificationManagerProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public CoreUploadRepositoryImpl get() {
    return newInstance(contextProvider.get(), workManagerProvider.get(), uploadApiProvider.get(), progressStorageProvider.get(), notificationManagerProvider.get(), gsonProvider.get());
  }

  public static CoreUploadRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<WorkManager> workManagerProvider, Provider<CoreUploadApi> uploadApiProvider,
      Provider<UploadProgressStorage> progressStorageProvider,
      Provider<UploadNotificationManager> notificationManagerProvider,
      Provider<Gson> gsonProvider) {
    return new CoreUploadRepositoryImpl_Factory(contextProvider, workManagerProvider, uploadApiProvider, progressStorageProvider, notificationManagerProvider, gsonProvider);
  }

  public static CoreUploadRepositoryImpl newInstance(Context context, WorkManager workManager,
      CoreUploadApi uploadApi, UploadProgressStorage progressStorage,
      UploadNotificationManager notificationManager, Gson gson) {
    return new CoreUploadRepositoryImpl(context, workManager, uploadApi, progressStorage, notificationManager, gson);
  }
}
