package com.bukuwarung.edc.core.upload.data.storage;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UploadProgressStorage_Factory implements Factory<UploadProgressStorage> {
  @Override
  public UploadProgressStorage get() {
    return newInstance();
  }

  public static UploadProgressStorage_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static UploadProgressStorage newInstance() {
    return new UploadProgressStorage();
  }

  private static final class InstanceHolder {
    private static final UploadProgressStorage_Factory INSTANCE = new UploadProgressStorage_Factory();
  }
}
