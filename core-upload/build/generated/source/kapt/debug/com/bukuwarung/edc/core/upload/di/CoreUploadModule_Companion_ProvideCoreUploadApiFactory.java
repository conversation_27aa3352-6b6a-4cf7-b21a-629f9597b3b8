package com.bukuwarung.edc.core.upload.di;

import com.bukuwarung.edc.core.upload.data.api.CoreUploadApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("com.bukuwarung.edc.core.upload.di.CoreUploadRetrofit")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CoreUploadModule_Companion_ProvideCoreUploadApiFactory implements Factory<CoreUploadApi> {
  private final Provider<Retrofit> retrofitProvider;

  public CoreUploadModule_Companion_ProvideCoreUploadApiFactory(
      Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public CoreUploadApi get() {
    return provideCoreUploadApi(retrofitProvider.get());
  }

  public static CoreUploadModule_Companion_ProvideCoreUploadApiFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new CoreUploadModule_Companion_ProvideCoreUploadApiFactory(retrofitProvider);
  }

  public static CoreUploadApi provideCoreUploadApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(CoreUploadModule.Companion.provideCoreUploadApi(retrofit));
  }
}
