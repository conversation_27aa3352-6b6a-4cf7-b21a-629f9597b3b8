package com.bukuwarung.edc.core.upload.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata({
    "com.bukuwarung.edc.core.upload.di.CoreUploadOkHttpClient",
    "javax.inject.Named"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CoreUploadModule_Companion_ProvideCoreUploadOkHttpClientFactory implements Factory<OkHttpClient> {
  private final Provider<OkHttpClient> existingOkHttpClientProvider;

  public CoreUploadModule_Companion_ProvideCoreUploadOkHttpClientFactory(
      Provider<OkHttpClient> existingOkHttpClientProvider) {
    this.existingOkHttpClientProvider = existingOkHttpClientProvider;
  }

  @Override
  public OkHttpClient get() {
    return provideCoreUploadOkHttpClient(existingOkHttpClientProvider.get());
  }

  public static CoreUploadModule_Companion_ProvideCoreUploadOkHttpClientFactory create(
      Provider<OkHttpClient> existingOkHttpClientProvider) {
    return new CoreUploadModule_Companion_ProvideCoreUploadOkHttpClientFactory(existingOkHttpClientProvider);
  }

  public static OkHttpClient provideCoreUploadOkHttpClient(OkHttpClient existingOkHttpClient) {
    return Preconditions.checkNotNullFromProvides(CoreUploadModule.Companion.provideCoreUploadOkHttpClient(existingOkHttpClient));
  }
}
