package com.bukuwarung.edc.core.upload.di;

import com.google.gson.Gson;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata({
    "com.bukuwarung.edc.core.upload.di.CoreUploadRetrofit",
    "com.bukuwarung.edc.core.upload.di.CoreUploadOkHttpClient",
    "com.bukuwarung.edc.core.upload.di.CoreUploadGson"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CoreUploadModule_Companion_ProvideCoreUploadRetrofitFactory implements Factory<Retrofit> {
  private final Provider<OkHttpClient> okHttpClientProvider;

  private final Provider<Gson> gsonProvider;

  public CoreUploadModule_Companion_ProvideCoreUploadRetrofitFactory(
      Provider<OkHttpClient> okHttpClientProvider, Provider<Gson> gsonProvider) {
    this.okHttpClientProvider = okHttpClientProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public Retrofit get() {
    return provideCoreUploadRetrofit(okHttpClientProvider.get(), gsonProvider.get());
  }

  public static CoreUploadModule_Companion_ProvideCoreUploadRetrofitFactory create(
      Provider<OkHttpClient> okHttpClientProvider, Provider<Gson> gsonProvider) {
    return new CoreUploadModule_Companion_ProvideCoreUploadRetrofitFactory(okHttpClientProvider, gsonProvider);
  }

  public static Retrofit provideCoreUploadRetrofit(OkHttpClient okHttpClient, Gson gson) {
    return Preconditions.checkNotNullFromProvides(CoreUploadModule.Companion.provideCoreUploadRetrofit(okHttpClient, gson));
  }
}
