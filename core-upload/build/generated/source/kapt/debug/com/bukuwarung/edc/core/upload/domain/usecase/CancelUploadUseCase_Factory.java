package com.bukuwarung.edc.core.upload.domain.usecase;

import com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CancelUploadUseCase_Factory implements Factory<CancelUploadUseCase> {
  private final Provider<CoreUploadRepository> repositoryProvider;

  public CancelUploadUseCase_Factory(Provider<CoreUploadRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public CancelUploadUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static CancelUploadUseCase_Factory create(
      Provider<CoreUploadRepository> repositoryProvider) {
    return new CancelUploadUseCase_Factory(repositoryProvider);
  }

  public static CancelUploadUseCase newInstance(CoreUploadRepository repository) {
    return new CancelUploadUseCase(repository);
  }
}
