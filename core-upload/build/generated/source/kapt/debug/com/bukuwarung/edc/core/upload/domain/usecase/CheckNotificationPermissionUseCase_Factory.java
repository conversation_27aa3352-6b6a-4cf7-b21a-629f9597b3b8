package com.bukuwarung.edc.core.upload.domain.usecase;

import com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CheckNotificationPermissionUseCase_Factory implements Factory<CheckNotificationPermissionUseCase> {
  private final Provider<CoreUploadRepository> repositoryProvider;

  public CheckNotificationPermissionUseCase_Factory(
      Provider<CoreUploadRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public CheckNotificationPermissionUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static CheckNotificationPermissionUseCase_Factory create(
      Provider<CoreUploadRepository> repositoryProvider) {
    return new CheckNotificationPermissionUseCase_Factory(repositoryProvider);
  }

  public static CheckNotificationPermissionUseCase newInstance(CoreUploadRepository repository) {
    return new CheckNotificationPermissionUseCase(repository);
  }
}
