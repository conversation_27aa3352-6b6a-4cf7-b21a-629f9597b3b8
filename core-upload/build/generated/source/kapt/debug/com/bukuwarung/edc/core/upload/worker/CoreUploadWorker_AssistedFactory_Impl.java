package com.bukuwarung.edc.core.upload.worker;

import android.content.Context;
import androidx.work.WorkerParameters;
import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CoreUploadWorker_AssistedFactory_Impl implements CoreUploadWorker_AssistedFactory {
  private final CoreUploadWorker_Factory delegateFactory;

  CoreUploadWorker_AssistedFactory_Impl(CoreUploadWorker_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public CoreUploadWorker create(Context context, WorkerParameters parameters) {
    return delegateFactory.get(context, parameters);
  }

  public static Provider<CoreUploadWorker_AssistedFactory> create(
      CoreUploadWorker_Factory delegateFactory) {
    return InstanceFactory.create(new CoreUploadWorker_AssistedFactory_Impl(delegateFactory));
  }
}
