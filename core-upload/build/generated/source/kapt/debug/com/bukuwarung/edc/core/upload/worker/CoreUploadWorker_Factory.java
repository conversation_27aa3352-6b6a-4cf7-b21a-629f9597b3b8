package com.bukuwarung.edc.core.upload.worker;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.bukuwarung.edc.core.upload.data.notification.UploadNotificationManager;
import com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CoreUploadWorker_Factory {
  private final Provider<CoreUploadRepository> repositoryProvider;

  private final Provider<UploadNotificationManager> notificationManagerProvider;

  public CoreUploadWorker_Factory(Provider<CoreUploadRepository> repositoryProvider,
      Provider<UploadNotificationManager> notificationManagerProvider) {
    this.repositoryProvider = repositoryProvider;
    this.notificationManagerProvider = notificationManagerProvider;
  }

  public CoreUploadWorker get(Context context, WorkerParameters workerParams) {
    return newInstance(context, workerParams, repositoryProvider.get(), notificationManagerProvider.get());
  }

  public static CoreUploadWorker_Factory create(Provider<CoreUploadRepository> repositoryProvider,
      Provider<UploadNotificationManager> notificationManagerProvider) {
    return new CoreUploadWorker_Factory(repositoryProvider, notificationManagerProvider);
  }

  public static CoreUploadWorker newInstance(Context context, WorkerParameters workerParams,
      CoreUploadRepository repository, UploadNotificationManager notificationManager) {
    return new CoreUploadWorker(context, workerParams, repository, notificationManager);
  }
}
