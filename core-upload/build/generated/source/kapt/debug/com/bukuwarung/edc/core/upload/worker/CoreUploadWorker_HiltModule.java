package com.bukuwarung.edc.core.upload.worker;

import androidx.hilt.work.WorkerAssistedFactory;
import androidx.work.ListenableWorker;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;
import javax.annotation.processing.Generated;

@Generated("androidx.hilt.AndroidXHiltProcessor")
@Module
@InstallIn(SingletonComponent.class)
@OriginatingElement(
    topLevelClass = CoreUploadWorker.class
)
public interface CoreUploadWorker_HiltModule {
  @Binds
  @IntoMap
  @StringKey("com.bukuwarung.edc.core.upload.worker.CoreUploadWorker")
  WorkerAssistedFactory<? extends ListenableWorker> bind(CoreUploadWorker_AssistedFactory factory);
}
