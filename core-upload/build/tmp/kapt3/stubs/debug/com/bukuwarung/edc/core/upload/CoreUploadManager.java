package com.bukuwarung.edc.core.upload;

/**
 * Main entry point for the core upload module
 * This is the public API that other modules should use
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B1\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u0006\u0010\r\u001a\u00020\u000eJ*\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0012\u001a\u00020\u0013H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u0015J\b\u0010\u0016\u001a\u00020\u0013H\u0002J\u0012\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\u00190\u0018J\u0006\u0010\u001b\u001a\u00020\u001cJ\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00182\u0006\u0010\u0012\u001a\u00020\u0013Jh\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00130\u00102\u0006\u0010\u001f\u001a\u00020\u00132\u0006\u0010 \u001a\u00020\u00132\u0006\u0010!\u001a\u00020\"2\u0014\b\u0002\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00130$2\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u00132\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u0013H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\'\u0010(R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006)"}, d2 = {"Lcom/bukuwarung/edc/core/upload/CoreUploadManager;", "", "context", "Landroid/content/Context;", "startUploadUseCase", "Lcom/bukuwarung/edc/core/upload/domain/usecase/StartUploadUseCase;", "getUploadProgressUseCase", "Lcom/bukuwarung/edc/core/upload/domain/usecase/GetUploadProgressUseCase;", "cancelUploadUseCase", "Lcom/bukuwarung/edc/core/upload/domain/usecase/CancelUploadUseCase;", "repository", "Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "(Landroid/content/Context;Lcom/bukuwarung/edc/core/upload/domain/usecase/StartUploadUseCase;Lcom/bukuwarung/edc/core/upload/domain/usecase/GetUploadProgressUseCase;Lcom/bukuwarung/edc/core/upload/domain/usecase/CancelUploadUseCase;Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;)V", "areNotificationsEnabled", "", "cancelUpload", "Lkotlin/Result;", "", "uploadId", "", "cancelUpload-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateUploadId", "getAllActiveUploads", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;", "getNotificationPermissionStatus", "Lcom/bukuwarung/edc/core/upload/domain/model/NotificationPermissionStatus;", "getUploadProgress", "startUpload", "filePath", "category", "config", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadConfig;", "metadata", "", "originatingActivity", "originatingFeature", "startUpload-bMdYcbs", "(Ljava/lang/String;Ljava/lang/String;Lcom/bukuwarung/edc/core/upload/domain/model/UploadConfig;Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "core-upload_debug"})
public final class CoreUploadManager {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.usecase.StartUploadUseCase startUploadUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.usecase.GetUploadProgressUseCase getUploadProgressUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.usecase.CancelUploadUseCase cancelUploadUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository = null;
    
    @javax.inject.Inject
    public CoreUploadManager(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.usecase.StartUploadUseCase startUploadUseCase, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.usecase.GetUploadProgressUseCase getUploadProgressUseCase, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.usecase.CancelUploadUseCase cancelUploadUseCase, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository) {
        super();
    }
    
    /**
     * Get upload progress for a specific upload
     * @param uploadId The upload ID to track
     * @return Flow of upload progress updates
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<com.bukuwarung.edc.core.upload.domain.model.UploadProgress> getUploadProgress(@org.jetbrains.annotations.NotNull
    java.lang.String uploadId) {
        return null;
    }
    
    /**
     * Get all active uploads
     * @return Flow of all active upload progress
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.bukuwarung.edc.core.upload.domain.model.UploadProgress>> getAllActiveUploads() {
        return null;
    }
    
    /**
     * Check notification permission status
     * @return NotificationPermissionStatus with detailed permission info
     */
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.NotificationPermissionStatus getNotificationPermissionStatus() {
        return null;
    }
    
    /**
     * Check if notifications are enabled for uploads
     * @return true if notifications can be shown, false otherwise
     */
    public final boolean areNotificationsEnabled() {
        return false;
    }
    
    /**
     * Generate a unique upload ID
     */
    private final java.lang.String generateUploadId() {
        return null;
    }
}