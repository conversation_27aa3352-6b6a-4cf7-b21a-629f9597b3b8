package com.bukuwarung.edc.core.upload.data.api;

/**
 * Generic API interface for file uploads
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001Ji\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0014\b\u0003\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\b2\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\n2\b\b\u0001\u0010\f\u001a\u00020\n2\u000e\b\u0003\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJC\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u00112\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u00112\b\b\u0001\u0010\f\u001a\u00020\nH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0012\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u0013"}, d2 = {"Lcom/bukuwarung/edc/core/upload/data/api/CoreUploadApi;", "", "uploadFile", "Lretrofit2/Response;", "Lokhttp3/ResponseBody;", "url", "", "headers", "", "category", "Lokhttp3/MultipartBody$Part;", "fileName", "file", "additionalParts", "", "(Ljava/lang/String;Ljava/util/Map;Lokhttp3/MultipartBody$Part;Lokhttp3/MultipartBody$Part;Lokhttp3/MultipartBody$Part;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadFileWithFields", "Lokhttp3/RequestBody;", "(Ljava/lang/String;Lokhttp3/RequestBody;Lokhttp3/RequestBody;Lokhttp3/MultipartBody$Part;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "core-upload_debug"})
public abstract interface CoreUploadApi {
    
    /**
     * Generic multipart upload endpoint
     * @param url The upload endpoint URL
     * @param headers Authentication and other headers
     * @param category Category field (configurable name)
     * @param fileName File name field (configurable name)
     * @param file The file part (configurable name)
     * @param additionalParts Any additional form fields
     */
    @retrofit2.http.Multipart
    @retrofit2.http.POST
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object uploadFile(@retrofit2.http.Url
    @org.jetbrains.annotations.NotNull
    java.lang.String url, @retrofit2.http.HeaderMap
    @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, java.lang.String> headers, @retrofit2.http.Part
    @org.jetbrains.annotations.Nullable
    okhttp3.MultipartBody.Part category, @retrofit2.http.Part
    @org.jetbrains.annotations.Nullable
    okhttp3.MultipartBody.Part fileName, @retrofit2.http.Part
    @org.jetbrains.annotations.NotNull
    okhttp3.MultipartBody.Part file, @retrofit2.http.Part
    @org.jetbrains.annotations.NotNull
    java.util.List<okhttp3.MultipartBody.Part> additionalParts, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<okhttp3.ResponseBody>> $completion);
    
    /**
     * Alternative upload method with separate category and fileName parameters
     * This matches the existing replacement API structure
     * Note: @Multipart automatically sets Content-Type with boundary
     */
    @retrofit2.http.Multipart
    @retrofit2.http.POST
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object uploadFileWithFields(@retrofit2.http.Url
    @org.jetbrains.annotations.NotNull
    java.lang.String url, @retrofit2.http.Part(value = "category")
    @org.jetbrains.annotations.Nullable
    okhttp3.RequestBody category, @retrofit2.http.Part(value = "fileName")
    @org.jetbrains.annotations.Nullable
    okhttp3.RequestBody fileName, @retrofit2.http.Part
    @org.jetbrains.annotations.NotNull
    okhttp3.MultipartBody.Part file, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<okhttp3.ResponseBody>> $completion);
    
    /**
     * Generic API interface for file uploads
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}