package com.bukuwarung.edc.core.upload.data.interceptor;

/**
 * Interceptor that ensures multipart requests don't have their Content-Type overridden
 * This prevents conflicts with HeadersInterceptor that sets Content-Type to application/json
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a8\u0006\u0007"}, d2 = {"Lcom/bukuwarung/edc/core/upload/data/interceptor/MultipartContentTypeInterceptor;", "Lokhttp3/Interceptor;", "()V", "intercept", "Lokhttp3/Response;", "chain", "Lokhttp3/Interceptor$Chain;", "core-upload_debug"})
public final class MultipartContentTypeInterceptor implements okhttp3.Interceptor {
    
    public MultipartContentTypeInterceptor() {
        super();
    }
    
    @java.lang.Override
    @kotlin.jvm.Throws(exceptionClasses = {java.io.IOException.class})
    @org.jetbrains.annotations.NotNull
    public okhttp3.Response intercept(@org.jetbrains.annotations.NotNull
    okhttp3.Interceptor.Chain chain) throws java.io.IOException {
        return null;
    }
}