package com.bukuwarung.edc.core.upload.data.repository;

/**
 * Implementation of CoreUploadRepository
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B9\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ*\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0012\u001a\u00020\u0013H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u0015J\u0011\u0010\u0016\u001a\u00020\u0011H\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0017J\u0018\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0019\u001a\u00020\u00132\u0006\u0010\u001a\u001a\u00020\u0013H\u0002J\u0014\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e0\u001d0\u001cH\u0016J\b\u0010\u001f\u001a\u00020 H\u0016J\u0016\u0010!\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001c2\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J\u0018\u0010\"\u001a\u00020#2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010$\u001a\u00020\u0013H\u0002J*\u0010%\u001a\b\u0012\u0004\u0012\u00020#0\u00102\u0006\u0010&\u001a\u00020\'H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b(\u0010)J*\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00130\u00102\u0006\u0010&\u001a\u00020\'H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b+\u0010)J\u0019\u0010,\u001a\u00020\u00112\u0006\u0010-\u001a\u00020\u001eH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010.J*\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010&\u001a\u00020\'H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b0\u0010)R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00061"}, d2 = {"Lcom/bukuwarung/edc/core/upload/data/repository/CoreUploadRepositoryImpl;", "Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "context", "Landroid/content/Context;", "workManager", "Landroidx/work/WorkManager;", "uploadApi", "Lcom/bukuwarung/edc/core/upload/data/api/CoreUploadApi;", "progressStorage", "Lcom/bukuwarung/edc/core/upload/data/storage/UploadProgressStorage;", "notificationManager", "Lcom/bukuwarung/edc/core/upload/data/notification/UploadNotificationManager;", "gson", "Lcom/google/gson/Gson;", "(Landroid/content/Context;Landroidx/work/WorkManager;Lcom/bukuwarung/edc/core/upload/data/api/CoreUploadApi;Lcom/bukuwarung/edc/core/upload/data/storage/UploadProgressStorage;Lcom/bukuwarung/edc/core/upload/data/notification/UploadNotificationManager;Lcom/google/gson/Gson;)V", "cancelUpload", "Lkotlin/Result;", "", "uploadId", "", "cancelUpload-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearCompletedUploads", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "determineMimeType", "category", "fileName", "getActiveUploads", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;", "getNotificationPermissionStatus", "Lcom/bukuwarung/edc/core/upload/domain/model/NotificationPermissionStatus;", "getUploadProgress", "parseUploadResponse", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadResult;", "responseBody", "performUpload", "request", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadRequest;", "performUpload-gIAlu-s", "(Lcom/bukuwarung/edc/core/upload/domain/model/UploadRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startUpload", "startUpload-gIAlu-s", "updateProgress", "progress", "(Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateFile", "validateFile-gIAlu-s", "core-upload_debug"})
public final class CoreUploadRepositoryImpl implements com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.work.WorkManager workManager = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.data.api.CoreUploadApi uploadApi = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.data.storage.UploadProgressStorage progressStorage = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.data.notification.UploadNotificationManager notificationManager = null;
    @org.jetbrains.annotations.NotNull
    private final com.google.gson.Gson gson = null;
    
    @javax.inject.Inject
    public CoreUploadRepositoryImpl(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    androidx.work.WorkManager workManager, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.data.api.CoreUploadApi uploadApi, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.data.storage.UploadProgressStorage progressStorage, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.data.notification.UploadNotificationManager notificationManager, @org.jetbrains.annotations.NotNull
    com.google.gson.Gson gson) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<com.bukuwarung.edc.core.upload.domain.model.UploadProgress> getUploadProgress(@org.jetbrains.annotations.NotNull
    java.lang.String uploadId) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.bukuwarung.edc.core.upload.domain.model.UploadProgress>> getActiveUploads() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object updateProgress(@org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.UploadProgress progress, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object clearCompletedUploads(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.String determineMimeType(java.lang.String category, java.lang.String fileName) {
        return null;
    }
    
    private final com.bukuwarung.edc.core.upload.domain.model.UploadResult parseUploadResponse(java.lang.String uploadId, java.lang.String responseBody) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.bukuwarung.edc.core.upload.domain.model.NotificationPermissionStatus getNotificationPermissionStatus() {
        return null;
    }
}