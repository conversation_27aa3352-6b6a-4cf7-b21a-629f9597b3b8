package com.bukuwarung.edc.core.upload.data.storage;

/**
 * In-memory storage for upload progress
 * In a production app, this could be backed by Room database for persistence
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\n\b\u0007\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u0011\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u0011\u0010\u000b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u0012\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\rJ\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\rJ\u0016\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\r2\u0006\u0010\u0011\u001a\u00020\u0006J\u0019\u0010\u0012\u001a\u00020\t2\u0006\u0010\u0011\u001a\u00020\u0006H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\u0019\u0010\u0014\u001a\u00020\t2\u0006\u0010\u0015\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0016R \u0010\u0003\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u0018"}, d2 = {"Lcom/bukuwarung/edc/core/upload/data/storage/UploadProgressStorage;", "", "()V", "_uploads", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;", "clearAllUploads", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearCompletedUploads", "getActiveUploads", "Lkotlinx/coroutines/flow/Flow;", "", "getAllUploads", "getUploadProgress", "uploadId", "removeUpload", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProgress", "progress", "(Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "core-upload_debug"})
public final class UploadProgressStorage {
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, com.bukuwarung.edc.core.upload.domain.model.UploadProgress>> _uploads = null;
    @org.jetbrains.annotations.NotNull
    private static final java.util.Set<com.bukuwarung.edc.core.upload.domain.model.UploadStatus> ACTIVE_UPLOAD_STATUSES = null;
    @org.jetbrains.annotations.NotNull
    private static final java.util.Set<com.bukuwarung.edc.core.upload.domain.model.UploadStatus> COMPLETED_UPLOAD_STATUSES = null;
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.edc.core.upload.data.storage.UploadProgressStorage.Companion Companion = null;
    
    @javax.inject.Inject
    public UploadProgressStorage() {
        super();
    }
    
    /**
     * Get upload progress for a specific upload ID
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<com.bukuwarung.edc.core.upload.domain.model.UploadProgress> getUploadProgress(@org.jetbrains.annotations.NotNull
    java.lang.String uploadId) {
        return null;
    }
    
    /**
     * Get all active uploads (not completed, failed, or cancelled)
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.bukuwarung.edc.core.upload.domain.model.UploadProgress>> getActiveUploads() {
        return null;
    }
    
    /**
     * Get all uploads
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.bukuwarung.edc.core.upload.domain.model.UploadProgress>> getAllUploads() {
        return null;
    }
    
    /**
     * Update upload progress
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateProgress(@org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.UploadProgress progress, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Remove upload progress
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object removeUpload(@org.jetbrains.annotations.NotNull
    java.lang.String uploadId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Clear completed uploads
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object clearCompletedUploads(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Clear all uploads
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object clearAllUploads(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/bukuwarung/edc/core/upload/data/storage/UploadProgressStorage$Companion;", "", "()V", "ACTIVE_UPLOAD_STATUSES", "", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadStatus;", "COMPLETED_UPLOAD_STATUSES", "core-upload_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}