package com.bukuwarung.edc.core.upload.di;

/**
 * Hilt module for core upload dependencies
 */
@dagger.Module
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00072\u00020\u0001:\u0001\u0007B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\'\u00a8\u0006\b"}, d2 = {"Lcom/bukuwarung/edc/core/upload/di/CoreUploadModule;", "", "()V", "bindCoreUploadRepository", "Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "coreUploadRepositoryImpl", "Lcom/bukuwarung/edc/core/upload/data/repository/CoreUploadRepositoryImpl;", "Companion", "core-upload_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract class CoreUploadModule {
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.edc.core.upload.di.CoreUploadModule.Companion Companion = null;
    
    public CoreUploadModule() {
        super();
    }
    
    @dagger.Binds
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public abstract com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository bindCoreUploadRepository(@org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.data.repository.CoreUploadRepositoryImpl coreUploadRepositoryImpl);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u0007\u001a\u00020\bH\u0007J\u0012\u0010\t\u001a\u00020\n2\b\b\u0001\u0010\u000b\u001a\u00020\nH\u0007J\u001c\u0010\f\u001a\u00020\u00062\b\b\u0001\u0010\r\u001a\u00020\n2\b\b\u0001\u0010\u000e\u001a\u00020\bH\u0007J\b\u0010\u000f\u001a\u00020\bH\u0007J\u0012\u0010\u0010\u001a\u00020\u00112\b\b\u0001\u0010\u0012\u001a\u00020\u0013H\u0007\u00a8\u0006\u0014"}, d2 = {"Lcom/bukuwarung/edc/core/upload/di/CoreUploadModule$Companion;", "", "()V", "provideCoreUploadApi", "Lcom/bukuwarung/edc/core/upload/data/api/CoreUploadApi;", "retrofit", "Lretrofit2/Retrofit;", "provideCoreUploadGson", "Lcom/google/gson/Gson;", "provideCoreUploadOkHttpClient", "Lokhttp3/OkHttpClient;", "existingOkHttpClient", "provideCoreUploadRetrofit", "okHttpClient", "gson", "provideGson", "provideWorkManager", "Landroidx/work/WorkManager;", "context", "Landroid/content/Context;", "core-upload_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @dagger.Provides
        @javax.inject.Singleton
        @org.jetbrains.annotations.NotNull
        public final androidx.work.WorkManager provideWorkManager(@dagger.hilt.android.qualifiers.ApplicationContext
        @org.jetbrains.annotations.NotNull
        android.content.Context context) {
            return null;
        }
        
        @dagger.Provides
        @javax.inject.Singleton
        @CoreUploadGson
        @org.jetbrains.annotations.NotNull
        public final com.google.gson.Gson provideCoreUploadGson() {
            return null;
        }
        
        @dagger.Provides
        @javax.inject.Singleton
        @CoreUploadOkHttpClient
        @org.jetbrains.annotations.NotNull
        public final okhttp3.OkHttpClient provideCoreUploadOkHttpClient(@javax.inject.Named(value = "normal")
        @org.jetbrains.annotations.NotNull
        okhttp3.OkHttpClient existingOkHttpClient) {
            return null;
        }
        
        @dagger.Provides
        @javax.inject.Singleton
        @CoreUploadRetrofit
        @org.jetbrains.annotations.NotNull
        public final retrofit2.Retrofit provideCoreUploadRetrofit(@CoreUploadOkHttpClient
        @org.jetbrains.annotations.NotNull
        okhttp3.OkHttpClient okHttpClient, @CoreUploadGson
        @org.jetbrains.annotations.NotNull
        com.google.gson.Gson gson) {
            return null;
        }
        
        @dagger.Provides
        @javax.inject.Singleton
        @org.jetbrains.annotations.NotNull
        public final com.bukuwarung.edc.core.upload.data.api.CoreUploadApi provideCoreUploadApi(@CoreUploadRetrofit
        @org.jetbrains.annotations.NotNull
        retrofit2.Retrofit retrofit) {
            return null;
        }
        
        @dagger.Provides
        @javax.inject.Singleton
        @org.jetbrains.annotations.NotNull
        public final com.google.gson.Gson provideGson() {
            return null;
        }
    }
}