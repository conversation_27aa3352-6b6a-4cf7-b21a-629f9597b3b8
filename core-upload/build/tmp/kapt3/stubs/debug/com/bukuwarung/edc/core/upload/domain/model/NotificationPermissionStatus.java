package com.bukuwarung.edc.core.upload.domain.model;

/**
 * Data class representing the current notification permission status
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00032\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017J\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0016\u001a\u00020\u0017J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0015H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\tR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\t\u00a8\u0006\u001c"}, d2 = {"Lcom/bukuwarung/edc/core/upload/domain/model/NotificationPermissionStatus;", "", "notificationsEnabled", "", "channelEnabled", "canShowNotifications", "requiresPermissionRequest", "(ZZZZ)V", "getCanShowNotifications", "()Z", "getChannelEnabled", "getNotificationsEnabled", "getRequiresPermissionRequest", "component1", "component2", "component3", "component4", "copy", "equals", "other", "getStatusMessage", "", "context", "Landroid/content/Context;", "getSuggestedAction", "hashCode", "", "toString", "core-upload_debug"})
public final class NotificationPermissionStatus {
    
    /**
     * Whether notifications are enabled at the app level
     */
    private final boolean notificationsEnabled = false;
    
    /**
     * Whether the specific notification channel is enabled
     */
    private final boolean channelEnabled = false;
    
    /**
     * Whether notifications can be shown (both app and channel enabled)
     */
    private final boolean canShowNotifications = false;
    
    /**
     * Whether the app needs to request notification permission (Android 13+)
     */
    private final boolean requiresPermissionRequest = false;
    
    public NotificationPermissionStatus(boolean notificationsEnabled, boolean channelEnabled, boolean canShowNotifications, boolean requiresPermissionRequest) {
        super();
    }
    
    /**
     * Whether notifications are enabled at the app level
     */
    public final boolean getNotificationsEnabled() {
        return false;
    }
    
    /**
     * Whether the specific notification channel is enabled
     */
    public final boolean getChannelEnabled() {
        return false;
    }
    
    /**
     * Whether notifications can be shown (both app and channel enabled)
     */
    public final boolean getCanShowNotifications() {
        return false;
    }
    
    /**
     * Whether the app needs to request notification permission (Android 13+)
     */
    public final boolean getRequiresPermissionRequest() {
        return false;
    }
    
    /**
     * Get a user-friendly message describing the notification status
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getStatusMessage(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    /**
     * Get suggested action for the user
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSuggestedAction(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.NotificationPermissionStatus copy(boolean notificationsEnabled, boolean channelEnabled, boolean canShowNotifications, boolean requiresPermissionRequest) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}