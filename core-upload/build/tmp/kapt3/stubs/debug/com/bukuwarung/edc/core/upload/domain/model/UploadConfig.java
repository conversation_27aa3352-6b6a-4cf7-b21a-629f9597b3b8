package com.bukuwarung.edc.core.upload.domain.model;

/**
 * Configuration for upload operations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BK\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0014\b\u0002\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u000bH\u00c6\u0003J\t\u0010 \u001a\u00020\rH\u00c6\u0003JQ\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0014\b\u0002\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\t\u0010\"\u001a\u00020#H\u00d6\u0001J\u0013\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\'H\u00d6\u0003J\t\u0010(\u001a\u00020#H\u00d6\u0001J\t\u0010)\u001a\u00020\u0003H\u00d6\u0001J\u0019\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020#H\u00d6\u0001R\u001d\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006/"}, d2 = {"Lcom/bukuwarung/edc/core/upload/domain/model/UploadConfig;", "Landroid/os/Parcelable;", "endpoint", "", "authHeaders", "", "fieldNames", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadFieldNames;", "validation", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadValidation;", "retryConfig", "Lcom/bukuwarung/edc/core/upload/domain/model/RetryConfig;", "notificationConfig", "Lcom/bukuwarung/edc/core/upload/domain/model/NotificationConfig;", "(Ljava/lang/String;Ljava/util/Map;Lcom/bukuwarung/edc/core/upload/domain/model/UploadFieldNames;Lcom/bukuwarung/edc/core/upload/domain/model/UploadValidation;Lcom/bukuwarung/edc/core/upload/domain/model/RetryConfig;Lcom/bukuwarung/edc/core/upload/domain/model/NotificationConfig;)V", "getAuthHeaders", "()Ljava/util/Map;", "getEndpoint", "()Ljava/lang/String;", "getFieldNames", "()Lcom/bukuwarung/edc/core/upload/domain/model/UploadFieldNames;", "getNotificationConfig", "()Lcom/bukuwarung/edc/core/upload/domain/model/NotificationConfig;", "getRetryConfig", "()Lcom/bukuwarung/edc/core/upload/domain/model/RetryConfig;", "getValidation", "()Lcom/bukuwarung/edc/core/upload/domain/model/UploadValidation;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "describeContents", "", "equals", "", "other", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "core-upload_debug"})
@kotlinx.parcelize.Parcelize
public final class UploadConfig implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String endpoint = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Map<java.lang.String, java.lang.String> authHeaders = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.model.UploadFieldNames fieldNames = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.model.UploadValidation validation = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.model.RetryConfig retryConfig = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.model.NotificationConfig notificationConfig = null;
    
    public UploadConfig(@org.jetbrains.annotations.NotNull
    java.lang.String endpoint, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, java.lang.String> authHeaders, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.UploadFieldNames fieldNames, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.UploadValidation validation, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.RetryConfig retryConfig, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.NotificationConfig notificationConfig) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getEndpoint() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.String> getAuthHeaders() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.UploadFieldNames getFieldNames() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.UploadValidation getValidation() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.RetryConfig getRetryConfig() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.NotificationConfig getNotificationConfig() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.String> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.UploadFieldNames component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.UploadValidation component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.RetryConfig component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.NotificationConfig component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.UploadConfig copy(@org.jetbrains.annotations.NotNull
    java.lang.String endpoint, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, java.lang.String> authHeaders, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.UploadFieldNames fieldNames, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.UploadValidation validation, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.RetryConfig retryConfig, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.NotificationConfig notificationConfig) {
        return null;
    }
    
    @java.lang.Override
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override
    public void writeToParcel(@org.jetbrains.annotations.NotNull
    android.os.Parcel parcel, int flags) {
    }
}