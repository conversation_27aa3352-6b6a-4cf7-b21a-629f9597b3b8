package com.bukuwarung.edc.core.upload.domain.repository;

/**
 * Repository interface for core upload functionality
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0011\u0010\t\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\r0\fH&J\b\u0010\u000f\u001a\u00020\u0010H&J\u0016\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000e0\f2\u0006\u0010\u0005\u001a\u00020\u0006H&J*\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u00032\u0006\u0010\u0014\u001a\u00020\u0015H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0016\u0010\u0017J*\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0014\u001a\u00020\u0015H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0019\u0010\u0017J\u0019\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u000eH\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001cJ*\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0014\u001a\u00020\u0015H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001e\u0010\u0017\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u001f"}, d2 = {"Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "", "cancelUpload", "Lkotlin/Result;", "", "uploadId", "", "cancelUpload-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearCompletedUploads", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveUploads", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;", "getNotificationPermissionStatus", "Lcom/bukuwarung/edc/core/upload/domain/model/NotificationPermissionStatus;", "getUploadProgress", "performUpload", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadResult;", "request", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadRequest;", "performUpload-gIAlu-s", "(Lcom/bukuwarung/edc/core/upload/domain/model/UploadRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startUpload", "startUpload-gIAlu-s", "updateProgress", "progress", "(Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateFile", "validateFile-gIAlu-s", "core-upload_debug"})
public abstract interface CoreUploadRepository {
    
    /**
     * Get upload progress as a flow
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.bukuwarung.edc.core.upload.domain.model.UploadProgress> getUploadProgress(@org.jetbrains.annotations.NotNull
    java.lang.String uploadId);
    
    /**
     * Get all active uploads
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.bukuwarung.edc.core.upload.domain.model.UploadProgress>> getActiveUploads();
    
    /**
     * Update upload progress
     */
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateProgress(@org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.model.UploadProgress progress, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Clear completed uploads
     */
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearCompletedUploads(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Check notification permission status
     * @return NotificationPermissionStatus with detailed permission info
     */
    @org.jetbrains.annotations.NotNull
    public abstract com.bukuwarung.edc.core.upload.domain.model.NotificationPermissionStatus getNotificationPermissionStatus();
}