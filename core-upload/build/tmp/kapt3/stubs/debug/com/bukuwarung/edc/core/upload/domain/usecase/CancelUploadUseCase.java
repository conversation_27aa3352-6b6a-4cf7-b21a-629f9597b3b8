package com.bukuwarung.edc.core.upload.domain.usecase;

/**
 * Use case for cancelling upload operations
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\f"}, d2 = {"Lcom/bukuwarung/edc/core/upload/domain/usecase/CancelUploadUseCase;", "", "repository", "Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "(Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;)V", "invoke", "Lkotlin/Result;", "", "uploadId", "", "invoke-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "core-upload_debug"})
public final class CancelUploadUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository = null;
    
    @javax.inject.Inject
    public CancelUploadUseCase(@org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository) {
        super();
    }
}