package com.bukuwarung.edc.core.upload.domain.usecase;

/**
 * Use case for checking notification permission status
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0005\u001a\u00020\u0006J\t\u0010\u0007\u001a\u00020\bH\u0086\u0002J\u0006\u0010\t\u001a\u00020\u0006R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/bukuwarung/edc/core/upload/domain/usecase/CheckNotificationPermissionUseCase;", "", "repository", "Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "(Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;)V", "areNotificationsEnabled", "", "invoke", "Lcom/bukuwarung/edc/core/upload/domain/model/NotificationPermissionStatus;", "shouldRequestPermission", "core-upload_debug"})
public final class CheckNotificationPermissionUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository = null;
    
    @javax.inject.Inject
    public CheckNotificationPermissionUseCase(@org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository) {
        super();
    }
    
    /**
     * Check current notification permission status
     * @return NotificationPermissionStatus with detailed permission info
     */
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.edc.core.upload.domain.model.NotificationPermissionStatus invoke() {
        return null;
    }
    
    /**
     * Check if notifications are enabled and can be shown
     * @return true if notifications can be displayed, false otherwise
     */
    public final boolean areNotificationsEnabled() {
        return false;
    }
    
    /**
     * Check if permission request is needed (Android 13+)
     * @return true if app should request notification permission, false otherwise
     */
    public final boolean shouldRequestPermission() {
        return false;
    }
}