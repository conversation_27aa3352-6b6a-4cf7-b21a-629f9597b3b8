package com.bukuwarung.edc.core.upload.domain.usecase;

/**
 * Use case for getting upload progress
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0012\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006J\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0086\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/bukuwarung/edc/core/upload/domain/usecase/GetUploadProgressUseCase;", "", "repository", "Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "(Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;)V", "getAllActiveUploads", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadProgress;", "invoke", "uploadId", "", "core-upload_debug"})
public final class GetUploadProgressUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository = null;
    
    @javax.inject.Inject
    public GetUploadProgressUseCase(@org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository) {
        super();
    }
    
    /**
     * Get upload progress for a specific upload
     * @param uploadId The upload ID to track
     * @return Flow of upload progress updates
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<com.bukuwarung.edc.core.upload.domain.model.UploadProgress> invoke(@org.jetbrains.annotations.NotNull
    java.lang.String uploadId) {
        return null;
    }
    
    /**
     * Get all active uploads
     * @return Flow of all active upload progress
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.bukuwarung.edc.core.upload.domain.model.UploadProgress>> getAllActiveUploads() {
        return null;
    }
}