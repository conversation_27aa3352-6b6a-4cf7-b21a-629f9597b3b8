package com.bukuwarung.edc.core.upload.util;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0000\u001a\u001c\u0010\u0004\u001a\u0004\u0018\u0001H\u0005\"\u0006\b\u0000\u0010\u0005\u0018\u0001*\u00020\u0006H\u0086\b\u00a2\u0006\u0002\u0010\u0007\u001a%\u0010\u0004\u001a\u0004\u0018\u0001H\u0005\"\u0004\b\u0000\u0010\u0005*\u00020\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u0002H\u00050\t\u00a2\u0006\u0002\u0010\n\u001a\n\u0010\u000b\u001a\u00020\u0006*\u00020\f\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\u00a8\u0006\r"}, d2 = {"gson", "Lcom/google/gson/Gson;", "getGson", "()Lcom/google/gson/Gson;", "fromJson", "T", "", "(Ljava/lang/String;)Ljava/lang/Object;", "clazz", "Ljava/lang/Class;", "(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;", "toJson", "", "core-upload_debug"})
public final class JsonExtensionsKt {
    
    /**
     * Extension functions for JSON serialization/deserialization
     */
    @org.jetbrains.annotations.NotNull
    private static final com.google.gson.Gson gson = null;
    
    /**
     * Extension functions for JSON serialization/deserialization
     */
    @org.jetbrains.annotations.NotNull
    public static final com.google.gson.Gson getGson() {
        return null;
    }
    
    /**
     * Convert object to JSON string
     */
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toJson(@org.jetbrains.annotations.NotNull
    java.lang.Object $this$toJson) {
        return null;
    }
    
    /**
     * Convert JSON string to object with explicit type
     */
    @org.jetbrains.annotations.Nullable
    public static final <T extends java.lang.Object>T fromJson(@org.jetbrains.annotations.NotNull
    java.lang.String $this$fromJson, @org.jetbrains.annotations.NotNull
    java.lang.Class<T> clazz) {
        return null;
    }
}