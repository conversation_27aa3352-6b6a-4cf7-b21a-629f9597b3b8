package com.bukuwarung.edc.core.upload.util;

/**
 * Helper class for handling notification permissions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007J\u000e\u0010\b\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u0006\u001a\u00020\u0007J\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0006\u001a\u00020\u0007J\u0016\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u0014\u0010\r\u001a\u00020\f2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013J\u0016\u0010\r\u001a\u00020\f2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0016\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/bukuwarung/edc/core/upload/util/NotificationPermissionHelper;", "", "()V", "NOTIFICATION_PERMISSION", "", "getPermissionRationaleMessage", "context", "Landroid/content/Context;", "getSettingsMessage", "isNotificationPermissionGranted", "", "openNotificationSettings", "", "requestNotificationPermission", "activity", "Landroid/app/Activity;", "requestCode", "", "launcher", "Landroidx/activity/result/ActivityResultLauncher;", "fragment", "Landroidx/fragment/app/Fragment;", "shouldShowPermissionRationale", "core-upload_debug"})
public final class NotificationPermissionHelper {
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String NOTIFICATION_PERMISSION = "android.permission.POST_NOTIFICATIONS";
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.edc.core.upload.util.NotificationPermissionHelper INSTANCE = null;
    
    private NotificationPermissionHelper() {
        super();
    }
    
    /**
     * Check if notification permission is granted (Android 13+)
     * @param context The context to check permission
     * @return true if permission is granted or not required, false otherwise
     */
    public final boolean isNotificationPermissionGranted(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return false;
    }
    
    /**
     * Check if we should show permission rationale
     * @param activity The activity to check rationale
     * @return true if rationale should be shown, false otherwise
     */
    public final boolean shouldShowPermissionRationale(@org.jetbrains.annotations.NotNull
    android.app.Activity activity) {
        return false;
    }
    
    /**
     * Request notification permission using Activity
     * @param activity The activity to request permission from
     * @param requestCode The request code for the permission
     */
    public final void requestNotificationPermission(@org.jetbrains.annotations.NotNull
    android.app.Activity activity, int requestCode) {
    }
    
    /**
     * Request notification permission using Fragment
     * @param fragment The fragment to request permission from
     * @param requestCode The request code for the permission
     */
    public final void requestNotificationPermission(@org.jetbrains.annotations.NotNull
    androidx.fragment.app.Fragment fragment, int requestCode) {
    }
    
    /**
     * Request notification permission using ActivityResultLauncher
     * @param launcher The launcher to request permission
     */
    public final void requestNotificationPermission(@org.jetbrains.annotations.NotNull
    androidx.activity.result.ActivityResultLauncher<java.lang.String> launcher) {
    }
    
    /**
     * Open app notification settings
     * @param context The context to open settings from
     */
    public final void openNotificationSettings(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
    }
    
    /**
     * Get user-friendly message for permission request
     * @param context The context for string resources
     * @return User-friendly message explaining why notification permission is needed
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPermissionRationaleMessage(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    /**
     * Get user-friendly message for settings redirect
     * @param context The context for string resources
     * @return User-friendly message explaining how to enable notifications in settings
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSettingsMessage(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
}