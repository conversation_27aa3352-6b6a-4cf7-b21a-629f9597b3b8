package com.bukuwarung.edc.core.upload.util;

/**
 * Constants used throughout the core upload module
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\t\n\u0002\b\r\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\tR\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\tR\u000e\u0010\u000e\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/bukuwarung/edc/core/upload/util/UploadConstants;", "", "()V", "DATABASE_VERSION", "", "DEFAULT_ALLOWED_EXTENSIONS", "", "", "getDEFAULT_ALLOWED_EXTENSIONS", "()Ljava/util/List;", "DEFAULT_ALLOWED_IMAGE_TYPES", "getDEFAULT_ALLOWED_IMAGE_TYPES", "DEFAULT_ALLOWED_VIDEO_TYPES", "getDEFAULT_ALLOWED_VIDEO_TYPES", "DEFAULT_MAX_FILE_SIZE_BYTES", "", "DEFAULT_MAX_RETRIES", "DEFAULT_RETRY_DELAY_MS", "DEFAULT_TIMEOUT_SECONDS", "NOTIFICATION_CHANNEL_ID", "PROGRESS_UPDATE_INTERVAL_MS", "RESULT_ERROR_MESSAGE", "RESULT_SERVER_PATH", "RESULT_SIGNED_URL", "UPLOAD_DATABASE_NAME", "UPLOAD_TABLE_NAME", "WORK_TAG_CLEANUP", "WORK_TAG_UPLOAD", "core-upload_debug"})
public final class UploadConstants {
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String WORK_TAG_UPLOAD = "core_upload";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String WORK_TAG_CLEANUP = "upload_cleanup";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String RESULT_SIGNED_URL = "signed_url";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String RESULT_SERVER_PATH = "server_path";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String RESULT_ERROR_MESSAGE = "error_message";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String NOTIFICATION_CHANNEL_ID = "upload_channel";
    public static final long DEFAULT_MAX_FILE_SIZE_BYTES = 104857600L;
    public static final long DEFAULT_TIMEOUT_SECONDS = 300L;
    public static final long DEFAULT_RETRY_DELAY_MS = 1000L;
    public static final int DEFAULT_MAX_RETRIES = 3;
    @org.jetbrains.annotations.NotNull
    private static final java.util.List<java.lang.String> DEFAULT_ALLOWED_IMAGE_TYPES = null;
    @org.jetbrains.annotations.NotNull
    private static final java.util.List<java.lang.String> DEFAULT_ALLOWED_VIDEO_TYPES = null;
    @org.jetbrains.annotations.NotNull
    private static final java.util.List<java.lang.String> DEFAULT_ALLOWED_EXTENSIONS = null;
    public static final long PROGRESS_UPDATE_INTERVAL_MS = 500L;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String UPLOAD_DATABASE_NAME = "upload_database";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String UPLOAD_TABLE_NAME = "uploads";
    public static final int DATABASE_VERSION = 1;
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.edc.core.upload.util.UploadConstants INSTANCE = null;
    
    private UploadConstants() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getDEFAULT_ALLOWED_IMAGE_TYPES() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getDEFAULT_ALLOWED_VIDEO_TYPES() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getDEFAULT_ALLOWED_EXTENSIONS() {
        return null;
    }
}