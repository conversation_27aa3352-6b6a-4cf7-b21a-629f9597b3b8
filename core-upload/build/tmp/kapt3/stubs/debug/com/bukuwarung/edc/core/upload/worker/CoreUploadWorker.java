package com.bukuwarung.edc.core.upload.worker;

/**
 * WorkManager worker for handling file uploads in the background
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0003\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u0000 +2\u00020\u0001:\u0001+B+\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J!\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u000eH\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0014J\u001c\u0010\u0015\u001a\u00020\f2\b\u0010\u0016\u001a\u0004\u0018\u00010\u000e2\b\u0010\u0017\u001a\u0004\u0018\u00010\u000eH\u0002J\u0011\u0010\u0018\u001a\u00020\u0019H\u0096@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001aJ\u0010\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eH\u0002J \u0010\u001f\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020!H\u0002JA\u0010#\u001a\u00020$2\u0006\u0010\u0013\u001a\u00020\u000e2\u0006\u0010%\u001a\u00020&2\b\b\u0002\u0010\'\u001a\u00020!2\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010)\u001a\u00020!H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006,"}, d2 = {"Lcom/bukuwarung/edc/core/upload/worker/CoreUploadWorker;", "Landroidx/work/CoroutineWorker;", "context", "Landroid/content/Context;", "workerParams", "Landroidx/work/WorkerParameters;", "repository", "Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;", "notificationManager", "Lcom/bukuwarung/edc/core/upload/data/notification/UploadNotificationManager;", "(Landroid/content/Context;Landroidx/work/WorkerParameters;Lcom/bukuwarung/edc/core/upload/domain/repository/CoreUploadRepository;Lcom/bukuwarung/edc/core/upload/data/notification/UploadNotificationManager;)V", "createErrorData", "Landroidx/work/Data;", "message", "", "createForegroundInfo", "Landroidx/work/ForegroundInfo;", "request", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadRequest;", "uploadId", "(Lcom/bukuwarung/edc/core/upload/domain/model/UploadRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSuccessData", "signedUrl", "serverPath", "doWork", "Landroidx/work/ListenableWorker$Result;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isRetryableError", "", "error", "", "shouldRetry", "currentRetryCount", "", "maxRetries", "updateProgress", "", "status", "Lcom/bukuwarung/edc/core/upload/domain/model/UploadStatus;", "progressPercent", "errorMessage", "retryCount", "(Ljava/lang/String;Lcom/bukuwarung/edc/core/upload/domain/model/UploadStatus;ILjava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "core-upload_debug"})
@androidx.hilt.work.HiltWorker
public final class CoreUploadWorker extends androidx.work.CoroutineWorker {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.work.WorkerParameters workerParams = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository = null;
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.edc.core.upload.data.notification.UploadNotificationManager notificationManager = null;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String KEY_UPLOAD_REQUEST = "upload_request";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String KEY_UPLOAD_ID = "upload_id";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String KEY_RETRY_COUNT = "retry_count";
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.edc.core.upload.worker.CoreUploadWorker.Companion Companion = null;
    
    @dagger.assisted.AssistedInject
    public CoreUploadWorker(@dagger.assisted.Assisted
    @org.jetbrains.annotations.NotNull
    android.content.Context context, @dagger.assisted.Assisted
    @org.jetbrains.annotations.NotNull
    androidx.work.WorkerParameters workerParams, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository repository, @org.jetbrains.annotations.NotNull
    com.bukuwarung.edc.core.upload.data.notification.UploadNotificationManager notificationManager) {
        super(null, null);
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object doWork(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super androidx.work.ListenableWorker.Result> $completion) {
        return null;
    }
    
    private final java.lang.Object createForegroundInfo(com.bukuwarung.edc.core.upload.domain.model.UploadRequest request, java.lang.String uploadId, kotlin.coroutines.Continuation<? super androidx.work.ForegroundInfo> $completion) {
        return null;
    }
    
    private final java.lang.Object updateProgress(java.lang.String uploadId, com.bukuwarung.edc.core.upload.domain.model.UploadStatus status, int progressPercent, java.lang.String errorMessage, int retryCount, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final boolean shouldRetry(java.lang.Throwable error, int currentRetryCount, int maxRetries) {
        return false;
    }
    
    private final boolean isRetryableError(java.lang.Throwable error) {
        return false;
    }
    
    private final androidx.work.Data createErrorData(java.lang.String message) {
        return null;
    }
    
    private final androidx.work.Data createSuccessData(java.lang.String signedUrl, java.lang.String serverPath) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/bukuwarung/edc/core/upload/worker/CoreUploadWorker$Companion;", "", "()V", "KEY_RETRY_COUNT", "", "KEY_UPLOAD_ID", "KEY_UPLOAD_REQUEST", "core-upload_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}