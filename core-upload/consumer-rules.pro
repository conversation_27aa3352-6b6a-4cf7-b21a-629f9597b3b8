# Core Upload Module ProGuard Rules

# Keep all public classes and methods in the core upload module
-keep public class com.bukuwarung.edc.core.upload.** { *; }

# Keep WorkManager classes
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.CoroutineWorker

# Keep data classes used for serialization
-keep class com.bukuwarung.edc.core.upload.domain.model.** { *; }
-keep class com.bukuwarung.edc.core.upload.data.model.** { *; }

# Keep Retrofit interfaces
-keep interface com.bukuwarung.edc.core.upload.data.api.** { *; }

# Keep Hilt generated classes
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.android.internal.managers.ApplicationComponentManager
