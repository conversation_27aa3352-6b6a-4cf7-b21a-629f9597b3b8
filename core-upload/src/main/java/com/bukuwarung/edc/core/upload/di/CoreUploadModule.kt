package com.bukuwarung.edc.core.upload.di

import android.content.Context
import androidx.work.WorkManager
import com.bukuwarung.edc.core.upload.data.api.CoreUploadApi
import com.bukuwarung.edc.core.upload.data.interceptor.MultipartContentTypeInterceptor
import com.bukuwarung.edc.core.upload.data.repository.CoreUploadRepositoryImpl
import com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * Hilt module for core upload dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class CoreUploadModule {

    @Binds
    @Singleton
    abstract fun bindCoreUploadRepository(
        coreUploadRepositoryImpl: CoreUploadRepositoryImpl
    ): CoreUploadRepository

    companion object {

        @Provides
        @Singleton
        fun provideWorkManager(@ApplicationContext context: Context): WorkManager {
            return WorkManager.getInstance(context)
        }

        @Provides
        @Singleton
        @CoreUploadGson
        fun provideCoreUploadGson(): Gson {
            return GsonBuilder()
                .setLenient()
                .create()
        }

        @Provides
        @Singleton
        @CoreUploadOkHttpClient
        fun provideCoreUploadOkHttpClient(
            @Named("normal") existingOkHttpClient: OkHttpClient
        ): OkHttpClient {
            // Use the existing OkHttpClient but extend timeouts for large file uploads
            // and add multipart content type interceptor
            return existingOkHttpClient.newBuilder()
                .addInterceptor(MultipartContentTypeInterceptor())
                .readTimeout(300, TimeUnit.SECONDS) // 5 minutes for large file uploads
                .writeTimeout(300, TimeUnit.SECONDS)
                .build()
        }

        @Provides
        @Singleton
        @CoreUploadRetrofit
        fun provideCoreUploadRetrofit(
            @CoreUploadOkHttpClient okHttpClient: OkHttpClient,
            @CoreUploadGson gson: Gson
        ): Retrofit {
            return Retrofit.Builder()
                .baseUrl("https://api.example.com/") // This will be overridden by @Url in API calls
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build()
        }

        @Provides
        @Singleton
        fun provideCoreUploadApi(@CoreUploadRetrofit retrofit: Retrofit): CoreUploadApi {
            return retrofit.create(CoreUploadApi::class.java)
        }

        @Provides
        @Singleton
        fun provideGson(): Gson {
            return GsonBuilder()
                .setLenient()
                .create()
        }
    }
}

/**
 * Qualifiers for core upload specific dependencies
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CoreUploadGson

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CoreUploadOkHttpClient

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CoreUploadRetrofit
