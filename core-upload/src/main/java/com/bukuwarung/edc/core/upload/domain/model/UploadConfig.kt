package com.bukuwarung.edc.core.upload.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Configuration for upload operations
 */
@Parcelize
data class UploadConfig(
    val endpoint: String,
    val authHeaders: Map<String, String> = emptyMap(),
    val fieldNames: UploadFieldNames = UploadFieldNames(),
    val validation: UploadValidation = UploadValidation(),
    val retryConfig: RetryConfig = RetryConfig(),
    val notificationConfig: NotificationConfig = NotificationConfig()
) : Parcelable

/**
 * Field names for multipart upload
 */
@Parcelize
data class UploadFieldNames(
    val fileField: String = "file",
    val categoryField: String = "category",
    val fileNameField: String = "fileName"
) : Parcelable

/**
 * Upload validation rules
 */
@Parcelize
data class UploadValidation(
    val maxFileSizeBytes: Long = Long.MAX_VALUE,
    val allowedMimeTypes: List<String> = listOf("image/*", "video/*"),
    val allowedExtensions: List<String> = listOf("jpg", "jpeg", "png", "mp4", "mov", "avi")
) : Parcelable

/**
 * Retry configuration
 */
@Parcelize
data class RetryConfig(
    val maxRetries: Int = 3,
    val initialDelayMs: Long = 1000L,
    val maxDelayMs: Long = 30000L,
    val backoffMultiplier: Double = 2.0
) : Parcelable

/**
 * Notification configuration
 */
@Parcelize
data class NotificationConfig(
    val channelId: String = "upload_channel",
    val channelName: String = "File Uploads", // Will be overridden by string resources
    val notificationTitle: String = "Uploading file...", // Will be overridden by string resources
    val successTitle: String = "Upload completed", // Will be overridden by string resources
    val errorTitle: String = "Upload failed", // Will be overridden by string resources
    val showProgress: Boolean = true,
    val enableSound: Boolean = false,
    val enableVibration: Boolean = false,
    val clickAction: String? = null
) : Parcelable
