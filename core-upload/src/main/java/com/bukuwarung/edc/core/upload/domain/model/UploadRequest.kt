package com.bukuwarung.edc.core.upload.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Upload request containing file and metadata
 */
@Parcelize
data class UploadRequest(
    val id: String,
    val filePath: String,
    val category: String,
    val config: UploadConfig,
    val metadata: Map<String, String> = emptyMap(),
    val originatingActivity: String? = null,
    val originatingFeature: String? = null
) : Parcelable

/**
 * Upload status enumeration
 */
enum class UploadStatus {
    PENDING,
    PROCESSING,
    UPLOADING,
    COMPLETED,
    FAILED,
    CANCELLED
}

/**
 * Upload progress information
 */
@Parcelize
data class UploadProgress(
    val uploadId: String,
    val status: UploadStatus,
    val progressPercent: Int = 0,
    val bytesUploaded: Long = 0L,
    val totalBytes: Long = 0L,
    val errorMessage: String? = null,
    val retryCount: Int = 0,
    val startTime: Long = System.currentTimeMillis(),
    val endTime: Long? = null
) : Parcelable

/**
 * Upload result containing response data
 */
@Parcelize
data class UploadResult(
    val uploadId: String,
    val success: Boolean,
    val signedUrl: String? = null,
    val serverPath: String? = null,
    val responseData: Map<String, String> = emptyMap(),
    val errorMessage: String? = null,
    val httpCode: Int? = null
) : Parcelable
