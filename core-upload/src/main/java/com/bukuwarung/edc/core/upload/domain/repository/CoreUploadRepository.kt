package com.bukuwarung.edc.core.upload.domain.repository

import com.bukuwarung.edc.core.upload.domain.model.NotificationPermissionStatus
import com.bukuwarung.edc.core.upload.domain.model.UploadProgress
import com.bukuwarung.edc.core.upload.domain.model.UploadRequest
import com.bukuwarung.edc.core.upload.domain.model.UploadResult
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for core upload functionality
 */
interface CoreUploadRepository {
    
    /**
     * Start an upload operation
     */
    suspend fun startUpload(request: UploadRequest): Result<String>
    
    /**
     * Perform the actual upload operation
     */
    suspend fun performUpload(request: UploadRequest): Result<UploadResult>
    
    /**
     * Cancel an ongoing upload
     */
    suspend fun cancelUpload(uploadId: String): Result<Unit>
    
    /**
     * Get upload progress as a flow
     */
    fun getUploadProgress(uploadId: String): Flow<UploadProgress>
    
    /**
     * Get all active uploads
     */
    fun getActiveUploads(): Flow<List<UploadProgress>>
    
    /**
     * Update upload progress
     */
    suspend fun updateProgress(progress: UploadProgress)
    
    /**
     * Clear completed uploads
     */
    suspend fun clearCompletedUploads()
    
    /**
     * Validate file before upload
     */
    suspend fun validateFile(request: UploadRequest): Result<Unit>

    /**
     * Check notification permission status
     * @return NotificationPermissionStatus with detailed permission info
     */
    fun getNotificationPermissionStatus(): NotificationPermissionStatus
}
