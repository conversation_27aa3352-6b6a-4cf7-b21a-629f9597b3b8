package com.bukuwarung.edc.core.upload.domain.usecase

import com.bukuwarung.edc.core.upload.domain.model.UploadProgress
import com.bukuwarung.edc.core.upload.domain.repository.CoreUploadRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for getting upload progress
 */
@Singleton
class GetUploadProgressUseCase @Inject constructor(
    private val repository: CoreUploadRepository
) {
    
    /**
     * Get upload progress for a specific upload
     * @param uploadId The upload ID to track
     * @return Flow of upload progress updates
     */
    operator fun invoke(uploadId: String): Flow<UploadProgress> {
        return repository.getUploadProgress(uploadId)
    }
    
    /**
     * Get all active uploads
     * @return Flow of all active upload progress
     */
    fun getAllActiveUploads(): Flow<List<UploadProgress>> {
        return repository.getActiveUploads()
    }
}
