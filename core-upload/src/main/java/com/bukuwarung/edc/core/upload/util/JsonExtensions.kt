package com.bukuwarung.edc.core.upload.util

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Extension functions for JSON serialization/deserialization
 */

val gson = Gson()

/**
 * Convert object to JSON string
 */
fun Any.toJson(): String {
    return gson.toJson(this)
}

/**
 * Convert JSON string to object
 */
inline fun <reified T> String.fromJson(): T? {
    return try {
        gson.fromJson(this, object : TypeToken<T>() {}.type)
    } catch (e: Exception) {
        null
    }
}

/**
 * Convert JSON string to object with explicit type
 */
fun <T> String.fromJson(clazz: Class<T>): T? {
    return try {
        gson.fromJson(this, clazz)
    } catch (e: Exception) {
        null
    }
}
