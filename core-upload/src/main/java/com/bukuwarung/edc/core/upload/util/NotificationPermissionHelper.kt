package com.bukuwarung.edc.core.upload.util

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.bukuwarung.edc.core.upload.R

/**
 * Helper class for handling notification permissions
 */
object NotificationPermissionHelper {
    
    const val NOTIFICATION_PERMISSION = Manifest.permission.POST_NOTIFICATIONS
    
    /**
     * Check if notification permission is granted (Android 13+)
     * @param context The context to check permission
     * @return true if permission is granted or not required, false otherwise
     */
    fun isNotificationPermissionGranted(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                NOTIFICATION_PERMISSION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Pre-Android 13, notification permission is not required
            true
        }
    }
    
    /**
     * Check if we should show permission rationale
     * @param activity The activity to check rationale
     * @return true if rationale should be shown, false otherwise
     */
    fun shouldShowPermissionRationale(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                NOTIFICATION_PERMISSION
            )
        } else {
            false
        }
    }
    
    /**
     * Request notification permission using Activity
     * @param activity The activity to request permission from
     * @param requestCode The request code for the permission
     */
    fun requestNotificationPermission(activity: Activity, requestCode: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(NOTIFICATION_PERMISSION),
                requestCode
            )
        }
    }
    
    /**
     * Request notification permission using Fragment
     * @param fragment The fragment to request permission from
     * @param requestCode The request code for the permission
     */
    fun requestNotificationPermission(fragment: Fragment, requestCode: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            fragment.requestPermissions(
                arrayOf(NOTIFICATION_PERMISSION),
                requestCode
            )
        }
    }
    
    /**
     * Request notification permission using ActivityResultLauncher
     * @param launcher The launcher to request permission
     */
    fun requestNotificationPermission(launcher: ActivityResultLauncher<String>) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            launcher.launch(NOTIFICATION_PERMISSION)
        }
    }
    
    /**
     * Open app notification settings
     * @param context The context to open settings from
     */
    fun openNotificationSettings(context: Context) {
        val intent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
            }
        } else {
            Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
            }
        }
        
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }
    
    /**
     * Get user-friendly message for permission request
     * @param context The context for string resources
     * @return User-friendly message explaining why notification permission is needed
     */
    fun getPermissionRationaleMessage(context: Context): String {
        return context.getString(R.string.core_upload_permission_rationale)
    }
    
    /**
     * Get user-friendly message for settings redirect
     * @param context The context for string resources
     * @return User-friendly message explaining how to enable notifications in settings
     */
    fun getSettingsMessage(context: Context): String {
        return context.getString(R.string.core_upload_permission_settings_message)
    }
}
