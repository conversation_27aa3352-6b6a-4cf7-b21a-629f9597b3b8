package com.bukuwarung.edc.core.upload.util

/**
 * Constants used throughout the core upload module
 */
object UploadConstants {
    
    // WorkManager tags
    const val WORK_TAG_UPLOAD = "core_upload"
    const val WORK_TAG_CLEANUP = "upload_cleanup"
    
    // Work result keys
    const val RESULT_SIGNED_URL = "signed_url"
    const val RESULT_SERVER_PATH = "server_path"
    const val RESULT_ERROR_MESSAGE = "error_message"
    
    // Notification constants
    const val NOTIFICATION_CHANNEL_ID = "upload_channel"
    
    // Default values
    const val DEFAULT_MAX_FILE_SIZE_BYTES = 100L * 1024 * 1024 // 100MB
    const val DEFAULT_TIMEOUT_SECONDS = 300L // 5 minutes
    const val DEFAULT_RETRY_DELAY_MS = 1000L
    const val DEFAULT_MAX_RETRIES = 3
    
    // File validation
    val DEFAULT_ALLOWED_IMAGE_TYPES = listOf("image/jpeg", "image/jpg", "image/png")
    val DEFAULT_ALLOWED_VIDEO_TYPES = listOf("video/mp4", "video/mov", "video/avi")
    val DEFAULT_ALLOWED_EXTENSIONS = listOf("jpg", "jpeg", "png", "mp4", "mov", "avi")
    
    // Progress update intervals
    const val PROGRESS_UPDATE_INTERVAL_MS = 500L
    
    // Database constants
    const val UPLOAD_DATABASE_NAME = "upload_database"
    const val UPLOAD_TABLE_NAME = "uploads"
    const val DATABASE_VERSION = 1
}
