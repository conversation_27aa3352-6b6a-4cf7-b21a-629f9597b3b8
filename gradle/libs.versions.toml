[versions]
# Build Plugins
agp = "8.11.1"
kotlin = "2.2.0"
ksp = "2.2.0-2.0.2"
googleServicesPlugin = "4.4.3"
hiltPlugin = "2.57"
firebaseCrashlyticsPlugin = "3.0.4"
firebasePerfPlugin = "1.4.2"
gson = "2.13.1"
lifecycleExtensions = "2.2.0"
ok2curl = "0.8.0"
androidTargetSdk = "35"
androidMinSdk = "23"
androidCompileSdk = "36"

# Dependencies
activity = "1.10.1"
annotation = "1.9.1"
appcompat = "1.7.1"
auth0Jwt = "2.0.2"
browser = "1.8.0"
camera = "1.4.2"
compressor = "3.0.1"
constraintlayout = "2.2.1"
coreKtx = "1.16.0"
databinding = "8.11.1"
espresso = "3.6.1"
firebaseAppCheck = "18.0.0"
firebaseAuth = "23.2.1"
firebaseBom = "33.16.0"
firebaseFirestore = "25.1.4"
fragment = "1.8.8"
glide = "4.16.0"
hilt = "2.57" # Updated version
junit = "4.13.2"
junitExt = "1.2.1"
kotlinBom = "2.0.21"
lifecycle = "2.9.2"
lottie = "3.4.2"
material = "1.12.0"
mixpanel = "7.5.2"
mlkitFaceDetection = "16.1.5"
bureauDeviceIntelligence = "4.0.0"
bureauApponomics = "4.0.2"
okhttp = "4.9.1"
paging = "3.3.6"
paperdb = "2.7.1"
paxstoreSdk = "9.3.0"
playAppUpdate = "2.1.0"
playServicesAuth = "19.2.0"
playServicesLocation = "21.0.1"
playServicesMaps = "18.0.0"
progressbutton = "2.1.0"
retrofit = "2.9.0"
rxjava2 = "2.2.21" # TODO remove this
securityCrypto = "1.1.0-beta01"
shimmer = "0.5.0"
swiperefreshlayout = "1.1.0"
tiktokSdk = "1.3.5"
workRuntime = "2.10.2"
zohoMobilisten = "4.3.3"
hiltWork = "1.2.0"

[libraries]
# AndroidX
androidx-activity-ktx = { module = "androidx.activity:activity-ktx", version.ref = "activity" }
androidx-annotation = { module = "androidx.annotation:annotation", version.ref = "annotation" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-browser = { module = "androidx.browser:browser", version.ref = "browser" }
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "camera" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camera" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "camera" }
androidx-camera-video = { module = "androidx.camera:camera-video", version.ref = "camera" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-databinding-adapters = { module = "androidx.databinding:databinding-adapters", version.ref = "databinding" }
androidx-databinding-common = { module = "androidx.databinding:databinding-common", version.ref = "databinding" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragment" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-paging-runtime-ktx = { module = "androidx.paging:paging-runtime-ktx", version.ref = "paging" }
androidx-security-crypto = { module = "androidx.security:security-crypto", version.ref = "securityCrypto" }
androidx-swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version.ref = "swiperefreshlayout" }
androidx-work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "workRuntime" }

# Google Hilt
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hilt" }

# Google Firebase & Play Services
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
firebase-appcheck-playintegrity = { module = "com.google.firebase:firebase-appcheck-playintegrity", version.ref = "firebaseAppCheck" }
firebase-auth = { module = "com.google.firebase:firebase-auth", version.ref = "firebaseAuth" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-config-ktx = { group = "com.google.firebase", name = "firebase-config-ktx" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
firebase-firestore-ktx = { module = "com.google.firebase:firebase-firestore-ktx", version.ref = "firebaseFirestore" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging" }
firebase-perf = { group = "com.google.firebase", name = "firebase-perf" }
google-material = { module = "com.google.android.material:material", version.ref = "material" }
google-play-app-update-ktx = { module = "com.google.android.play:app-update-ktx", version.ref = "playAppUpdate" }
google-play-services-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "playServicesAuth" }
google-play-services-location = { module = "com.google.android.gms:play-services-location", version.ref = "playServicesLocation" }
google-play-services-maps = { module = "com.google.android.gms:play-services-maps", version.ref = "playServicesMaps" }
google-mlkit-face-detection = { module = "com.google.mlkit:face-detection", version.ref = "mlkitFaceDetection" }

# Bureau
bureau-device-intelligence = { module = "id.bureau:device-intelligence", version.ref = "bureauDeviceIntelligence" }
bureau-apponomics = { module = "id.bureau:apponomics", version.ref = "bureauApponomics" }

# Square
okhttp-core = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okhttp-logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp" }
retrofit-core = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-adapter-rxjava2 = { module = "com.squareup.retrofit2:adapter-rxjava2", version.ref = "retrofit" }
retrofit-converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }

# JetBrains
kotlin-bom = { module = "org.jetbrains.kotlin:kotlin-bom", version.ref = "kotlinBom" }

# Others
auth0-jwtdecode = { module = "com.auth0.android:jwtdecode", version.ref = "auth0Jwt" }
compressor = { module = "id.zelory:compressor", version.ref = "compressor" }
glide-core = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-compiler = { module = "com.github.bumptech.glide:ksp", version.ref = "glide" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
mixpanel = { module = "com.mixpanel.android:mixpanel-android", version.ref = "mixpanel" }
ok2curl = { module = "com.github.mrmike:ok2curl", version.ref = "ok2curl" } # New library
paperdb = { module = "io.github.pilgr:paperdb", version.ref = "paperdb" }
paxstore-sdk = { module = "com.whatspos.sdk:paxstore-3rd-app-android-sdk", version.ref = "paxstoreSdk" }
progressbutton = { module = "com.github.razir.progressbutton:progressbutton", version.ref = "progressbutton" }
rxjava2 = { module = "io.reactivex.rxjava2:rxjava", version.ref = "rxjava2" }
shimmer = { module = "com.facebook.shimmer:shimmer", version.ref = "shimmer" }
tiktok-sdk = { module = "com.github.tiktok:tiktok-business-android-sdk", version.ref = "tiktokSdk" }
zoho-mobilisten = { module = "com.zoho.salesiq:mobilisten", version.ref = "zohoMobilisten" }
google-gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
androidx-lifecycle-extensions = { module = "androidx.lifecycle:lifecycle-extensions", version.ref = "lifecycleExtensions" }

# Testing
junit = { module = "junit:junit", version.ref = "junit" }
androidx-test-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espresso" }
androidx-test-junit = { module = "androidx.test.ext:junit", version.ref = "junitExt" }
androidx-hilt-work = { group = "androidx.hilt", name = "hilt-work", version.ref = "hiltWork" }

[bundles]
# Bundles group common dependencies together
camera = ["androidx-camera-camera2", "androidx-camera-lifecycle", "androidx-camera-view", "androidx-camera-video"]
databinding = ["androidx-databinding-common", "androidx-databinding-adapters"]
firebase = ["firebase-analytics", "firebase-config-ktx", "firebase-crashlytics", "firebase-messaging", "firebase-perf"]
lifecycle = ["androidx-lifecycle-livedata-ktx", "androidx-lifecycle-viewmodel-ktx"]
retrofit = ["retrofit-core", "retrofit-converter-gson", "okhttp-core", "okhttp-logging-interceptor"]
testing-android = ["androidx-test-junit", "androidx-test-espresso-core"]

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsPlugin" }
firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "firebasePerfPlugin" }
google-services = { id = "com.google.gms.google-services", version.ref = "googleServicesPlugin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hiltPlugin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }