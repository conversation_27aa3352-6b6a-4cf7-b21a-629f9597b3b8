plugins {
    alias libs.plugins.android.library
    alias libs.plugins.kotlin.android
    alias libs.plugins.hilt
    alias libs.plugins.ksp
}

android {
    namespace 'com.bukuwarung.network'
    compileSdkVersion 36

    defaultConfig {
        versionCode 1
        versionName "1.0"
    }

    buildFeatures {
        buildConfig true
    }

    defaultConfig {
        minSdkVersion libs.versions.androidMinSdk.get().toInteger()
        targetSdkVersion libs.versions.androidTargetSdk.get().toInteger()
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "true"
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "false"
        }
    }

    flavorDimensions = ["env"]
    productFlavors {
        dev {
            dimension "env"
            buildConfigField("String", "API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-dev.bukuwarung.com"')
        }
        stg {
            dimension "env"
            buildConfigField("String", "API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
        }
        prod {
            dimension "env"
            buildConfigField("String", "API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-v4.bukuwarung.com"')
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.hilt.android
    ksp libs.hilt.compiler

    implementation libs.ok2curl

    implementation libs.google.gson
    implementation libs.bundles.retrofit
}