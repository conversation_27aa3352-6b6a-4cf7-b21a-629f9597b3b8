// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import com.bukuwarung.network.utils.AppProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvideOkHttpClientForLocationFactory implements Factory<OkHttpClient> {
  private final Provider<AppProvider> appProvider;

  public NetworkModule_ProvideOkHttpClientForLocationFactory(Provider<AppProvider> appProvider) {
    this.appProvider = appProvider;
  }

  @Override
  public OkHttpClient get() {
    return provideOkHttpClientForLocation(appProvider.get());
  }

  public static NetworkModule_ProvideOkHttpClientForLocationFactory create(
      Provider<AppProvider> appProvider) {
    return new NetworkModule_ProvideOkHttpClientForLocationFactory(appProvider);
  }

  public static OkHttpClient provideOkHttpClientForLocation(AppProvider appProvider) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideOkHttpClientForLocation(appProvider));
  }
}
