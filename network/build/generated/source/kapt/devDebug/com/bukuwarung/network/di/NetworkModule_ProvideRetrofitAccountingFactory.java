// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import com.bukuwarung.network.utils.AppProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvideRetrofitAccountingFactory implements Factory<Retrofit> {
  private final Provider<AppProvider> appProvider;

  public NetworkModule_ProvideRetrofitAccountingFactory(Provider<AppProvider> appProvider) {
    this.appProvider = appProvider;
  }

  @Override
  public Retrofit get() {
    return provideRetrofitAccounting(appProvider.get());
  }

  public static NetworkModule_ProvideRetrofitAccountingFactory create(
      Provider<AppProvider> appProvider) {
    return new NetworkModule_ProvideRetrofitAccountingFactory(appProvider);
  }

  public static Retrofit provideRetrofitAccounting(AppProvider appProvider) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideRetrofitAccounting(appProvider));
  }
}
