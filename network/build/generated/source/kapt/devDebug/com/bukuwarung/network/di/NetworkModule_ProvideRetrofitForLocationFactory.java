// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvideRetrofitForLocationFactory implements Factory<Retrofit> {
  private final Provider<OkHttpClient> okhttpClientProvider;

  private final Provider<String> bASE_URLProvider;

  public NetworkModule_ProvideRetrofitForLocationFactory(
      Provider<OkHttpClient> okhttpClientProvider, Provider<String> bASE_URLProvider) {
    this.okhttpClientProvider = okhttpClientProvider;
    this.bASE_URLProvider = bASE_URLProvider;
  }

  @Override
  public Retrofit get() {
    return provideRetrofitForLocation(okhttpClientProvider.get(), bASE_URLProvider.get());
  }

  public static NetworkModule_ProvideRetrofitForLocationFactory create(
      Provider<OkHttpClient> okhttpClientProvider, Provider<String> bASE_URLProvider) {
    return new NetworkModule_ProvideRetrofitForLocationFactory(okhttpClientProvider, bASE_URLProvider);
  }

  public static Retrofit provideRetrofitForLocation(OkHttpClient okhttpClient, String BASE_URL) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideRetrofitForLocation(okhttpClient, BASE_URL));
  }
}
