// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvideRetrofitForPinFactory implements Factory<Retrofit> {
  private final Provider<OkHttpClient> okhttpClientProvider;

  private final Provider<String> bASE_URLProvider;

  public NetworkModule_ProvideRetrofitForPinFactory(Provider<OkHttpClient> okhttpClientProvider,
      Provider<String> bASE_URLProvider) {
    this.okhttpClientProvider = okhttpClientProvider;
    this.bASE_URLProvider = bASE_URLProvider;
  }

  @Override
  public Retrofit get() {
    return provideRetrofitForPin(okhttpClientProvider.get(), bASE_URLProvider.get());
  }

  public static NetworkModule_ProvideRetrofitForPinFactory create(
      Provider<OkHttpClient> okhttpClientProvider, Provider<String> bASE_URLProvider) {
    return new NetworkModule_ProvideRetrofitForPinFactory(okhttpClientProvider, bASE_URLProvider);
  }

  public static Retrofit provideRetrofitForPin(OkHttpClient okhttpClient, String BASE_URL) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideRetrofitForPin(okhttpClient, BASE_URL));
  }
}
