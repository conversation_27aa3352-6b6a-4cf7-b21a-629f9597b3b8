// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import com.bukuwarung.network.session.SessionRemoteDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class SessionApiModule_ProvideSessionDataSourceFactory implements Factory<SessionRemoteDataSource> {
  private final SessionApiModule module;

  private final Provider<Retrofit> retrofitProvider;

  public SessionApiModule_ProvideSessionDataSourceFactory(SessionApiModule module,
      Provider<Retrofit> retrofitProvider) {
    this.module = module;
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public SessionRemoteDataSource get() {
    return provideSessionDataSource(module, retrofitProvider.get());
  }

  public static SessionApiModule_ProvideSessionDataSourceFactory create(SessionApiModule module,
      Provider<Retrofit> retrofitProvider) {
    return new SessionApiModule_ProvideSessionDataSourceFactory(module, retrofitProvider);
  }

  public static SessionRemoteDataSource provideSessionDataSource(SessionApiModule instance,
      Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(instance.provideSessionDataSource(retrofit));
  }
}
