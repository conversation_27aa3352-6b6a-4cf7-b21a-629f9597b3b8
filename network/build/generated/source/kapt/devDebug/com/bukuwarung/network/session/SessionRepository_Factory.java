// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.session;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class SessionRepository_Factory implements Factory<SessionRepository> {
  private final Provider<SessionRemoteDataSource> remoteDataSourceProvider;

  public SessionRepository_Factory(Provider<SessionRemoteDataSource> remoteDataSourceProvider) {
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public SessionRepository get() {
    return newInstance(remoteDataSourceProvider.get());
  }

  public static SessionRepository_Factory create(
      Provider<SessionRemoteDataSource> remoteDataSourceProvider) {
    return new SessionRepository_Factory(remoteDataSourceProvider);
  }

  public static SessionRepository newInstance(SessionRemoteDataSource remoteDataSource) {
    return new SessionRepository(remoteDataSource);
  }
}
