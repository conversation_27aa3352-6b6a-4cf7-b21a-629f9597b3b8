// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvideContextFactory implements Factory<Context> {
  private final Provider<Context> contextProvider;

  public NetworkModule_ProvideContextFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public Context get() {
    return provideContext(contextProvider.get());
  }

  public static NetworkModule_ProvideContextFactory create(Provider<Context> contextProvider) {
    return new NetworkModule_ProvideContextFactory(contextProvider);
  }

  public static Context provideContext(Context context) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideContext(context));
  }
}
