// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvidePostmanUrlFactory implements Factory<String> {
  @Override
  public String get() {
    return providePostmanUrl();
  }

  public static NetworkModule_ProvidePostmanUrlFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static String providePostmanUrl() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.providePostmanUrl());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvidePostmanUrlFactory INSTANCE = new NetworkModule_ProvidePostmanUrlFactory();
  }
}
