// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvideRetrofitPostmanFactory implements Factory<Retrofit> {
  private final Provider<OkHttpClient> okHttpClientProvider;

  private final Provider<String> postmanUrlProvider;

  public NetworkModule_ProvideRetrofitPostmanFactory(Provider<OkHttpClient> okHttpClientProvider,
      Provider<String> postmanUrlProvider) {
    this.okHttpClientProvider = okHttpClientProvider;
    this.postmanUrlProvider = postmanUrlProvider;
  }

  @Override
  public Retrofit get() {
    return provideRetrofitPostman(okHttpClientProvider.get(), postmanUrlProvider.get());
  }

  public static NetworkModule_ProvideRetrofitPostmanFactory create(
      Provider<OkHttpClient> okHttpClientProvider, Provider<String> postmanUrlProvider) {
    return new NetworkModule_ProvideRetrofitPostmanFactory(okHttpClientProvider, postmanUrlProvider);
  }

  public static Retrofit provideRetrofitPostman(OkHttpClient okHttpClient, String postmanUrl) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideRetrofitPostman(okHttpClient, postmanUrl));
  }
}
