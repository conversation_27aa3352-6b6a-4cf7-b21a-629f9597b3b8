// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import com.bukuwarung.network.utils.AppProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvideTmsRetrofitFactory implements Factory<Retrofit> {
  private final Provider<AppProvider> appProvider;

  public NetworkModule_ProvideTmsRetrofitFactory(Provider<AppProvider> appProvider) {
    this.appProvider = appProvider;
  }

  @Override
  public Retrofit get() {
    return provideTmsRetrofit(appProvider.get());
  }

  public static NetworkModule_ProvideTmsRetrofitFactory create(Provider<AppProvider> appProvider) {
    return new NetworkModule_ProvideTmsRetrofitFactory(appProvider);
  }

  public static Retrofit provideTmsRetrofit(AppProvider appProvider) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideTmsRetrofit(appProvider));
  }
}
