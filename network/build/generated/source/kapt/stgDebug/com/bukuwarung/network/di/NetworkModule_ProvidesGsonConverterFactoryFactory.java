// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.bukuwarung.network.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import retrofit2.converter.gson.GsonConverterFactory;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NetworkModule_ProvidesGsonConverterFactoryFactory implements Factory<GsonConverterFactory> {
  @Override
  public GsonConverterFactory get() {
    return providesGsonConverterFactory();
  }

  public static NetworkModule_ProvidesGsonConverterFactoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static GsonConverterFactory providesGsonConverterFactory() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.providesGsonConverterFactory());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvidesGsonConverterFactoryFactory INSTANCE = new NetworkModule_ProvidesGsonConverterFactoryFactory();
  }
}
