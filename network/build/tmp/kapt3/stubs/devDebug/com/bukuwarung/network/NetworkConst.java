package com.bukuwarung.network;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/bukuwarung/network/NetworkConst;", "", "()V", "BUKUWARUNG_TOKEN", "", "POSTMAN_URL", "SESSION_TOKEN", "network_devDebug"})
public final class NetworkConst {
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.network.NetworkConst INSTANCE = null;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String POSTMAN_URL = "https://60c8e33b-f999-4a33-9089-38e9c239231a.mock.pstmn.io";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BUKUWARUNG_TOKEN = "bukuwarung_token";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String SESSION_TOKEN = "session_token";
    
    private NetworkConst() {
        super();
    }
}