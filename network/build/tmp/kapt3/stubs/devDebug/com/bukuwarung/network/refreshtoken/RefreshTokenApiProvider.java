package com.bukuwarung.network.refreshtoken;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\u0007\u001a\u00020\b8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\n\u00a8\u0006\r"}, d2 = {"Lcom/bukuwarung/network/refreshtoken/RefreshTokenApiProvider;", "", "appProvider", "Lcom/bukuwarung/network/utils/AppProvider;", "(Lcom/bukuwarung/network/utils/AppProvider;)V", "getAppProvider", "()Lcom/bukuwarung/network/utils/AppProvider;", "authApiService", "Lcom/bukuwarung/network/session/SessionRemoteDataSource;", "getAuthApiService", "()Lcom/bukuwarung/network/session/SessionRemoteDataSource;", "authApiService$delegate", "Lkotlin/Lazy;", "network_devDebug"})
public final class RefreshTokenApiProvider {
    @org.jetbrains.annotations.NotNull
    private final com.bukuwarung.network.utils.AppProvider appProvider = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy authApiService$delegate = null;
    
    public RefreshTokenApiProvider(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.utils.AppProvider appProvider) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.network.utils.AppProvider getAppProvider() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.bukuwarung.network.session.SessionRemoteDataSource getAuthApiService() {
        return null;
    }
}