package com.bukuwarung.network.refreshtoken;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0012\u001a\u00020\u0005J\b\u0010\u0013\u001a\u0004\u0018\u00010\u0007J\b\u0010\u0014\u001a\u0004\u0018\u00010\u0007J\u00ea\u0001\u0010\u0015\u001a\u00020\u000526\u0010\b\u001a2\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u0018\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u0019\u0012\u0004\u0012\u00020\u00070\t26\u0010\u000b\u001a2\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u0018\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u001a\u0012\u0004\u0012\u00020\u00050\t2:\u0010\n\u001a6\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u0018\u0012\u0015\u0012\u0013\u0018\u00010\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u0019\u0012\u0006\u0012\u0004\u0018\u00010\u00070\t26\u0010\f\u001a2\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u0018\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\u0016\u0012\b\b\u0017\u0012\u0004\b\b(\u001a\u0012\u0004\u0012\u00020\u00050\tJ\u0011\u0010\u001b\u001a\u00020\u0005H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001cJ\u0016\u0010\u001d\u001a\u00020\u00052\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020\u0007R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R \u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\tX\u0082.\u00a2\u0006\u0002\n\u0000R$\u0010\n\u001a\u0018\u0012\u0004\u0012\u00020\u0007\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0006\u0012\u0004\u0018\u00010\u00070\tX\u0082.\u00a2\u0006\u0002\n\u0000R \u0010\u000b\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00050\tX\u0082.\u00a2\u0006\u0002\n\u0000R \u0010\f\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00050\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006 "}, d2 = {"Lcom/bukuwarung/network/refreshtoken/TokenManager;", "", "()V", "_sessionEnded", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "", "accessToken", "", "getEncryptedString", "Lkotlin/Function2;", "getPrefString", "putEncryptedString", "putPrefString", "refreshToken", "sessionEnded", "Lkotlinx/coroutines/flow/SharedFlow;", "getSessionEnded", "()Lkotlinx/coroutines/flow/SharedFlow;", "clear", "getAccessToken", "getRefreshToken", "init", "Lkotlin/ParameterName;", "name", "key", "default", "value", "notifySessionEnded", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTokens", "newAccess", "newRefresh", "network_devDebug"})
public final class TokenManager {
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.network.refreshtoken.TokenManager INSTANCE = null;
    @kotlin.jvm.Volatile
    private static volatile java.lang.String accessToken;
    @kotlin.jvm.Volatile
    private static volatile java.lang.String refreshToken;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getEncryptedString;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putEncryptedString;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getPrefString;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putPrefString;
    private static final kotlinx.coroutines.flow.MutableSharedFlow<kotlin.Unit> _sessionEnded = null;
    @org.jetbrains.annotations.NotNull
    private static final kotlinx.coroutines.flow.SharedFlow<kotlin.Unit> sessionEnded = null;
    
    private TokenManager() {
        super();
    }
    
    public final void init(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getEncryptedString, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putEncryptedString, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getPrefString, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putPrefString) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAccessToken() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getRefreshToken() {
        return null;
    }
    
    public final void updateTokens(@org.jetbrains.annotations.NotNull
    java.lang.String newAccess, @org.jetbrains.annotations.NotNull
    java.lang.String newRefresh) {
    }
    
    public final void clear() {
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.SharedFlow<kotlin.Unit> getSessionEnded() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object notifySessionEnded(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
}