package com.bukuwarung.network.session;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J+\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0003\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\u0007\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\t\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\n"}, d2 = {"Lcom/bukuwarung/network/session/SessionRemoteDataSource;", "", "createNewSession", "Lretrofit2/Response;", "Lcom/bukuwarung/network/model/response/SessionResponse;", "sessionId", "", "newSessionRequest", "Lcom/bukuwarung/network/model/request/SessionRequest;", "(Ljava/lang/String;Lcom/bukuwarung/network/model/request/SessionRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "network_devDebug"})
public abstract interface SessionRemoteDataSource {
    
    @org.jetbrains.annotations.Nullable
    @retrofit2.http.POST(value = "/api/v2/auth/users/bacon")
    public abstract java.lang.Object createNewSession(@org.jetbrains.annotations.NotNull
    @retrofit2.http.Header(value = "x-session-id")
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull
    @retrofit2.http.Body
    com.bukuwarung.network.model.request.SessionRequest newSessionRequest, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.bukuwarung.network.model.response.SessionResponse>> continuation);
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 3)
    public final class DefaultImpls {
    }
}