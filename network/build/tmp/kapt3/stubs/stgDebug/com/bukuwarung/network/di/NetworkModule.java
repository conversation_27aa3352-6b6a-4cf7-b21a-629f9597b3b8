package com.bukuwarung.network.di;

import java.lang.System;

@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\u0012\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\u0007\u001a\u00020\u0006H\u0007J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0010\u0010\f\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0010\u0010\r\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0007J\b\u0010\u000e\u001a\u00020\u0004H\u0007J\u001a\u0010\u000f\u001a\u00020\u00102\b\b\u0001\u0010\u0011\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\u0004H\u0007J\u0010\u0010\u0013\u001a\u00020\u00102\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u001a\u0010\u0014\u001a\u00020\u00102\b\b\u0001\u0010\u0015\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\u0004H\u0007J\u001a\u0010\u0016\u001a\u00020\u00102\b\b\u0001\u0010\u0015\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\u0004H\u0007J\u001c\u0010\u0017\u001a\u00020\u00102\b\b\u0001\u0010\u0011\u001a\u00020\t2\b\b\u0001\u0010\u0018\u001a\u00020\u0004H\u0007J\u0010\u0010\u0019\u001a\u00020\u00102\u0006\u0010\n\u001a\u00020\u000bH\u0007J\b\u0010\u001a\u001a\u00020\u001bH\u0007\u00a8\u0006\u001c"}, d2 = {"Lcom/bukuwarung/network/di/NetworkModule;", "", "()V", "provideBaseUrl", "", "provideContext", "Landroid/content/Context;", "context", "provideOkHttpClient", "Lokhttp3/OkHttpClient;", "appProvider", "Lcom/bukuwarung/network/utils/AppProvider;", "provideOkHttpClientForLocation", "provideOkHttpClientForPin", "providePostmanUrl", "provideRetrofit", "Lretrofit2/Retrofit;", "okHttpClient", "BASE_URL", "provideRetrofitAccounting", "provideRetrofitForLocation", "okhttpClient", "provideRetrofitForPin", "provideRetrofitPostman", "postmanUrl", "provideTmsRetrofit", "providesGsonConverterFactory", "Lretrofit2/converter/gson/GsonConverterFactory;", "network_stgDebug"})
@dagger.Module
public final class NetworkModule {
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.network.di.NetworkModule INSTANCE = null;
    
    private NetworkModule() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @dagger.Provides
    public final retrofit2.converter.gson.GsonConverterFactory providesGsonConverterFactory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @dagger.Provides
    public final java.lang.String provideBaseUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "postman")
    @dagger.Provides
    public final java.lang.String providePostmanUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @dagger.Provides
    public final android.content.Context provideContext(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "normal")
    @dagger.Provides
    @javax.inject.Singleton
    public final okhttp3.OkHttpClient provideOkHttpClient(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.utils.AppProvider appProvider) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "payment_pin")
    @dagger.Provides
    @javax.inject.Singleton
    public final okhttp3.OkHttpClient provideOkHttpClientForPin(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.utils.AppProvider appProvider) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "location")
    @dagger.Provides
    @javax.inject.Singleton
    public final okhttp3.OkHttpClient provideOkHttpClientForLocation(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.utils.AppProvider appProvider) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "normal")
    @dagger.Provides
    @javax.inject.Singleton
    public final retrofit2.Retrofit provideRetrofit(@org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "normal")
    okhttp3.OkHttpClient okHttpClient, @org.jetbrains.annotations.NotNull
    java.lang.String BASE_URL) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "postman")
    @dagger.Provides
    @javax.inject.Singleton
    public final retrofit2.Retrofit provideRetrofitPostman(@org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "normal")
    okhttp3.OkHttpClient okHttpClient, @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "postman")
    java.lang.String postmanUrl) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "location")
    @dagger.Provides
    @javax.inject.Singleton
    public final retrofit2.Retrofit provideRetrofitForLocation(@org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "location")
    okhttp3.OkHttpClient okhttpClient, @org.jetbrains.annotations.NotNull
    java.lang.String BASE_URL) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "payment_pin")
    @dagger.Provides
    @javax.inject.Singleton
    public final retrofit2.Retrofit provideRetrofitForPin(@org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "payment_pin")
    okhttp3.OkHttpClient okhttpClient, @org.jetbrains.annotations.NotNull
    java.lang.String BASE_URL) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "accounting-retrofit")
    @javax.inject.Singleton
    @dagger.Provides
    public final retrofit2.Retrofit provideRetrofitAccounting(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.utils.AppProvider appProvider) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Named(value = "tms-retrofit")
    @javax.inject.Singleton
    @dagger.Provides
    public final retrofit2.Retrofit provideTmsRetrofit(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.utils.AppProvider appProvider) {
        return null;
    }
}