package com.bukuwarung.network.refreshtoken;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\u0018\u0000 \r2\u00020\u0001:\u0001\rB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\b2\u0006\u0010\t\u001a\u00020\nH\u0016J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\t\u001a\u00020\nH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/bukuwarung/network/refreshtoken/TokenAuthenticator;", "Lokhttp3/Authenticator;", "appProvider", "Lcom/bukuwarung/network/utils/AppProvider;", "(Lcom/bukuwarung/network/utils/AppProvider;)V", "authenticate", "Lokhttp3/Request;", "route", "Lokhttp3/Route;", "response", "Lokhttp3/Response;", "responseCount", "", "Companion", "network_stgDebug"})
public final class TokenAuthenticator implements okhttp3.Authenticator {
    private final com.bukuwarung.network.utils.AppProvider appProvider = null;
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.network.refreshtoken.TokenAuthenticator.Companion Companion = null;
    private static final int BAD_REQUEST_STATUS_CODE = 400;
    private static final int FORBIDDEN_STATUS_CODE = 403;
    
    public TokenAuthenticator(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.utils.AppProvider appProvider) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public okhttp3.Request authenticate(@org.jetbrains.annotations.Nullable
    okhttp3.Route route, @org.jetbrains.annotations.NotNull
    okhttp3.Response response) {
        return null;
    }
    
    private final int responseCount(okhttp3.Response response) {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/bukuwarung/network/refreshtoken/TokenAuthenticator$Companion;", "", "()V", "BAD_REQUEST_STATUS_CODE", "", "FORBIDDEN_STATUS_CODE", "network_stgDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}