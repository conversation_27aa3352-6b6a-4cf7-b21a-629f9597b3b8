package com.bukuwarung.network.refreshtoken;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\f\u001a\u00020\tJ\b\u0010\r\u001a\u0004\u0018\u00010\u0004J\b\u0010\u000e\u001a\u0004\u0018\u00010\u0004J\u00ea\u0001\u0010\u000f\u001a\u00020\t26\u0010\u0005\u001a2\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0012\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0013\u0012\u0004\u0012\u00020\u00040\u000626\u0010\b\u001a2\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0012\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0014\u0012\u0004\u0012\u00020\t0\u00062:\u0010\u0007\u001a6\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0012\u0012\u0015\u0012\u0013\u0018\u00010\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0013\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u000626\u0010\n\u001a2\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0012\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0010\u0012\b\b\u0011\u0012\u0004\b\b(\u0014\u0012\u0004\u0012\u00020\t0\u0006J\u0016\u0010\u0015\u001a\u00020\t2\u0006\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R \u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R$\u0010\u0007\u001a\u0018\u0012\u0004\u0012\u00020\u0004\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R \u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\t0\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R \u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\t0\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/bukuwarung/network/refreshtoken/TokenManager;", "", "()V", "accessToken", "", "getEncryptedString", "Lkotlin/Function2;", "getPrefString", "putEncryptedString", "", "putPrefString", "refreshToken", "clear", "getAccessToken", "getRefreshToken", "init", "Lkotlin/ParameterName;", "name", "key", "default", "value", "updateTokens", "newAccess", "newRefresh", "network_stgDebug"})
public final class TokenManager {
    @org.jetbrains.annotations.NotNull
    public static final com.bukuwarung.network.refreshtoken.TokenManager INSTANCE = null;
    @kotlin.jvm.Volatile
    private static volatile java.lang.String accessToken;
    @kotlin.jvm.Volatile
    private static volatile java.lang.String refreshToken;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getEncryptedString;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putEncryptedString;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getPrefString;
    private static kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putPrefString;
    
    private TokenManager() {
        super();
    }
    
    public final void init(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getEncryptedString, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putEncryptedString, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, java.lang.String> getPrefString, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> putPrefString) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAccessToken() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getRefreshToken() {
        return null;
    }
    
    public final void updateTokens(@org.jetbrains.annotations.NotNull
    java.lang.String newAccess, @org.jetbrains.annotations.NotNull
    java.lang.String newRefresh) {
    }
    
    public final void clear() {
    }
}