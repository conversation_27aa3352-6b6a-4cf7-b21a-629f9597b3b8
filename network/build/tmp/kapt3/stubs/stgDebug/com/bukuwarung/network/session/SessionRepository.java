package com.bukuwarung.network.session;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\'\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\r"}, d2 = {"Lcom/bukuwarung/network/session/SessionRepository;", "Lcom/bukuwarung/network/session/SessionRemoteRepository;", "remoteDataSource", "Lcom/bukuwarung/network/session/SessionRemoteDataSource;", "(Lcom/bukuwarung/network/session/SessionRemoteDataSource;)V", "createNewSession", "Lretrofit2/Response;", "Lcom/bukuwarung/network/model/response/SessionResponse;", "sessionId", "", "sessionRequest", "Lcom/bukuwarung/network/model/request/SessionRequest;", "(Ljava/lang/String;Lcom/bukuwarung/network/model/request/SessionRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "network_stgDebug"})
public final class SessionRepository implements com.bukuwarung.network.session.SessionRemoteRepository {
    private final com.bukuwarung.network.session.SessionRemoteDataSource remoteDataSource = null;
    
    @javax.inject.Inject
    public SessionRepository(@org.jetbrains.annotations.NotNull
    com.bukuwarung.network.session.SessionRemoteDataSource remoteDataSource) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public java.lang.Object createNewSession(@org.jetbrains.annotations.NotNull
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull
    com.bukuwarung.network.model.request.SessionRequest sessionRequest, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.bukuwarung.network.model.response.SessionResponse>> continuation) {
        return null;
    }
}