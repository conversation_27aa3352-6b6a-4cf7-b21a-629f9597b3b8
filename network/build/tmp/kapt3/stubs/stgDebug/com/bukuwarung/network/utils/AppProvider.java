package com.bukuwarung.network.utils;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J&\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u0010\b\u0002\u0010\u0006\u001a\n\u0018\u00010\u0007j\u0004\u0018\u0001`\bH&J\b\u0010\t\u001a\u00020\u0003H&J\b\u0010\n\u001a\u00020\u0005H&J\b\u0010\u000b\u001a\u00020\u0005H&J\b\u0010\f\u001a\u00020\u0005H&J\b\u0010\r\u001a\u00020\u0005H&J\b\u0010\u000e\u001a\u00020\u0005H&J\b\u0010\u000f\u001a\u00020\u0005H&J\b\u0010\u0010\u001a\u00020\u0005H&J\b\u0010\u0011\u001a\u00020\u0005H&J\b\u0010\u0012\u001a\u00020\u0005H&J\b\u0010\u0013\u001a\u00020\u0014H&J\b\u0010\u0015\u001a\u00020\u0014H&\u00a8\u0006\u0016"}, d2 = {"Lcom/bukuwarung/network/utils/AppProvider;", "", "bLog", "", "msg", "", "ex", "Ljava/lang/Exception;", "Lkotlin/Exception;", "forceLogout", "getAppVersionCode", "getAppVersionName", "getAuthToken", "getBureauEventId", "getClientId", "getClientSecret", "getDeviceBrand", "getDeviceModel", "getUserId", "isConnected", "", "testingMock", "network_stgDebug"})
public abstract interface AppProvider {
    
    public abstract boolean isConnected();
    
    public abstract boolean testingMock();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getDeviceBrand();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getDeviceModel();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getAppVersionName();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getAppVersionCode();
    
    public abstract void bLog(@org.jetbrains.annotations.Nullable
    java.lang.String msg, @org.jetbrains.annotations.Nullable
    java.lang.Exception ex);
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getAuthToken();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getUserId();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getClientId();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getClientSecret();
    
    @org.jetbrains.annotations.NotNull
    public abstract java.lang.String getBureauEventId();
    
    public abstract void forceLogout();
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 3)
    public final class DefaultImpls {
    }
}