package com.bukuwarung.network.utils;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 2, d1 = {"\u00004\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001aE\u0010\u0000\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\b\b\u0000\u0010\u0002*\u00020\u00032\"\u0010\u0004\u001a\u001e\b\u0001\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020\u00070\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u0005H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\b\u001a2\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\f2\u0016\u0010\r\u001a\u0012\u0012\b\u0012\u00060\u000ej\u0002`\u000f\u0012\u0004\u0012\u00020\n0\u0005H\u0086\b\u00f8\u0001\u0001\u0082\u0002\u000b\n\u0002\b\u0019\n\u0005\b\u009920\u0001\u00a8\u0006\u0010"}, d2 = {"safeApiCall", "Lcom/bukuwarung/network/utils/ResourceState;", "T", "", "call", "Lkotlin/Function1;", "Lkotlin/coroutines/Continuation;", "Lretrofit2/Response;", "(Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "tryCatch", "", "condition", "Lkotlin/Function0;", "exception", "Ljava/lang/Exception;", "Lkotlin/Exception;", "network_stgDebug"})
public final class NetworkUtilsKt {
    
    public static final void tryCatch(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> condition, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> exception) {
    }
    
    @org.jetbrains.annotations.Nullable
    public static final <T extends java.lang.Object>java.lang.Object safeApiCall(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super retrofit2.Response<T>>, ? extends java.lang.Object> call, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.bukuwarung.network.utils.ResourceState<? extends T>> continuation) {
        return null;
    }
}