package com.bukuwarung.network.utils;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u00012\u00020\u0002:\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0003\u0082\u0001\u0003\u0007\b\t\u00a8\u0006\n"}, d2 = {"Lcom/bukuwarung/network/utils/ResourceState;", "T", "", "()V", "Failure", "Loading", "Success", "Lcom/bukuwarung/network/utils/ResourceState$Failure;", "Lcom/bukuwarung/network/utils/ResourceState$Loading;", "Lcom/bukuwarung/network/utils/ResourceState$Success;", "network_stgDebug"})
public abstract class ResourceState<T extends java.lang.Object> {
    
    private ResourceState() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000*\u0006\b\u0001\u0010\u0001 \u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\u0005\u00a2\u0006\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/bukuwarung/network/utils/ResourceState$Loading;", "T", "Lcom/bukuwarung/network/utils/ResourceState;", "()V", "network_stgDebug"})
    public static final class Loading<T extends java.lang.Object> extends com.bukuwarung.network.utils.ResourceState<T> {
        
        public Loading() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u0000*\u0006\b\u0001\u0010\u0001 \u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00028\u0001\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\b\u001a\u00028\u0001H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00010\u00002\b\b\u0002\u0010\u0003\u001a\u00028\u0001H\u00c6\u0001\u00a2\u0006\u0002\u0010\nJ\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001R\u0013\u0010\u0003\u001a\u00028\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0013"}, d2 = {"Lcom/bukuwarung/network/utils/ResourceState$Success;", "T", "Lcom/bukuwarung/network/utils/ResourceState;", "data", "(Ljava/lang/Object;)V", "getData", "()Ljava/lang/Object;", "Ljava/lang/Object;", "component1", "copy", "(Ljava/lang/Object;)Lcom/bukuwarung/network/utils/ResourceState$Success;", "equals", "", "other", "", "hashCode", "", "toString", "", "network_stgDebug"})
    public static final class Success<T extends java.lang.Object> extends com.bukuwarung.network.utils.ResourceState<T> {
        private final T data = null;
        
        @org.jetbrains.annotations.NotNull
        public final com.bukuwarung.network.utils.ResourceState.Success<T> copy(T data) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public Success(T data) {
            super();
        }
        
        public final T component1() {
            return null;
        }
        
        public final T getData() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\t\b\u0086\b\u0018\u0000*\u0006\b\u0001\u0010\u0001 \u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B5\u0012\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0002\u0010\u000bJ\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\nH\u00c6\u0003JD\u0010\u0019\u001a\b\u0012\u0004\u0012\u00028\u00010\u00002\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001aJ\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u00d6\u0003J\u001c\u0010\u001f\u001a\u0004\u0018\u0001H \"\n\b\u0002\u0010 \u0018\u0001*\u00020\u001eH\u0086\b\u00a2\u0006\u0002\u0010!J\b\u0010\"\u001a\u0004\u0018\u00010\bJ\b\u0010#\u001a\u0004\u0018\u00010\bJ\t\u0010$\u001a\u00020\u0006H\u00d6\u0001J\u0006\u0010%\u001a\u00020\u001cJ\t\u0010&\u001a\u00020\bH\u00d6\u0001R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u0003\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\'"}, d2 = {"Lcom/bukuwarung/network/utils/ResourceState$Failure;", "T", "Lcom/bukuwarung/network/utils/ResourceState;", "throwable", "", "responseCode", "", "message", "", "errorJsonObj", "Lorg/json/JSONObject;", "(Ljava/lang/Throwable;Ljava/lang/Integer;Ljava/lang/String;Lorg/json/JSONObject;)V", "getErrorJsonObj", "()Lorg/json/JSONObject;", "getMessage", "()Ljava/lang/String;", "getResponseCode", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getThrowable", "()Ljava/lang/Throwable;", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/Throwable;Ljava/lang/Integer;Ljava/lang/String;Lorg/json/JSONObject;)Lcom/bukuwarung/network/utils/ResourceState$Failure;", "equals", "", "other", "", "getErrorBody", "E", "()Ljava/lang/Object;", "getErrorCode", "getErrorMessage", "hashCode", "isResourceNotFound", "toString", "network_stgDebug"})
    public static final class Failure<T extends java.lang.Object> extends com.bukuwarung.network.utils.ResourceState<T> {
        @org.jetbrains.annotations.Nullable
        private final java.lang.Throwable throwable = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.Integer responseCode = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String message = null;
        @org.jetbrains.annotations.Nullable
        private final org.json.JSONObject errorJsonObj = null;
        
        @org.jetbrains.annotations.NotNull
        public final com.bukuwarung.network.utils.ResourceState.Failure<T> copy(@org.jetbrains.annotations.Nullable
        java.lang.Throwable throwable, @org.jetbrains.annotations.Nullable
        java.lang.Integer responseCode, @org.jetbrains.annotations.Nullable
        java.lang.String message, @org.jetbrains.annotations.Nullable
        org.json.JSONObject errorJsonObj) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public Failure() {
            super();
        }
        
        public Failure(@org.jetbrains.annotations.Nullable
        java.lang.Throwable throwable, @org.jetbrains.annotations.Nullable
        java.lang.Integer responseCode, @org.jetbrains.annotations.Nullable
        java.lang.String message, @org.jetbrains.annotations.Nullable
        org.json.JSONObject errorJsonObj) {
            super();
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.Throwable component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.Throwable getThrowable() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.Integer component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.Integer getResponseCode() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final org.json.JSONObject component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final org.json.JSONObject getErrorJsonObj() {
            return null;
        }
        
        /**
         * Based on [safeApiCall] & [safeApiCallFlow]
         * If resource is not exist, response code will be 404, has message and throwable would be null
         * This might happen when BE hasn't handled empty / nonexistent data
         */
        public final boolean isResourceNotFound() {
            return false;
        }
        
        /**
         * Check error response body with respective BE team
         * in case they still not use this format
         */
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getErrorCode() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getErrorMessage() {
            return null;
        }
    }
}