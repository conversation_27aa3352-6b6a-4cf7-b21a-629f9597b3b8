package com.bukuwarung.network.di

import android.content.Context
import android.util.Log
import com.bukuwarung.network.BuildConfig
import com.bukuwarung.network.NetworkConst
import com.bukuwarung.network.interceptors.CrashlyticsLoggingInterceptor
import com.bukuwarung.network.interceptors.HeadersInterceptor
import com.bukuwarung.network.interceptors.NetworkConnectionInterceptor
import com.bukuwarung.network.interceptors.SecuredInterceptor
import com.bukuwarung.network.refreshtoken.TokenAuthenticator
import com.bukuwarung.network.utils.AppProvider
import com.moczul.ok2curl.CurlInterceptor
import com.moczul.ok2curl.logger.Logger
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    fun providesGsonConverterFactory(): GsonConverterFactory {
        return GsonConverterFactory.create()
    }

    @Provides
    fun provideBaseUrl() = BuildConfig.API_BASE_URL

    @Provides
    @Named("postman")
    fun providePostmanUrl() = NetworkConst.POSTMAN_URL

    @Provides
    fun provideContext(@ApplicationContext context: Context) = context

    // adding Timeout for testing.
    @Singleton
    @Provides
    @Named("normal")
    fun provideOkHttpClient(appProvider: AppProvider) =
        OkHttpClient.Builder()
            .connectTimeout(120, TimeUnit.SECONDS)
            .readTimeout(120, TimeUnit.SECONDS)
            .callTimeout(120, TimeUnit.SECONDS)
            .writeTimeout(120, TimeUnit.SECONDS)
            .addInterceptor(NetworkConnectionInterceptor(appProvider))
            .addInterceptor(HeadersInterceptor(appProvider))
            .addInterceptor(SecuredInterceptor())
            .authenticator(TokenAuthenticator(appProvider))
            .retryOnConnectionFailure(false)
            .addInterceptor(CurlInterceptor(object : Logger {
                override fun log(message: String) {
                    Log.d("Ok2Curl", message)
                }
            }))
            .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
            .addInterceptor(CrashlyticsLoggingInterceptor(appProvider))
            .build()

    @Singleton
    @Provides
    @Named("payment_pin")
    fun provideOkHttpClientForPin(appProvider: AppProvider): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(NetworkConnectionInterceptor(appProvider))
            .addInterceptor(HeadersInterceptor(appProvider))
            .addInterceptor(SecuredInterceptor())
            .authenticator(TokenAuthenticator(appProvider))
            .addInterceptor(CurlInterceptor(object : Logger {
                override fun log(message: String) {
                    Log.d("Ok2Curl", message)
                }
            }))
            .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
            .addInterceptor(CrashlyticsLoggingInterceptor(appProvider))
            .build()
    }

    @Singleton
    @Provides
    @Named("location")
    fun provideOkHttpClientForLocation(appProvider: AppProvider) =
        OkHttpClient.Builder()
            .addInterceptor(NetworkConnectionInterceptor(appProvider))
            .addInterceptor(HeadersInterceptor(appProvider))
            .addInterceptor(SecuredInterceptor())
            .addInterceptor(CurlInterceptor(object : Logger {
                override fun log(message: String) {
                    Log.d("Ok2Curl", message)
                }
            }))
            .authenticator(TokenAuthenticator(appProvider))
            .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
            .addInterceptor(CrashlyticsLoggingInterceptor(appProvider))
            .build()

    @Singleton
    @Provides
    @Named("normal")
    fun provideRetrofit(@Named("normal") okHttpClient: OkHttpClient, BASE_URL: String): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .build()

    @Singleton
    @Provides
    @Named("postman")
    fun provideRetrofitPostman(
        @Named("normal") okHttpClient: OkHttpClient,
        @Named("postman") postmanUrl: String
    ): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(postmanUrl)
            .client(okHttpClient)
            .build()

    @Singleton
    @Provides
    @Named("location")
    fun provideRetrofitForLocation(@Named("location") okhttpClient: OkHttpClient, BASE_URL: String): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(BASE_URL)
            .client(okhttpClient)
            .build()

    @Singleton
    @Provides
    @Named("payment_pin")
    fun provideRetrofitForPin(@Named("payment_pin") okhttpClient: OkHttpClient, BASE_URL: String): Retrofit =
        Retrofit.Builder()
            .addConverterFactory(providesGsonConverterFactory())
            .baseUrl(BASE_URL)
            .client(okhttpClient)
            .build()

    @Provides
    @Singleton
    @Named("accounting-retrofit")
    fun provideRetrofitAccounting(appProvider:AppProvider): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.ACCOUNTING_API_BASE_URL +"/")
            .client(OkHttpClient.Builder()
                .addInterceptor(NetworkConnectionInterceptor(appProvider))
                .addInterceptor(HeadersInterceptor(appProvider))
                .addInterceptor(SecuredInterceptor())
                .addInterceptor(CurlInterceptor(object : Logger {
                    override fun log(message: String) {
                        Log.d("Ok2Curl", message)
                    }
                }))
                .authenticator(TokenAuthenticator(appProvider))
                .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
                .addInterceptor(CrashlyticsLoggingInterceptor(appProvider))
                .build())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    @Provides
    @Singleton
    @Named("tms-retrofit")
    fun provideTmsRetrofit(appProvider: AppProvider): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.ACCOUNTING_API_BASE_URL+"/")
            .client(OkHttpClient.Builder()
                .addInterceptor(NetworkConnectionInterceptor(appProvider))
                .addInterceptor(HeadersInterceptor(appProvider))
                .addInterceptor(SecuredInterceptor())
                .authenticator(TokenAuthenticator(appProvider))
                .addInterceptor(CurlInterceptor(object : Logger {
                    override fun log(message: String) {
                        Log.d("Ok2Curl", message)
                    }
                }))
                .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
                .addInterceptor(CrashlyticsLoggingInterceptor(appProvider))
                .build())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
}