package com.bukuwarung.network.di

import com.bukuwarung.network.session.SessionRemoteDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class SessionApiModule {

    @Singleton
    @Provides
    fun provideSessionDataSource(@Named("normal") retrofit: Retrofit): SessionRemoteDataSource =
        retrofit.create(SessionRemoteDataSource::class.java)
}