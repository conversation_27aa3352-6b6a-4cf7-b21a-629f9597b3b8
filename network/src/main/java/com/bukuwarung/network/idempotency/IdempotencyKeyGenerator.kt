package com.bukuwarung.network.idempotency

import android.content.Context
import android.provider.Settings
import com.bukuwarung.network.utils.AppProvider
import okhttp3.Request
import okio.Buffer
import java.security.MessageDigest
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Generates idempotency keys for API requests to prevent duplicate operations.
 * 
 * The key is generated based on:
 * - Request endpoint (method + path)
 * - Request body content
 * - User context (user ID, device ID)
 * - Timestamp (for time-sensitive operations)
 */
@Singleton
class IdempotencyKeyGenerator @Inject constructor(
    private val appProvider: AppProvider
) {
    
    companion object {
        private const val IDEMPOTENCY_HEADER = "Idempotency-Key"
        private const val IDEMPOTENCY_TIMESTAMP_HEADER = "X-Idempotency-Timestamp"
        
        // Endpoints that require idempotency (financial operations)
        private val IDEMPOTENT_ENDPOINTS = setOf(
            "/api/payments/*/disbursements/*/",
            "/edc-adapter/transfer/posting/*",
            "/edc-adapter/cash-withdrawal/posting/*",
            "/finpro/api/saldo/topup",
            "/api/payments/*/bank_accounts_v2/customer_accounts/add/*"
        )
        
        // Time window for idempotency (5 minutes)
        private const val IDEMPOTENCY_WINDOW_MS = 5 * 60 * 1000L
    }
    
    /**
     * Generates an idempotency key for the given request
     */
    fun generateIdempotencyKey(request: Request): String? {
        if (!shouldApplyIdempotency(request)) {
            return null
        }
        
        val components = mutableListOf<String>()
        
        // 1. Request signature (method + normalized path)
        components.add(getRequestSignature(request))
        
        // 2. Request body hash (for POST/PUT requests)
        getRequestBodyHash(request)?.let { components.add(it) }
        
        // 3. User context
        components.add(getUserContext())
        
        // 4. Device context
        components.add(getDeviceContext())
        
        // 5. Time window (to ensure freshness)
        components.add(getTimeWindow().toString())
        
        // Generate final hash
        return generateHash(components.joinToString("|"))
    }
    
    /**
     * Checks if the request should have idempotency applied
     */
    private fun shouldApplyIdempotency(request: Request): Boolean {
        // Only apply to POST/PUT requests
        if (request.method !in setOf("POST", "PUT")) {
            return false
        }
        
        val path = request.url.encodedPath
        return IDEMPOTENT_ENDPOINTS.any { pattern ->
            matchesPattern(path, pattern)
        }
    }
    
    /**
     * Creates a signature for the request endpoint
     */
    private fun getRequestSignature(request: Request): String {
        val method = request.method
        val path = normalizePathForIdempotency(request.url.encodedPath)
        return "$method:$path"
    }
    
    /**
     * Normalizes the path by replacing dynamic segments with placeholders
     */
    private fun normalizePathForIdempotency(path: String): String {
        return path
            .replace(Regex("/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"), "/{uuid}")
            .replace(Regex("/\\d+"), "/{id}")
            .replace(Regex("/[a-zA-Z0-9_-]{20,}"), "/{long_id}")
    }
    
    /**
     * Generates hash of request body for POST/PUT requests
     */
    private fun getRequestBodyHash(request: Request): String? {
        val requestBody = request.body ?: return null
        
        return try {
            val buffer = Buffer()
            requestBody.writeTo(buffer)
            val bodyString = buffer.readUtf8()
            
            // Remove timestamp fields that might vary
            val normalizedBody = normalizeRequestBody(bodyString)
            generateHash(normalizedBody)
        } catch (e: Exception) {
            // If we can't read the body, use a placeholder
            "body_unreadable"
        }
    }
    
    /**
     * Normalizes request body by removing or standardizing time-sensitive fields
     */
    private fun normalizeRequestBody(body: String): String {
        return body
            // Remove common timestamp fields
            .replace(Regex("\"timestamp\"\\s*:\\s*\\d+"), "\"timestamp\":0")
            .replace(Regex("\"created_at\"\\s*:\\s*\"[^\"]+\""), "\"created_at\":\"\"")
            .replace(Regex("\"request_time\"\\s*:\\s*\"[^\"]+\""), "\"request_time\":\"\"")
            // Remove request IDs that might be auto-generated
            .replace(Regex("\"request_id\"\\s*:\\s*\"[^\"]+\""), "\"request_id\":\"\"")
    }
    
    /**
     * Gets user context for idempotency
     */
    private fun getUserContext(): String {
        val userId = appProvider.getUserId()
        val clientId = appProvider.getClientId()
        return "user:$userId:client:$clientId"
    }
    
    /**
     * Gets device context for idempotency
     */
    private fun getDeviceContext(): String {
        val deviceId = appProvider.getDeviceId()
        val serialNumber = appProvider.getSerialNumber()
        return "device:$deviceId:serial:$serialNumber"
    }
    
    /**
     * Gets current time window for idempotency
     * This ensures that the same request made after the window expires gets a new key
     */
    private fun getTimeWindow(): Long {
        return System.currentTimeMillis() / IDEMPOTENCY_WINDOW_MS
    }
    
    /**
     * Generates SHA-256 hash of the input string
     */
    private fun generateHash(input: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(input.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * Checks if a path matches a pattern with wildcards
     */
    private fun matchesPattern(path: String, pattern: String): Boolean {
        val regex = pattern
            .replace("*", "[^/]*")
            .replace("**", ".*")
        return path.matches(Regex(regex))
    }
    
    /**
     * Gets the current timestamp for the idempotency timestamp header
     */
    fun getCurrentTimestamp(): String {
        return System.currentTimeMillis().toString()
    }
}
