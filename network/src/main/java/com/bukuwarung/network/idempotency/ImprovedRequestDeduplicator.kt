package com.bukuwarung.network.idempotency

import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Protocol
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Improved Request Deduplicator with better error handling
 */
@Singleton
class ImprovedRequestDeduplicator @Inject constructor() {
    
    companion object {
        // How long to cache successful responses (30 seconds)
        private const val SUCCESS_CACHE_DURATION_MS = 30_000L
        
        // How long to cache error responses (5 seconds - shorter than success)
        private const val ERROR_CACHE_DURATION_MS = 5_000L
        
        // How long to keep in-flight request tracking (2 minutes - shorter than before)
        private const val IN_FLIGHT_DURATION_MS = 2 * 60_000L
        
        // Cleanup interval (30 seconds - more frequent)
        private const val CLEANUP_INTERVAL_MS = 30_000L
    }
    
    // Track requests currently in flight
    private val inFlightRequests = ConcurrentHashMap<String, Long>()
    
    // Cache successful responses
    private val successCache = ConcurrentHashMap<String, CachedResponse>()
    
    // Cache error responses (shorter duration)
    private val errorCache = ConcurrentHashMap<String, CachedResponse>()
    
    // Executor for cleanup tasks
    private val cleanupExecutor: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor { r ->
        Thread(r, "IdempotencyCleanup").apply { isDaemon = true }
    }
    
    init {
        // Schedule periodic cleanup
        cleanupExecutor.scheduleAtFixedRate(
            ::cleanup,
            CLEANUP_INTERVAL_MS,
            CLEANUP_INTERVAL_MS,
            TimeUnit.MILLISECONDS
        )
    }
    
    /**
     * Data class to hold cached response with timestamp
     */
    private data class CachedResponse(
        val response: Response,
        val timestamp: Long,
        val isError: Boolean = false
    )
    
    /**
     * Checks if a request with the same idempotency key is already in flight or cached
     */
    fun checkForDuplicate(idempotencyKey: String): Response? {
        val now = System.currentTimeMillis()
        
        // First check success cache
        successCache[idempotencyKey]?.let { cached ->
            if (now - cached.timestamp < SUCCESS_CACHE_DURATION_MS) {
                return createReplayedResponse(cached.response, "success_cache")
            } else {
                successCache.remove(idempotencyKey)
            }
        }
        
        // Then check error cache (shorter duration)
        errorCache[idempotencyKey]?.let { cached ->
            if (now - cached.timestamp < ERROR_CACHE_DURATION_MS) {
                return createReplayedResponse(cached.response, "error_cache")
            } else {
                errorCache.remove(idempotencyKey)
            }
        }
        
        // Finally check if request is currently in flight
        inFlightRequests[idempotencyKey]?.let { timestamp ->
            if (now - timestamp < IN_FLIGHT_DURATION_MS) {
                return createConflictResponse("Request is currently being processed")
            } else {
                // Remove expired in-flight entry
                inFlightRequests.remove(idempotencyKey)
            }
        }
        
        return null
    }
    
    /**
     * Registers a request as in-flight
     */
    fun registerRequest(idempotencyKey: String) {
        inFlightRequests[idempotencyKey] = System.currentTimeMillis()
    }
    
    /**
     * Removes a request from in-flight tracking only
     */
    fun removeFromInFlight(idempotencyKey: String) {
        inFlightRequests.remove(idempotencyKey)
    }
    
    /**
     * Completely removes a request (from in-flight and all caches)
     */
    fun removeRequest(idempotencyKey: String) {
        inFlightRequests.remove(idempotencyKey)
        successCache.remove(idempotencyKey)
        errorCache.remove(idempotencyKey)
    }
    
    /**
     * Caches a response (success or error)
     */
    fun cacheResponse(idempotencyKey: String, response: Response) {
        val cachedResponse = CachedResponse(
            response = response,
            timestamp = System.currentTimeMillis(),
            isError = !response.isSuccessful
        )
        
        if (response.isSuccessful) {
            successCache[idempotencyKey] = cachedResponse
        } else {
            // Only cache certain error types
            when (response.code) {
                400, 401, 403, 404, 422 -> {
                    // Business logic errors - cache them
                    errorCache[idempotencyKey] = cachedResponse
                }
                // Don't cache retryable errors (408, 429, 5xx)
            }
        }
    }
    
    /**
     * Creates a replayed response from cache
     */
    private fun createReplayedResponse(originalResponse: Response, cacheType: String): Response {
        return originalResponse.newBuilder()
            .addHeader("X-Idempotency-Replayed", "true")
            .addHeader("X-Cache-Hit", cacheType)
            .addHeader("X-Cache-Timestamp", System.currentTimeMillis().toString())
            .build()
    }
    
    /**
     * Creates a 409 Conflict response for duplicate in-flight requests
     */
    private fun createConflictResponse(message: String): Response {
        val responseBody = """
            {
                "error": "duplicate_request",
                "message": "$message",
                "code": "DUPLICATE_REQUEST_IN_FLIGHT",
                "timestamp": ${System.currentTimeMillis()}
            }
        """.trimIndent()
        
        return Response.Builder()
            .code(409)
            .message("Conflict - Duplicate Request")
            .protocol(Protocol.HTTP_1_1)
            .request(Request.Builder().url("http://localhost/").build())
            .addHeader("Content-Type", "application/json")
            .addHeader("X-Idempotency-Conflict", "true")
            .addHeader("X-Conflict-Reason", "in_flight")
            .body(responseBody.toResponseBody("application/json".toMediaType()))
            .build()
    }
    
    /**
     * Cleans up expired entries
     */
    private fun cleanup() {
        val now = System.currentTimeMillis()
        
        // Clean up expired in-flight requests
        val expiredInFlight = inFlightRequests.entries.filter { (_, timestamp) ->
            now - timestamp > IN_FLIGHT_DURATION_MS
        }
        expiredInFlight.forEach { (key, _) ->
            inFlightRequests.remove(key)
        }
        
        // Clean up expired success cache
        val expiredSuccess = successCache.entries.filter { (_, cached) ->
            now - cached.timestamp > SUCCESS_CACHE_DURATION_MS
        }
        expiredSuccess.forEach { (key, _) ->
            successCache.remove(key)
        }
        
        // Clean up expired error cache
        val expiredError = errorCache.entries.filter { (_, cached) ->
            now - cached.timestamp > ERROR_CACHE_DURATION_MS
        }
        expiredError.forEach { (key, _) ->
            errorCache.remove(key)
        }
        
        if (expiredInFlight.isNotEmpty() || expiredSuccess.isNotEmpty() || expiredError.isNotEmpty()) {
            println("[IDEMPOTENCY] Cleaned up ${expiredInFlight.size} in-flight, ${expiredSuccess.size} success cache, ${expiredError.size} error cache")
        }
    }
    
    /**
     * Gets current statistics for monitoring
     */
    fun getStats(): Map<String, Any> {
        return mapOf(
            "in_flight_requests" to inFlightRequests.size,
            "success_cache_size" to successCache.size,
            "error_cache_size" to errorCache.size,
            "total_tracked" to (inFlightRequests.size + successCache.size + errorCache.size)
        )
    }
    
    /**
     * Clears all cached data (useful for testing)
     */
    fun clear() {
        inFlightRequests.clear()
        successCache.clear()
        errorCache.clear()
    }
}
