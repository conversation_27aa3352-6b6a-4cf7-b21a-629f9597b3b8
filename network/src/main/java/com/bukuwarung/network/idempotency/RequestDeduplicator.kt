package com.bukuwarung.network.idempotency

import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Protocol
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages request deduplication by tracking in-flight requests and caching responses
 */
@Singleton
class RequestDeduplicator @Inject constructor() {
    
    companion object {
        // How long to cache successful responses (30 seconds)
        private const val RESPONSE_CACHE_DURATION_MS = 30_000L
        
        // How long to keep in-flight request tracking (5 minutes)
        private const val IN_FLIGHT_DURATION_MS = 5 * 60_000L
        
        // Cleanup interval (1 minute)
        private const val CLEANUP_INTERVAL_MS = 60_000L
    }
    
    // Track requests currently in flight
    private val inFlightRequests = ConcurrentHashMap<String, Long>()
    
    // Cache successful responses temporarily
    private val responseCache = ConcurrentHashMap<String, CachedResponse>()
    
    // Executor for cleanup tasks
    private val cleanupExecutor: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor { r ->
        Thread(r, "IdempotencyCleanup").apply { isDaemon = true }
    }
    
    init {
        // Schedule periodic cleanup
        cleanupExecutor.scheduleAtFixedRate(
            ::cleanup,
            CLEANUP_INTERVAL_MS,
            CLEANUP_INTERVAL_MS,
            TimeUnit.MILLISECONDS
        )
    }
    
    /**
     * Data class to hold cached response with timestamp
     */
    private data class CachedResponse(
        val response: Response,
        val timestamp: Long
    )
    
    /**
     * Checks if a request with the same idempotency key is already in flight or cached
     */
    fun checkForDuplicate(idempotencyKey: String): Response? {
        val now = System.currentTimeMillis()
        
        // First check if we have a cached response
        responseCache[idempotencyKey]?.let { cached ->
            if (now - cached.timestamp < RESPONSE_CACHE_DURATION_MS) {
                return createDuplicateResponse(cached.response)
            } else {
                // Remove expired cache entry
                responseCache.remove(idempotencyKey)
            }
        }
        
        // Check if request is currently in flight
        inFlightRequests[idempotencyKey]?.let { timestamp ->
            if (now - timestamp < IN_FLIGHT_DURATION_MS) {
                // Return a 409 Conflict response indicating duplicate request
                return createConflictResponse()
            } else {
                // Remove expired in-flight entry
                inFlightRequests.remove(idempotencyKey)
            }
        }
        
        return null
    }
    
    /**
     * Registers a request as in-flight
     */
    fun registerRequest(idempotencyKey: String) {
        inFlightRequests[idempotencyKey] = System.currentTimeMillis()
    }
    
    /**
     * Removes a request from in-flight tracking
     */
    fun removeRequest(idempotencyKey: String) {
        inFlightRequests.remove(idempotencyKey)
    }
    
    /**
     * Caches a successful response
     */
    fun cacheResponse(idempotencyKey: String, response: Response) {
        if (response.isSuccessful) {
            responseCache[idempotencyKey] = CachedResponse(
                response = response,
                timestamp = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * Creates a duplicate response from a cached response
     */
    private fun createDuplicateResponse(originalResponse: Response): Response {
        return originalResponse.newBuilder()
            .addHeader("X-Idempotency-Replayed", "true")
            .addHeader("X-Cache-Hit", "true")
            .build()
    }
    
    /**
     * Creates a 409 Conflict response for duplicate in-flight requests
     */
    private fun createConflictResponse(): Response {
        val responseBody = """
            {
                "error": "duplicate_request",
                "message": "A request with the same parameters is currently being processed",
                "code": "DUPLICATE_REQUEST_IN_FLIGHT"
            }
        """.trimIndent()
        
        return Response.Builder()
            .code(409)
            .message("Conflict - Duplicate Request")
            .protocol(Protocol.HTTP_1_1)
            .request(Request.Builder().url("http://localhost/").build())
            .addHeader("Content-Type", "application/json")
            .addHeader("X-Idempotency-Conflict", "true")
            .body(responseBody.toResponseBody("application/json".toMediaType()))
            .build()
    }
    
    /**
     * Cleans up expired entries
     */
    private fun cleanup() {
        val now = System.currentTimeMillis()
        
        // Clean up expired in-flight requests
        val expiredInFlight = inFlightRequests.entries.filter { (_, timestamp) ->
            now - timestamp > IN_FLIGHT_DURATION_MS
        }
        expiredInFlight.forEach { (key, _) ->
            inFlightRequests.remove(key)
        }
        
        // Clean up expired cached responses
        val expiredCache = responseCache.entries.filter { (_, cached) ->
            now - cached.timestamp > RESPONSE_CACHE_DURATION_MS
        }
        expiredCache.forEach { (key, _) ->
            responseCache.remove(key)
        }
        
        if (expiredInFlight.isNotEmpty() || expiredCache.isNotEmpty()) {
            println("[IDEMPOTENCY] Cleaned up ${expiredInFlight.size} in-flight and ${expiredCache.size} cached entries")
        }
    }
    
    /**
     * Gets current statistics for monitoring
     */
    fun getStats(): Map<String, Int> {
        return mapOf(
            "in_flight_requests" to inFlightRequests.size,
            "cached_responses" to responseCache.size
        )
    }
    
    /**
     * Clears all cached data (useful for testing)
     */
    fun clear() {
        inFlightRequests.clear()
        responseCache.clear()
    }
}
