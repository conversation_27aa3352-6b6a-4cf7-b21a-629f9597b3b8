package com.bukuwarung.network.interceptors

import com.bukuwarung.network.utils.AppProvider
import okhttp3.Interceptor
import okhttp3.Response

class CrashlyticsLoggingInterceptor(val appProvider: AppProvider) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        appProvider.bLog("[NETWORK REQUEST] ${request.method} ${request.url}")
        return try {
            val response = chain.proceed(request)
            val responseInfo = "[NETWORK RESPONSE] ${response.code} ${response.message} for ${request.url}"
            appProvider.bLog(responseInfo)
            response
        } catch (e: Exception) {
            appProvider.bLog("[NETWORK ERROR] ${e.message}", e)
            throw e
        }
    }
}