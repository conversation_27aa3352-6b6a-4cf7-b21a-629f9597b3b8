package com.bukuwarung.network.interceptors

import com.bukuwarung.network.utils.AppProvider
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.util.*

class HeadersInterceptor(val app: AppProvider) : Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val newRequest = chain.request().newBuilder()
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .addHeader("USER-PSEUDO-INSTANCE-ID", "edc-instance")
            .addHeader("X-APP-VERSION-NAME", "3.74.0")
            .addHeader("testing-mock", app.testingMock().toString())
            .addHeader("buku-origin", "bukuwarung-edc")
            .addHeader("X-APP-VERSION-CODE", "5000")
            .addHeader("x-device-brand", app.getDeviceBrand())
            .addHeader("x-device-model", app.getDeviceModel())
            .addHeader("X-TIMEZONE", TimeZone.getDefault().id)
            .addHeader("x-edc-app-version-name", app.getAppVersionName())
            .addHeader("x-edc-app-version-code", app.getAppVersionCode())
            .build()
        return chain.proceed(newRequest)
    }
}