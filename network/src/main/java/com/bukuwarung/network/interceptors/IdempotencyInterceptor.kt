package com.bukuwarung.network.interceptors

import com.bukuwarung.network.idempotency.IdempotencyKeyGenerator
import com.bukuwarung.network.idempotency.RequestDeduplicator
import com.bukuwarung.network.utils.AppProvider
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import javax.inject.Inject

/**
 * Interceptor that adds idempotency headers to requests and prevents duplicate requests
 */
class IdempotencyInterceptor @Inject constructor(
    private val idempotencyKeyGenerator: IdempotencyKeyGenerator,
    private val requestDeduplicator: RequestDeduplicator,
    private val appProvider: AppProvider
) : Interceptor {
    
    companion object {
        private const val IDEMPOTENCY_HEADER = "Idempotency-Key"
        private const val IDEMPOTENCY_TIMESTAMP_HEADER = "X-Idempotency-Timestamp"
        private const val REQUEST_ID_HEADER = "X-Request-ID"
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // Generate idempotency key if applicable
        val idempotencyKey = idempotencyKeyGenerator.generateIdempotencyKey(originalRequest)
        
        if (idempotencyKey != null) {
            // Check if this request is already in flight
            val duplicateResponse = requestDeduplicator.checkForDuplicate(idempotencyKey)
            if (duplicateResponse != null) {
                appProvider.bLog("[IDEMPOTENCY] Duplicate request detected, returning cached response for key: $idempotencyKey")
                return duplicateResponse
            }
            
            // Add idempotency headers
            val requestWithHeaders = originalRequest.newBuilder()
                .addHeader(IDEMPOTENCY_HEADER, idempotencyKey)
                .addHeader(IDEMPOTENCY_TIMESTAMP_HEADER, idempotencyKeyGenerator.getCurrentTimestamp())
                .addHeader(REQUEST_ID_HEADER, generateRequestId())
                .build()
            
            appProvider.bLog("[IDEMPOTENCY] Adding idempotency key: $idempotencyKey for ${originalRequest.method} ${originalRequest.url}")
            
            // Register the request as in-flight
            requestDeduplicator.registerRequest(idempotencyKey)
            
            return try {
                val response = chain.proceed(requestWithHeaders)
                
                // Cache successful responses for a short time
                if (response.isSuccessful) {
                    requestDeduplicator.cacheResponse(idempotencyKey, response)
                }
                
                response
            } catch (e: Exception) {
                // Remove from in-flight requests on error
                requestDeduplicator.removeRequest(idempotencyKey)
                throw e
            } finally {
                // Always remove from in-flight after processing
                requestDeduplicator.removeRequest(idempotencyKey)
            }
        } else {
            // No idempotency needed, proceed normally
            return chain.proceed(originalRequest)
        }
    }
    
    /**
     * Generates a unique request ID for tracking
     */
    private fun generateRequestId(): String {
        return "${System.currentTimeMillis()}-${(1000..9999).random()}"
    }
}
