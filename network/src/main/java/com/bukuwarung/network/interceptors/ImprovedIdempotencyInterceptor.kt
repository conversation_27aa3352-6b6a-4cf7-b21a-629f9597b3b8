package com.bukuwarung.network.interceptors

import com.bukuwarung.network.idempotency.IdempotencyKeyGenerator
import com.bukuwarung.network.idempotency.RequestDeduplicator
import com.bukuwarung.network.utils.AppProvider
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import javax.inject.Inject

/**
 * Improved Idempotency Interceptor with proper error handling
 */
class ImprovedIdempotencyInterceptor @Inject constructor(
    private val idempotencyKeyGenerator: IdempotencyKeyGenerator,
    private val requestDeduplicator: RequestDeduplicator,
    private val appProvider: AppProvider
) : Interceptor {
    
    companion object {
        private const val IDEMPOTENCY_HEADER = "Idempotency-Key"
        private const val IDEMPOTENCY_TIMESTAMP_HEADER = "X-Idempotency-Timestamp"
        private const val REQUEST_ID_HEADER = "X-Request-ID"
        
        // Error codes that should clear the idempotency key (allow retries)
        private val RETRYABLE_ERROR_CODES = setOf(
            408, // Request Timeout
            429, // Too Many Requests
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504  // Gateway Timeout
        )
        
        // Error codes that should NOT clear the key (business logic errors)
        private val NON_RETRYABLE_ERROR_CODES = setOf(
            400, // Bad Request
            401, // Unauthorized
            403, // Forbidden
            404, // Not Found
            422  // Unprocessable Entity
        )
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // Generate idempotency key if applicable
        val idempotencyKey = idempotencyKeyGenerator.generateIdempotencyKey(originalRequest)
        
        if (idempotencyKey != null) {
            // Check if this request is already in flight
            val duplicateResponse = requestDeduplicator.checkForDuplicate(idempotencyKey)
            if (duplicateResponse != null) {
                appProvider.bLog("[IDEMPOTENCY] Duplicate request detected, returning cached response for key: $idempotencyKey")
                return duplicateResponse
            }
            
            // Add idempotency headers
            val requestWithHeaders = originalRequest.newBuilder()
                .addHeader(IDEMPOTENCY_HEADER, idempotencyKey)
                .addHeader(IDEMPOTENCY_TIMESTAMP_HEADER, idempotencyKeyGenerator.getCurrentTimestamp())
                .addHeader(REQUEST_ID_HEADER, generateRequestId())
                .build()
            
            appProvider.bLog("[IDEMPOTENCY] Adding idempotency key: $idempotencyKey for ${originalRequest.method} ${originalRequest.url}")
            
            // Register the request as in-flight
            requestDeduplicator.registerRequest(idempotencyKey)
            
            return try {
                val response = chain.proceed(requestWithHeaders)
                
                // Handle response based on status code
                handleResponse(response, idempotencyKey)
                
                response
            } catch (e: Exception) {
                // Network errors - clear the key to allow retries
                appProvider.bLog("[IDEMPOTENCY] Network error for key $idempotencyKey: ${e.message}")
                requestDeduplicator.removeRequest(idempotencyKey)
                throw e
            }
        } else {
            // No idempotency needed, proceed normally
            return chain.proceed(originalRequest)
        }
    }
    
    /**
     * Handle response based on status code and determine if key should be cleared
     */
    private fun handleResponse(response: Response, idempotencyKey: String): Response {
        when {
            response.isSuccessful -> {
                // Success - cache the response and keep key for a short time
                appProvider.bLog("[IDEMPOTENCY] Success for key $idempotencyKey, caching response")
                requestDeduplicator.cacheResponse(idempotencyKey, response)
                // Remove from in-flight but keep in cache
                requestDeduplicator.removeFromInFlight(idempotencyKey)
            }
            
            response.code in RETRYABLE_ERROR_CODES -> {
                // Retryable errors - clear the key to allow retries
                appProvider.bLog("[IDEMPOTENCY] Retryable error ${response.code} for key $idempotencyKey, clearing key")
                requestDeduplicator.removeRequest(idempotencyKey)
            }
            
            response.code in NON_RETRYABLE_ERROR_CODES -> {
                // Business logic errors - cache the error response
                appProvider.bLog("[IDEMPOTENCY] Non-retryable error ${response.code} for key $idempotencyKey, caching error")
                requestDeduplicator.cacheResponse(idempotencyKey, response)
                requestDeduplicator.removeFromInFlight(idempotencyKey)
            }
            
            else -> {
                // Unknown error codes - be conservative and clear the key
                appProvider.bLog("[IDEMPOTENCY] Unknown error ${response.code} for key $idempotencyKey, clearing key")
                requestDeduplicator.removeRequest(idempotencyKey)
            }
        }
        
        return response
    }
    
    /**
     * Generates a unique request ID for tracking
     */
    private fun generateRequestId(): String {
        return "${System.currentTimeMillis()}-${(1000..9999).random()}"
    }
}
