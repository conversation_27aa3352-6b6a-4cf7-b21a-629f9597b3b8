package com.bukuwarung.network.interceptors

import com.bukuwarung.network.refreshtoken.TokenManager
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

class SecuredInterceptor : Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val token = TokenManager.getAccessToken()
        val requestBuilder = chain.request().newBuilder()
        if (!token.isNullOrEmpty()) {
            requestBuilder.addHeader("Authorization", "Bearer $token")
        }
        return chain.proceed(requestBuilder.build())
    }
}