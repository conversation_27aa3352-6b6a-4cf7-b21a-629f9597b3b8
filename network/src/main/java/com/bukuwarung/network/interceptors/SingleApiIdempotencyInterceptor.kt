package com.bukuwarung.network.interceptors

import com.bukuwarung.network.utils.AppProvider
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import javax.inject.Inject

/**
 * Idempotency interceptor for specific API endpoints only
 * Currently configured for disbursement API to prevent double disbursements
 */
class SingleApiIdempotencyInterceptor @Inject constructor(
    private val appProvider: AppProvider
) : Interceptor {
    
    companion object {
        private const val IDEMPOTENCY_HEADER = "Idempotency-Key"
        
        // Only these specific endpoints will get idempotency headers
        private val IDEMPOTENT_ENDPOINTS = setOf(
            "/api/payments/*/disbursements/*/" // Disbursement API
            // Add more endpoints here as needed:
            // "/edc-adapter/transfer/posting/*",
            // "/finpro/api/saldo/topup"
        )
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // Check if this endpoint needs idempotency
        if (shouldApplyIdempotency(originalRequest.url.encodedPath, originalRequest.method)) {
            
            // Check if request already has idempotency key
            val existingKey = originalRequest.header(IDEMPOTENCY_HEADER)
            
            if (existingKey != null) {
                // Request already has idempotency key, proceed normally
                appProvider.bLog("[IDEMPOTENCY] Request already has key: $existingKey for ${originalRequest.url}")
                return chain.proceed(originalRequest)
            } else {
                // Log that this endpoint needs idempotency but doesn't have a key
                appProvider.bLog("[IDEMPOTENCY] WARNING: ${originalRequest.url} needs idempotency key but none provided")
                return chain.proceed(originalRequest)
            }
        } else {
            // Not an idempotent endpoint, proceed normally
            return chain.proceed(originalRequest)
        }
    }
    
    /**
     * Checks if the request should have idempotency applied
     */
    private fun shouldApplyIdempotency(path: String, method: String): Boolean {
        // Only apply to POST requests
        if (method != "POST") {
            return false
        }
        
        return IDEMPOTENT_ENDPOINTS.any { pattern ->
            matchesPattern(path, pattern)
        }
    }
    
    /**
     * Checks if a path matches a pattern with wildcards
     */
    private fun matchesPattern(path: String, pattern: String): Boolean {
        val regex = pattern
            .replace("*", "[^/]*")
            .replace("**", ".*")
        return path.matches(Regex(regex))
    }
}
