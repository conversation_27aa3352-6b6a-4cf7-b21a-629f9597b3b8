package com.bukuwarung.network.model.request

import com.google.gson.annotations.SerializedName

data class SessionRequest(
    @SerializedName("deviceId")
    private val deviceId: String,
    @SerializedName("token")
    private val token: String,
    @SerializedName("register")
    private val register: <PERSON><PERSON>an,
    @SerializedName("deviceModel")
    private val deviceModel: String,
    @SerializedName("deviceBrand")
    private val deviceBrand: String,
    @SerializedName("userId")
    private val userId: String,
    @SerializedName("client")
    val clientId: String,
    @SerializedName("clientSecret")
    val clientSecret: String
)