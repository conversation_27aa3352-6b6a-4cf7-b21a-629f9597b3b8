package com.bukuwarung.network.refreshtoken

import android.os.Build
import com.bukuwarung.network.model.request.SessionRequest
import com.bukuwarung.network.utils.AppProvider
import kotlinx.coroutines.runBlocking
import okhttp3.Authenticator
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route

class TokenAuthenticator(private val appProvider: AppProvider) : Authenticator {

    companion object {
        private const val BAD_REQUEST_STATUS_CODE = 400
        private const val FORBIDDEN_STATUS_CODE = 403
    }

    override fun authenticate(route: Route?, response: Response): Request? {
        // Prevent infinite loop
        if (responseCount(response) >= 2) return null

        val newAccessToken = runBlocking {
            RefreshTokenLock.synchronizedRefresh {
                val currentToken = TokenManager.getAccessToken()

                // If the token used for this refresh is the same, it means no refresh has succeeded yet
                if (response.request.header("Authorization")?.contains(currentToken ?: "") == false) {
                    return@synchronizedRefresh currentToken
                }
                appProvider.bLog("RefreshToken: ${TokenManager.getRefreshToken()},${TokenManager.getAccessToken()}")
                if (TokenManager.getRefreshToken().isNullOrEmpty()) {
                    appProvider.forceLogout()
                    appProvider.bLog(ex = Exception("RefreshingToken-forceLogout: No token available"))
                    return@synchronizedRefresh null
                }
                val refreshToken = TokenManager.getRefreshToken() ?: return@synchronizedRefresh null
                val newSessionRequest = SessionRequest(
                    token = refreshToken,
                    register = false,
                    deviceId = "",
                    deviceModel = Build.MODEL,
                    deviceBrand = Build.MANUFACTURER,
                    userId = appProvider.getUserId(),
                    clientId = appProvider.getClientId(),
                    clientSecret = appProvider.getClientSecret()
                )
                try {
                    val refreshResponse =
                        RefreshTokenApiProvider(appProvider).authApiService.createNewSession(
                            appProvider.getBureauEventId(),
                            newSessionRequest
                        )
                    appProvider.bLog("RefreshToken-Response: ${refreshResponse.code()}, ${refreshResponse.message()}")
                    if (refreshResponse.isSuccessful) {
                        refreshResponse.body()?.let {
                            if(it.idToken.isNullOrEmpty() || it.refreshToken.isNullOrEmpty()) {
                                appProvider.bLog(ex = Exception("RefreshingToken-tokenEmpty: ${it.idToken}, ${it.refreshToken}"))
                                return@synchronizedRefresh null
                            }
                            TokenManager.updateTokens(it.idToken, it.refreshToken)
                            it.idToken
                        }?:let{
                            appProvider.bLog(ex = Exception("RefreshingToken-bodyNull: $refreshResponse"))
                        }
                    } else {
                        if (refreshResponse.code() == BAD_REQUEST_STATUS_CODE || refreshResponse.code() == FORBIDDEN_STATUS_CODE) {
                            // Device is blacklisted, notify session ended
                            appProvider.bLog("Device is blacklisted")
                            appProvider.forceLogout()
                            return@synchronizedRefresh null
                        }
                        null
                    }
                } catch (e: Exception) {
                    appProvider.bLog(ex = Exception("RefreshingToken-Exception: ${e.message}"))
                    TokenManager.clear()
                    null
                }
            }
        }

        return newAccessToken?.let {
            response.request.newBuilder()
                .header("Authorization", "Bearer $it")
                .build()
        }
    }

    private fun responseCount(response: Response): Int {
        var count = 1
        var prior = response.priorResponse
        while (prior != null) {
            count++
            prior = prior.priorResponse
        }
        return count
    }
}