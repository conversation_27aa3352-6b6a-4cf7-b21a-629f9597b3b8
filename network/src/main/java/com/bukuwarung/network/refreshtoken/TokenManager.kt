package com.bukuwarung.network.refreshtoken

import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.NetworkConst.SESSION_TOKEN
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

object TokenManager {
    @Volatile private var accessToken: String? = null
    @Volatile private var refreshToken: String? = null

    private lateinit var getEncryptedString: (String, String) -> String
    private lateinit var putEncryptedString: (String, String) -> Unit
    private lateinit var getPrefString: (String, String?) -> String?
    private lateinit var putPrefString: (String, String) -> Unit

    fun init(
        getEncryptedString: (key: String, default: String) -> String,
        putEncryptedString: (key: String, value: String) -> Unit,
        getPrefString: (key: String, default: String?) -> String?,
        putPrefString: (key: String, value: String) -> Unit
    ) {
        this.getEncryptedString = getEncryptedString
        this.putEncryptedString = putEncryptedString
        this.getPrefString = getPrefString
        this.putPrefString = putPrefString

        accessToken = getEncryptedString(BUKUWARUNG_TOKEN, "")
        refreshToken = getPrefString(SESSION_TOKEN, null)
    }

    fun getAccessToken(): String? = accessToken
    fun getRefreshToken(): String? = refreshToken

    fun updateTokens(newAccess: String, newRefresh: String) {
        accessToken = newAccess
        refreshToken = newRefresh
        putEncryptedString(BUKUWARUNG_TOKEN, newAccess)
        putPrefString(SESSION_TOKEN, newRefresh)
    }

    fun clear() {
        accessToken = null
        refreshToken = null
        putEncryptedString(BUKUWARUNG_TOKEN, "")
        putPrefString(SESSION_TOKEN, "")
    }
}