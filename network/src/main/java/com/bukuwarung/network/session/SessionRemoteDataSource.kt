package com.bukuwarung.network.session

import com.bukuwarung.network.model.request.SessionRequest
import com.bukuwarung.network.model.response.SessionResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface SessionRemoteDataSource {

    @POST("/api/v2/auth/users/bacon")
    suspend fun createNewSession(
        @Header("x-session-id") sessionId: String = "",
        @Body newSessionRequest: SessionRequest
    ): Response<SessionResponse>

}