package com.bukuwarung.network.session

import com.bukuwarung.network.model.request.SessionRequest
import com.bukuwarung.network.model.response.SessionResponse
import retrofit2.Response
import javax.inject.Inject

class SessionRepository @Inject constructor(private val remoteDataSource: SessionRemoteDataSource) :
    SessionRemoteRepository {
    override suspend fun createNewSession(sessionId: String, sessionRequest: SessionRequest) =
        remoteDataSource.createNewSession(sessionId, sessionRequest)
}

interface SessionRemoteRepository {
    suspend fun createNewSession(sessionId: String, sessionRequest: SessionRequest): Response<SessionResponse>
}
