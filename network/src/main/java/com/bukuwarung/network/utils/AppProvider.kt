package com.bukuwarung.network.utils

interface AppProvider {

    fun isConnected(): Boolean

    fun testingMock(): Boolean

    fun getDeviceBrand(): String

    fun getDeviceModel(): String

    fun getAppVersionName(): String

    fun getAppVersionCode(): String

    fun bLog(msg: String? = null, ex: Exception? = null)

    fun getAuthToken(): String

    fun getUserId(): String

    fun getClientId(): String

    fun getClientSecret(): String

    fun getBureauEventId(): String

    fun forceLogout()

}