package com.bukuwarung.network.utils

import android.util.Log
import org.json.JSONException
import org.json.JSONObject
import retrofit2.Response
import java.net.HttpURLConnection
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException

inline fun tryCatch(condition: () -> Unit, exception: (Exception) -> Unit) {
    try {
        condition.invoke()
    } catch (e: Exception) {
        exception.invoke(e)
    }
}

suspend fun <T : Any> safeApiCall(
    call: suspend () -> Response<T>
): ResourceState<T> {

    var responseCode: Int? = null
    var errorMessage: String? = null
    var objError: JSONObject? = null

    try {
        val response = call.invoke()
        if (response.isSuccessful) {
            return response.body()?.let {
                ResourceState.Success(it)
            } ?: run {
                @Suppress("UNCHECKED_CAST")
                ResourceState.Success(Unit as T)
            }
        }

        responseCode = response.code()

        objError = JSONObject(response.errorBody()?.string()?: "{}")
        errorMessage = objError.getString("error")
        return ResourceState.Failure(
            null,
            responseCode = responseCode,
            message = errorMessage,
            errorJsonObj = objError
        )

    } catch (e: Exception) {
        return ResourceState.Failure(
            throwable = e,
            responseCode = responseCode,
            message = errorMessage,
            errorJsonObj = objError
        )
    }
}

sealed class ResourceState<out T> {
    class Loading<out T> : ResourceState<T>()

    data class Success<out T>(val data: T) : ResourceState<T>()

    data class Failure<out T>(
        val throwable: Throwable? = null,
        val responseCode: Int? = null,
        val message: String? = null,
        val errorJsonObj: JSONObject? = null
    ) : ResourceState<T>() {

        /**
         * Parse the "error" object using Gson into the expected type [E].
         */
        inline fun <reified E : Any> getErrorBody(): E? {
            return try {
                if (errorJsonObj != null && errorJsonObj.has("error")) {
                    val errorJson = errorJsonObj.getJSONObject("error").toString()
                    Gson().fromJson(errorJson, E::class.java)
                } else {
                    null
                }
            } catch (e: JSONException) {
                Log.e("ResourceState", "JSON error parsing getErrorBody", e)
                null
            } catch (e: JsonSyntaxException) {
                Log.e("ResourceState", "Gson syntax error in getErrorBody", e)
                null
            }
        }

        /**
         * Based on [safeApiCall] & [safeApiCallFlow]
         * If resource is not exist, response code will be 404, has message and throwable would be null
         * This might happen when BE hasn't handled empty / nonexistent data
         */
        fun isResourceNotFound(): Boolean {
            return responseCode == HttpURLConnection.HTTP_NOT_FOUND && throwable == null && message != null
        }

        /**
         * Check error response body with respective BE team
         * in case they still not use this format
         */
        fun getErrorCode(): String? {
            return try {
                errorJsonObj
                    ?.optJSONObject("error")
                    ?.optString("code")
                    ?.takeIf { it.isNotEmpty() }
                    ?: responseCode?.toString()
            } catch (e: JSONException) {
                Log.e("ResourceState", "Error parsing getErrorCode", e)
                responseCode?.toString()
            }
        }

        fun getErrorMessage(): String? {
            return try {
                errorJsonObj
                    ?.getJSONObject("error")
                    ?.getString("message")
            } catch (e: JSONException) {
                Log.e("ResourceState", "Error parsing getErrorMessage", e)
                null
            }
        }
    }
}
