function sedi {
  if [ "$(uname)" == "Linux" ]; then
    sed -i "$@"
  else
    sed -i "" "$@"
  fi
}

echo "----- start version increment ------"

# Step 1: Get current versionMajor, versionMinor, and versionPatch
currentVersionMajor=$(awk '/def versionMajor/ {print $4}' ./build.gradle)
currentVersionMinor=$(awk '/def versionMinor/ {print $4}' ./build.gradle)
currentVersionPatch=$(awk '/def versionPatch/ {print $4}' ./build.gradle)

echo "Current version: ${currentVersionMajor}.${currentVersionMinor}.${currentVersionPatch}"

# Step 2: Increment versionMinor by 1
newVersionMinor=$((currentVersionMinor + 1))

# Step 3: Update versionMinor and versionName in build.gradle
sedi "s/def versionMinor = $currentVersionMinor/def versionMinor = $newVersionMinor/" ./build.gradle

newVersionName="${currentVersionMajor}.${newVersionMinor}.${currentVersionPatch}"
sedi "s/versionName \"${currentVersionMajor}.${currentVersionMinor}.${currentVersionPatch}\"/versionName \"${newVersionName}\"/" ./build.gradle

echo "New version: ${currentVersionMajor}.${newVersionMinor}.${currentVersionPatch}"

echo "Versioning and completed successfully. New version: ${newVersionName}"
