import org.gradle.api.initialization.resolve.RepositoriesMode

pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.zohodl.com' }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.zohodl.com' }
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    }
}
rootProject.name = "EDC"
include ':app'
include ':ui-component'
include ':bluetooth-devices-setup'
include ':core-upload'
//include ':neuro'
//include ':sdk'

