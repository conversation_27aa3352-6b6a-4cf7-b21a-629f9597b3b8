import org.gradle.api.initialization.resolve.RepositoriesMode

pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://maven.zohodl.com'
        }
        maven {url 'https://jitpack.io'}
        maven { url "https://packages.bureau.id/api/packages/Bureau/maven" }

    }
}
rootProject.name = "EDC"
include ':app'
include ':ui-component'
include ':bluetooth-devices-setup'
//include ':neuro'
//include ':sdk'
include ':network'
