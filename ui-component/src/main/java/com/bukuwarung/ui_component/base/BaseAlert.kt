package com.bukuwarung.ui_component.base

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.setSingleClickListener

open class BaseAlert(context: Context, attributeSet: AttributeSet? = null, defStyleAttr: Int = 0):
    ConstraintLayout(context, attributeSet, defStyleAttr) {

    val view: View = View.inflate(context, R.layout.alert, this)

    fun textColor(textColor: Int) {
        view.findViewById<TextView>(R.id.tv_alert_text).setTextColor(textColor)
    }

    fun backGroundColor(background: Int) {
        view.findViewById<ConstraintLayout>(R.id.cl_base).setBackgroundResource(background)
    }

    fun addLeftImage(leftImage: Int) {
        view.findViewById<ImageView>(R.id.iv_left).setImageResource(leftImage)
    }

    fun addRightImage(rightImage: Drawable?) {
        val actionImage = view.findViewById<ImageView>(R.id.iv_right)
        actionImage.visibility = View.VISIBLE
        actionImage.setImageDrawable(rightImage)
        actionImage.setOnClickListener {
            view.hideView()
        }
    }

    fun addText(text: String?) {
        view.findViewById<TextView>(R.id.tv_alert_text).text = text
    }

    fun addTextStyle(textStyle: Int) {
        view.findViewById<TextView>(R.id.tv_alert_text).setTextAppearance(context, textStyle)
    }

    fun addActionText(text: String) {
        val actionBtn = view.findViewById<TextView>(R.id.tv_action_btn)
        actionBtn.visibility = View.VISIBLE
        actionBtn.text = text
        actionBtn.setSingleClickListener {
            actionButtonClicked?.invoke()
        }
    }

    fun hideActionText(){ //this function is created because we are using BaseAlert in recyclerview.
        view.findViewById<TextView>(R.id.tv_action_btn).hideView()
    }

    fun actionTextColor(color: Int){
        view.findViewById<TextView>(R.id.tv_action_btn).setTextColor(color)
    }

    fun addText(text: SpannableStringBuilder) {
        val textView = view.findViewById<TextView>(R.id.tv_alert_text)
        textView.text = text
        textView.movementMethod = LinkMovementMethod.getInstance()
    }

    var actionButtonClicked: (() -> Unit)? = null

    fun setTextGravity(gravity: Int){
        view.findViewById<TextView>(R.id.tv_alert_text).gravity = gravity
    }

}