package com.bukuwarung.ui_component.base

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.Toolbar
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.utils.dp
import com.bukuwarung.ui_component.utils.setSingleClickListener

open class BaseAppBar(context: Context, attributeSet: AttributeSet? = null, defStyleAttr: Int = 0) :
    Too<PERSON><PERSON>(context, attributeSet, defStyleAttr) {

    init {
        setBackgroundColor(resources.getColor(R.color.blue60))
        minimumHeight = 56.dp
        initView(context,attributeSet,defStyleAttr)
    }

    private fun initView(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
        if (attrs == null) return
    }

    override fun setOnClickListener(l: OnClickListener?) {
        setSingleClickListener {
            super.performClick()
        }
    }

    fun setBackground(color : Int){
        background = resources.getDrawable(color)
    }

    override fun setTitle(title : Int){
        this.title = resources.getString(title)
    }

}