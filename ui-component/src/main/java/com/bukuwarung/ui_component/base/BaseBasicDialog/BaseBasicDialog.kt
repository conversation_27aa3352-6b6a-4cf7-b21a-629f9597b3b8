package com.bukuwarung.ui_component.base.BaseBasicDialog

import android.app.Activity
import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.Window
import com.bukuwarung.ui_component.databinding.BaseDialogBinding
import com.bukuwarung.ui_component.utils.hideView


class BaseBasicDialog(a: Activity, var dialogType: BasicDialogType) : Dialog(a) {

    private lateinit var binding: BaseDialogBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        binding = BaseDialogBinding.inflate(layoutInflater)
        setContentView(binding.root)
        val window: Window? = getWindow()
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        initViews()
    }

    private fun initViews() {
        binding.ivClose.setOnClickListener {
            dismiss()
        }

//        setCancelable(false)
//        setCanceledOnTouchOutside(false)


        when (dialogType) {

            BasicDialogType.DIALOG_WITH_ONE_ACTION -> {
                binding.dialogImage.hideView()
                binding.btnActionTwo.hideView()
                binding.etInput.hideView()
            }
            BasicDialogType.DIALOG_WITH_TWO_ACTIONS -> {
                binding.dialogImage.hideView()
                binding.etInput.hideView()

            }
            BasicDialogType.DIALOG_WITH_IMAGE_AND_ONE_ACTION -> {
                binding.btnActionTwo.hideView()
                binding.etInput.hideView()

            }
            BasicDialogType.DIALOG_WITH_IMAGE_AND_TWO_ACTION -> {
                binding.etInput.hideView()
            }
            BasicDialogType.DIALOG_WITH_EDIT_TEXT -> {
                binding.dialogImage.hideView()
                binding.btnActionTwo.hideView()

            }
            BasicDialogType.DIALOG_WITH_EDIT_TEXT_2_ACTIONS -> {
                binding.dialogImage.hideView()
            }

        }

    }

    fun setupDialog(
        title: String,
        message: String,
        imageId: Int,
        action1Title: String,
        action2Title: String,
        action1: View.OnClickListener,
        action2: View.OnClickListener
    ) {
        binding.dialogImage.setImageResource(imageId)
        binding.tvTitle.text = title
        binding.tvMessage.text = message
        binding.btnActionOne.setOnClickListener(action1)
        binding.btnActionTwo.setOnClickListener(action2)
        binding.btnActionOne.text = action1Title
        binding.btnActionTwo.text = action2Title
    }

    fun getEditTextString(): String {
        return binding.etInput.text.toString()
    }


}




