package com.bukuwarung.ui_component.base

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.widget.Button
import androidx.core.content.res.ResourcesCompat
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.google.android.material.button.MaterialButton

open class BaseButton :
    MaterialButton {

    constructor(context: Context) : super(context) {
        initView(context, null, 0)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(context, attrs, defStyleAttr)
    }

    private fun initView(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
        typeface = ResourcesCompat.getFont(context, R.font.roboto_bold)
        gravity = Gravity.CENTER
    }

//    override fun setOnClickListener(l: OnClickListener?) {
//        setSingleClickListener {
//            super.performClick()
//        }
//    }

    fun isEnabled(enabled: Boolean = true) {
        this.isEnabled = isEnabled
    }

    fun isBorderEnabled(isBorderEnabled: Boolean = true) {
        if (isBorderEnabled) {
            this.strokeWidth = 8
            this.cornerRadius = 8
        } else {
            this.strokeWidth = 0
        }
    }

    fun setBorderColor(color: Int) {
        this.setStrokeColorResource(color)
    }


}