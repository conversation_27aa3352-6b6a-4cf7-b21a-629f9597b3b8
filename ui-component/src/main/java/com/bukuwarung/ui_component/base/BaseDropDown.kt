package com.bukuwarung.ui_component.base

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatSpinner

open class BaseDropDown(context: Context, attributeSet: AttributeSet?, val itemList: List<String>?) : AppCompatSpinner(context, attributeSet) {

    var baseAdapter = itemList?.let { BaseDropdownAdapter(context, it) }

    init {
        this.adapter = baseAdapter
    }

    fun getSelected(): String {
        return baseAdapter?.selectedPos?.let { itemList?.get(it - 1) } ?: ""
    }

    fun setInitialTexts(selectText: String, labelText: String?, badgeText: String?) {
        baseAdapter?.setTopTexts(selectText = selectText, labelText = labelText, badgeText = badgeText)
    }
}