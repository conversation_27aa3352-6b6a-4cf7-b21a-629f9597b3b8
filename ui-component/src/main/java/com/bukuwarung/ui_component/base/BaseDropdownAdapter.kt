package com.bukuwarung.ui_component.base

import android.content.Context
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import com.bukuwarung.ui_component.R

class BaseDropdownAdapter(context: Context, val itemList: List<String>) : ArrayAdapter<String>(context, 0) {
    val layoutInflater: LayoutInflater = LayoutInflater.from(context)
    var selectText: String = "Select"
    var labelText: String? = null
    var badgeText: String? = null
    var selectedPos: Int? = null

    override fun getItem(position: Int): String? {
        if (position == 0) {
            return null
        }
        return itemList[position - 1]
    }

    override fun getCount() = itemList.size + 1
    override fun isEnabled(position: Int) = position != 0

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view: View = convertView ?: layoutInflater.inflate(R.layout.dropdown_top_item, parent, false)
        setTopDropDownItems(view)
        return view
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view: View
        if (position == 0) {
            view = layoutInflater.inflate(R.layout.dropdown_top_item, parent, false)
            view.setOnClickListener {
                val root = parent.rootView
                root.dispatchKeyEvent(KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_BACK))
                root.dispatchKeyEvent(KeyEvent(KeyEvent.ACTION_UP, KeyEvent.KEYCODE_BACK))
            }
        } else {
            if (selectedPos == position) {
                view = layoutInflater.inflate(R.layout.dropdown_selected_item, parent, false)
                getItem(position)?.let { item ->
                    view.findViewById<TextView>(R.id.tv_item).text = item
                }
            } else {
                view = layoutInflater.inflate(R.layout.dropdown_normal_item, parent, false)
                getItem(position)?.let { item ->
                    view.findViewById<TextView>(R.id.tv_item).text = item
                }
            }
            view.setOnClickListener {
                selectedPos = if (selectedPos == position) {
                    0
                } else {
                    position
                }
            }
        }
        return view
    }

    private fun setTopDropDownItems(view: View) {
        view.findViewById<TextView>(R.id.tv_select).text = selectText
        labelText?.let {
            val labelTV = view.findViewById<TextView>(R.id.tv_label)
            labelTV.visibility = View.VISIBLE
            labelTV.text = it
        }
        badgeText?.let {
            val badgeTV = view.findViewById<TextView>(R.id.tv_badge)
            badgeTV.visibility = View.VISIBLE
            badgeTV.text = it
        }
    }

    fun setTopTexts(selectText: String, labelText: String?, badgeText: String?) {
        this.selectText = selectText
        this.labelText = labelText
        this.badgeText = badgeText
    }
}