package com.bukuwarung.ui_component.base

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.ui_component.databinding.EmptyViewBinding
import com.bukuwarung.ui_component.utils.textHTML


open class BaseEmptyView(
    context: Context,
    attributeSet: AttributeSet? = null
) : ConstraintLayout(context, attributeSet) {

    companion object {
        enum class EmptyStateType {
            SET_PRICE, CONNECTION_ERROR, NO_STOCK_RUNNING_LOW, ADD_STOCK,
            CREATE_NOTE_REMINDER, REFERRAL_NOT_USED, CUSTOM
        }
    }

    val binding = EmptyViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setTitle(title: String?) {
        binding.tvEmptyTitle.textHTML(title)
    }

    fun setMessage(message: String?) {
        binding.tvEmptyMessage.textHTML(message)
    }

    fun setCtaText(buttonText: String?) {
        binding.btnEmptyCta.text = buttonText
    }

    fun setEmptyIcon(iconRes: Int) {
        binding.ivEmptyIcon.setImageResource(iconRes)
    }

    private var callback: Callback? = null

    interface Callback {
        fun ctaClicked()
        fun messageClicked()
    }

    fun addCallback(callback: Callback) {
        this.callback = callback
        binding.btnEmptyCta.setOnClickListener { callback.ctaClicked() }
        binding.tvEmptyMessage.setOnClickListener { callback.messageClicked() }
    }
}