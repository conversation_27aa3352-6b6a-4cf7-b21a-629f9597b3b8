package com.bukuwarung.ui_component.base

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.ui_component.databinding.ErrorViewBinding
import com.bukuwarung.ui_component.utils.textHTML

open class BaseErrorView(
    context: Context,
    attributeSet: AttributeSet? = null
) : ConstraintLayout(context, attributeSet) {

    companion object {
        enum class ErrorType { SERVER_UNREACHABLE, SERVER_ERROR, CONNECTION_ERROR, CUSTOM }
    }

    val binding = ErrorViewBinding.inflate(LayoutInflater.from(context), this, true)
    var type = ErrorType.CUSTOM

    fun setTitle(title: String?) {
        binding.tvErrorTitle.textHTML(title)
    }

    fun setMessage(message: String?) {
        binding.tvErrorMessage.textHTML(message)
    }

    fun setCtaText(buttonText: String?) {
        binding.btnErrorCta.text = buttonText
    }

    fun setErrorIcon(iconRes: Int) {
        binding.ivErrorIcon.setImageResource(iconRes)
    }

    private var callback: Callback? = null

    interface Callback {
        fun ctaClicked()
        fun messageClicked()
    }

    fun addCallback(callback: Callback) {
        this.callback = callback
        binding.btnErrorCta.setOnClickListener { callback.ctaClicked() }
        binding.tvErrorMessage.setOnClickListener { callback.messageClicked() }
    }
}