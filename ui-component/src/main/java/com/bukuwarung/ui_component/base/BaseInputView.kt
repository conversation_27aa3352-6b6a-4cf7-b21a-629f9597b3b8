package com.bukuwarung.ui_component.base

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.doAfterTextChanged
import com.bukuwarung.ui_component.databinding.InputViewBinding
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import com.bukuwarung.ui_component.utils.setDrawableRightListener
import com.bukuwarung.ui_component.utils.showView

open class BaseInputView(
    context: Context,
    attributeSet: AttributeSet? = null
) : ConstraintLayout(context, attributeSet)  {


    val binding = InputViewBinding.inflate(LayoutInflater.from(context), this, true)

    var textWatcher: TextWatcher? = null

    fun getLength(): Int {
        return binding.etNumber.text.length
    }

    fun getText():String {
        return binding.etNumber.text.toString()
    }

    fun setText(text: String?) {
        binding.etNumber.setText(text)
        binding.etNumber.setSelection(binding.etNumber.length())
    }

    fun setFocus(){
        binding.etNumber.requestFocus()
    }

    fun setSelection() {
        binding.etNumber.setSelection(binding.etNumber.text.length)
    }

    fun setTitle(title: String?) {
        if (title.isNotNullOrEmpty())
            binding.tvTitle.text = title
        else
            binding.tvTitle.hideView()
    }

    fun setTitleHint(titleHint: String?) {
        if (titleHint.isNotNullOrEmpty())
            binding.tvTitleHint.text = titleHint
        else
            binding.tvTitleHint.hideView()
    }

    fun setHint(hint: String?) {
        binding.etNumber.hint = hint
    }

    fun setBottomText(text: String?) {
        if (text.isNotNullOrEmpty()) {
            binding.tvBottomText.showView()
            binding.tvBottomText.text = text
        } else {
            binding.tvBottomText.hideView()
        }
    }

    fun setBottomTextColor(colorRes: Int) {
        binding.tvBottomText.setTextColor(colorRes)
    }

    fun setInputTextColor(colorRes: Int) {
        binding.etNumber.setTextColor(colorRes)
        binding.etNumber.backgroundTintList = ColorStateList.valueOf(colorRes)
    }

    fun setInputType(inputType: Int) {
        binding.etNumber.inputType = inputType
    }

    fun setRightDrawable(drawable: Drawable?) {
        binding.etNumber.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable,  null)
    }

    fun setLeftDrawable(drawable: Drawable?) {
        binding.etNumber.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null,  null)
    }

    fun setRightDrawableWithListener(drawable: Drawable?, action: (() -> Unit)?) {
        binding.etNumber.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable,  null)
        if (action != null) {
            binding.etNumber.setDrawableRightListener {
                action()
            }
        }
    }

    fun onTextChanged(action: (String) -> Unit) {
        textWatcher = binding.etNumber.doAfterTextChanged {
            action(it.toString())
        }
    }

    fun onTextChangeActionListeners(
        beforeTextChangedAction: () -> Unit,
        onTextChangedAction: () -> Unit,
        afterTextChangedAction: () -> Unit
    ) {
        binding.etNumber.addTextChangedListener(
            object : TextWatcher {
                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                    beforeTextChangedAction()
                }

                override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                    binding.etNumber.removeTextChangedListener(this)
                    onTextChangedAction()
                    binding.etNumber.addTextChangedListener(this)
                }

                override fun afterTextChanged(p0: Editable?) {
                    afterTextChangedAction()
                }

            }
        )
    }

    fun removeTextChanged() {
        binding.etNumber.removeTextChangedListener(textWatcher)
    }
}