package com.bukuwarung.ui_component.base

import android.app.Activity
import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import com.bukuwarung.ui_component.R
import com.google.android.material.button.MaterialButton


class BasePopoverDialog(a: Activity) : Dialog(a) {


    private lateinit var tvMessage: TextView
    private lateinit var tvTitle: TextView
    private lateinit var closeBtn: ImageView
    private lateinit var actionOne: MaterialButton
    private lateinit var ivPopover: ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(R.layout.base_popover_dialog)

        val window: Window? = getWindow()
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)

        initViews()
    }

    private fun initViews() {
        ivPopover = findViewById(R.id.iv_popover)
        tvTitle = findViewById(R.id.tv_title)
        tvMessage = findViewById(R.id.tv_message)
        actionOne = findViewById(R.id.btn_action_one)
        closeBtn = findViewById(R.id.iv_close)
        closeBtn.setOnClickListener {
            dismiss()
        }

//        setCancelable(false)
//        setCanceledOnTouchOutside(false)

    }

    fun setupDialog(
        title: String,
        message: String,
        imageId: Int,
        action1Title: String,
        action1: View.OnClickListener
    ) {
        ivPopover.setImageResource(imageId)
        tvTitle.text = title
        tvMessage.text = message
        actionOne.setOnClickListener(action1)
        actionOne.text = action1Title
    }

}




