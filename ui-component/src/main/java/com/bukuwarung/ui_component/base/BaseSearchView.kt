package com.bukuwarung.ui_component.base

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.ui_component.databinding.SearchViewBinding

open class BaseSearchView(
    context: Context,
    attributeSet: AttributeSet? = null
) : ConstraintLayout(context, attributeSet) {

    val binding = SearchViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun getText(): String {
        return binding.tilSearch.editText?.text.toString()
    }

    fun setText(text: String?) {
        binding.tilSearch.editText?.setText(text)
    }

    fun setInputType(inputType: Int) {
        binding.tilSearch.editText?.inputType = inputType
    }

    fun setHint(text: String?) {
        binding.tilSearch.editText?.setHint(text)
    }

    fun setLeftDrawable(drawable: Drawable, action: () -> Unit) {
        binding.tilSearch.startIconDrawable = drawable
        binding.tilSearch.setStartIconOnClickListener { action() }
    }

    fun setRightDrawable(drawable: Drawable, action: () -> Unit) {
        binding.tilSearch.endIconDrawable = drawable
        binding.tilSearch.setEndIconOnClickListener { action() }
    }

    fun setFocus() {
        binding.tilSearch.editText?.requestFocus()
    }

    fun onTextChangeActionListeners(
        beforeTextChangedAction: () -> Unit,
        onTextChangedAction: () -> Unit,
        afterTextChangedAction: () -> Unit
    ) {
        binding.tilSearch.editText?.addTextChangedListener(
            object : TextWatcher {
                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                    beforeTextChangedAction()
                }

                override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                    binding.tilSearch.editText?.removeTextChangedListener(this)
                    onTextChangedAction()
                    binding.tilSearch.editText?.addTextChangedListener(this)
                }

                override fun afterTextChanged(p0: Editable?) {
                    afterTextChangedAction()
                }
            }
        )
    }

}