package com.bukuwarung.ui_component.component

import android.content.Context
import android.util.AttributeSet
import android.widget.Button
import android.widget.RelativeLayout
import androidx.core.content.res.ResourcesCompat
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.google.android.material.button.MaterialButton

open class Badge(context: Context, attributeSet: AttributeSet? = null, defStyleAttr: Int = 0) :
    RelativeLayout(context, attributeSet, defStyleAttr) {

    init {
//        typeface = ResourcesCompat.getFont(context, R.font.roboto_bold)
    }

    override fun setOnClickListener(l: OnClickListener?) {
        setSingleClickListener {
            l?.onClick(this)
            super.performClick()
        }
    }

    fun isEnabled(enabled: Boolean = true) {
        this.isEnabled = isEnabled
    }

    fun isBorderEnabled(isBorderEnabled: Boolean = true) {
//        if (isBorderEnabled) {
//            this.strokeWidth = 8
//            this.cornerRadius = 8
//        } else {
//            this.strokeWidth = 0
//        }
    }

    fun setBorderColor(color: Int) {
//        this.setStrokeColorResource(color)
    }


}