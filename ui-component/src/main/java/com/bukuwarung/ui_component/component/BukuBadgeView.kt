package com.bukuwarung.ui_component.component

import android.content.Context
import android.content.res.Resources
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.OvalShape
import android.os.Build
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.ViewCompat
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.utils.dp
import kotlin.math.roundToInt

class BukuBadgeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    androidx.appcompat.widget.AppCompatTextView(context, attrs, defStyleAttr) {
    private var badgeType: Int = 0
    private var backgroundColor = 0
    private var borderColor = 0
    private var borderWidth = 0f
    private var borderAlpha = 0f
    private var ctType = 0
    private var density = 0f
    private var mShadowRadius = 0
    private var shadowYOffset = 0
    private var shadowXOffset = 0
    private var basePadding = 0
    private var diffWH = 0
    private var isHighLightMode = false
    private fun init(context: Context, attrs: AttributeSet?) {
        gravity = Gravity.CENTER
        density = getContext().resources.displayMetrics.density
        mShadowRadius = (density * SHADOW_RADIUS).toInt()
        shadowYOffset = (density * Y_OFFSET).toInt()
        shadowXOffset = (density * X_OFFSET).toInt()
        basePadding = (mShadowRadius * 2.5).toInt()
        val textHeight = textSize
        val textWidth = textHeight / 4
        diffWH = (Math.abs(textHeight - textWidth) / 2).toInt()
        val horizontalPadding = basePadding + diffWH
        setPadding(horizontalPadding, basePadding, horizontalPadding, basePadding)
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.BukuBadgeView)
        backgroundColor =
            typedArray.getColor(R.styleable.BukuBadgeView_android_background, Color.WHITE)
        borderColor = typedArray.getColor(
            R.styleable.BukuBadgeView_mbtv_border_color,
            Color.TRANSPARENT
        )

        badgeType = typedArray.getInt(
            R.styleable.BukuBadgeView_badge_type,-1)

        borderWidth =
            typedArray.getDimension(R.styleable.BukuBadgeView_mbtv_border_width, 0f)
        borderAlpha = typedArray.getFloat(R.styleable.BukuBadgeView_mbtv_border_alpha, 1f)
        ctType = typedArray.getInt(R.styleable.BukuBadgeView_mbtv_type, DEFAULT_FILL_TYPE)
        typedArray.recycle()

        clipToOutline = true

        if(badgeType==0){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                setCompoundDrawablesWithIntrinsicBounds(context.getDrawable(R.drawable.success_check),null,null,null)
                compoundDrawablePadding = 4.dp
                backgroundColor = context.getColor(R.color.green_80)
                setText(context.getString(R.string.success))
                setTextColor(context.getColor(R.color.white))
            }
        }
        else if(badgeType==1){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                setCompoundDrawablesWithIntrinsicBounds(context.getDrawable(R.drawable.pending_clock),null,null,null)
                compoundDrawablePadding = 4.dp
                backgroundColor = context.getColor(R.color.yellow_60)
                setText(context.getString(R.string.pending))
                setTextColor(context.getColor(R.color.white))
            }
        }
        else if(badgeType==2){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                setCompoundDrawablesWithIntrinsicBounds(context.getDrawable(R.drawable.failed_cross),null,null,null)
                compoundDrawablePadding = 4.dp
                backgroundColor = context.getColor(R.color.red_60)
                setText(context.getString(R.string.failed))
                setTextColor(context.getColor(R.color.white))
            }
        }
        else if(badgeType==3){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                setCompoundDrawablesWithIntrinsicBounds(context.getDrawable(R.drawable.expired_sq_crross),null,null,null)
                compoundDrawablePadding = 4.dp
                backgroundColor = context.getColor(R.color.black20)
                setText(context.getString(R.string.expired))
                setTextColor(context.getColor(R.color.white))
            }
        }

    }

    override fun onTextChanged(
        text: CharSequence,
        start: Int,
        lengthBefore: Int,
        lengthAfter: Int
    ) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter)
        val strText = text?.toString()?.trim { it <= ' ' } ?: ""
        if (isHighLightMode && "" != strText) {
            val lp = layoutParams
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT
            lp.width = ViewGroup.LayoutParams.WRAP_CONTENT
            layoutParams = lp
            isHighLightMode = false
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        refreshBackgroundDrawable(w, h)
    }

    private fun refreshBackgroundDrawable(targetWidth: Int, targetHeight: Int) {
        if (targetWidth <= 0 || targetHeight <= 0) {
            return
        }
        val text = text ?: return
        if (text.length == 1) {
            val max = Math.max(targetWidth, targetHeight)
            val circle: ShapeDrawable
            val diameter = max - 2 * mShadowRadius
            val oval: OvalShape = OvalShadow(mShadowRadius.toFloat(), diameter.toFloat())
            circle = ShapeDrawable(oval)
            ViewCompat.setLayerType(this, View.LAYER_TYPE_SOFTWARE, circle.paint)
            circle.paint.setShadowLayer(
                mShadowRadius.toFloat(),
                shadowXOffset.toFloat(),
                shadowYOffset.toFloat(),
                KEY_SHADOW_COLOR
            )
            circle.paint.color = backgroundColor
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.JELLY_BEAN) {
                setBackgroundDrawable(circle)
            } else {
                background = circle
            }
        } else if (text.length > 1) {
            val sr: SemiCircleRectDrawable = SemiCircleRectDrawable()
            ViewCompat.setLayerType(this, View.LAYER_TYPE_SOFTWARE, sr.paint)
            sr.paint.setShadowLayer(
                mShadowRadius.toFloat(),
                shadowXOffset.toFloat(),
                shadowYOffset.toFloat(),
                KEY_SHADOW_COLOR
            )
            sr.paint.color = backgroundColor
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                background = sr
            } else {
                setBackgroundDrawable(sr)
            }
        } else {
        }
    }

    fun setBadgeCount(count: String) {
        setBadgeCount(count, false)
    }

    fun setBadgeCount(count: String, goneWhenZero: Boolean) {
        var temp = -1
        try {
            temp = count.toInt()
        } catch (e: Exception) {
        }
        if (temp != -1) {
            setBadgeCount(temp, goneWhenZero)
        }
    }

    fun setBadgeCount(count: Int) {
        setBadgeCount(count, true)
    }

    fun setBadgeCount(count: Int, goneWhenZero: Boolean) {
        if (count > 0 && count <= 99) {
            text = count.toString()
            visibility = VISIBLE
        } else if (count > 99) {
            text = "99+"
            visibility = VISIBLE
        } else if (count <= 0) {
            text = "0"
            visibility = if (goneWhenZero) {
                GONE
            } else {
                VISIBLE
            }
        }
    }

    fun setHighLightMode() {
        setHighLightMode(false)
    }

    fun clearHighLightMode() {
        isHighLightMode = false
        setBadgeCount(0)
    }

    /**
     *
     * @param isDisplayInToolbarMenu
     */
    fun setHighLightMode(isDisplayInToolbarMenu: Boolean) {
        isHighLightMode = true
        val params = layoutParams
        params.width = dp2px(context, 8f)
        params.height = params.width
        if (isDisplayInToolbarMenu && params is FrameLayout.LayoutParams) {
            params.topMargin = dp2px(
                context, 8f
            )
            params.rightMargin = dp2px(
                context, 8f
            )
        }
        layoutParams = params
        val drawable = ShapeDrawable(OvalShape())
        ViewCompat.setLayerType(this, View.LAYER_TYPE_SOFTWARE, drawable.paint)
        drawable.paint.color = backgroundColor
        drawable.paint.isAntiAlias = true
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            background = drawable
        } else {
            setBackgroundDrawable(drawable)
        }
        text = ""
        visibility = VISIBLE
    }

    override fun setBackgroundColor(color: Int) {
        backgroundColor = color
        refreshBackgroundDrawable(width, height)
    }

    private inner class OvalShadow(shadowRadius: Float, circleDiameter: Float) :
        OvalShape() {
        private val mRadialGradient: RadialGradient
        private val mShadowPaint: Paint
        private val mCircleDiameter: Float
        override fun draw(canvas: Canvas, paint: Paint) {
            val viewWidth = <EMAIL>
            val viewHeight = <EMAIL>
            canvas.drawCircle(
                (viewWidth / 2).toFloat(),
                (viewHeight / 2).toFloat(),
                (mCircleDiameter / 2 + mShadowRadius).toFloat(),
                mShadowPaint
            )
            canvas.drawCircle(
                (viewWidth / 2).toFloat(),
                (viewHeight / 2).toFloat(),
                (mCircleDiameter / 2).toFloat(),
                paint
            )
        }

        init {
            mShadowPaint = Paint()
            mShadowRadius = shadowRadius.roundToInt()
            mCircleDiameter = circleDiameter
            mRadialGradient = RadialGradient(
                mCircleDiameter.toFloat() , mCircleDiameter.toFloat(),
                mShadowRadius.toFloat(), intArrayOf(
                    FILL_SHADOW_COLOR, Color.TRANSPARENT
                ), null, Shader.TileMode.CLAMP)
            mShadowPaint.shader = mRadialGradient
        }
    }

    internal inner class SemiCircleRectDrawable : Drawable() {
        val paint: Paint
        private var rectF: RectF? = null
        override fun setBounds(left: Int, top: Int, right: Int, bottom: Int) {
            super.setBounds(left, top, right, bottom)
            if (rectF == null) {
                rectF = RectF(
                    (left + diffWH).toFloat(),
                    (top + mShadowRadius + 4).toFloat(),
                    (right - diffWH).toFloat(),
                    (bottom - mShadowRadius - 4).toFloat()
                )
            } else {
                rectF!![(left + diffWH).toFloat(), (top + mShadowRadius + 4).toFloat(), (right - diffWH).toFloat()] =
                    (bottom - mShadowRadius - 4).toFloat()
            }
        }

        override fun draw(canvas: Canvas) {
            var R = (rectF!!.bottom * 0.4).toFloat()
            if (rectF!!.right < rectF!!.bottom) {
                R = (rectF!!.right * 0.4).toFloat()
            }


            canvas.drawRoundRect(rectF!!, 20f, 20f, paint)
//            canvas.drawRect(rectF!!,paint)
//            canvas.drawRect(rectF!!,paint)
        }

        override fun setAlpha(alpha: Int) {
            paint.alpha = alpha
        }

        override fun setColorFilter(colorFilter: ColorFilter?) {
            paint.colorFilter = colorFilter
        }

        override fun getOpacity(): Int {
            return PixelFormat.TRANSPARENT
        }

        init {
            paint = Paint()
            paint.isAntiAlias = true
        }
    }

    inner class RoundedCornersDrawable(resources: Resources?, bitmap: Bitmap?, borderRadius: Float) :
        BitmapDrawable(resources, bitmap) {
        private val bitmapShader: BitmapShader
        private val p: Paint
        private val rect: RectF
        private val borderRadius: Float
        override fun draw(canvas: Canvas) {
            canvas.drawRoundRect(rect, borderRadius, borderRadius, p)
        }

        init {
            bitmapShader =
                BitmapShader(getBitmap(), Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
            val b: Bitmap = getBitmap()
            p = paint
            p.setAntiAlias(true)
            p.setShader(bitmapShader)
            val w = b.width
            val h = b.height
            rect = RectF(0f, 0f, w.toFloat(), h.toFloat())
            this.borderRadius = if (borderRadius < 0) 0.15f * Math.min(w, h) else borderRadius
        }
    }

    companion object {
        private const val DEFAULT_FILL_TYPE = 0
        private const val SHADOW_RADIUS = 3.5f
        private const val FILL_SHADOW_COLOR = 0x55000000
        private const val KEY_SHADOW_COLOR = 0x55000000
        private const val X_OFFSET = 0f
        private const val Y_OFFSET = 1.75f
        fun dp2px(context: Context, dpValue: Float): Int {
            return try {
                val scale = context.resources.displayMetrics.density
                (dpValue * scale + 0.5f).toInt()
            } catch (e: Exception) {
                (dpValue + 0.5f).toInt()
            }
        }
    }

    init {
        init(context, attrs)
    }
}