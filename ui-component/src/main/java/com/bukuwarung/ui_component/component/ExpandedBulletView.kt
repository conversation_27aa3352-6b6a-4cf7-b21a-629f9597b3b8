package com.bukuwarung.ui_component.component

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.databinding.ExpandedBulletItemViewBinding
import com.bukuwarung.ui_component.databinding.ExpandedBulletViewBinding
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.showView

class ExpandedBulletView(context: Context, attrs: AttributeSet?) :
    ConstraintLayout(context, attrs) {

    private var isExpanded = false
    private var message: String? = null
    private var items: List<String> = mutableListOf()

    private val binding =
        ExpandedBulletViewBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        if (isInEditMode) { // Only used in the layout editor for preview
            binding.tvWarning.text = "Saat ini ada beberapa Bank yang mengalami gangguan transfer."
            binding.rootExpandedBulletView.setBackgroundResource(R.drawable.bg_solid_yellow80_corner_8dp_stroke_yellow5)
        }
        binding.ivExpandIcon.setOnClickListener {
            isExpanded = !isExpanded
            renderData()
        }
    }

    fun setData(message: String, items: List<String> = mutableListOf()) {
        this.message = message
        this.items = items
        renderData()
    }

    private fun renderData() {
        binding.apply {
            tvWarning.text = message
            if (items.isEmpty()) {
                ivExpandIcon.hideView()
                llWarningList.hideView()
            } else {
                inflateInfoText(items)
                ivExpandIcon.showView()
                llWarningList.showView()
            }
            if (isExpanded) {
                ivExpandIcon.setImageResource(R.drawable.ic_chevron_up)
                llWarningList.showView()
            } else {
                ivExpandIcon.setImageResource(R.drawable.ic_chevron_down)
                llWarningList.hideView()
            }
        }
    }

    private fun inflateInfoText(items: List<String>) {
        binding.llWarningList.removeAllViews()
        items.forEachIndexed { index, info ->
            val item = ExpandedBulletItemViewBinding.inflate(LayoutInflater.from(context))
            item.tvBullet.text = "${index + 1}."
            item.tvContent.text = info
            binding.llWarningList.addView(item.root)
        }
    }
}