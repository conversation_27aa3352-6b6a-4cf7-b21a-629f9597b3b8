package com.bukuwarung.ui_component.component.alert

import android.content.Context
import android.util.AttributeSet
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseAlert
import com.bukuwarung.ui_component.utils.getDrawableCompat

class BukuAlert(context: Context, attributeSet: AttributeSet?):
    BaseAlert(context, attributeSet) {

        init {
            val type = context.obtainStyledAttributes(attributeSet, R.styleable.BukuAlert, 0, 0)
            val typeVal = R.styleable.BukuAlert_type
            val actionImage = R.styleable.BukuAlert_actionImage
            val actionText = R.styleable.BukuAlert_actionText
            val text = R.styleable.BukuAlert_alertText

            if (type.hasValue(typeVal)) {
                setBackgroundType(type.getString(typeVal))
            }

            if (type.hasValue(actionImage)) {
                val actionButtonImage = type.getDrawable(actionImage)
                if (actionButtonImage != null) {
                    super.addRightImage(actionButtonImage)
                }
            }
            if (type.hasValue(actionText)) {
                if (!type.getString(actionText).isNullOrBlank())
                    super.addActionText(type.getString(actionText)!!)
            }

            if (type.hasValue(text)) {
                super.addText(type.getString(text))
            }

            type.recycle()

        }

    fun setClickAction(clickAction:(() -> Unit)?){
        super.actionButtonClicked = clickAction
    }

    fun setBackgroundType(type: String?) {
        when(type) {
            "info" -> {
                super.addLeftImage(R.drawable.ic_alert_info)
                super.backGroundColor(R.drawable.background_info)
            }
            "success" -> {
                super.addLeftImage(R.drawable.ic_alert_success)
                super.backGroundColor(R.drawable.background_success)
            }
            "warning" -> {
                super.addLeftImage(R.drawable.ic_alert_warning)
                super.backGroundColor(R.drawable.background_warning)
            }
            "warning_without_drawable" -> {
                super.addLeftImage(0)
                super.backGroundColor(R.drawable.background_warning)
            }
            "error_without_drawable" -> {
                super.addLeftImage(0)
                super.backGroundColor(R.drawable.background_error)
            }
            "information" -> {
                super.addLeftImage(R.drawable.ic_info_yellow)
                super.backGroundColor(R.drawable.bg_solid_yellow80_corner_8dp_stroke_yellow5)
            }
            "red_with_cross" -> {
                super.addRightImage(context.getDrawableCompat(R.drawable.ic_cross_red))
                super.backGroundColor(R.color.red5)
            }
            "blue_with_cross" -> {
                super.addRightImage(context.getDrawableCompat(R.drawable.ic_cross_blue))
                super.backGroundColor(R.color.blue5)
            }
            else -> {
                super.addLeftImage(R.drawable.ic_alert_error)
                super.backGroundColor(R.drawable.background_error)
            }
        }
    }

    fun setInfoViewStyle(textStyle: Int, textColor: Int, backgroundType:String){
      super.addTextStyle(textStyle)
      super.textColor(textColor)
      setBackgroundType(backgroundType)
    }
}