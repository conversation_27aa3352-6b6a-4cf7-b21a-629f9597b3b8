package com.bukuwarung.ui_component.component.appbar

import android.app.Activity
import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseAppBar


class AppBarWithOptions :
    SimpleAppBar {

    private var addBackButton = false
    private lateinit var optionsLayout: LinearLayout
    private lateinit var customTitleLayout: LinearLayout

    constructor(context: Context) : super(context) {
        initView(context, null, 0)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(context, attrs, defStyleAttr)
    }

    private fun initView(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
        if (attrs == null) return


//    this.setNavigationOnClickListener {
//        (context as Activity).onBackPressed()
//    }
        with(context.obtainStyledAttributes(attrs, R.styleable.SimpleAppBar, defStyleAttr, 0)) {
            addBackButton = getBoolean(R.styleable.SimpleAppBar_backButton, false)
            recycle()
        }

        if (addBackButton) {
            this.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
            invalidate()
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            setTitleTextColor(context.getColor(R.color.white))
            navigationIcon?.setTint(context.getColor(R.color.white))
            invalidate()
        }

        optionsLayout = LinearLayout(context)
        optionsLayout.gravity = Gravity.CENTER_VERTICAL
        customTitleLayout = LinearLayout(context)
        addView(optionsLayout)
        addView(customTitleLayout)

    }

    fun addOption(title: String = "", imageResId: Int, clickListener: OnClickListener) {
        val toolbarOption =
            LayoutInflater.from(context).inflate(R.layout.toolbar_options, this, false)
        val l1: Toolbar.LayoutParams =
            LayoutParams(Toolbar.LayoutParams.WRAP_CONTENT, Toolbar.LayoutParams.MATCH_PARENT)
        l1.gravity = Gravity.END or Gravity.CENTER_VERTICAL
        optionsLayout.setLayoutParams(l1)
        val optionImage = toolbarOption.findViewById<AppCompatImageView>(R.id.tv_option_image)
        val optionTitle = toolbarOption.findViewById<TextView>(R.id.tv_option_title)
        optionImage.setImageResource(imageResId)
        if (title.isEmpty()) {
            optionTitle.visibility = View.GONE
        } else {
            optionTitle.text = title
        }
        toolbarOption.setOnClickListener(clickListener)
        toolbarOption.setPadding(0, 0, 8, 0)
        optionsLayout.addView(toolbarOption)
        invalidate()

    }

    fun addCustomTitle(
        title: String = "",
        subTitle: String = "",
        imageResId: Int,
        clickListener: OnClickListener
    ) {
        val customTitleView =
            LayoutInflater.from(context).inflate(R.layout.toolbar_title_subtitle, this, false)
        val l1: Toolbar.LayoutParams =
            LayoutParams(Toolbar.LayoutParams.WRAP_CONTENT, Toolbar.LayoutParams.WRAP_CONTENT)
        l1.gravity = Gravity.START
        customTitleLayout.setLayoutParams(l1)
        val imageView = customTitleView.findViewById<AppCompatImageView>(R.id.iv_image)
        val titleView = customTitleView.findViewById<TextView>(R.id.tv_title)
        val subTitleView = customTitleView.findViewById<TextView>(R.id.tv_subtitle)
        imageView.setBackgroundResource(imageResId)
        if (title.isEmpty()) {
            titleView.visibility = View.GONE
        } else {
            titleView.text = title
        }
        subTitleView.text = subTitle
        customTitleView.setOnClickListener(clickListener)
        customTitleView.setPadding(0, 0, 8, 0)
        customTitleLayout.addView(customTitleView)
        invalidate()
    }

    fun addCustomView(view: View) {
        val newLayout = LinearLayout(context)
        val l1 =
            LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT)
        view.setPadding(0, 0, 8, 0)
        newLayout.addView(view)
        l1.gravity = Gravity.CENTER_VERTICAL
        newLayout.layoutParams = l1
        newLayout.gravity = Gravity.CENTER_VERTICAL
        optionsLayout.addView(newLayout)
        invalidate()
    }

}