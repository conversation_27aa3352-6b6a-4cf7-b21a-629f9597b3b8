package com.bukuwarung.ui_component.component.appbar

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseAppBar

open class SimpleAppBar :
    BaseAppBar {

    private var addBackButton = false

    constructor(context: Context) : super(context) {
        initView(context, null, 0)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(context, attrs, defStyleAttr)
    }

    private fun initView(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
        if (attrs == null) return

        with(context.obtainStyledAttributes(attrs,R.styleable.SimpleAppBar, defStyleAttr, 0)) {
            addBackButton = getBoolean(R.styleable.SimpleAppBar_backButton,false)
            recycle()
        }

        if (addBackButton) {
            this.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
            invalidate()
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            setTitleTextColor(context.getColor(R.color.white))
            navigationIcon?.setTint(context.getColor(R.color.white))
        }



//        if(addBackButton)
//        {
//            this.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
//        }

    }


}