package com.bukuwarung.ui_component.component.button

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.view.Gravity
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.updatePadding
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseButton
import com.bukuwarung.ui_component.utils.dp

class BukuButton: BaseButton {

    lateinit var type : TypedArray
    var buttonType : Int = -1
    var buttonSize : Int = -1
    var buttonText : Int = -1

    constructor(context: Context) : super(context) {
        initView(context, null, 0)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(context, attrs, defStyleAttr)
    }

    private fun initView(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
        type = context.obtainStyledAttributes(attrs, R.styleable.BukuButton, 0, 0)
        buttonType = R.styleable.BukuButton_buttonType
        buttonSize = R.styleable.BukuButton_buttonDimension
        buttonText = R.styleable.BukuButton_buttonText

        setText(type.getString(buttonText))

        if (type.hasValue(buttonType)) {
            type.getString(buttonType)?.let { setButtonType(it) }
        }

        if (type.hasValue(buttonSize)) {
            textSize = when(type.getString(buttonSize)) {
                "large" -> {
                    resources.getDimension(R.dimen.dimen_16sp)
                }
                "medium" -> {
                    resources.getDimension(R.dimen.dimen_14sp)
                }
                else -> {
                    resources.getDimension(R.dimen.dimen_12sp)
                }
            }
        }
    }

    fun setButtonText(text: String) {
        setText(text)
    }
    fun setButtonType(typeString : String) {
        when (typeString) {
            "blue80" -> {
                setTextColor(resources.getColor(R.color.white))
                setBackgroundColor(resources.getColor(R.color.blue80))
            }
            "blue60" -> {
                setTextColor(resources.getColor(R.color.white))
                setBackgroundColor(resources.getColor(R.color.blue60))
            }
            "yellow60" -> {
                setTextColor(resources.getColor(R.color.black60))
                setBackgroundColor(resources.getColor(R.color.yellow60))
            }
            "yellow60WithBlack80" -> {
                setTextColor(resources.getColor(R.color.black80))
                setBackgroundColor(resources.getColor(R.color.yellow60))
            }
            "black5" -> {
                setTextColor(resources.getColor(R.color.blue40))
                setBackgroundColor(resources.getColor(R.color.black5))
            }
            "withBorder" -> {
                super.isBorderEnabled(true)
                super.setBorderColor(R.color.black40)
                setTextColor(resources.getColor(R.color.black60))
                setBackgroundColor(resources.getColor(R.color.transparent))
            }
            "withoutBorder" -> {
                super.isBorderEnabled(false)
                setTextColor(resources.getColor(R.color.black60))
                setBackgroundColor(resources.getColor(R.color.transparent))
            }
            "disableGrey" -> {
                setTextColor(resources.getColor(R.color.white))
                setBackgroundColor(resources.getColor(R.color.black20))
            }
        }
    }

    fun disableButton(){
        setTextColor(resources.getColor(R.color.white))
        setBackgroundColor(resources.getColor(R.color.black20))
        this.isEnabled = false
    }

    enum class ButtonType (val string: String) {

        YELLOW_60("yellow60"),
        BLUE_80("blue80"),
        BLUE_60("blue60"),
        YELLOW_60_WITH_BLACK_80("yellow60WithBlack80"),
        BLACK_5("black5"),
        DISABLE_GREY("disableGrey"),

    }

}