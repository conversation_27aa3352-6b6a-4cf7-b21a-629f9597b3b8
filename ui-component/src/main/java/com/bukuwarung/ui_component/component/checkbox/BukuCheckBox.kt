package com.bukuwarung.ui_component.component.checkbox

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.utils.dp
import com.google.android.material.checkbox.MaterialCheckBox

/**
 * A class that creates a Material Themed CheckBox.
 * B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends MaterialC<PERSON>ckBox to support an intermediate state as well.
 *
 * While using BukuCheckBox, a StateChangeListener should be set instead of CheckedChangeListener
 * BukuCheckBox can be in 3 states
 * 1. UNCHECKED
 * 2. INDETERMINATE
 * 3. CHECKED
 *
 * StateChangeListener will pass the state to the listener instead of passing checked state.
 * Note: checked is set to false when state is INDETERMINATE.
 */
class BukuCheckBox : MaterialCheckBox {

    private val mDefaultPaddingStart = 10.dp
    private val mDefaultLineSpacing = 4.dp.toFloat()
    private val mDefaultTextSize = 14f

    constructor(context: Context) : super(context) {
        init(null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(attrs)
    }

    constructor(
        context: Context, attrs: AttributeSet?, defStyleAttr: Int
    ) : super(context, attrs, defStyleAttr) {
        init(attrs)
    }

    enum class BukuCheckboxState {
        UNCHECKED, INDETERMINATE, CHECKED
    }

    private var state = BukuCheckboxState.UNCHECKED
    private var onStateChanged: ((state: BukuCheckboxState) -> Unit)? = null

    fun getState(): BukuCheckboxState {
        return state
    }

    fun setState(state: BukuCheckboxState) {
        this.state = state
        isChecked = when (state) {
            BukuCheckboxState.UNCHECKED -> false
            BukuCheckboxState.INDETERMINATE -> false
            BukuCheckboxState.CHECKED -> true
        }
        updateCheckBoxView()
    }

    fun setOnStateChangeListener(onStateChanged: ((state: BukuCheckboxState) -> Unit)) {
        this.onStateChanged = onStateChanged
    }

    fun check() {
        isChecked = true
        setState(BukuCheckboxState.CHECKED)
    }

    fun uncheck() {
        isChecked = false
        setState(BukuCheckboxState.UNCHECKED)
    }

    fun indeterminate() {
        isChecked = false
        setState(BukuCheckboxState.INDETERMINATE)
    }

    private fun init(attributeSet: AttributeSet?) {
        if (paddingStart == 0) {// setting default padding
            setPadding(mDefaultPaddingStart, paddingTop, paddingRight, paddingBottom)
        }

        if (lineSpacingExtra == 0f) {// setting default line spacing
            setLineSpacing(mDefaultLineSpacing, lineSpacingMultiplier)
        }

        val type =
            context.theme.obtainStyledAttributes(attributeSet, R.styleable.BukuCheckBox, 0, 0)
        if (!type.hasValue(R.styleable.BukuCheckBox_android_textSize))
            textSize = mDefaultTextSize
        if (!type.hasValue(R.styleable.BukuCheckBox_android_textColor))
            setTextColor(ContextCompat.getColor(context, R.color.black80))
        if (!type.hasValue(R.styleable.BukuCheckBox_android_fontFamily)) {
            val typeface = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                resources.getFont(R.font.roboto)
            } else {
                ResourcesCompat.getFont(context, R.font.roboto)
            }
            setTypeface(typeface)
        }
        type.recycle()

        setOnCheckedChangeListener { _, _ ->
            state = when (state) {
                BukuCheckboxState.INDETERMINATE -> BukuCheckboxState.UNCHECKED
                BukuCheckboxState.UNCHECKED -> BukuCheckboxState.CHECKED
                BukuCheckboxState.CHECKED -> BukuCheckboxState.UNCHECKED
            }
            onStateChanged?.invoke(state)
            updateCheckBoxView()
        }
        updateCheckBoxView()
    }

    private fun updateCheckBoxView() {
        buttonDrawable = null
        val btnDrawable = if (isEnabled) {
            when (state) {
                BukuCheckboxState.UNCHECKED -> R.drawable.buku_checkbox_unchecked
                BukuCheckboxState.INDETERMINATE -> R.drawable.buku_checkbox_indeterminate
                BukuCheckboxState.CHECKED -> R.drawable.buku_checkbox_checked
            }
        } else {
            when (state) {
                BukuCheckboxState.UNCHECKED -> R.drawable.buku_checkbox_unchecked_disabled
                BukuCheckboxState.INDETERMINATE -> R.drawable.buku_checkbox_indeterminate_disabled
                BukuCheckboxState.CHECKED -> R.drawable.buku_checkbox_checked_disabled
            }
        }
        // This is required otherwise set drawables doesn't look as intended
        buttonTintList = null
        setButtonDrawable(btnDrawable)
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        updateCheckBoxView()
    }
}