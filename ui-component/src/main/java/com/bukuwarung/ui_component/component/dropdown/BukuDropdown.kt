package com.bukuwarung.ui_component.component.dropdown

import android.content.Context
import android.util.AttributeSet
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseDropDown

class BukuDropdown(context: Context, attributeSet: AttributeSet? = null, defStyleAttr: Int = 0, itemList: List<String>) : BaseDropDown(context, attributeSet, itemList) {

    init {
        initView(context, attributeSet, defStyleAttr)
    }

    private fun initView(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) {
        if (attrs == null) return

        with(context.obtainStyledAttributes(attrs, R.styleable.BukuDropdown, defStyleAttr, 0)) {
            val selectText = getString(R.styleable.BukuDropdown_selectText) ?: ""
            val labelText = getString(R.styleable.BukuDropdown_labelText)
            val badgeText = getString(R.styleable.BukuDropdown_badgeText)
            super.setInitialTexts(selectText = selectText, labelText = labelText, badgeText = badgeText)
            recycle()
        }
    }

    open fun getSelectedString(): String {
        return super.getSelected()
    }
}