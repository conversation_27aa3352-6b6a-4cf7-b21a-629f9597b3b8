package com.bukuwarung.ui_component.component.empty_view

import android.content.Context
import android.util.AttributeSet
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseEmptyView
import com.bukuwarung.ui_component.utils.hideView


/**
 * BukuEmptyView is a standard UI component to show empty states for various cases.
 * It supports following default types of empty states
 * 1. SET_PRICE - To set the price of a stock
 * 2. CONNECTION_ERROR - When there is an issue with the user's connection
 * 3. NO_STOCK_RUNNING_LOW - For the screen that shows stocks which are running low
 * 4. ADD_STOCK - For the screen that shows stocks
 * 5. CREATE_NOTE_REMINDER - If there are no reminders set by the user
 * 6. REFERRAL_NOT_USED - If the user's referral hasn't been used.
 *
 * Other than these default empty states, BukuEmptyView also supports the CUSTOM empty state,
 * where the user gets complete control over the empty state icon, title, message and text of the CTA.
 *
 * BukuEmptyView defines a Callback interface to let developers handle the click listeners.
 *
 * Note: TextView for title and message support HTML text, so typeface of any substring in the
 * complete title/message can be changed easily.
 */
class BukuEmptyView(context: Context, attributeSet: AttributeSet?) :
    BaseEmptyView(context, attributeSet) {

    init {
        val attr =
            context.theme.obtainStyledAttributes(attributeSet, R.styleable.BukuEmptyView, 0, 0)
        val typeInt = R.styleable.BukuEmptyView_empty_view_type
        if (attr.hasValue(typeInt)) {
            val type = Companion.EmptyStateType.values()[attr.getInt(
                R.styleable.BukuEmptyView_empty_view_type,
                -1
            )]
            when (type) {
                Companion.EmptyStateType.SET_PRICE -> {
                    binding.tvEmptyTitle.hideView()
                    super.setMessage(context.getString(R.string.empty_price_message))
                    super.setCtaText(context.getString(R.string.set_price))
                    super.setEmptyIcon(R.drawable.ic_empty_box)
                }
                Companion.EmptyStateType.CONNECTION_ERROR -> {
                    super.setTitle(context.getString(R.string.connection_error_default_title))
                    super.setMessage(context.getString(R.string.check_connection_try_again))
                    binding.btnEmptyCta.hideView()
                    super.setEmptyIcon(R.drawable.ic_connection_error)
                }
                Companion.EmptyStateType.NO_STOCK_RUNNING_LOW -> {
                    super.setTitle(context.getString(R.string.empty_low_stock_title))
                    super.setMessage(context.getString(R.string.empty_low_stock_message))
                    binding.btnEmptyCta.hideView()
                    super.setEmptyIcon(R.drawable.ic_empty_box)
                }
                Companion.EmptyStateType.ADD_STOCK -> {
                    super.setTitle(context.getString(R.string.empty_stock_title))
                    super.setMessage(context.getString(R.string.empty_stock_message))
                    binding.btnEmptyCta.hideView()
                    super.setEmptyIcon(R.drawable.ic_empty_box)
                }
                Companion.EmptyStateType.CREATE_NOTE_REMINDER -> {
                    super.setTitle(context.getString(R.string.empty_reminder_title))
                    super.setMessage(context.getString(R.string.empty_reminder_message))
                    binding.btnEmptyCta.hideView()
                    super.setEmptyIcon(R.drawable.ic_empty_clock)
                }
                Companion.EmptyStateType.REFERRAL_NOT_USED -> {
                    super.setTitle(context.getString(R.string.empty_referral_title))
                    super.setMessage(context.getString(R.string.empty_referral_message))
                    binding.btnEmptyCta.hideView()
                    super.setEmptyIcon(R.drawable.ic_empty_referral)
                }
                Companion.EmptyStateType.CUSTOM -> {
                    val title = R.styleable.BukuEmptyView_empty_view_title
                    if (attr.hasValue(title)) {
                        super.setTitle(attr.getString(title))
                    } else {
                        binding.tvEmptyTitle.hideView()
                    }
                    val message = R.styleable.BukuEmptyView_empty_view_message
                    if (attr.hasValue(message)) {
                        super.setMessage(attr.getString(message))
                    } else {
                        binding.tvEmptyMessage.hideView()
                    }
                    val ctaText = R.styleable.BukuEmptyView_empty_view_cta_text
                    if (attr.hasValue(ctaText)) {
                        super.setCtaText(attr.getString(ctaText))
                    } else {
                        binding.btnEmptyCta.hideView()
                    }
                    val icon = R.styleable.BukuEmptyView_empty_view_icon
                    if (attr.hasValue(icon)) {
                        super.setEmptyIcon(attr.getResourceId(icon, R.drawable.ic_empty_box))
                    } else {
                        binding.ivEmptyIcon.hideView()
                    }
                }
            }
        }
        attr.recycle()
    }
}