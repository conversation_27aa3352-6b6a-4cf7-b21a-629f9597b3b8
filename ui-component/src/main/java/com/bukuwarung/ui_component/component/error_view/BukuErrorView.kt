package com.bukuwarung.ui_component.component.error_view

import android.content.Context
import android.util.AttributeSet
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty


/**
 * BukuErrorView is a standard UI component to show different types of errors.
 * It supports 3 default types of errors
 * 1. SERVER_UNREACHABLE - When the server is down
 * 2. SERVER_ERROR - When there is an error on the server i.e. INTERNAL_SERVER_ERROR
 * 3. CONNECTION_ERROR - When there is an issue with the user's connection
 *
 * Other than these 3 default error types, BukuErrorView also supports the CUSTOM error type,
 * where the user gets complete control over the error icon, title, message and text of the CTA.
 *
 * BukuErrorView defines a Callback interface to let developers handle the click listeners.
 *
 * Note: TextView for title and message support HTML text, so typeface of any substring in the
 * complete title/message can be changed easily.
 */
class BukuErrorView(context: Context, attributeSet: AttributeSet?) :
    BaseErrorView(context, attributeSet) {

    init {
        val attr =
            context.theme.obtainStyledAttributes(attributeSet, R.styleable.BukuErrorView, 0, 0)
        val typeInt = R.styleable.BukuErrorView_error_view_type
        if (attr.hasValue(typeInt)) {
            type = Companion.ErrorType.values()[attr.getInt(
                R.styleable.BukuErrorView_error_view_type,
                0
            )]
            when (type) {
                Companion.ErrorType.SERVER_UNREACHABLE -> setServerUnreachable()
                Companion.ErrorType.SERVER_ERROR -> setServerError()
                Companion.ErrorType.CONNECTION_ERROR -> setConnectionError()
                Companion.ErrorType.CUSTOM -> {
                    val title = R.styleable.BukuErrorView_error_view_title
                    val titleString = if (attr.hasValue(title)) {
                        attr.getString(title)
                    } else {
                        null
                    }
                    val message = R.styleable.BukuErrorView_error_view_message
                    val messageString = if (attr.hasValue(message)) {
                        attr.getString(message)
                    } else {
                        null
                    }
                    val ctaText = R.styleable.BukuErrorView_error_view_cta_text
                    val ctaString = if (attr.hasValue(ctaText)) {
                        attr.getString(ctaText)
                    } else {
                        null
                    }
                    val icon = R.styleable.BukuErrorView_error_view_icon
                    val iconRes = if (attr.hasValue(icon)) {
                        attr.getResourceId(icon, R.drawable.ic_server_error)
                    } else {
                        R.drawable.ic_server_error
                    }
                    setCustomError(titleString, messageString, ctaString, iconRes)
                }
            }
        }
        attr.recycle()
    }

    private fun setServerUnreachable() {
        super.setTitle(context.getString(R.string.server_unreachable_default_title))
        super.setMessage(context.getString(R.string.server_unreachable_default_message))
        super.setCtaText(context.getString(R.string.go_back))
        super.setErrorIcon(R.drawable.ic_server_unreachable)
    }

    private fun setServerError() {
        super.setTitle(context.getString(R.string.server_error_default_title))
        super.setMessage(context.getString(R.string.server_error_default_message))
        super.setCtaText(context.getString(R.string.try_again))
        super.setErrorIcon(R.drawable.ic_server_error)
    }

    private fun setConnectionError() {
        super.setTitle(context.getString(R.string.connection_error_default_title))
        super.setMessage(context.getString(R.string.connection_error_default_message))
        super.setCtaText(context.getString(R.string.go_back))
        super.setErrorIcon(R.drawable.ic_connection_error)
    }

    private fun setCustomError(title: String?, message: String?, ctaText: String?, iconRes: Int?) {
        if (title.isNullOrEmpty()) {
            binding.tvErrorTitle.hideView()
        } else {
            super.setTitle(title)
        }
        if (message.isNullOrEmpty()) {
            binding.tvErrorMessage.hideView()
        } else {
            super.setMessage(message)
        }
        if (ctaText.isNullOrEmpty()) {
            binding.btnErrorCta.hideView()
        } else {
            super.setCtaText(ctaText)
        }
        if (iconRes == null) {
            super.setErrorIcon(R.drawable.ic_server_error)
        } else {
            super.setErrorIcon(iconRes)
        }
    }

    fun setErrorType(
        type: Companion.ErrorType,
        title: String? = null,
        message: String? = null,
        ctaText: String? = null,
        iconRes: Int? = null
    ) {
        this.type = type
        when (type) {
            Companion.ErrorType.SERVER_UNREACHABLE -> setServerUnreachable()
            Companion.ErrorType.SERVER_ERROR -> setServerError()
            Companion.ErrorType.CONNECTION_ERROR -> setConnectionError()
            Companion.ErrorType.CUSTOM -> setCustomError(title, message, ctaText, iconRes)
        }
        if (title.isNotNullOrEmpty()) super.setTitle(title)
        if (message.isNotNullOrEmpty()) super.setMessage(message)
        if (ctaText.isNotNullOrEmpty()) super.setCtaText(ctaText)
        if (iconRes != null) super.setErrorIcon(iconRes)
    }
}