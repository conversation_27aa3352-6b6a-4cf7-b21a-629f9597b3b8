package com.bukuwarung.ui_component.component.inputview

import android.content.Context
import android.util.AttributeSet
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseInputView
import com.bukuwarung.ui_component.utils.getColorCompat
import com.bukuwarung.ui_component.utils.getDrawableCompat

class BukuInputView(context: Context, attributeSet: AttributeSet?) :
    BaseInputView(context, attributeSet) {

    init {
        val type = context.obtainStyledAttributes(attributeSet, R.styleable.BukuInputView, 0, 0)
        val title = R.styleable.BukuInputView_title
        val titleHint = R.styleable.BukuInputView_titleHint
        val hint = R.styleable.BukuInputView_hint
        val bottomText = R.styleable.BukuInputView_bottomText
        val rightDrawable = R.styleable.BukuInputView_rightDrawable
        val leftDrawable = R.styleable.BukuInputView_leftDrawable

        if (type.hasValue(title)) {
            super.setTitle(type.getString(title))
        }

        if(type.hasValue(titleHint)){
            super.setTitleHint(type.getString(titleHint))
        }

        if (type.hasValue(hint)) {
            super.setHint(type.getString(hint))
        }

        if (type.hasValue(bottomText)) {
            super.setBottomText(type.getString(bottomText))
        }

        if (type.hasValue(rightDrawable)) {
            val rightDrawableImage = type.getDrawable(rightDrawable)
            if (rightDrawableImage != null) {
                super.setRightDrawable(rightDrawableImage)
            }
        }

        if (type.hasValue(leftDrawable)) {
            val leftDrawableImage = type.getDrawable(leftDrawable)
            if (leftDrawableImage != null) {
                super.setLeftDrawable(leftDrawableImage)
            }
        }

        type.recycle()

    }

    fun setClearDrawable() {
        if (getText().isNotBlank()) {
            super.setRightDrawableWithListener(
                context.getDrawableCompat(R.drawable.ic_clear)
            ) {
                super.setText(null)
            }
        } else {
            super.setRightDrawable(null)
        }
    }

    fun setDropDownDrawable(action: () -> Unit) {
        context.getDrawableCompat(R.drawable.ic_down)
            ?.let { super.setRightDrawable(it) }
        binding.etNumber.setOnClickListener {
            action()
        }
        binding.etNumber.keyListener = null
        binding.etNumber.isFocusable = false
    }

    fun setErrorMessage(message: String) {
        super.setBottomText(message)
        super.setInputTextColor(context.getColorCompat(R.color.red_60))
        super.setBottomTextColor(context.getColorCompat(R.color.red_60))
    }

    fun setSuccessState(message: String) {
        super.setBottomText(message)
        super.setBottomTextColor(context.getColorCompat(R.color.black80))
        super.setInputTextColor(context.getColorCompat(R.color.black80))
    }



}