package com.bukuwarung.ui_component.component.inputview

import android.content.Context
import android.util.AttributeSet
import android.widget.EditText
import com.bukuwarung.ui_component.R
import com.bukuwarung.ui_component.base.BaseSearchView
import com.bukuwarung.ui_component.utils.setSingleClickListener

class BukuSearchView(context: Context, attributeSet: AttributeSet?) :
    BaseSearchView(context, attributeSet) {

    init {
        val type = context.obtainStyledAttributes(attributeSet, R.styleable.BukuSearchView, 0, 0)
        val editText = R.styleable.BukuSearchView_editText
        val hint = R.styleable.BukuSearchView_editTextHint
        val leftDrawable = R.styleable.BukuSearchView_editTextLeftDrawable
        val rightDrawable = R.styleable.BukuSearchView_editTextRightDrawable

        if (type.hasValue(editText)) {
            super.setText(type.getString(editText))
        }

        if (type.hasValue(hint)) {
            super.setHint(type.getString(hint))
        }

        if (type.hasValue(leftDrawable)) {
            val leftDrawableImage = type.getDrawable(leftDrawable)
            if (leftDrawableImage != null) {
                super.setLeftDrawable(leftDrawableImage, {})
            }
        }

        if (type.hasValue(rightDrawable)) {
            val rightDrawableImage = type.getDrawable(leftDrawable)
            if (rightDrawableImage != null) {
                super.setLeftDrawable(rightDrawableImage, {})
            }
        }

        type.recycle()
    }

    fun getEditText(): EditText? {
        return binding.tilSearch.editText
    }

    fun addSpaceAfterEveryFourCharacter() {
        val inputNumber = getTextByRemovingSpaces()
        var inputNumberWithSpaces = ""
        for (i in inputNumber.indices) {
            if (i != 0 && i % 4 == 0) {
                inputNumberWithSpaces += " "
            }
            inputNumberWithSpaces += inputNumber[i]
        }
        super.setText(inputNumberWithSpaces)
        binding.tilSearch.editText?.setSelection(binding.tilSearch.editText?.length() ?: 0)
    }

    fun getTextByRemovingSpaces(): String {
        val stringWithSpaces = binding.tilSearch.editText?.text.toString()
        return stringWithSpaces.replace(" ", "")
    }

    fun isErrorEnabled(flag: Boolean) {
        binding.tilSearch.isErrorEnabled = flag
    }

    fun setSearchClick(action: () -> Unit) {
        binding.tilSearch.isFocusableInTouchMode = false
        binding.tietSearch.isFocusableInTouchMode = false
        binding.tilSearch.editText?.setSingleClickListener {
            action()
        }
    }

}