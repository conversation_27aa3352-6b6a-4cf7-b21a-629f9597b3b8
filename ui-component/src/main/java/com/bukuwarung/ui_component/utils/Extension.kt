package com.bukuwarung.ui_component.utils

import android.content.Context
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.SystemClock
import android.text.Html
import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat

fun View.setSingleClickListener(debounceTime: Long = 500L, action: () -> Unit) {
    this.setOnClickListener(object : View.OnClickListener {
        private var lastClickTime: Long = 0
        override fun onClick(v: View) {
            if (SystemClock.elapsedRealtime() - lastClickTime < debounceTime) return
            else action()
            lastClickTime = SystemClock.elapsedRealtime()
        }
    })
}

val Int.dp: Int
    get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()

fun TextView.textHTML(html: String?) {
    html ?: return
    text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        Html.fromHtml(html, Html.FROM_HTML_MODE_COMPACT)
    } else {
        Html.fromHtml(html)
    }
}

fun View.showView() {
    this.visibility = View.VISIBLE
}

fun View.hideView() {
    this.visibility = View.GONE
}

fun View.invisibleView() {
    this.visibility = View.INVISIBLE
}

fun String?.isNotNullOrEmpty() = !this.isNullOrEmpty()

fun Context.getColorCompat(colorRes: Int): Int {
    return ContextCompat.getColor(this, colorRes)
}

fun Context.getDrawableCompat(drawableRes: Int): Drawable? {
    return ContextCompat.getDrawable(this, drawableRes)
}

fun TextView.setDrawableRightListener(listener: () -> Unit) {
    isClickable = true
    setOnTouchListener { _, motionEvent ->
        val drawableRight = 2
        if (motionEvent.action != MotionEvent.ACTION_UP || compoundDrawables[drawableRight] == null)
            return@setOnTouchListener false
        if (motionEvent.rawX >= (right - compoundDrawables[drawableRight].bounds.width())) {
            listener()
            return@setOnTouchListener false
        }
        false
    }
}