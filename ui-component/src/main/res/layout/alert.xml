<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/cl_base"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/blue40"
    android:paddingVertical="@dimen/dimen_10dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:srcCompat="@drawable/ic_alert_error" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_alert_text"
        style="@style/BukuTextStyle.Black60.12Regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        tool:text="Laporan Dummy Text Is notnotnotnotnot not not good, its not "
        app:layout_constraintEnd_toStartOf="@id/action_barrier"
        app:layout_constraintStart_toEndOf="@+id/iv_left"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:srcCompat="@drawable/ic_alert_warning" />

    <TextView
        android:id="@+id/tv_action_btn"
        style="@style/TextAppearance.AppCompat.Subhead"
        android:layout_width="0dp"
        android:minWidth="@dimen/dimen_8dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:textColor="@color/blue60"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:text="Action" />

    <androidx.constraintlayout.widget.Barrier
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="left"
        android:id="@+id/action_barrier"
        app:constraint_referenced_ids="iv_right, tv_action_btn"/>

</androidx.constraintlayout.widget.ConstraintLayout>