<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/dimen_16dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="title" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:contentDescription="TODO"
        android:src="@drawable/ic_cross"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

    <ImageView
        android:id="@+id/dialogImage"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dimen_30dp"
        android:background="@color/gray_200"
        android:contentDescription="TODO"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:visibility="gone" />

    <EditText
        android:id="@+id/et_input"
        style="@style/EditTextBordered"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="@dimen/dimen_26dp"
        android:background="@drawable/bg_edittext_default"
        android:focusedByDefault="true"
        android:padding="@dimen/dimen_16dp"
        android:textColor="@color/by_date_category"
        android:textColorHint="@color/hint_color"
        android:textSize="15sp"
        app:layout_constraintTop_toBottomOf="@+id/dialogImage"
        tools:text="Empty"
        tools:visibility="gone" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintTop_toBottomOf="@+id/et_input"
        tools:text="message" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_action_two"
        style="@style/ButtonOutline.Blue"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:textAllCaps="false"
        android:textAppearance="@style/Heading3"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toStartOf="@+id/btn_action_one"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message"
        tools:text="yes" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_action_one"
        style="@style/ButtonFill.Blue"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:paddingVertical="@dimen/dimen_11dp"
        android:textAppearance="@style/Heading3"
        app:cornerRadius="@dimen/dimen_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_action_two"
        app:layout_constraintTop_toBottomOf="@id/tv_message"
        tools:text="no" />


</androidx.constraintlayout.widget.ConstraintLayout>