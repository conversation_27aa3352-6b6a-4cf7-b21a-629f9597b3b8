<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingHorizontal="@dimen/dimen_8dp">

    <ImageView
        android:id="@+id/iv_popover"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/gray_200"
        android:scaleType="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:contentDescription="TODO"
        android:padding="@dimen/dimen_10dp"
        android:src="@drawable/ic_cross_circle_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_popover" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_25dp"
        android:layout_marginTop="@dimen/dimen_30dp"
        app:layout_constraintEnd_toEndOf="@+id/iv_popover"
        app:layout_constraintStart_toStartOf="@+id/iv_popover"
        app:layout_constraintTop_toBottomOf="@+id/iv_popover"
        tools:text="title" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_25dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="message" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_action_one"
        style="@style/ButtonFill.Yellow"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:paddingVertical="@dimen/dimen_11dp"
        android:textAppearance="@style/Heading3"
        app:cornerRadius="@dimen/dimen_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/tv_message"
        app:layout_constraintStart_toStartOf="@+id/tv_message"
        app:layout_constraintTop_toBottomOf="@id/tv_message"
        tools:text="no" />


</androidx.constraintlayout.widget.ConstraintLayout>