<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dimen_4dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:textColor="@color/black80"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Label" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_badge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_4dp"
        android:background="@drawable/rounded_corner"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:textColor="@color/black80"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_label"
        app:layout_constraintStart_toEndOf="@id/tv_label"
        app:layout_constraintTop_toTopOf="@id/tv_label"
        tools:text="Badge" />

    <View
        android:id="@+id/view_spinner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_48dp"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:background="@drawable/dropdown_corner"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_label" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_8dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:textColor="@color/black80"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/view_spinner"
        app:layout_constraintStart_toStartOf="@id/view_spinner"
        app:layout_constraintTop_toTopOf="@id/view_spinner"
        tools:text="Select" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_8dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_select"
        app:layout_constraintEnd_toEndOf="@id/view_spinner"
        app:layout_constraintTop_toTopOf="@id/tv_select"
        app:srcCompat="@drawable/ic_down" />

</androidx.constraintlayout.widget.ConstraintLayout>