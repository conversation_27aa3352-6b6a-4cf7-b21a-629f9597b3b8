<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dimen_24dp"
    android:paddingBottom="@dimen/dimen_8dp">

    <ImageView
        android:id="@+id/iv_empty_icon"
        android:layout_width="160dp"
        android:layout_height="160dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@drawable/ic_server_error" />

    <TextView
        android:id="@+id/tv_empty_title"
        style="@style/BukuTextStyle.Black80.18Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_40dp"
        android:layout_marginTop="@dimen/dimen_14dp"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dimen_24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_empty_icon"
        tools:text="@string/connection_error_default_title" />

    <TextView
        android:id="@+id/tv_empty_message"
        style="@style/BukuTextStyle.Black60.14Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_40dp"
        android:layout_marginTop="@dimen/dimen_14dp"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dimen_24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_empty_title"
        tools:text="@string/connection_error_default_message" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_empty_cta"
        style="@style/BukuEmptyViewButtonFill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_40dp"
        android:layout_marginTop="@dimen/dimen_14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_empty_message"
        tools:text="@string/set_price" />

</androidx.constraintlayout.widget.ConstraintLayout>