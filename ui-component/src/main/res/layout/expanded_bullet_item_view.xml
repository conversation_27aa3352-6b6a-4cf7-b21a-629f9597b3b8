<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_bullet"
        style="@style/BukuTextStyle.Black60.14Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="\u2022"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_content"
        style="@style/BukuTextStyle.Black60.14Regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_bullet"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="This is markdown content" />

</androidx.constraintlayout.widget.ConstraintLayout>