<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_expanded_bullet_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_yellow80_corner_8dp_stroke_yellow5">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_warning_icon"
        android:layout_width="@dimen/dimen_18dp"
        android:layout_height="@dimen/dimen_18dp"
        android:layout_margin="@dimen/dimen_8dp"
        android:src="@drawable/ic_alert_warning"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_warning"
        style="@style/BukuTextStyle.Black80.14Bold"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_8dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/ll_warning_list"
        app:layout_constraintEnd_toStartOf="@id/iv_expand_icon"
        app:layout_constraintStart_toEndOf="@id/iv_warning_icon"
        app:layout_constraintTop_toTopOf="@id/iv_warning_icon"
        tools:text="Saat ini ada beberapa Bank yang mengalami gangguan transfer. Cek daftarnya di sini."
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_expand_icon"
        android:layout_width="@dimen/dimen_24dp"
        android:layout_height="@dimen/dimen_24dp"
        android:layout_margin="@dimen/dimen_8dp"
        android:src="@drawable/ic_chevron_down"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_warning_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_8dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_warning" />

</androidx.constraintlayout.widget.ConstraintLayout>