<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        style="@style/BukuTextStyle.Black80.14Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Title text here" />

    <TextView
        android:id="@+id/tv_title_hint"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_4dp"
        android:textColor="@color/black40"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_title"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        tools:text="(opsional)"
        tools:visibility="visible" />

    <EditText
        android:id="@+id/et_number"
        style="@style/BukuTextStyle.Black80.18Bold"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:imeOptions="actionDone"
        android:inputType="number"
        android:singleLine="true"
        android:textColorHint="@color/black10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_bottom_text"
        style="@style/BukuTextStyle.Black60.10Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_4dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_number"
        tools:text="Bottom message or error message here"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>