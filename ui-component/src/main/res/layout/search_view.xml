<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.textfield.TextInputLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/til_search"
    style="@style/SearchTextInputLayoutStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/tiet_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black80"
        android:textColorHint="@color/black20" />

</com.google.android.material.textfield.TextInputLayout>