<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/custom_title_main_container"
    android:orientation="horizontal"
    android:gravity="center"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/iv_image"
        android:src="@android:mipmap/sym_def_app_icon"
        />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/dimen_8dp"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="title"
            android:id="@+id/tv_title"
            style="@style/Body2"
            />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="subtitle"
            android:id="@+id/tv_subtitle"
            style="@style/Label2"
            />
    </LinearLayout>


</LinearLayout>