<?xml version="1.0" encoding="utf-8"?>
<resources>

<declare-styleable name="BukuBadgeView">
<attr name="android:background" />
<attr name="mbtv_border_width" format="dimension" />
<attr name="mbtv_border_color" format="color" />
<attr name="mbtv_border_alpha" format="float" />
<attr name="mbtv_type" format="enum">
    <enum name="shadow_gradual" value="1"/>
    <enum name="shadow_alpha" value="2"/>
</attr>
    <attr name="badgeType" format="string"/>

    <attr name="badge_type" format="enum">
        <enum name="success" value="0"/>
        <enum name="pending" value="1"/>
        <enum name="failed" value="2"/>
        <enum name="expired" value="3"/>
    </attr>
</declare-styleable>


    <declare-styleable name="BaseAppBar">
    </declare-styleable>

    <declare-styleable name="SimpleAppBar">
        <attr name="backButton" format="boolean" />
    </declare-styleable>

    <declare-styleable name="BukuAlert">
        <attr name="type" format="string" />
        <attr name="actionImage" format="reference" />
        <attr name="actionText" format="string" />
        <attr name="alertText" format="string" />
    </declare-styleable>

    <declare-styleable name="BukuButton">
        <attr name="buttonType" format="string" />
        <attr name="buttonDimension" format="string" />
        <attr name="buttonText" format="string" />
    </declare-styleable>
    <declare-styleable name="BukuErrorView">
        <attr name="error_view_type" format="enum">
            <enum name="SERVER_UNREACHABLE" value="0" />
            <enum name="SERVER_ERROR" value="1" />
            <enum name="CONNECTION_ERROR" value="2" />
            <enum name="CUSTOM" value="3" />
        </attr>
        <attr name="error_view_title" format="string" />
        <attr name="error_view_message" format="string|reference" />
        <attr name="error_view_cta_text" format="string" />
        <attr name="error_view_icon" format="reference" />
    </declare-styleable>
    <declare-styleable name="BukuEmptyView">
        <attr name="empty_view_type" format="enum">
            <enum name="SET_PRICE" value="0" />
            <enum name="CONNECTION_ERROR" value="1" />
            <enum name="NO_STOCK_RUNNING_LOW" value="2" />
            <enum name="ADD_STOCK" value="3" />
            <enum name="CREATE_NOTE_REMINDER" value="4" />
            <enum name="REFERRAL_NOT_USED" value="5" />
            <enum name="CUSTOM" value="6" />
        </attr>
        <attr name="empty_view_title" format="string" />
        <attr name="empty_view_message" format="string|reference" />
        <attr name="empty_view_cta_text" format="string" />
        <attr name="empty_view_icon" format="reference" />
    </declare-styleable>

    <declare-styleable name="BukuCheckBox">
        <attr name="android:textColor"/>
        <attr name="android:textSize"/>
        <attr name="android:fontFamily"/>
    </declare-styleable>

    <declare-styleable name="BukuDropdown">
        <attr name="selectText" format="string" />
        <attr name="labelText" format="string" />
        <attr name="badgeText" format="string" />
    </declare-styleable>

    <declare-styleable name="BukuInputView">
        <attr name="title" format="string" />
        <attr name="titleHint" format="string" />
        <attr name="hint" format="string" />
        <attr name="bottomText" format="string" />
        <attr name="rightDrawable" format="reference" />
        <attr name="leftDrawable" format="reference" />
    </declare-styleable>

    <declare-styleable name="BukuSearchView">
        <attr name="editText" format="string" />
        <attr name="editTextHint" format="string" />
        <attr name="editTextRightDrawable" format="reference" />
        <attr name="editTextLeftDrawable" format="reference" />
    </declare-styleable>

</resources>