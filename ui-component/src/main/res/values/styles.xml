<resources>

    <!--Text styles-->
    <style name="BukuTextStyle">
        <item name="android:ellipsize">end</item>
    </style>

    <style name="BukuTextStyle.Black80" parent="BukuTextStyle">
        <item name="android:textColor">@color/black80</item>
    </style>

    <style name="BukuTextStyle.Black60" parent="BukuTextStyle">
        <item name="android:textColor">@color/black60</item>
    </style>


    <style name="BukuTextStyle.Black40" parent="BukuTextStyle">
        <item name="android:textColor">@color/black40</item>
    </style>

    <style name="BukuTextStyle.Black80.14Regular" parent="BukuTextStyle.Black80">
        <item name="android:textSize">@dimen/dimen_14sp</item>
        <item name="fontFamily">@font/roboto</item>
    </style>

    <style name="BukuTextStyle.Black80.14Bold" parent="BukuTextStyle.Black80">
        <item name="android:textSize">@dimen/dimen_14sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
    </style>

    <style name="BukuTextStyle.Black60.16Bold" parent="BukuTextStyle.Black60">
        <item name="android:textSize">@dimen/dimen_16sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
    </style>

    <style name="BukuTextStyle.Black60.14Regular" parent="BukuTextStyle.Black60">
        <item name="android:textSize">@dimen/dimen_14sp</item>
        <item name="fontFamily">@font/roboto</item>
    </style>


    <style name="BukuTextStyle.Black60.12Regular" parent="BukuTextStyle.Black60">
        <item name="android:textSize">@dimen/dimen_12sp</item>
        <item name="fontFamily">@font/roboto</item>
    </style>

    <style name="BukuTextStyle.Black60.10Regular" parent="BukuTextStyle.Black60">
        <item name="android:textSize">@dimen/dimen_10sp</item>
        <item name="fontFamily">@font/roboto</item>
    </style>

    <style name="BukuTextStyle.Black40.18Regular" parent="BukuTextStyle.Black40">
        <item name="android:textSize">@dimen/dimen_18sp</item>
        <item name="fontFamily">@font/roboto</item>
    </style>

    <style name="ButtonFill" parent="Widget.MaterialComponents.Button.TextButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/btn_text_color_state</item>
        <item name="backgroundTint">@color/btn_color_state</item>
        <item name="iconGravity">textStart</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="ButtonFill.Blue" parent="ButtonFill">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/colorPrimary</item>
    </style>

    <style name="ButtonFill.Yellow" parent="ButtonFill">
        <item name="android:textColor">@color/black</item>
        <item name="backgroundTint">@color/colorAccent</item>
    </style>

    <style name="Heading1" parent="BaseHeading">
        <item name="android:textSize">@dimen/dimen_24sp</item>
        <item name="android:lineSpacingExtra">8sp</item>
    </style>

    <style name="Heading2" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_18sp</item>
    </style>

    <style name="Heading3" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_16sp</item>
    </style>

    <style name="SubHeading1" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_14sp</item>
    </style>

    <style name="SubHeading2" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Body1" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_16sp</item>
    </style>

    <style name="Body2" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_14sp</item>
    </style>

    <style name="Body3" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Body4" parent="Body3">
        <item name="android:textColor">@color/black40</item>
    </style>

    <style name="Body5" parent="Body3">
        <item name="android:textColor">@color/black60</item>
    </style>

    <style name="Label1" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Label2" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_10sp</item>
    </style>

    <!--Text Styles-->
    <style name="BaseTextView" parent="Widget.AppCompat.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:lineSpacingExtra">6sp</item>
        <item name="android:textColor">@color/black80</item>
    </style>

    <style name="BaseHeading" parent="BaseTextView">
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="BukuTextStyle.Black80.18Bold" parent="BukuTextStyle.Black80">
        <item name="android:textSize">@dimen/dimen_18sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
    </style>

    <style name="BukuTextStyle.Black80.16Bold" parent="BukuTextStyle.Black80">
        <item name="android:textSize">@dimen/dimen_16sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
    </style>

    <style name="BukuEmptyViewButtonFill" parent="Widget.MaterialComponents.Button">
        <item name="android:textSize">@dimen/dimen_16sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="android:textColor">@color/black80</item>
        <item name="android:textAppearance">@style/BukuTextStyle.Black80.16Bold</item>
        <item name="cornerRadius">@dimen/dimen_4dp</item>
        <item name="textAllCaps">false</item>
        <item name="backgroundTint">@color/yellow60</item>
        <item name="rippleColor">@color/yellow5</item>
    </style>


    <style name="BukuErrorButtonOutline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textSize">@dimen/dimen_16sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="android:textColor">@color/blue60</item>
        <item name="android:textAppearance">@style/BukuTextStyle.Black60.16Bold</item>
        <item name="cornerRadius">@dimen/dimen_4dp</item>
        <item name="strokeColor">@color/blue60</item>
        <item name="strokeWidth">@dimen/dimen_1dp</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="SearchTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeColor">@color/search_input_box_stroke</item>
        <item name="boxBackgroundColor">@color/white</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/dimen_8dp</item>
        <item name="boxCornerRadiusBottomStart">@dimen/dimen_8dp</item>
        <item name="boxCornerRadiusTopEnd">@dimen/dimen_8dp</item>
        <item name="boxCornerRadiusTopStart">@dimen/dimen_8dp</item>
    </style>

    <style name="PpobFilterChipStyle">
        <item name="chipBackgroundColor">@color/ppob_chip_color_state</item>
        <item name="android:textColor">@color/ppob_chip_text_color_state</item>
        <item name="chipStrokeColor">@color/ppob_chip_stroke_tint_new</item>
        <item name="android:checkable">true</item>
        <item name="checkedIconEnabled">false</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="ButtonOutline" parent="Widget.MaterialComponents.Button.OutlinedButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/web_orange</item>
        <item name="strokeColor">@color/web_orange</item>
        <item name="cornerRadius">4dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="ButtonOutline.Blue" parent="ButtonOutline">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
        <item name="strokeColor">@color/colorPrimary</item>
    </style>


</resources>
